# Organization Deactivation Handling

This document describes the comprehensive organization deactivation handling system implemented in the Aran Care healthcare application.

## Overview

The organization deactivation feature provides robust access control when organizations are deactivated by super administrators. It ensures users cannot access deactivated organization resources while providing clear messaging and alternative access paths.

## Features Implemented

### 1. Database Schema Changes

- **Organization Status Field**: Added `status` field to Organization table with values `active` (default) or `inactive`
- **Database Constraints**: Enforced valid status values with check constraint
- **Indexing**: Added index on status field for performance
- **Migration**: Created migration file for production deployment

### 2. Organization Deactivated Page (`/organization-deactivated`)

**Location**: `apps/frontend/src/app/organization-deactivated/page.tsx`

**Features**:

- Professional UI with clear deactivation messaging
- Organization name and logo display
- Instructions to contact system administrator
- Multi-organization user support with active organization switching
- Automatic sign-out option for users with no active organizations
- Responsive design consistent with existing UI patterns

### 3. Enhanced Middleware Protection

**Location**: `apps/frontend/src/middleware/organization-status.ts`

**Features**:

- Automatic organization status checking for authenticated users
- Redirect to deactivation page when accessing inactive organizations
- Performance optimized with database queries
- Error handling to prevent application breakage
- Integration with existing authentication middleware

### 4. Login Flow Enhancement

**Location**: `apps/frontend/src/app/api/login/route.ts`

**Features**:

- Organization status checking during login
- Automatic fallback to active organizations
- Enhanced user experience for multi-organization users
- Seamless handling of default organization deactivation

### 5. Organization Switching Enhancement

**Locations**:

- `apps/frontend/src/app/api/user/switch-context/route.ts`
- `apps/frontend/src/components/organization-switcher.tsx`
- `apps/frontend/src/components/organization-header-switcher.tsx`
- `apps/frontend/src/components/context-switcher.tsx`

**Features**:

- Status validation before organization switching
- Clear error handling with deactivation page redirection
- Visual status indicators (Active/Inactive badges)
- Disabled styling for inactive organizations
- Consistent behavior across all switcher components

### 6. API Enhancements

**Enhanced APIs**:

- `/api/user/organizations` - Now includes organization status
- `/api/user/switch-context` - Validates organization status
- `/api/admin/organizations/[id]/status` - Toggle organization status (Super Admin)

**New Helper Functions**:

- `isOrganizationActive()` - Check if organization is active
- `getOrganizationStatus()` - Get organization details with status
- `isCurrentOrganizationActive()` - Check current user's organization status

## Technical Implementation Details

### Database Changes Applied

```sql
-- Add status field with default value
ALTER TABLE "Organization" ADD COLUMN "status" TEXT NOT NULL DEFAULT 'active';

-- Create index for performance
CREATE INDEX "Organization_status_idx" ON "Organization"("status");

-- Add constraint for valid values
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_status_check"
CHECK ("status" IN ('active', 'inactive'));
```

### Middleware Flow

1. **Request Interception**: Middleware checks all authenticated routes
2. **Organization Context**: Extracts organization ID from cookies
3. **Status Validation**: Queries database for organization status
4. **Conditional Redirect**: Redirects to deactivation page if inactive
5. **Error Handling**: Graceful fallback on errors

### Multi-Organization User Handling

1. **Active Organization Detection**: System identifies available active organizations
2. **Automatic Switching**: Provides options to switch to active organizations
3. **Fallback Handling**: Sign-out option when no active organizations available
4. **Session Preservation**: Maintains user session while blocking deactivated org access

## User Experience Flow

### Single Organization User

1. User attempts to access deactivated organization
2. Redirected to deactivation page with organization details
3. Clear messaging about deactivation status
4. Option to sign out and contact administrator

### Multi-Organization User

1. User attempts to access deactivated organization
2. Redirected to deactivation page
3. List of available active organizations displayed
4. One-click switching to active organizations
5. Fallback sign-out option available

## Super Admin Controls

Super administrators can:

- View all organizations with status indicators in `/admin/organizations`
- Toggle organization status between active/inactive
- Monitor organization access patterns
- Receive audit logs of status changes

## Security Considerations

1. **Server-Side Validation**: All status checks performed server-side
2. **Cookie Security**: Secure cookie handling for organization context
3. **Database Constraints**: Enforced data integrity at database level
4. **Audit Trail**: Status changes logged for compliance
5. **Graceful Degradation**: System remains functional during errors

## Performance Optimizations

1. **Database Indexing**: Optimized queries with status index
2. **Caching Strategy**: Minimal database calls in middleware
3. **Error Handling**: Fast fallback to prevent blocking
4. **Selective Checking**: Only authenticated routes checked

## Testing

The implementation includes:

- Database constraint validation
- Status field functionality testing
- Middleware redirection testing
- UI component status indicator testing
- Multi-organization flow testing

## Production Deployment

1. **Migration File**: `20250616000001_add_organization_status/migration.sql`
2. **Environment Variables**: No new environment variables required
3. **Database Changes**: Automatically applied via migration
4. **Backward Compatibility**: Existing organizations default to 'active'

## Monitoring and Maintenance

- Monitor organization status changes in admin logs
- Track deactivation page access patterns
- Review user feedback on deactivation messaging
- Ensure migration success in production deployment

This comprehensive implementation ensures robust organization deactivation handling while maintaining excellent user experience and system security.

---

# Super Admin Setup and Database Synchronization

## Production-Ready Super Admin Creation API

### API Endpoint: `/api/admin/create-super-admin`

**Enhanced for Production Use:**

- **Development**: Works without authentication
- **Production**: Requires `x-admin-auth-key` header with `SUPER_ADMIN_CREATION_KEY` environment variable

**Usage:**

```bash
# Development
curl -X POST http://localhost:3000/api/admin/create-super-admin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'

# Production
curl -X POST https://yourapp.com/api/admin/create-super-admin \
  -H "Content-Type: application/json" \
  -H "x-admin-auth-key: YOUR_SECRET_KEY" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

**Features:**

- Email validation
- Password strength validation (minimum 8 characters)
- Automatic name generation from email if not provided
- Creates super admin organization automatically
- Proper role assignment in UserOrganization table

## Super Admin Access Control

### Middleware Protection

- **Super Admins**: Restricted to `/admin` routes only
- **Regular Users**: Cannot access `/admin` routes
- **Automatic Redirection**: Based on user role during login

### Login Flow Enhancement

- Super admins automatically redirected to `/admin` after login
- Regular users redirected to `/dashboard`
- Role-based routing handled in login API response

## Database Schema Synchronization

### Applied Database Changes

1. **Organization Status Field**: Added with active/inactive values
2. **LabTestRequest Table**: Created with full relations
3. **DiagnosticReport Enhancement**: Added labTestRequestId field
4. **Invoice Tables**: Created Invoice and InvoiceItem tables
5. **LinkTokenRequest Table**: Created for ABDM functionality

### Migration File

- **Location**: `prisma/migrations/20250617000001_add_lab_test_request_and_sync_schema/migration.sql`
- **Production Ready**: Includes IF NOT EXISTS checks
- **Idempotent**: Safe to run multiple times

## Environment Variables for Production

Add to your production environment:

```env
# Super Admin Creation (Production Only)
SUPER_ADMIN_CREATION_KEY=your-very-secure-random-key-here
```

## Security Features

1. **Production Authentication**: Requires secret key for super admin creation
2. **Role Isolation**: Super admins cannot access main app routes
3. **Automatic Redirection**: Prevents unauthorized access attempts
4. **Database Constraints**: Enforced data integrity
5. **Audit Trail**: All admin actions logged

## Deployment Checklist

- [ ] Set `SUPER_ADMIN_CREATION_KEY` environment variable
- [ ] Run database migration
- [ ] Create initial super admin user
- [ ] Test admin portal access
- [ ] Verify role-based redirection
- [ ] Test organization deactivation flow

This implementation provides a complete, production-ready super admin system with proper security, access control, and database synchronization.
