"use client";

import { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form } from "@/components/ui/form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

import { Loader2, Plus, Pill } from "lucide-react";
import { toast } from "sonner";
// import { useDebounce } from "@/hooks/use-debounce";
import { Input } from "@/components/ui/input";
import {
  FormField,
  FormItem,
  // FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

// interface RxNormMedication {
//   rxcui: string;
//   name: string;
//   strength: string;
//   form: string;
// }

const prescriptionItemSchema = z.object({
  medicationName: z.string().min(1, "Medication name is required"),
  dosage: z.string()
    .min(1, "Dosage is required")
    .regex(/^\d+-\d+-\d+$/, "Dosage must be in format X-X-X (e.g., 1-0-1)")
    .refine((val) => {
      console.log("🔍 DOSAGE VALIDATION:", val);
      return true;
    }, "Dosage validation check"),
  timing: z.string().min(1, "Timing is required"), // Changed from frequency to timing
  duration: z.string().min(1, "Duration is required"),
  route: z.string().min(1, "Route is required"),
  method: z.string().min(1, "Method is required"), // Added method field
  instructions: z.string().optional(),
  reason: z.string().min(1, "Reason is required"), // Added reason field
  rxcui: z.string().optional(),
  strength: z.string().optional(),
  form: z.string().optional(),
});

const prescriptionFormSchema = z.object({
  items: z
    .array(prescriptionItemSchema)
    .min(1, "At least one medication is required"),
});

type PrescriptionFormValues = z.infer<typeof prescriptionFormSchema>;

interface PrescriptionFormProps {
  consultationId: string;
  patientId: string;
  doctorId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

export function PrescriptionForm({
  consultationId,
  patientId,
  doctorId,
  onCancel,
  onSuccess,
}: PrescriptionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const form = useForm<PrescriptionFormValues>({
    resolver: zodResolver(prescriptionFormSchema),
    defaultValues: {
      items: [
        {
          medicationName: "",
          dosage: "1-0-1",
          timing: "1-1-D", // Default timing format
          duration: "",
          route: "Oral",
          method: "swallow", // Default method
          instructions: "",
          reason: "", // Default reason
        },
      ],
    },
  });

  const { fields, append } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Debug: Watch form values to see what's happening
  const watchedValues = form.watch();
  console.log("🔍 FORM VALUES CHANGED:", watchedValues);

  const onSubmit = async (data: PrescriptionFormValues) => {
    try {
      setIsSubmitting(true);

      // Debug: Log the form data to see what's being submitted
      console.log("🔍 PRESCRIPTION FORM DATA:", data);
      data.items.forEach((item, index) => {
        console.log(`📋 Item ${index + 1} dosage:`, item.dosage);
      });

      const itemsWithRxNormData = data.items.map((item) => ({
        ...item,
        rxNormData: item.rxcui
          ? {
              rxcui: item.rxcui,
              name: item.medicationName,
              strength: item.strength,
              form: item.form,
            }
          : undefined,
      }));

      const response = await fetch("/api/prescriptions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          consultationId,
          patientId,
          doctorId,
          items: itemsWithRxNormData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create prescription");
      }

      toast.success("Prescription created successfully");
      onSuccess();
    } catch (error) {
      console.error("Error creating prescription:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to create prescription",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="border-2 border-primary/10 shadow-md">
      <CardHeader className="bg-muted/30 pb-4">
        <CardTitle className="text-xl font-bold flex items-center text-primary">
          <Pill className="h-5 w-5 mr-2" /> Create Prescription
        </CardTitle>
        <CardDescription>
          Prescribe medications with detailed instructions
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-6 pt-4">
            <div className="bg-muted/20 p-4 rounded-lg border border-muted">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  Medications
                </h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    append({
                      medicationName: "",
                      dosage: "1-0-1",
                      timing: "1-1-D",
                      duration: "",
                      route: "Oral",
                      method: "swallow",
                      instructions: "",
                      reason: "",
                    })
                  }
                  className="bg-primary/5 border-primary/20 hover:bg-primary/10 text-primary"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Medication
                </Button>
              </div>

              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div
                    key={field.id}
                    className="bg-white border rounded-lg shadow-sm p-4 space-y-4"
                  >
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium text-sm text-muted-foreground">
                        Medication {index + 1}
                      </h4>
                    </div>

                    {/* First Row: Medication Name, Dosage, Timing */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {/* Medication Name */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.medicationName`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="e.g., Paracetamol"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Dosage */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.dosage`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="Morning-Afternoon-Night (e.g., 1-0-1)"
                                title="Format: Morning-Afternoon-Night doses"
                                onChange={(e) => {
                                  console.log(`🔍 Dosage field ${index} changed to:`, e.target.value);
                                  field.onChange(e);
                                }}
                                onBlur={(e) => {
                                  console.log(`🔍 Dosage field ${index} blurred with value:`, e.target.value);
                                  field.onBlur();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />


                      {/* Timing */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.timing`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select timing" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="1-1-D">1-1-D (Once daily)</SelectItem>
                                  <SelectItem value="2-1-D">2-1-D (Twice daily)</SelectItem>
                                  <SelectItem value="3-1-D">3-1-D (Thrice daily)</SelectItem>
                                  <SelectItem value="1-2-D">1-2-D (Every 2 days)</SelectItem>
                                  <SelectItem value="1-1-WK">1-1-WK (Once weekly)</SelectItem>
                                  <SelectItem value="1-2-WK">1-2-WK (Every 2 weeks)</SelectItem>
                                  <SelectItem value="1-1-MO">1-1-MO (Once monthly)</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Second Row: Route, Method, Reason */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {/* Route */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.route`}
                        render={({ field }) => (
                          <FormItem>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select route" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Oral">Oral</SelectItem>
                                <SelectItem value="Topical">Topical</SelectItem>
                                <SelectItem value="Inhalation">Inhalation</SelectItem>
                                <SelectItem value="Injection">Injection</SelectItem>
                                <SelectItem value="Syrup">Syrup</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Method */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.method`}
                        render={({ field }) => (
                          <FormItem>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select method" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="swallow">Swallow</SelectItem>
                                <SelectItem value="drink">Drink</SelectItem>
                                <SelectItem value="chew">Chew</SelectItem>
                                <SelectItem value="dissolve">Dissolve</SelectItem>
                                <SelectItem value="apply">Apply</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Reason */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.reason`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Input {...field} placeholder="e.g., fever, pain" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Third Row: Duration, Instructions */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Duration */}
                      <FormField
                        control={form.control}
                        name={`items.${index}.duration`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Input {...field} placeholder="e.g., 5 days" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Instructions */}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between bg-muted/20 border-t pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="border-gray-300 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
                </>
              ) : (
                <>
                  <Pill className="mr-2 h-4 w-4" /> Create Prescription
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
