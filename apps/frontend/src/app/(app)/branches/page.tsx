"use client";

import { useState } from "react";
import { useBranch } from "@/contexts/branch-context";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "lucide-react";
import { BranchGrid } from "@/components/branches/branch-grid";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { toast } from "sonner";

export default function BranchesPage() {
  const { branches, isLoading, refreshBranches } = useBranch();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isHeadOfficeDialogOpen, setIsHeadOfficeDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState<any>(null);

  const handleDeleteClick = (branch: any) => {
    setSelectedBranch(branch);
    setIsDeleteDialogOpen(true);
  };

  const handleSetHeadOfficeClick = (branch: any) => {
    setSelectedBranch(branch);
    setIsHeadOfficeDialogOpen(true);
  };

  const handleDeleteBranch = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/branches/${selectedBranch.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Branch deleted successfully");
        setIsDeleteDialogOpen(false);
        refreshBranches();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete branch");
      }
    } catch (error) {
      console.error("Error deleting branch:", error);
      toast.error("An error occurred while deleting the branch");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSetHeadOffice = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/branches/set-head-office`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ branchId: selectedBranch.id }),
      });

      if (response.ok) {
        toast.success(`${selectedBranch.name} set as head office successfully`);
        setIsHeadOfficeDialogOpen(false);
        refreshBranches();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to set head office");
      }
    } catch (error) {
      console.error("Error setting head office:", error);
      toast.error("An error occurred while setting the head office");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Branches</h1>
          <p className="text-muted-foreground">
            Manage branches in your organization
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href="/branches/add">
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Branch
            </Link>
          </Button>
        </div>
      </div>

      {/* Branch Grid */}
      <BranchGrid
        branches={branches}
        isLoading={isLoading}
        onDelete={(branchId) => {
          const branch = branches.find((b) => b.id === branchId);
          if (branch) handleDeleteClick(branch);
        }}
        onSetHeadOffice={(branchId) => {
          const branch = branches.find((b) => b.id === branchId);
          if (branch && !branch.isHeadOffice) handleSetHeadOfficeClick(branch);
        }}
      />

      {/* Grid View is the only view now */}

      {/* Edit functionality moved to dedicated page at /branches/edit/[id] */}

      {/* Delete Branch Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Branch</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete the branch "{selectedBranch?.name}
              "? This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteBranch}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete Branch"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Set Head Office Dialog */}
      <Dialog
        open={isHeadOfficeDialogOpen}
        onOpenChange={setIsHeadOfficeDialogOpen}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Set as Head Office</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to set "{selectedBranch?.name}" as the head
              office? This will change the current head office to a regular
              branch.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button onClick={handleSetHeadOffice} disabled={isSubmitting}>
              {isSubmitting ? "Setting..." : "Set as Head Office"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
