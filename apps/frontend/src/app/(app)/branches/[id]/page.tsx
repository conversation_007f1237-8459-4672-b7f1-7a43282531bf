import { Metada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Building2, MapPin, ShieldCheck } from "lucide-react";
import { FacilityIdForm } from "@/components/branches/facility-id-form";
import { cookies } from "next/headers";
import { getCurrentUser } from "@/lib/session";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BranchMapCard } from "@/components/branches/branch-map-card";
import { Badge } from "@/components/ui/badge";
import { BranchOverview } from "@/components/branches/branch-overview";

type BranchViewProps = {
  id: string;
  name: string;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  pincode?: string | null;
  phone?: string | null;
  email?: string | null;
  latitude?: string | null;
  longitude?: string | null;
  isHeadOffice: boolean;
  isActive?: boolean;
  facilityType: string;
  createdAt?: string;
  updatedAt?: string;
};

export const metadata: Metadata = {
  title: "Branch Details",
  description: "View and manage branch details",
};

async function getBranch(branchId: string, organizationId: string) {
  const branch = await prisma.branch.findFirst({
    where: {
      id: branchId,
      organizationId,
    },
  });

  if (!branch) {
    notFound();
  }

  return branch;
}

export default async function BranchDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    redirect("/sign-in");
  }

  // Get the organization ID from cookies
  let organizationId = user.organizationId;

  // Fallback: Try to get organization ID from user-info cookie
  if (!organizationId) {
    const userInfoCookie = cookies().get("user-info")?.value;
    if (userInfoCookie) {
      try {
        const userInfo = JSON.parse(userInfoCookie);
        organizationId = userInfo.organizationId;
      } catch (error) {
        console.error("Error parsing user info cookie:", error);
      }
    }
  }

  // If still no organization ID, redirect to sign-in
  if (!organizationId) {
    console.error("No organization ID found in user or cookies");
    redirect("/sign-in");
  }

  const branch = await getBranch(params.id, organizationId);

  const branchView: BranchViewProps = {
    id: branch.id,
    name: branch.name,
    address: branch.address,
    city: branch.city,
    state: branch.state,
    pincode: branch.pincode,
    phone: branch.phone,
    email: branch.email,
    latitude: branch.latitude,
    longitude: branch.longitude,
    isHeadOffice: branch.isHeadOffice,
    facilityType: branch.facilityType,
    createdAt: branch.createdAt?.toISOString(),
    updatedAt: branch.updatedAt?.toISOString(),
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Branch Details: {branch.name}
          </h1>
          <p className="text-muted-foreground">
            View and manage branch settings
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/branches">
              <ArrowLeft className="h-4 w-4 mr-2" /> Back to Branches
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Branch Information Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-primary" />
              Branch Information
              {branch.isHeadOffice && (
                <Badge
                  className="ml-2 bg-amber-500 hover:bg-amber-600"
                  variant="secondary"
                >
                  Head Office
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BranchOverview branch={branchView} />
          </CardContent>
        </Card>

        {/* Branch Location Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-primary" />
              Branch Location
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BranchMapCard branch={branch} />
          </CardContent>
        </Card>

        {/* ABDM Integration Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShieldCheck className="h-5 w-5 text-primary" />
              ABDM Integration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <FacilityIdForm
              branchId={branch.id}
              initialFacilityId={branch.hipId || ""}
              initialFacilityName={branch.name || ""}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
