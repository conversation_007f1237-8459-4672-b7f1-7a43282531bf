"use client";

import { useState } from "react";
import { useStaff } from "@/contexts/staff-context";

import { useBranch } from "@/contexts/branch-context";
import { Button } from "@/components/ui/button";
import { PlusIcon, Trash2, Mail } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

// Extended staff type to include pending invitations
type StaffWithInvitation = {
  id: string;
  name?: string;
  isPendingInvitation?: boolean;
  user?: {
    id: string | null;
    name: string;
    email: string;
    image: string | null;
  } | null;
  email?: string;
  invitationId?: string;
  branchIds?: string[];
  department?: {
    name: string;
  } | null;
  branches?: Array<{
    id: string;
    name: string;
  }>;
  role?: string;
  contactNumber?: string;
  position?: string;
  status?: string;
  joiningDate?: string;
};

export default function StaffPage() {
  const { staff, isLoading, refreshStaff } = useStaff();
  const { currentBranch, branches } = useBranch();

  // We don't need this effect anymore as the staff context handles branch changes
  // The staff context will automatically refresh when the branch changes
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    position: "",
    contactNumber: "",
    branchIds: [] as string[],
    status: "active",
  });

  // Reset form data
  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      position: "",
      contactNumber: "",
      branchIds: [] as string[],
      status: "active",
    });
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle branch selection
  const handleBranchChange = (branchId: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      branchIds: checked
        ? [...prev.branchIds, branchId]
        : prev.branchIds.filter((id) => id !== branchId),
    }));
  };

  // Handle delete click
  const handleDeleteClick = (staffMember: any) => {
    setSelectedStaff(staffMember);
    setIsDeleteDialogOpen(true);
  };

  // Handle add staff
  const handleAddStaff = async () => {
    if (!formData.name || !formData.email) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.branchIds.length === 0) {
      toast.error("Please select at least one branch");
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare staff data for API
      const staffData = {
        ...formData,
      };

      // Create staff invitation
      const response = await fetch("/api/staff", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(staffData),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message || "Staff invitation sent successfully");
        setIsAddDialogOpen(false);
        resetForm();
        refreshStaff();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to send staff invitation");
      }
    } catch (error) {
      console.error("Error adding staff member:", error);
      toast.error("An error occurred while adding the staff member");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete staff
  const handleDeleteStaff = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/staff/${selectedStaff.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Staff member deleted successfully");
        setIsDeleteDialogOpen(false);
        refreshStaff();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete staff member");
      }
    } catch (error) {
      console.error("Error deleting staff member:", error);
      toast.error("An error occurred while deleting the staff member");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Staff</h1>
          {currentBranch && (
            <p className="text-sm text-muted-foreground">
              Showing staff for branch:{" "}
              <span className="font-medium">{currentBranch.name}</span>
            </p>
          )}
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Invite Staff
        </Button>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  Loading...
                </TableCell>
              </TableRow>
            ) : staff.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  No staff found
                </TableCell>
              </TableRow>
            ) : (
              (staff as StaffWithInvitation[]).map((staffMember) => (
                <TableRow key={staffMember.id}>
                  <TableCell className="font-medium">
                    {staffMember.isPendingInvitation ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-muted-foreground">
                          {staffMember.user?.name || staffMember.email}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          Pending
                        </Badge>
                      </div>
                    ) : (
                      staffMember.name
                    )}
                  </TableCell>
                  <TableCell>
                    {staffMember.isPendingInvitation
                      ? "Staff (Pending)"
                      : staffMember.role}
                  </TableCell>
                  <TableCell>
                    {staffMember.isPendingInvitation
                      ? staffMember.email
                      : staffMember.contactNumber || "—"}
                  </TableCell>
                  <TableCell>
                    {staffMember.department ? staffMember.department.name : "—"}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        staffMember.isPendingInvitation
                          ? "secondary"
                          : staffMember.status === "active"
                            ? "success"
                            : "destructive"
                      }
                    >
                      {staffMember.isPendingInvitation
                        ? "Invitation Sent"
                        : staffMember.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    {!staffMember.isPendingInvitation && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteClick(staffMember)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Add Staff Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Invite New Staff Member</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">Position</Label>
                <Input
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={handleInputChange}
                  placeholder="e.g., Nurse, Technician, Receptionist"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="contactNumber">Contact Number</Label>
              <Input
                id="contactNumber"
                name="contactNumber"
                value={formData.contactNumber}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <Label>Branches *</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {branches.map((branch) => (
                  <div key={branch.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`branch-${branch.id}`}
                      checked={formData.branchIds.includes(branch.id)}
                      onChange={(e) =>
                        handleBranchChange(branch.id, e.target.checked)
                      }
                      className="rounded"
                    />
                    <Label
                      htmlFor={`branch-${branch.id}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {branch.name}
                    </Label>
                  </div>
                ))}
              </div>
              {formData.branchIds.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  Please select at least one branch
                </p>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-800 font-medium">
                  An invitation email will be sent to set up their account
                </span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button onClick={handleAddStaff} disabled={isSubmitting}>
              {isSubmitting ? "Sending Invitation..." : "Send Staff Invitation"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Staff Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Staff</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete the staff member "
              {selectedStaff?.name}"? This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteStaff}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete Staff"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
