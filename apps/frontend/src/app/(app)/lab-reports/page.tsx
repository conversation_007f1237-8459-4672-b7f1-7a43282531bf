import { <PERSON>ada<PERSON> } from "next";
import { <PERSON>boardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/shell";
import { LabReportsContent } from "@/components/lab-reports/lab-reports-content";

export const metadata: Metadata = {
  title: "Lab Reports",
  description: "Manage lab test requests and diagnostic reports",
};

export default async function LabReportsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Lab Reports"
        text="Manage lab test requests and view diagnostic reports."
      />
      <div className="grid gap-4">
        <LabReportsContent />
      </div>
    </DashboardShell>
  );
}
