"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import {
  CalendarIcon,
  ClockIcon,
  UserIcon,
  PhoneIcon,
  MailIcon,
  StethoscopeIcon,
  FilterIcon,
  SearchIcon,
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useCurrentBranch } from "@/hooks/use-current-branch";
import { useDepartment } from "@/contexts/department-context";

// Types
interface Doctor {
  id: string;
  user: {
    id: string;
    name: string;
    email?: string;
    image?: string;
  };
  specialization?: string;
  qualification?: string;
  yearsOfExperience: number;
  contactEmail?: string;
  contactPhone?: string;
  department: {
    id: string;
    name: string;
  };
  status: string;
}

interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
  duration: number;
  status: "available" | "booked" | "unavailable";
  appointmentId?: string;
}

interface DoctorWithSlots extends Doctor {
  timeSlots: TimeSlot[];
  totalSlots: number;
  availableSlots: number;
  bookedSlots: number;
}

export default function DoctorAppointmentsPage() {
  const router = useRouter();
  const { currentBranch } = useCurrentBranch();
  const { departments = [] } = useDepartment();

  // State
  const [doctors, setDoctors] = useState<DoctorWithSlots[]>([]);
  const [filteredDoctors, setFilteredDoctors] = useState<DoctorWithSlots[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "availability" | "experience">(
    "availability",
  );

  // Stats
  const [stats, setStats] = useState({
    totalAppointments: 0,
    availableSlots: 0,
    activeDoctors: 0,
  });

  // Fetch doctors and their schedules
  const fetchDoctorsWithSchedules = async () => {
    console.log("fetchDoctorsWithSchedules called", {
      branchId: currentBranch?.id,
      selectedDate,
      hasCurrentBranch: !!currentBranch,
      hasSelectedDate: !!selectedDate,
    });

    if (!currentBranch?.id || !selectedDate) {
      console.log("Missing dependencies, skipping fetch");
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const dateStr = format(selectedDate, "yyyy-MM-dd");
      console.log(
        "Making API call:",
        `/api/doctors-with-schedules?branchId=${currentBranch.id}&date=${dateStr}`,
      );

      const response = await fetch(
        `/api/doctors-with-schedules?branchId=${currentBranch.id}&date=${dateStr}`,
      );

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API error:", errorData);
        throw new Error(errorData.error || "Failed to fetch doctors");
      }

      const data = await response.json();
      console.log("API response:", data);
      setDoctors(data.doctors || []);
      setStats(
        data.stats || {
          totalAppointments: 0,
          availableSlots: 0,
          activeDoctors: 0,
        },
      );
    } catch (error) {
      console.error("Error fetching doctors:", error);
      toast.error("Failed to load doctors");
      setDoctors([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and sort doctors
  useEffect(() => {
    let filtered = [...doctors];

    // Filter by department
    if (selectedDepartment !== "all") {
      filtered = filtered.filter(
        (doctor) => doctor.department.id === selectedDepartment,
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (doctor) =>
          doctor.user.name.toLowerCase().includes(query) ||
          doctor.specialization?.toLowerCase().includes(query) ||
          doctor.department.name.toLowerCase().includes(query),
      );
    }

    // Sort doctors
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.user.name.localeCompare(b.user.name);
        case "availability":
          return b.availableSlots - a.availableSlots;
        case "experience":
          return b.yearsOfExperience - a.yearsOfExperience;
        default:
          return 0;
      }
    });

    setFilteredDoctors(filtered);
  }, [doctors, selectedDepartment, searchQuery, sortBy]);

  // Load data when dependencies change
  useEffect(() => {
    console.log("useEffect triggered", {
      currentBranchId: currentBranch?.id,
      selectedDate,
      currentBranch,
    });
    fetchDoctorsWithSchedules();
  }, [currentBranch?.id, selectedDate]);

  // Debug current branch
  useEffect(() => {
    console.log("Current branch changed:", currentBranch);
  }, [currentBranch]);

  // Handle time slot click
  const handleTimeSlotClick = (doctor: Doctor, timeSlot: TimeSlot) => {
    if (timeSlot.status !== "available") return;

    // Navigate to appointment booking with pre-filled data
    const params = new URLSearchParams({
      doctorId: doctor.id,
      date: format(selectedDate, "yyyy-MM-dd"),
      startTime: timeSlot.startTime,
      duration: timeSlot.duration.toString(),
    });

    router.push(`/appointments/new?${params.toString()}`);
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Format time slot display
  const formatTimeSlot = (startTime: string, endTime: string) => {
    // Safety check for undefined values
    if (!startTime || !endTime) {
      return "Invalid time";
    }

    const formatTime = (time: string) => {
      if (!time || !time.includes(":")) {
        return "Invalid";
      }
      const [hours, minutes] = time.split(":");
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? "PM" : "AM";
      const displayHour = hour % 12 || 12;
      return `${displayHour}:${minutes} ${ampm}`;
    };

    return `${formatTime(startTime)} - ${formatTime(endTime)}`;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Doctor Appointments
          </h1>
          <p className="text-muted-foreground">
            View doctor schedules and book appointments
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Today's Appointments
            </CardTitle>
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalAppointments}</div>
            <p className="text-xs text-muted-foreground">
              Scheduled for {format(selectedDate, "MMM d, yyyy")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Available Slots
            </CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.availableSlots}</div>
            <p className="text-xs text-muted-foreground">Open for booking</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Doctors
            </CardTitle>
            <UserIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeDoctors}</div>
            <p className="text-xs text-muted-foreground">Available today</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FilterIcon className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date Picker */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Date</label>
              <DatePicker
                date={selectedDate}
                setDate={(date) => setSelectedDate(date || new Date())}
                placeholder="Select date"
              />
            </div>

            {/* Department Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Department</label>
              <Select
                value={selectedDepartment}
                onValueChange={setSelectedDepartment}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search doctors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                />
              </div>
            </div>

            {/* Sort */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort By</label>
              <Select
                value={sortBy}
                onValueChange={(value: any) => setSortBy(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="availability">Availability</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="experience">Experience</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Doctors Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-3/4" />
                  <div className="grid grid-cols-3 gap-2">
                    {[1, 2, 3, 4, 5, 6].map((j) => (
                      <Skeleton key={j} className="h-8 w-full" />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredDoctors.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <StethoscopeIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No doctors found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || selectedDepartment !== "all"
                  ? "Try adjusting your filters or search criteria"
                  : "No doctors are available for the selected date"}
              </p>
              {(searchQuery || selectedDepartment !== "all") && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedDepartment("all");
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDoctors.map((doctor) => (
            <Card
              key={doctor.id}
              className="hover:shadow-lg transition-shadow duration-200"
            >
              <CardHeader className="pb-4">
                <div className="flex items-start space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={doctor.user.image}
                      alt={doctor.user.name}
                    />
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                      {getInitials(doctor.user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg leading-tight">
                      {doctor.user.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {doctor.specialization || "General Practice"}
                    </p>
                    <div className="flex items-center gap-4 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {doctor.yearsOfExperience} years exp.
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {doctor.department.name}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Contact Information */}
                {(doctor.contactPhone || doctor.contactEmail) && (
                  <div className="space-y-2">
                    {doctor.contactPhone && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <PhoneIcon className="h-4 w-4" />
                        <span>{doctor.contactPhone}</span>
                      </div>
                    )}
                    {doctor.contactEmail && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <MailIcon className="h-4 w-4" />
                        <span className="truncate">{doctor.contactEmail}</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Qualification */}
                {doctor.qualification && (
                  <div className="text-sm">
                    <span className="font-medium">Qualification: </span>
                    <span className="text-muted-foreground">
                      {doctor.qualification}
                    </span>
                  </div>
                )}

                {/* Schedule Summary */}
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Today's Schedule:</span>
                  <div className="flex gap-2">
                    <Badge
                      variant="default"
                      className="bg-green-100 text-green-800 hover:bg-green-100"
                    >
                      {doctor.availableSlots} available
                    </Badge>
                    <Badge
                      variant="secondary"
                      className="bg-red-100 text-red-800"
                    >
                      {doctor.bookedSlots} booked
                    </Badge>
                  </div>
                </div>

                {/* Time Slots */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Available Time Slots</h4>
                  {doctor.timeSlots.length === 0 ? (
                    <div className="text-center py-4 text-sm text-muted-foreground">
                      No schedule available for this date
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                      {doctor.timeSlots.map((slot) => (
                        <Button
                          key={slot.id}
                          variant={
                            slot.status === "available"
                              ? "outline"
                              : slot.status === "booked"
                                ? "secondary"
                                : "ghost"
                          }
                          size="sm"
                          className={cn(
                            "h-auto py-2 px-3 text-xs font-normal transition-all duration-200",
                            slot.status === "available" &&
                              "border-green-200 bg-green-50 text-green-800 hover:bg-green-100 hover:border-green-300 cursor-pointer",
                            slot.status === "booked" &&
                              "bg-red-50 text-red-800 border-red-200 cursor-not-allowed opacity-75",
                            slot.status === "unavailable" &&
                              "bg-gray-50 text-gray-500 border-gray-200 cursor-not-allowed opacity-60",
                          )}
                          disabled={slot.status !== "available"}
                          onClick={() => handleTimeSlotClick(doctor, slot)}
                          aria-label={`Book appointment at ${formatTimeSlot(slot.startTime, slot.endTime)}`}
                        >
                          <div className="text-center">
                            <div className="font-medium">
                              {formatTimeSlot(slot.startTime, slot.endTime)}
                            </div>
                            <div className="text-xs opacity-75">
                              {slot.duration} min
                            </div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Quick Book Button */}
                {doctor.availableSlots > 0 && (
                  <Button
                    className="w-full"
                    onClick={() => {
                      const firstAvailable = doctor.timeSlots.find(
                        (slot) => slot.status === "available",
                      );
                      if (firstAvailable) {
                        handleTimeSlotClick(doctor, firstAvailable);
                      }
                    }}
                  >
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    Quick Book Next Available
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
