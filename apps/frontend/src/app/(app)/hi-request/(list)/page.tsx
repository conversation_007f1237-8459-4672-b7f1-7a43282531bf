import * as React from "react";
import { DataTableSkeleton } from "@workspace/data-table/component/data-table-skeleton";
import { HiRequestsTable } from "./_component/table";
import { getHiRequests } from "./_lib/queries";
import { GetHiRequestsSchema } from "./_lib/validations";

interface HiRequestsPageProps {
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}

export default async function HiRequestsPage({
  searchParams,
}: HiRequestsPageProps) {
  // Parse search params
  const parsedParams = GetHiRequestsSchema.parse({
    page: searchParams.page ? Number(searchParams.page) : 1,
    perPage: searchParams.per_page ? Number(searchParams.per_page) : 10,
    sort: searchParams.sort
      ? JSON.parse(decodeURIComponent(searchParams.sort as string))
      : undefined,
    requestId: searchParams.requestId,
    transactionId: searchParams.transactionId,
    consentId: searchParams.consentId,
    acknowledgementSent: searchParams.acknowledgementSent
      ? searchParams.acknowledgementSent === "true"
      : undefined,
  });

  // Fetch data
  const hiRequests = await getHiRequests(parsedParams);

  return (
    <React.Suspense fallback={<DataTableSkeleton columnCount={6} />}>
      <HiRequestsTable initialData={hiRequests} />
    </React.Suspense>
  );
}
