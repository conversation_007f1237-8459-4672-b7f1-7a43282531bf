import { z } from "zod";

export const GetHiRequestsSchema = z.object({
  page: z.coerce.number().optional(),
  perPage: z.coerce.number().optional(),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      }),
    )
    .optional(),
  requestId: z.string().optional(),
  transactionId: z.string().optional(),
  consentId: z.string().optional(),
  acknowledgementSent: z.boolean().optional(),
});
