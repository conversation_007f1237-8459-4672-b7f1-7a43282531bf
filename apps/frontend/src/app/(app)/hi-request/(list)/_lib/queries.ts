import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { GetHiRequestsSchema } from "./validations";

export interface HiRequest {
  id: string;
  requestId: string;
  transactionId: string;
  consentId: string;
  dateRange: {
    from: string;
    to: string;
  };
  dataPushUrl: string;
  keyMaterial: any;
  acknowledgementSent: boolean;
  createdAt: string;
  updatedAt: string;
}

export async function getHiRequests(
  params: z.infer<typeof GetHiRequestsSchema> = {},
) {
  try {
    const {
      page = 1,
      perPage = 10,
      sort = [],
      requestId,
      transactionId,
      consentId,
      acknowledgementSent,
    } = params;

    // Calculate pagination
    const skip = (page - 1) * perPage;
    const take = perPage;

    // Build where conditions
    const whereConditions: any = {};

    if (requestId) {
      whereConditions.requestId = {
        contains: requestId,
        mode: "insensitive",
      };
    }

    if (transactionId) {
      whereConditions.transactionId = {
        contains: transactionId,
        mode: "insensitive",
      };
    }

    if (consentId) {
      whereConditions.consentId = {
        contains: consentId,
        mode: "insensitive",
      };
    }

    if (acknowledgementSent !== undefined) {
      whereConditions.acknowledgementSent = acknowledgementSent;
    }

    // Build order by
    const orderBy: any[] = [];
    if (sort.length > 0) {
      for (const { id, desc } of sort) {
        orderBy.push({
          [id]: desc ? "desc" : "asc",
        });
      }
    } else {
      // Default sorting by createdAt desc
      orderBy.push({
        createdAt: "desc",
      });
    }

    // Execute query
    const [hiRequests, totalHiRequests] = await Promise.all([
      prisma.hiRequest.findMany({
        where: whereConditions,
        orderBy,
        skip,
        take,
      }),
      prisma.hiRequest.count({
        where: whereConditions,
      }),
    ]);

    // Calculate page count
    const pageCount = Math.ceil(totalHiRequests / perPage);

    // Map database results to the expected interface
    const mappedHiRequests: HiRequest[] = hiRequests.map((request) => ({
      id: request.id,
      requestId: request.requestId,
      transactionId: request.transactionId,
      consentId: request.consentId,
      dateRange: request.dateRange as { from: string; to: string },
      dataPushUrl: request.dataPushUrl,
      keyMaterial: request.keyMaterial,
      acknowledgementSent: request.acknowledgementSent,
      createdAt: request.createdAt.toISOString(),
      updatedAt: request.updatedAt.toISOString(),
    }));

    return {
      data: mappedHiRequests,
      pageCount,
    };
  } catch (error) {
    console.error("Error fetching HI requests:", error);
    throw error;
  }
}
