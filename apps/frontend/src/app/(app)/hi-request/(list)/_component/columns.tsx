"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@workspace/ui/components/badge";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { DataTableColumnHeader } from "@workspace/data-table/component/data-table-column-header";
import { formatDate } from "@/lib/utils";
import { type HiRequest } from "../_lib/queries";

export function getHiRequestsTableColumns(): ColumnDef<HiRequest>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "requestId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Request ID" />
      ),
      cell: ({ row }) => {
        return (
          <div className="max-w-[200px] truncate font-medium">
            {row.getValue("requestId")}
          </div>
        );
      },
    },
    {
      accessorKey: "transactionId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Transaction ID" />
      ),
      cell: ({ row }) => {
        return (
          <div className="max-w-[200px] truncate">
            {row.getValue("transactionId")}
          </div>
        );
      },
    },
    {
      accessorKey: "consentId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Consent ID" />
      ),
      cell: ({ row }) => {
        return (
          <div className="max-w-[200px] truncate">
            {row.getValue("consentId")}
          </div>
        );
      },
    },
    {
      accessorKey: "acknowledgementSent",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Acknowledgement" />
      ),
      cell: ({ row }) => {
        const acknowledgementSent = row.getValue(
          "acknowledgementSent",
        ) as boolean;
        return (
          <Badge
            variant={acknowledgementSent ? "success" : "destructive"}
            className="whitespace-nowrap"
          >
            {acknowledgementSent ? "Sent" : "Not Sent"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => {
        return (
          <div className="whitespace-nowrap">
            {formatDate(row.getValue("createdAt"))}
          </div>
        );
      },
    },
  ];
}
