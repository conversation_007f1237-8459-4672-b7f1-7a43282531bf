"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import type { HiRequest } from "../_lib/queries";
import { DataTable } from "@workspace/data-table/component/data-table";
import { useDataTable } from "@workspace/data-table/hooks/use-data-table";
import { DataTableToolbar } from "@workspace/data-table/component/data-table-toolbar";
import { DataTableSortList } from "@workspace/data-table/component/data-table-sort-list";
import { getHiRequestsTableColumns } from "./columns";

interface HiRequestsTableProps {
  initialData: {
    data: HiRequest[];
    pageCount: number;
  };
}

export function HiRequestsTable({ initialData }: HiRequestsTableProps) {
  const router = useRouter();

  // Get table columns
  const columns = React.useMemo(() => getHiRequestsTableColumns(), []);

  // Initialize data table
  const dataTable = useDataTable({
    columns,
    data: initialData.data,
    pageCount: initialData.pageCount,
  });

  // Handle row click
  function handleRowClick(row: HiRequest) {
    router.push(`/hi-request/${row.id}/view`);
  }

  return (
    <div className="space-y-4">
      <DataTableToolbar table={dataTable.table} />
      <DataTableSortList table={dataTable.table} />
      <DataTable
        table={dataTable.table}
        onRowClick={handleRowClick}
        doctype="Health Information Request"
      />
    </div>
  );
}
