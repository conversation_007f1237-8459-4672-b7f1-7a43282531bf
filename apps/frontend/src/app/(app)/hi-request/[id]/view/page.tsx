import { notFound } from "next/navigation";
import { getHiRequestById } from "./_lib/queries";
import { RequestHeader } from "./_components/request-header";
import { RequestDetails } from "./_components/request-details";
import { JsonView } from "./_components/json-view";
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON><PERSON>ist,
  TabsTrigger,
} from "@workspace/ui/components/tabs";

interface HiRequestDetailPageProps {
  params: {
    id: string;
  };
}

export default async function HiRequestDetailPage({
  params,
}: HiRequestDetailPageProps) {
  if (!params.id) {
    return notFound();
  }

  try {
    const request = await getHiRequestById(params.id);

    return (
      <div className="space-y-6">
        <RequestHeader request={request} />

        <Tabs defaultValue="formatted" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="formatted">Formatted View</TabsTrigger>
            <TabsTrigger value="json">JSON View</TabsTrigger>
          </TabsList>

          <TabsContent value="formatted">
            <RequestDetails request={request} />
          </TabsContent>

          <TabsContent value="json">
            <JsonView request={request} />
          </TabsContent>
        </Tabs>
      </div>
    );
  } catch (error) {
    console.error("Error fetching health information request:", error);
    return notFound();
  }
}
