"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@workspace/ui/components/card";
import { formatDate } from "@/lib/utils";
import type { HiRequestDetail } from "../_lib/queries";

interface RequestDetailsProps {
  request: HiRequestDetail;
}

export function RequestDetails({ request }: RequestDetailsProps) {
  // Format date range
  const formatDateRange = () => {
    const dateRange = request.dateRange;
    if (!dateRange) return "Not specified";

    const from = dateRange.from ? formatDate(dateRange.from) : "Not specified";
    const to = dateRange.to ? formatDate(dateRange.to) : "Not specified";

    return `${from} to ${to}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Request Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Request Information
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-xs text-muted-foreground">Request ID</p>
              <p className="break-all">{request.requestId}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Transaction ID</p>
              <p className="break-all">{request.transactionId}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Consent ID</p>
              <p className="break-all">{request.consentId}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">
                Acknowledgement Status
              </p>
              <p>{request.acknowledgementSent ? "Sent" : "Not Sent"}</p>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Date Range
          </h3>
          <p>{formatDateRange()}</p>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Data Push URL
          </h3>
          <p className="break-all">{request.dataPushUrl}</p>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Key Material
          </h3>
          <div className="rounded-md bg-muted p-4 overflow-auto max-h-[200px]">
            <pre className="text-xs">
              {JSON.stringify(request.keyMaterial, null, 2)}
            </pre>
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Timestamps
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-xs text-muted-foreground">Created At</p>
              <p>{formatDate(request.createdAt)}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Updated At</p>
              <p>{formatDate(request.updatedAt)}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
