import Link from "next/link";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { FileX2 } from "lucide-react";

export default function NotFound() {
  return (
    <div className="flex h-[50vh] flex-col items-center justify-center space-y-4 text-center">
      <FileX2 className="h-16 w-16 text-muted-foreground" />
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">
          Health Information Request Not Found
        </h1>
        <p className="text-muted-foreground">
          The health information request you're looking for doesn't exist or you
          don't have permission to view it.
        </p>
      </div>
      <Button asChild>
        <Link href="/hi-request">Back to Health Information Requests</Link>
      </Button>
    </div>
  );
}
