import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export interface HiRequestDetail {
  id: string;
  requestId: string;
  transactionId: string;
  consentId: string;
  dateRange: {
    from: string;
    to: string;
  };
  dataPushUrl: string;
  keyMaterial: any;
  acknowledgementSent: boolean;
  createdAt: string;
  updatedAt: string;
}

export async function getHiRequestById(id: string): Promise<HiRequestDetail> {
  try {
    const hiRequest = await prisma.hiRequest.findUnique({
      where: {
        id,
      },
    });

    if (!hiRequest) {
      return notFound();
    }

    return hiRequest as unknown as HiRequestDetail;
  } catch (error) {
    console.error("Error fetching HI request:", error);
    throw error;
  }
}
