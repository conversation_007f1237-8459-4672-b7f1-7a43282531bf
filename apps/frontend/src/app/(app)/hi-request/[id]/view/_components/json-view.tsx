"use client";

import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Copy } from "lucide-react";
import type { HiRequestDetail } from "../_lib/queries";

interface JsonViewProps {
  request: HiRequestDetail;
}

export function JsonView({ request }: JsonViewProps) {
  const [copied, setCopied] = useState(false);

  // Format the request data as JSON
  const jsonData = JSON.stringify(request, null, 2);

  // Copy JSON to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonData);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>JSON Data</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={copyToClipboard}
          className="h-8 gap-1"
        >
          <Copy className="h-4 w-4" />
          {copied ? "Copied!" : "Copy"}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="relative rounded-md bg-slate-950 p-4">
          <pre className="max-h-[500px] overflow-auto text-sm text-slate-50">
            <code>{jsonData}</code>
          </pre>
        </div>
      </CardContent>
    </Card>
  );
}
