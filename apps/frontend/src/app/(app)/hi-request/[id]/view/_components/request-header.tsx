"use client";

import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent } from "@workspace/ui/components/card";
import { formatDate } from "@/lib/utils";
import type { HiRequestDetail } from "../_lib/queries";

interface RequestHeaderProps {
  request: HiRequestDetail;
}

export function RequestHeader({ request }: RequestHeaderProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-2xl font-bold">Health Information Request</h2>
            <p className="text-sm text-muted-foreground">
              Transaction ID: {request.transactionId}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Badge
              variant={request.acknowledgementSent ? "success" : "destructive"}
              className="whitespace-nowrap"
            >
              {request.acknowledgementSent
                ? "Acknowledged"
                : "Not Acknowledged"}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {formatDate(request.createdAt)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
