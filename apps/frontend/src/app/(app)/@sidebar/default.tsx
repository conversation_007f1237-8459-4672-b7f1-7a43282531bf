import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import Link from "next/link";
import SmallLogo from "./_components/logo";
import { RoleBasedSidebar } from "./_components/role-based-sidebar";

export default function SidebarLayout() {
  return (
    <Sidebar collapsible="icon" variant="floating">
      <SidebarHeader className="p-3 group-data-[collapsible=icon]:p-2">
        <SidebarMenu>
          <Link href="/dashboard">
            <SidebarMenuItem className="flex gap-2 hover:bg-transparent">
              <SmallLogo />

              <div className="grid flex-1 text-left leading-tight group-data-[collapsible=icon]:hidden">
                <span className="truncate font-semibold text-sm">
                  Aran Care
                </span>
                <span className="text-xs text-muted-foreground">
                  Healthcare Solutions
                </span>
              </div>
            </SidebarMenuItem>
          </Link>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <RoleBasedSidebar />
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
