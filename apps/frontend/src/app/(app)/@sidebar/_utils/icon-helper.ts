import * as LucideIcons from "lucide-react";

/**
 * Helper function to convert a string icon name to a Lucide icon component
 * @param iconName The name of the icon (without the "Icon" suffix)
 * @returns The corresponding Lucide icon component
 */
export function getIconComponent(iconName: string) {
  // If no icon name is provided, return null
  if (!iconName) return null;

  // Try to get the icon component from Lucide
  const IconComponent =
    (LucideIcons as any)[`${iconName}Icon`] || LucideIcons.CircleIcon;

  return IconComponent;
}
