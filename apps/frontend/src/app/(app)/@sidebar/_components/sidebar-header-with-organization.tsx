"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import {
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import <PERSON><PERSON><PERSON> from "./logo";

// Helper function to get cookie value by name
function getCookie(name: string): string | null {
  if (typeof document === "undefined") return null; // SSR check

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(";").shift() || null;
  }
  return null;
}

export function SidebarHeaderWithOrganization() {
  const [organizationName, setOrganizationName] = useState<string>("Healthcare Solutions");

  useEffect(() => {
    // Function to update organization name from cookies
    const updateOrganizationName = () => {
      try {
        const userInfoCookie = getCookie("user-info");
        console.log("Sidebar: user-info cookie:", userInfoCookie);
        if (userInfoCookie) {
          const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
          console.log("Sidebar: parsed user info:", userInfo);
          if (userInfo.organizationName) {
            console.log("Sidebar: setting organization name to:", userInfo.organizationName);
            setOrganizationName(userInfo.organizationName);
          } else {
            console.log("Sidebar: no organizationName found, using default");
            setOrganizationName("Healthcare Solutions");
          }
        } else {
          console.log("Sidebar: no user-info cookie found");
          setOrganizationName("Healthcare Solutions");
        }
      } catch (error) {
        console.error("Error parsing user info cookie:", error);
        setOrganizationName("Healthcare Solutions");
      }
    };

    // Update on mount
    updateOrganizationName();

    // Listen for cookie changes (when organization is switched)
    const handleStorageChange = () => {
      updateOrganizationName();
    };

    // Listen for custom events when organization changes
    const handleOrganizationChange = () => {
      updateOrganizationName();
    };

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("organizationChanged", handleOrganizationChange);

    // Also check periodically for cookie changes (fallback)
    const interval = setInterval(updateOrganizationName, 500);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("organizationChanged", handleOrganizationChange);
      clearInterval(interval);
    };
  }, []);

  return (
    <SidebarHeader className="p-3 group-data-[collapsible=icon]:p-2">
      <SidebarMenu>
        <Link href="/dashboard">
          <SidebarMenuItem className="flex gap-2 hover:bg-transparent">
            <SmallLogo />

            <div className="grid flex-1 text-left leading-tight group-data-[collapsible=icon]:hidden">
              <span className="truncate font-semibold text-sm">
                Aran Care
              </span>
              <span className="text-xs text-muted-foreground">
                {organizationName}
              </span>
            </div>
          </SidebarMenuItem>
        </Link>
      </SidebarMenu>
    </SidebarHeader>
  );
}
