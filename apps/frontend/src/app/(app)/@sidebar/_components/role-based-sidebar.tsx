"use client";

import React, { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@workspace/ui/components/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import { ChevronRight } from "lucide-react";
import {
  dashboardItem,
  getMenuByRole,
  isRouteActive,
  type UserRole,
  type MenuGroup,
  type MenuItem,
} from "../_constants/roleBasedSidebarConfig";

// Helper function to get cookie value by name
function getCookie(name: string): string | null {
  if (typeof document === "undefined") return null; // SSR check

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(";").shift() || null;
  }
  return null;
}

export function RoleBasedSidebar() {
  const pathName = usePathname() || "";
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [menuGroups, setMenuGroups] = useState<MenuGroup[]>([]);
  const { setOpenMobile } = useSidebar();

  // Function to update role and menu
  const updateRoleAndMenu = (role: UserRole) => {
    setUserRole(role);
    setMenuGroups(getMenuByRole(role));
  };

  useEffect(() => {
    const checkAndUpdateRole = () => {
      try {
        // Get current role from current-role cookie (for dynamic role switching)
        const currentRoleCookie = getCookie("current-role");

        if (currentRoleCookie) {
          // Use the current role from the cookie
          updateRoleAndMenu(currentRoleCookie as UserRole);
        } else {
          // Fallback: Get role from user-info cookie
          const userInfoCookie = getCookie("user-info");
          if (userInfoCookie) {
            const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
            if (userInfo.role) {
              updateRoleAndMenu(userInfo.role as UserRole);
            } else {
              // Default to hospitalAdmin if no role is found
              updateRoleAndMenu("hospitalAdmin");
            }
          } else {
            // Default to hospitalAdmin if no cookies are found
            updateRoleAndMenu("hospitalAdmin");
          }
        }
      } catch (error) {
        console.warn("Error parsing role cookies:", error);
        // Default to hospitalAdmin on error
        updateRoleAndMenu("hospitalAdmin");
      }
    };

    // Initial check
    checkAndUpdateRole();

    // Listen for cookie changes (when role is switched)
    const interval = setInterval(checkAndUpdateRole, 1000); // Check every second

    return () => clearInterval(interval);
  }, []);

  if (!userRole) {
    return null; // Don't render anything until we have a role
  }

  return (
    <>
      {/* Dashboard Item */}
      <SidebarGroup>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              isActive={isRouteActive(pathName, dashboardItem.href)}
              tooltip={dashboardItem.title}
            >
              <Link
                href={dashboardItem.href}
                onClick={() => setOpenMobile(false)}
              >
                <dashboardItem.icon className="mr-2 h-5 w-5" />
                <span>{dashboardItem.title}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroup>

      {/* Menu Groups */}
      {menuGroups.map((group) => (
        <SidebarGroup key={group.title}>
          <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
          <SidebarMenu>
            {group.items.map((item) => (
              <React.Fragment key={item.title}>
                {item.items ? (
                  <SidebarMenuCollapsible item={item} pathName={pathName} />
                ) : (
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      isActive={isRouteActive(pathName, item.href)}
                      tooltip={item.title}
                    >
                      <Link
                        href={item.href}
                        onClick={() => setOpenMobile(false)}
                      >
                        <item.icon className="mr-2 h-5 w-5" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )}
              </React.Fragment>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      ))}
    </>
  );
}

// Badge component removed as it's not being used

// Collapsible menu component for nested menu items
const SidebarMenuCollapsible = ({
  item,
  pathName,
}: {
  item: MenuItem;
  pathName: string;
}) => {
  const { setOpenMobile } = useSidebar();

  // Check if this item or any of its children are active
  const isActive =
    isRouteActive(pathName, item.href) ||
    (item.items?.some((subItem: any) =>
      isRouteActive(pathName, subItem.href),
    ) ??
      false);

  return (
    <Collapsible asChild defaultOpen={isActive} className="group/collapsible">
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={item.title} isActive={isActive}>
            <item.icon className="mr-2 h-5 w-5" />
            <span>{item.title}</span>
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="CollapsibleContent">
          <SidebarMenuSub>
            {item.items?.map((subItem: any) => (
              <SidebarMenuSubItem key={subItem.title}>
                <SidebarMenuSubButton
                  asChild
                  isActive={isRouteActive(pathName, subItem.href)}
                >
                  <Link
                    href={subItem.href}
                    onClick={() => setOpenMobile(false)}
                  >
                    {subItem.icon && <subItem.icon className="mr-2 h-4 w-4" />}
                    <span>{subItem.title}</span>
                  </Link>
                </SidebarMenuSubButton>
              </SidebarMenuSubItem>
            ))}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
};
