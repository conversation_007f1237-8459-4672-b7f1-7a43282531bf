import { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/session";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/shell";
import { DoctorDashboard } from "@/components/doctor/doctor-dashboard";

// Force dynamic rendering for this page
export const dynamic = "force-dynamic";

export const metadata: Metadata = {
  title: "Doctor Dashboard",
  description: "Unified clinical dashboard for doctors",
};

export default async function DoctorDashboardPage() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/sign-in");
  }

  // Only doctors should access this page
  if (user.role !== "doctor") {
    redirect("/dashboard");
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Clinical Dashboard"
        text="Manage patient consultations, view clinical records, and access ABHA-linked data."
      />
      <div className="grid gap-4">
        <DoctorDashboard />
      </div>
    </DashboardShell>
  );
}
