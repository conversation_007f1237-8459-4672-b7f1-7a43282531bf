"use client";

// Define the correct interface for the layout props
interface LayoutProps {
  children: React.ReactNode;
  doctor: React.ReactNode;
  branchAdmin: React.ReactNode;
  hospitalAdmin: React.ReactNode;
  nurse: React.ReactNode;
  receptionist: React.ReactNode;
  patient: React.ReactNode;
}

export default function DashboardLayout({ children }: LayoutProps) {
  // Remove role-based layout restrictions - all roles can access the main dashboard
  // The sidebar will still be role-based, but the content is accessible to everyone
  return children;
}
