"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useOrganization } from "@/contexts/organization-context";
import { useBranch } from "@/contexts/branch-context";
import { format, addDays } from "date-fns";
import { useSocket } from "@/components/providers/socket-provider";
import { joinBranch, joinOrganization } from "@/lib/socket-client";
import {
  Users,
  Calendar,
  Clock,
  UserPlus,
  ArrowRight,
  Search,
  Phone,
  Mail,
  ShieldCheck,
  CalendarDays,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

export default function ReceptionistDashboard() {
  const { currentOrganization } = useOrganization();
  const { currentBranch } = useBranch();
  const { socket, isConnected } = useSocket();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("today");
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Fetch metrics from API with branch filter
        const branchId = currentBranch?.id;
        const metricsResponse = await fetch(
          `/api/dashboard/metrics?timeRange=today${branchId ? `&branchId=${branchId}` : ""}`,
        );
        if (!metricsResponse.ok) {
          throw new Error("Failed to fetch dashboard metrics");
        }
        const metricsData = await metricsResponse.json();

        // Fetch today's appointments
        const todayAppointmentsResponse = await fetch(
          `/api/dashboard/appointments?dateRange=today${branchId ? `&branchId=${branchId}` : ""}`,
        );
        if (!todayAppointmentsResponse.ok) {
          throw new Error("Failed to fetch today's appointments");
        }
        const todayAppointmentsData = await todayAppointmentsResponse.json();

        // Fetch upcoming appointments
        const upcomingAppointmentsResponse = await fetch(
          `/api/dashboard/appointments?dateRange=week${branchId ? `&branchId=${branchId}` : ""}`,
        );
        if (!upcomingAppointmentsResponse.ok) {
          throw new Error("Failed to fetch upcoming appointments");
        }
        const upcomingAppointmentsData =
          await upcomingAppointmentsResponse.json();

        // Fetch recent patient registrations
        const patientsResponse = await fetch(
          `/api/dashboard/patients?timeRange=week${branchId ? `&branchId=${branchId}` : ""}`,
        );
        if (!patientsResponse.ok) {
          throw new Error("Failed to fetch patient statistics");
        }
        const patientsData = await patientsResponse.json();

        // Format today's appointments
        const todayAppointments =
          todayAppointmentsData.appointments?.map((app: any) => ({
            id: app.id,
            patientName: app.patientName,
            time: app.time,
            doctor: app.doctor,
            status: app.queueStatus || app.status,
            phone: app.patientPhone || "+91 98765 43210", // Fallback phone number
          })) || [];

        // Format upcoming appointments (excluding today)
        const upcomingAppointments: any[] = [];
        if (upcomingAppointmentsData.groupedAppointments) {
          // Get all dates except today
          const today = format(new Date(), "yyyy-MM-dd");
          Object.entries(upcomingAppointmentsData.groupedAppointments)
            .filter(([date]) => date !== today)
            .forEach(([date, appointments]: [string, any]) => {
              appointments.forEach((app: any) => {
                upcomingAppointments.push({
                  id: app.id,
                  patientName: app.patientName,
                  date,
                  time: app.time,
                  doctor: app.doctor,
                  status: "confirmed",
                  phone: app.patientPhone || "+91 98765 43220", // Fallback phone number
                });
              });
            });
        }

        // Format recent registrations
        const recentRegistrations =
          patientsData.recentPatients?.map((patient: any) => ({
            id: patient.id,
            name: patient.name,
            age: patient.age || 0,
            gender: patient.gender || "Unknown",
            registeredAt: patient.registrationDate,
            phone: patient.phone || "+91 98765 43225",
            email: patient.email || "<EMAIL>",
            abhaLinked: patient.abhaLinked,
          })) || [];

        // Calculate summary metrics
        const waitingPatients = todayAppointments.filter(
          (app: any) => app.status === "waiting",
        ).length;
        const inConsultation = todayAppointments.filter(
          (app: any) => app.status === "in-consultation",
        ).length;
        const completed = todayAppointments.filter(
          (app: any) => app.status === "completed",
        ).length;
        const cancelled = todayAppointments.filter(
          (app: any) => app.status === "cancelled",
        ).length;

        // Combine data
        const combinedData = {
          branch: metricsData.branch || {
            id: currentBranch?.id || "branch-1",
            name: currentBranch?.name || "Main Branch",
          },
          summary: {
            totalAppointmentsToday: todayAppointments.length,
            waitingPatients,
            inConsultation,
            completed,
            cancelled,
            registrationsToday: patientsData.summary?.newPatients || 5,
            abhaLinkedToday: patientsData.summary?.newAbhaLinkedPatients || 2,
          },
          todayAppointments,
          upcomingAppointments,
          recentRegistrations,
        };

        setDashboardData(combinedData);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        // Fallback to mock data if API fails
        const mockData = {
          branch: {
            id: currentBranch?.id || "branch-1",
            name: currentBranch?.name || "Main Branch",
          },
          summary: {
            totalAppointmentsToday: 38,
            waitingPatients: 6,
            inConsultation: 2,
            completed: 30,
            cancelled: 0,
            registrationsToday: 5,
            abhaLinkedToday: 2,
          },
          todayAppointments: [
            {
              id: "apt-1",
              patientName: "John Smith",
              time: "10:30 AM",
              doctor: "Dr. Sarah Johnson",
              status: "waiting",
              phone: "+91 98765 43210",
            },
            {
              id: "apt-2",
              patientName: "Maria Garcia",
              time: "10:45 AM",
              doctor: "Dr. Michael Chen",
              status: "waiting",
              phone: "+91 98765 43211",
            },
            {
              id: "apt-3",
              patientName: "Robert Johnson",
              time: "11:00 AM",
              doctor: "Dr. Emily Rodriguez",
              status: "waiting",
              phone: "+91 98765 43212",
            },
            {
              id: "apt-4",
              patientName: "Jennifer Lee",
              time: "11:15 AM",
              doctor: "Dr. Lisa Patel",
              status: "waiting",
              phone: "+91 98765 43213",
            },
            {
              id: "apt-5",
              patientName: "William Brown",
              time: "11:30 AM",
              doctor: "Dr. Sarah Johnson",
              status: "waiting",
              phone: "+91 98765 43214",
            },
            {
              id: "apt-6",
              patientName: "James Wilson",
              time: "10:00 AM",
              doctor: "Dr. Michael Chen",
              status: "in-consultation",
              phone: "+91 98765 43215",
            },
            {
              id: "apt-7",
              patientName: "Patricia Moore",
              time: "10:15 AM",
              doctor: "Dr. Lisa Patel",
              status: "in-consultation",
              phone: "+91 98765 43216",
            },
            {
              id: "apt-8",
              patientName: "Michael Davis",
              time: "09:00 AM",
              doctor: "Dr. Sarah Johnson",
              status: "completed",
              phone: "+91 98765 43217",
            },
            {
              id: "apt-9",
              patientName: "Linda Wilson",
              time: "09:15 AM",
              doctor: "Dr. Emily Rodriguez",
              status: "completed",
              phone: "+91 98765 43218",
            },
            {
              id: "apt-10",
              patientName: "Elizabeth Taylor",
              time: "09:30 AM",
              doctor: "Dr. Michael Chen",
              status: "completed",
              phone: "+91 98765 43219",
            },
          ],
          upcomingAppointments: [
            {
              id: "apt-11",
              patientName: "David Johnson",
              date: format(addDays(new Date(), 1), "yyyy-MM-dd"),
              time: "10:00 AM",
              doctor: "Dr. Sarah Johnson",
              status: "confirmed",
              phone: "+91 98765 43220",
            },
            {
              id: "apt-12",
              patientName: "Susan Brown",
              date: format(addDays(new Date(), 1), "yyyy-MM-dd"),
              time: "11:30 AM",
              doctor: "Dr. Michael Chen",
              status: "confirmed",
              phone: "+91 98765 43221",
            },
            {
              id: "apt-13",
              patientName: "Thomas Wilson",
              date: format(addDays(new Date(), 2), "yyyy-MM-dd"),
              time: "09:15 AM",
              doctor: "Dr. Emily Rodriguez",
              status: "confirmed",
              phone: "+91 98765 43222",
            },
            {
              id: "apt-14",
              patientName: "Nancy Davis",
              date: format(addDays(new Date(), 2), "yyyy-MM-dd"),
              time: "02:45 PM",
              doctor: "Dr. Lisa Patel",
              status: "confirmed",
              phone: "+91 98765 43223",
            },
            {
              id: "apt-15",
              patientName: "Charles Miller",
              date: format(addDays(new Date(), 3), "yyyy-MM-dd"),
              time: "10:30 AM",
              doctor: "Dr. Sarah Johnson",
              status: "confirmed",
              phone: "+91 98765 43224",
            },
          ],
          recentRegistrations: [
            {
              id: "pat-1",
              name: "Sophia Martinez",
              age: 29,
              gender: "Female",
              registeredAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
              phone: "+91 98765 43225",
              email: "<EMAIL>",
              abhaLinked: true,
            },
            {
              id: "pat-2",
              name: "Daniel Thompson",
              age: 45,
              gender: "Male",
              registeredAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
              phone: "+91 98765 43226",
              email: "<EMAIL>",
              abhaLinked: false,
            },
            {
              id: "pat-3",
              name: "Olivia Wilson",
              age: 32,
              gender: "Female",
              registeredAt: new Date(
                Date.now() - 150 * 60 * 1000,
              ).toISOString(),
              phone: "+91 98765 43227",
              email: "<EMAIL>",
              abhaLinked: true,
            },
            {
              id: "pat-4",
              name: "Ethan Johnson",
              age: 38,
              gender: "Male",
              registeredAt: new Date(
                Date.now() - 210 * 60 * 1000,
              ).toISOString(),
              phone: "+91 98765 43228",
              email: "<EMAIL>",
              abhaLinked: false,
            },
            {
              id: "pat-5",
              name: "Ava Brown",
              age: 27,
              gender: "Female",
              registeredAt: new Date(
                Date.now() - 270 * 60 * 1000,
              ).toISOString(),
              phone: "+91 98765 43229",
              email: "<EMAIL>",
              abhaLinked: false,
            },
          ],
        };
        setDashboardData(mockData);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentBranch]);

  // Connect to WebSocket when component mounts
  useEffect(() => {
    if (socket && isConnected && currentOrganization?.id) {
      // Join organization room
      joinOrganization(currentOrganization.id);

      // Join branch room if available
      if (currentBranch?.id) {
        joinBranch(currentBranch.id);
      }

      // Listen for queue updates
      socket.on("queue-status-updated", (data) => {
        console.log("Queue status updated:", data);
        // Refresh data
        fetchDashboardData();
      });

      return () => {
        socket.off("queue-status-updated");
      };
    }

    // Define fetchDashboardData function for the socket event handler
    const fetchDashboardData = async () => {
      // In a real implementation, you would fetch updated data from your API
      // For this demo, we'll just log that we received an update
      console.log("Refreshing data after queue update");
    };
  }, [socket, isConnected, currentOrganization?.id, currentBranch?.id]);

  // Status colors
  const STATUS_COLORS = {
    waiting: "bg-yellow-100 text-yellow-800",
    "in-consultation": "bg-blue-100 text-blue-800",
    completed: "bg-green-100 text-green-800",
    cancelled: "bg-red-100 text-red-800",
    confirmed: "bg-purple-100 text-purple-800",
  };

  // Filter appointments based on search query
  const filteredTodayAppointments =
    dashboardData?.todayAppointments.filter(
      (appointment: any) =>
        appointment.patientName
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        appointment.doctor.toLowerCase().includes(searchQuery.toLowerCase()) ||
        appointment.phone.includes(searchQuery),
    ) || [];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Front Desk</h1>
          <p className="text-muted-foreground">
            {dashboardData.branch.name} -{" "}
            {format(new Date(), "EEEE, MMMM d, yyyy")}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            New Patient
          </Button>
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            New Appointment
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Today's Appointments
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.summary.totalAppointmentsToday}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {dashboardData.summary.completed} completed,{" "}
              {dashboardData.summary.waitingPatients} waiting
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Waiting Patients
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.summary.waitingPatients}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {dashboardData.summary.inConsultation} currently in consultation
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              New Registrations
            </CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.summary.registrationsToday}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              New patients registered today
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ABHA Linked</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dashboardData.summary.abhaLinkedToday}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              New ABHA profiles linked today
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search patients, doctors, or phone numbers..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Tabs */}
      <Tabs
        defaultValue={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="today">Today's Appointments</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming Appointments</TabsTrigger>
          <TabsTrigger value="registrations">Recent Registrations</TabsTrigger>
        </TabsList>

        <TabsContent value="today" className="space-y-4">
          {/* Today's Appointments */}
          <Card>
            <CardHeader>
              <CardTitle>Today's Appointments</CardTitle>
              <CardDescription>
                All appointments scheduled for today
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredTodayAppointments.length > 0 ? (
                <div className="space-y-4">
                  {filteredTodayAppointments.map((appointment: any) => (
                    <div
                      key={appointment.id}
                      className="flex items-center p-3 border rounded-md"
                    >
                      <div className="w-9 h-9 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                        <Users className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">
                          {appointment.patientName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {appointment.time} with {appointment.doctor}
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <a
                          href={`tel:${appointment.phone}`}
                          className="text-muted-foreground hover:text-primary"
                        >
                          <Phone className="h-4 w-4" />
                        </a>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${STATUS_COLORS[appointment.status as keyof typeof STATUS_COLORS] || "bg-gray-100 text-gray-800"}`}
                        >
                          {appointment.status}
                        </span>
                        <Button size="sm" variant="outline">
                          Check In
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    {searchQuery
                      ? "No appointments matching your search"
                      : "No appointments for today"}
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Appointments
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          {/* Upcoming Appointments */}
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Appointments</CardTitle>
              <CardDescription>
                Appointments scheduled for the next few days
              </CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardData.upcomingAppointments.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.upcomingAppointments.map(
                    (appointment: any) => (
                      <div
                        key={appointment.id}
                        className="flex items-center p-3 border rounded-md"
                      >
                        <div className="w-9 h-9 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                          <CalendarDays className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">
                            {appointment.patientName}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(appointment.date), "EEE, MMM d")}{" "}
                            at {appointment.time} with {appointment.doctor}
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <a
                            href={`tel:${appointment.phone}`}
                            className="text-muted-foreground hover:text-primary"
                          >
                            <Phone className="h-4 w-4" />
                          </a>
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${STATUS_COLORS[appointment.status as keyof typeof STATUS_COLORS] || "bg-gray-100 text-gray-800"}`}
                          >
                            {appointment.status}
                          </span>
                          <Button size="sm" variant="outline">
                            Reschedule
                          </Button>
                        </div>
                      </div>
                    ),
                  )}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    No upcoming appointments
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View Calendar
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="registrations" className="space-y-4">
          {/* Recent Registrations */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Patient Registrations</CardTitle>
              <CardDescription>Newly registered patients</CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardData.recentRegistrations.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.recentRegistrations.map((patient: any) => (
                    <div
                      key={patient.id}
                      className="flex items-center p-3 border rounded-md"
                    >
                      <Avatar className="h-10 w-10 mr-3">
                        <AvatarFallback>
                          {patient.name
                            .split(" ")
                            .map((n: string) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="font-medium">{patient.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {patient.age} yrs, {patient.gender} • Registered{" "}
                          {format(new Date(patient.registeredAt), "h:mm a")}
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <a
                          href={`tel:${(patient as any).communicationMobile || patient.phone}`}
                          className="text-muted-foreground hover:text-primary"
                          title={`Call ${(patient as any).communicationMobile ? "communication" : "Aadhaar-linked"} mobile`}
                        >
                          <Phone className="h-4 w-4" />
                        </a>
                        <a
                          href={`mailto:${patient.email}`}
                          className="text-muted-foreground hover:text-primary"
                        >
                          <Mail className="h-4 w-4" />
                        </a>
                        {patient.abhaLinked ? (
                          <Badge
                            variant="outline"
                            className="bg-green-100 text-green-800"
                          >
                            <ShieldCheck className="h-3 w-3 mr-1" />
                            ABHA Linked
                          </Badge>
                        ) : (
                          <Button size="sm" variant="outline">
                            Link ABHA
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    No recent registrations
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Patients
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
