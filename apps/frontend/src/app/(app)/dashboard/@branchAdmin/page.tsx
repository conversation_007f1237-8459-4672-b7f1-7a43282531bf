"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useBranch } from "@/contexts/branch-context";
import { format } from "date-fns";
import { toast } from "sonner";

// Import custom components
import { MetricCard } from "@/components/dashboard/metric-card";
import { DashboardChart } from "@/components/dashboard/dashboard-chart";
import {
  DashboardFilters,
  TimeRange,
} from "@/components/dashboard/dashboard-filters";
import { useSocketEvents } from "@/hooks/use-socket-events";
import { fetchWithCache } from "@/lib/api-utils";
import { downloadCSV } from "@/lib/export-utils";
import {
  Users,
  Calendar,
  Stethoscope,
  Clock,
  ArrowRight,
  CheckCircle,
  XCircle,
  ShieldCheck,
  Download,
} from "lucide-react";

export default function BranchAdminDashboard() {
  const { currentBranch } = useBranch();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>("week");
  const [customDateRange, setCustomDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });

  // Socket events for real-time updates
  useSocketEvents({
    branchId: currentBranch?.id,
    events: [
      {
        name: "appointment-created",
        handler: () => {
          toast.info("New appointment created");
          refetchData();
        },
      },
      {
        name: "appointment-updated",
        handler: () => {
          toast.info("Appointment updated");
          refetchData();
        },
      },
      {
        name: "queue-status-updated",
        handler: () => {
          toast.info("Queue status updated");
          refetchData();
        },
      },
    ],
    showToasts: false,
  });

  // Function to refetch data
  const refetchData = async () => {
    setLoading(true);
    try {
      await fetchDashboardData();
    } catch (error) {
      console.error("Error refetching dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      // Fetch metrics from API with branch filter
      const branchId = currentBranch?.id;
      if (!branchId) {
        toast.error("No branch selected. Please select a branch first.");
        return;
      }

      // Fetch metrics data
      const metricsData = (await fetchWithCache(
        `/api/dashboard/metrics?timeRange=${timeRange}&branchId=${branchId}`,
        undefined,
        `branch_admin_metrics_${timeRange}_${branchId}`,
      )) as {
        summary: {
          totalPatients: number;
          totalDoctors: number;
          totalAppointments: number;
          totalConsultations: number;
          abhaLinkedPatients: number;
          abhaLinkPercentage: number;
          consentRequests: number;
          consentGranted: number;
        };
        trends: {
          patientGrowth: number;
          appointmentGrowth: number;
          consultationGrowth: number;
          abhaLinkGrowth: number;
        };
        appointmentsByDay: Array<{ name: string; appointments: number }>;
      };

      // Fetch doctor statistics
      const doctorsData = (await fetchWithCache(
        `/api/dashboard/doctors?timeRange=${timeRange}&branchId=${branchId}`,
        undefined,
        `branch_admin_doctors_${timeRange}_${branchId}`,
      )) as {
        doctorAvailability: Array<{
          name: string;
          status: string;
          appointments: number;
          completed: number;
        }>;
        topDoctors: Array<{
          name: string;
          appointments: number;
          completed: number;
        }>;
      };

      // Fetch patient statistics
      const patientsData = (await fetchWithCache(
        `/api/dashboard/patients?timeRange=${timeRange}&branchId=${branchId}`,
        undefined,
        `branch_admin_patients_${timeRange}_${branchId}`,
      )) as {
        summary: {
          totalPatients: number;
          newPatients: number;
          abhaLinkedPatients: number;
          abhaLinkPercentage: number;
          newPatientGrowthRate: number;
        };
      };

      // Fetch appointments
      const appointmentsData = (await fetchWithCache(
        `/api/dashboard/appointments?timeRange=${timeRange}&branchId=${branchId}`,
        undefined,
        `branch_admin_appointments_${timeRange}_${branchId}`,
      )) as { appointments: any[]; totalAppointments: number };

      // Format upcoming appointments
      const waitingAppointments =
        appointmentsData.appointments?.filter(
          (app: any) => app.queueStatus === "waiting" || !app.queueStatus,
        ) || [];

      // Format in-consultation appointments
      const inConsultationAppointments =
        appointmentsData.appointments?.filter(
          (app: any) => app.queueStatus === "in-consultation",
        ) || [];

      // Combine data
      const combinedData = {
        branch: {
          id: currentBranch.id,
          name: currentBranch.name,
          address: currentBranch.address,
          city: currentBranch.city,
          state: currentBranch.state,
          pincode: currentBranch.pincode,
        },
        summary: {
          ...metricsData.summary,
          totalPatients: patientsData.summary?.totalPatients || 0,
          newPatients: patientsData.summary?.newPatients || 0,
          abhaLinkedPatients: patientsData.summary?.abhaLinkedPatients || 0,
          abhaLinkPercentage: patientsData.summary?.abhaLinkPercentage || 0,
          totalAppointmentsToday: appointmentsData.totalAppointments || 0,
          waitingPatients: waitingAppointments.length || 0,
          inConsultation: inConsultationAppointments.length || 0,
          completed:
            appointmentsData.appointments?.filter(
              (app: any) => app.status === "completed",
            ).length || 0,
          cancelled:
            appointmentsData.appointments?.filter(
              (app: any) => app.status === "cancelled",
            ).length || 0,
        },
        trends: metricsData.trends || {
          patientGrowth: patientsData.summary?.newPatientGrowthRate || 0,
          appointmentGrowth: 0,
          consultationGrowth: 0,
          abhaLinkGrowth: 0,
        },
        todayAppointments: {
          scheduled: appointmentsData.totalAppointments || 0,
          completed:
            appointmentsData.appointments?.filter(
              (app: any) => app.status === "completed",
            ).length || 0,
          inProgress: inConsultationAppointments.length || 0,
          waiting: waitingAppointments.length || 0,
          cancelled:
            appointmentsData.appointments?.filter(
              (app: any) => app.status === "cancelled",
            ).length || 0,
        },
        appointmentsByStatus: [
          {
            name: "Completed",
            value:
              appointmentsData.appointments?.filter(
                (app: any) => app.status === "completed",
              ).length || 0,
          },
          {
            name: "In Progress",
            value: inConsultationAppointments.length || 0,
          },
          { name: "Waiting", value: waitingAppointments.length || 0 },
          {
            name: "Cancelled",
            value:
              appointmentsData.appointments?.filter(
                (app: any) => app.status === "cancelled",
              ).length || 0,
          },
        ],
        appointmentsByDay: metricsData.appointmentsByDay || [],
        doctorAvailability: doctorsData.doctorAvailability || [],
        topDoctors: doctorsData.topDoctors || [],
        upcomingAppointments: waitingAppointments.map((app: any) => ({
          id: app.id,
          patientName: app.patientName,
          time: app.time,
          doctor: app.doctor,
          status: app.queueStatus || "waiting",
        })),
        inConsultation: inConsultationAppointments.map((app: any) => ({
          id: app.id,
          patientName: app.patientName,
          time: app.time,
          doctor: app.doctor,
          duration: "In progress",
        })),
        abhaMetrics: {
          linked: patientsData.summary?.abhaLinkedPatients || 0,
          notLinked:
            patientsData.summary?.totalPatients -
              patientsData.summary?.abhaLinkedPatients || 0,
          consentRequested: metricsData.summary?.consentRequests || 0,
          consentGranted: metricsData.summary?.consentGranted || 0,
        },
      };

      setDashboardData(combinedData);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast.error(
        `Failed to load dashboard data: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timeRange, currentBranch?.id]);

  // Colors for status
  const STATUS_COLORS = {
    waiting: "bg-yellow-100 text-yellow-800",
    "in-consultation": "bg-blue-100 text-blue-800",
    completed: "bg-green-100 text-green-800",
    cancelled: "bg-red-100 text-red-800",
    available: "text-green-500",
    busy: "text-yellow-500",
    unavailable: "text-red-500",
  };

  if (loading && !dashboardData) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">
            Branch Dashboard
          </h1>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="animate-pulse bg-muted h-12 w-full rounded mb-6"></div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-32 rounded"></div>
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-80 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold tracking-tight">Branch Dashboard</h1>
        <p className="text-muted-foreground">
          No data available. Please try again later.
        </p>
        <Button onClick={refetchData}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Branch Dashboard
          </h1>
          <p className="text-muted-foreground">
            {dashboardData.branch.name} - {dashboardData.branch.city},{" "}
            {dashboardData.branch.state}
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <DashboardFilters
            timeRange={timeRange}
            onTimeRangeChange={setTimeRange}
            customDateRange={customDateRange}
            onCustomDateRangeChange={setCustomDateRange}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              downloadCSV(
                [
                  {
                    category: "Summary",
                    metric: "Total Patients",
                    value: dashboardData.summary.totalPatients,
                  },
                  {
                    category: "Summary",
                    metric: "Total Appointments",
                    value: dashboardData.summary.totalAppointments,
                  },
                  {
                    category: "Summary",
                    metric: "Total Consultations",
                    value: dashboardData.summary.totalConsultations,
                  },
                  {
                    category: "Summary",
                    metric: "ABHA Linked Patients",
                    value: dashboardData.summary.abhaLinkedPatients,
                  },
                ],
                `branch_dashboard_${format(new Date(), "yyyy-MM-dd")}.csv`,
                [
                  { key: "category", label: "Category" },
                  { key: "metric", label: "Metric" },
                  { key: "value", label: "Value" },
                ],
              )
            }
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Patients"
          value={dashboardData.summary.totalPatients.toLocaleString()}
          icon={<Users className="h-4 w-4" />}
          trend={dashboardData.trends.patientGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={loading}
        />

        <MetricCard
          title="Total Appointments"
          value={
            dashboardData.summary.totalAppointments?.toLocaleString() || "0"
          }
          icon={<Calendar className="h-4 w-4" />}
          trend={dashboardData.trends.appointmentGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={loading}
        />

        <MetricCard
          title="Total Consultations"
          value={
            dashboardData.summary.totalConsultations?.toLocaleString() || "0"
          }
          icon={<Stethoscope className="h-4 w-4" />}
          trend={dashboardData.trends.consultationGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={loading}
        />

        <MetricCard
          title="ABHA Linked Patients"
          value={dashboardData.summary.abhaLinkedPatients.toLocaleString()}
          description={`${dashboardData.summary.abhaLinkPercentage}% of total patients`}
          icon={<ShieldCheck className="h-4 w-4" />}
          trend={dashboardData.trends.abhaLinkGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={loading}
        />
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Appointments by Day */}
        <DashboardChart
          title="Appointments by Day"
          data={dashboardData.appointmentsByDay}
          type="bar"
          xAxisKey="name"
          dataKey="appointments"
          height={300}
          loading={loading}
          allowTypeChange={true}
          exportable={true}
          exportFilename="appointments_by_day"
          onRefresh={refetchData}
        />

        {/* Appointments by Status */}
        <DashboardChart
          title="Appointment Status"
          data={dashboardData.appointmentsByStatus}
          type="pie"
          nameKey="name"
          valueKey="value"
          height={300}
          loading={loading}
          allowTypeChange={true}
          exportable={true}
          exportFilename="appointments_by_status"
          onRefresh={refetchData}
          colors={["#4ade80", "#60a5fa", "#facc15", "#f87171"]}
        />

        {/* ABHA Linking Status */}
        <Card>
          <CardHeader>
            <CardTitle>ABHA Linking Status</CardTitle>
            <CardDescription>Patients with ABHA accounts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-card">
              <h3 className="text-lg font-semibold mb-2">
                ABHA Linking Status
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Patients with ABHA accounts
              </p>

              {loading ? (
                <div className="flex justify-center items-center h-48">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  {/* Simple pie chart visualization */}
                  <div className="relative w-48 h-48 rounded-full overflow-hidden mb-4">
                    {/* Calculate percentages */}
                    {(() => {
                      // Use summary data directly for more accurate values
                      const linked =
                        dashboardData.summary.abhaLinkedPatients || 0;
                      const total = dashboardData.summary.totalPatients || 0;
                      // Calculate percentage directly without storing notLinked
                      const linkedPercent =
                        total > 0 ? (linked / total) * 100 : 0;

                      return (
                        <>
                          {/* Not Linked segment (background) */}
                          <div className="absolute inset-0 bg-orange-400" />

                          {/* Linked segment (foreground) */}
                          <div
                            className="absolute inset-0 bg-green-400"
                            style={{
                              clipPath: `polygon(0 0, ${linkedPercent}% 0, ${linkedPercent}% 100%, 0 100%)`,
                            }}
                          />

                          {/* Center circle with percentages */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="bg-white rounded-full w-24 h-24 flex items-center justify-center">
                              <div className="text-center">
                                <div className="text-xl font-bold">
                                  {Math.round(linkedPercent)}%
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  Linked
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      );
                    })()}
                  </div>

                  {/* Legend */}
                  <div className="flex justify-center gap-6 mt-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-400 mr-2" />
                      <span className="text-sm">
                        ABHA Linked (
                        {dashboardData.summary.abhaLinkedPatients || 0})
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-orange-400 mr-2" />
                      <span className="text-sm">
                        Not Linked (
                        {dashboardData.summary.totalPatients -
                          dashboardData.summary.abhaLinkedPatients || 0}
                        )
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Appointments and Doctors Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Upcoming Appointments */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Appointments</CardTitle>
            <CardDescription>Next patients in the queue</CardDescription>
          </CardHeader>
          <CardContent>
            {dashboardData.upcomingAppointments.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.upcomingAppointments
                  .slice(0, 5)
                  .map((appointment: any) => (
                    <div
                      key={appointment.id}
                      className="flex items-center p-3 border rounded-md"
                    >
                      <div className="w-9 h-9 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                        <Users className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">
                          {appointment.patientName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {appointment.time} with {appointment.doctor}
                        </div>
                      </div>
                      <div>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${STATUS_COLORS[appointment.status as keyof typeof STATUS_COLORS] || "bg-gray-100 text-gray-800"}`}
                        >
                          {appointment.status}
                        </span>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-muted-foreground">
                  No upcoming appointments
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              View All Appointments
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* Doctor Availability */}
        <Card>
          <CardHeader>
            <CardTitle>Doctor Availability</CardTitle>
            <CardDescription>
              Current status of doctors in this branch
            </CardDescription>
          </CardHeader>
          <CardContent>
            {dashboardData.doctorAvailability.length > 0 ? (
              <div className="space-y-4">
                {dashboardData.doctorAvailability
                  .slice(0, 5)
                  .map((doctor: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center p-3 border rounded-md"
                    >
                      <div className="w-9 h-9 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                        <Stethoscope className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{doctor.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {doctor.appointments} appointments, {doctor.completed}{" "}
                          completed
                        </div>
                      </div>
                      <div className="text-right">
                        <div
                          className={`font-medium ${STATUS_COLORS[doctor.status as keyof typeof STATUS_COLORS] || "text-gray-500"}`}
                        >
                          {doctor.status === "available" ? (
                            <CheckCircle className="h-5 w-5 inline-block mr-1" />
                          ) : doctor.status === "busy" ? (
                            <Clock className="h-5 w-5 inline-block mr-1" />
                          ) : (
                            <XCircle className="h-5 w-5 inline-block mr-1" />
                          )}
                          {doctor.status.charAt(0).toUpperCase() +
                            doctor.status.slice(1)}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-muted-foreground">
                  No doctor data available
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              Manage Doctor Schedules
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
