"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useOrganization } from "@/contexts/organization-context";
import { useBranch, Branch } from "@/contexts/branch-context";
import { format } from "date-fns";
import { toast } from "sonner";

// Import custom components
import { MetricCard } from "@/components/dashboard/metric-card";
import { DashboardChart } from "@/components/dashboard/dashboard-chart";
import { DataTable } from "@/components/dashboard/data-table";
import {
  DashboardFilters,
  TimeRange,
} from "@/components/dashboard/dashboard-filters";
import { useDashboardData } from "@/hooks/use-dashboard-data";
import { useSocketEvents } from "@/hooks/use-socket-events";
import { fetchWithCache } from "@/lib/api-utils";
import { downloadCSV } from "@/lib/export-utils";
import {
  Users,
  Activity,
  Stethoscope,
  ClipboardList,
  Download,
  Plus,
  Heart,
  Thermometer,
  Weight,
  Ruler,
  Droplet,
  ArrowRight,
  Pill,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";

export default function NurseDashboard() {
  const { currentOrganization } = useOrganization();
  const { currentBranch, branches, setCurrentBranch } = useBranch();
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("queue");
  const [timeRange, setTimeRange] = useState<TimeRange>("today");
  const [selectedBranchId, setSelectedBranchId] = useState<string | undefined>(
    currentBranch?.id,
  );

  // Use custom hook for dashboard data
  const { isLoading: isMetricsLoading, refetch: refetchMetrics } =
    useDashboardData({
      url: `/api/dashboard/metrics?timeRange=${timeRange}${selectedBranchId ? `&branchId=${selectedBranchId}` : ""}`,
      dependencies: [timeRange, selectedBranchId],
      cacheKey: `nurse_metrics_${timeRange}_${selectedBranchId || "all"}`,
      onSuccess: (data) => {
        // Additional data fetching after metrics are loaded
        fetchAdditionalData(data);
      },
      onError: (error) => {
        if (error.message.includes("Organization not found")) {
          toast.error(
            "Organization not found. Please ensure you are logged in with an account that has an associated organization.",
          );
        } else {
          toast.error(`Failed to load dashboard data: ${error.message}`);
        }
        // Load fallback data
        setDashboardData(getFallbackData());
      },
    });

  // Update selectedBranchId when currentBranch changes
  useEffect(() => {
    if (currentBranch?.id && currentBranch.id !== selectedBranchId) {
      setSelectedBranchId(currentBranch.id);
    }
  }, [currentBranch?.id, selectedBranchId]);

  // Use socket events for real-time updates
  useSocketEvents({
    organizationId: currentOrganization?.id,
    branchId: selectedBranchId || currentBranch?.id,
    events: [
      {
        name: "appointment-created",
        handler: () => {
          toast.info("New appointment created");
          refetchMetrics();
        },
      },
      {
        name: "appointment-updated",
        handler: () => {
          toast.info("Appointment updated");
          refetchMetrics();
        },
      },
      {
        name: "queue-status-updated",
        handler: () => {
          toast.info("Queue status updated");
          refetchMetrics();
        },
      },
      {
        name: "vitals-recorded",
        handler: () => {
          toast.success("New vitals recorded");
          refetchMetrics();
        },
      },
    ],
    showToasts: false,
  });

  // Fetch additional data (appointments, vitals, tasks)
  const fetchAdditionalData = async (metricsData: any) => {
    try {
      setLoading(true);

      // Fetch today's appointments for queue
      const appointmentsData: any = await fetchWithCache(
        `/api/dashboard/appointments?dateRange=today${selectedBranchId ? `&branchId=${selectedBranchId}` : ""}`,
        undefined,
        `nurse_appointments_${selectedBranchId || "all"}`,
      );

      // Fetch vitals data
      const vitalsData: any = await fetchWithCache(
        `/api/dashboard/vitals?timeRange=${timeRange}${selectedBranchId ? `&branchId=${selectedBranchId}` : ""}`,
        undefined,
        `nurse_vitals_${timeRange}_${selectedBranchId || "all"}`,
      );

      // Fetch recent vitals records
      const recentVitalsData: any = await fetchWithCache(
        `/api/vitals/recent${selectedBranchId ? `?branchId=${selectedBranchId}` : ""}`,
        undefined,
        `nurse_recent_vitals_${selectedBranchId || "all"}`,
      );

      // Format waiting patients
      const waitingPatients =
        appointmentsData.appointments
          ?.filter(
            (app: any) => app.queueStatus === "waiting" || !app.queueStatus,
          )
          .map((app: any) => ({
            id: app.patientId,
            name: app.patientName,
            age: app.patientAge || 0,
            gender: app.patientGender || "Unknown",
            doctor: app.doctor,
            appointmentTime: app.time,
            waitingTime: "Waiting", // This would need to be calculated
          })) || [];

      // Format in-consultation patients
      const inConsultationPatients =
        appointmentsData.appointments
          ?.filter((app: any) => app.queueStatus === "in-consultation")
          .map((app: any) => ({
            id: app.patientId,
            name: app.patientName,
            age: app.patientAge || 0,
            gender: app.patientGender || "Unknown",
            doctor: app.doctor,
            appointmentTime: app.time,
            duration: "In progress", // This would need to be calculated
          })) || [];

      // Fetch pending tasks
      const tasksData: any = await fetchWithCache(
        `/api/dashboard/tasks?timeRange=${timeRange}${selectedBranchId ? `&branchId=${selectedBranchId}` : ""}`,
        undefined,
        `nurse_tasks_${timeRange}_${selectedBranchId || "all"}`,
      );

      // Get pending tasks
      const pendingTasks = tasksData?.tasks || [];

      // Calculate summary metrics
      const totalPatientsToday = appointmentsData.totalAppointments || 0;
      const vitalsRecorded = recentVitalsData.vitals?.length || 0;
      const waitingForVitals = waitingPatients.length;

      // Combine data
      const combinedData = {
        branch: metricsData.branch || {
          id: currentBranch?.id || "branch-1",
          name: currentBranch?.name || "Main Branch",
        },
        summary: {
          totalPatientsToday,
          waitingForVitals,
          vitalsRecorded,
          pendingTasks: pendingTasks.length,
          abnormalVitals: vitalsData?.summary?.abnormalVitals || 0,
          normalPercentage: vitalsData?.summary?.normalPercentage || 100,
        },
        queue: {
          waitingPatients,
          inConsultation: inConsultationPatients,
        },
        recentVitals: recentVitalsData.vitals || [],
        pendingTasks,
        vitalsStats: {
          vitalsByDate: vitalsData?.vitalsByDate || [],
          vitalsByType: vitalsData?.vitalsByType || [],
        },
      };

      setDashboardData(combinedData);
    } catch (error) {
      console.error("Error fetching additional dashboard data:", error);
      // If we have metrics data but failed to get additional data,
      // still show what we have rather than falling back completely
      if (!dashboardData) {
        setDashboardData(getFallbackData());
      }
    } finally {
      setLoading(false);
    }
  };

  // Get fallback data for error cases
  const getFallbackData = () => {
    return {
      branch: {
        id: currentBranch?.id || "branch-1",
        name: currentBranch?.name || "Main Branch",
      },
      summary: {
        totalPatientsToday: 0,
        waitingForVitals: 0,
        vitalsRecorded: 0,
        pendingTasks: 0,
        abnormalVitals: 0,
        normalPercentage: 0,
      },
      queue: {
        waitingPatients: [],
        inConsultation: [],
      },
      recentVitals: [],
      pendingTasks: [],
      vitalsStats: {
        vitalsByDate: [],
        vitalsByType: [],
      },
      branches: [],
    };
  };

  // Handle branch selection
  const handleBranchChange = async (branchId: string) => {
    const selectedBranchIdValue = branchId === "all" ? undefined : branchId;
    setSelectedBranchId(selectedBranchIdValue);

    // If a specific branch is selected, update the global branch context
    if (selectedBranchIdValue && branches.length > 0) {
      const branch = branches.find(
        (b: Branch) => b.id === selectedBranchIdValue,
      );
      if (branch) {
        try {
          await setCurrentBranch(branch);
        } catch (error) {
          console.error("Error updating current branch:", error);
        }
      }
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (range: TimeRange) => {
    setTimeRange(range);
  };

  // Handle export of vitals data
  const handleExportVitals = () => {
    if (!dashboardData?.recentVitals) return;

    // Prepare data for export
    const exportData = dashboardData.recentVitals.map((vital: any) => ({
      patientName: vital.patientName,
      age: vital.age,
      gender: vital.gender,
      recordedAt: format(new Date(vital.recordedAt), "yyyy-MM-dd HH:mm:ss"),
      bloodPressure: vital.vitals.bloodPressure,
      pulse: vital.vitals.pulse,
      temperature: vital.vitals.temperature,
      oxygenSaturation: vital.vitals.oxygenSaturation,
      respiratoryRate: vital.vitals.respiratoryRate,
      height: vital.vitals.height,
      weight: vital.vitals.weight,
      bmi: vital.vitals.bmi,
    }));

    // Download as CSV
    downloadCSV(
      exportData,
      `vitals_records_${format(new Date(), "yyyy-MM-dd")}.csv`,
    );

    toast.success("Vitals data exported successfully");
  };

  // Status colors
  const PRIORITY_COLORS = {
    high: "text-red-500",
    medium: "text-yellow-500",
    low: "text-green-500",
  };

  // Combined loading state from both hooks
  const isPageLoading = loading || isMetricsLoading;

  if (isPageLoading && !dashboardData) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">Nurse Dashboard</h1>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="animate-pulse bg-muted h-12 w-full rounded mb-6"></div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-32 rounded"></div>
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-80 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Nurse Dashboard</h1>
          <p className="text-muted-foreground">
            {dashboardData.branch.name} -{" "}
            {format(new Date(), "EEEE, MMMM d, yyyy")}
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <DashboardFilters
            timeRange={timeRange}
            onTimeRangeChange={handleTimeRangeChange}
            branches={
              dashboardData?.branches?.map((branch: any) => ({
                value: branch.id,
                label: branch.name,
              })) || []
            }
            selectedBranchId={selectedBranchId}
            onBranchChange={handleBranchChange}
          />
          <Button onClick={handleExportVitals}>
            <Download className="h-4 w-4 mr-2" />
            Export Vitals
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Patients Today"
          value={dashboardData.summary.totalPatientsToday}
          icon={<Users className="h-4 w-4" />}
          description={`${dashboardData.summary.vitalsRecorded} vitals recorded`}
          loading={isPageLoading}
        />

        <MetricCard
          title="Waiting for Vitals"
          value={dashboardData.summary.waitingForVitals}
          icon={<Activity className="h-4 w-4" />}
          description="Patients waiting for vitals check"
          loading={isPageLoading}
        />

        <MetricCard
          title="In Consultation"
          value={dashboardData.queue.inConsultation.length}
          icon={<Stethoscope className="h-4 w-4" />}
          description="Patients currently with doctors"
          loading={isPageLoading}
        />

        <MetricCard
          title="Pending Tasks"
          value={dashboardData.summary.pendingTasks}
          icon={<ClipboardList className="h-4 w-4" />}
          description="Tasks requiring attention"
          loading={isPageLoading}
        />
      </div>

      {/* Tabs */}
      <Tabs
        defaultValue={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="queue">Patient Queue</TabsTrigger>
          <TabsTrigger value="vitals">Recent Vitals</TabsTrigger>
          <TabsTrigger value="tasks">Pending Tasks</TabsTrigger>
        </TabsList>

        <TabsContent value="queue" className="space-y-4">
          {/* Waiting Patients */}
          <Card>
            <CardHeader>
              <CardTitle>Waiting for Vitals</CardTitle>
              <CardDescription>
                Patients waiting for vitals check
              </CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardData.queue.waitingPatients.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.queue.waitingPatients.map((patient: any) => (
                    <div
                      key={patient.id}
                      className="flex items-center p-3 border rounded-md"
                    >
                      <div className="w-9 h-9 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                        <Users className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{patient.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {patient.age} yrs, {patient.gender} •{" "}
                          {patient.appointmentTime} with {patient.doctor}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant="outline"
                          className="bg-yellow-100 text-yellow-800"
                        >
                          {patient.waitingTime}
                        </Badge>
                        <Button size="sm">Record Vitals</Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    No patients waiting for vitals
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* In Consultation */}
          <Card>
            <CardHeader>
              <CardTitle>Currently In Consultation</CardTitle>
              <CardDescription>Patients currently with doctors</CardDescription>
            </CardHeader>
            <CardContent>
              {dashboardData.queue.inConsultation.length > 0 ? (
                <div className="space-y-4">
                  {dashboardData.queue.inConsultation.map((patient: any) => (
                    <div
                      key={patient.id}
                      className="flex items-center p-3 border rounded-md"
                    >
                      <div className="w-9 h-9 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <Stethoscope className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{patient.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {patient.age} yrs, {patient.gender} • Started at{" "}
                          {patient.appointmentTime} with {patient.doctor}
                        </div>
                      </div>
                      <div>
                        <Badge
                          variant="outline"
                          className="bg-blue-100 text-blue-800"
                        >
                          {patient.duration}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    No patients currently in consultation
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="vitals" className="space-y-4">
          {/* Vitals Statistics */}
          <div className="grid gap-4 md:grid-cols-2">
            <DashboardChart
              title="Vitals Recorded by Day"
              data={dashboardData.vitalsStats?.vitalsByDate || []}
              type="bar"
              xAxisKey="date"
              dataKey="count"
              height={250}
              loading={isPageLoading}
              allowTypeChange={true}
              exportable={true}
              exportFilename="vitals_by_day"
              onRefresh={refetchMetrics}
            />

            <Card>
              <CardHeader>
                <CardTitle>Vitals Status</CardTitle>
                <CardDescription>Normal vs. Abnormal readings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Normal Readings</p>
                      <p className="text-2xl font-bold text-green-600">
                        {dashboardData.summary.normalPercentage}%
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Abnormal Readings</p>
                      <p className="text-2xl font-bold text-red-600">
                        {dashboardData.summary.abnormalVitals}
                      </p>
                    </div>
                  </div>

                  <Progress
                    value={dashboardData.summary.normalPercentage}
                    className="h-2 w-full bg-red-100"
                  />

                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Vitals */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recently Recorded Vitals</CardTitle>
                <CardDescription>
                  Last vitals recorded for patients
                </CardDescription>
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Record New
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {dashboardData.recentVitals.map((record: any) => (
                  <div key={record.id} className="border rounded-md p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium">{record.patientName}</h3>
                        <p className="text-sm text-muted-foreground">
                          {record.age} yrs, {record.gender} • Recorded{" "}
                          {format(new Date(record.recordedAt), "h:mm a")}
                        </p>
                      </div>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Heart className="h-4 w-4 mr-1 text-red-500" />
                          BP / Pulse
                        </div>
                        <div className="font-medium">
                          {record.vitals.bloodPressure} • {record.vitals.pulse}{" "}
                          bpm
                        </div>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Thermometer className="h-4 w-4 mr-1 text-orange-500" />
                          Temperature
                        </div>
                        <div className="font-medium">
                          {record.vitals.temperature}°C
                        </div>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Droplet className="h-4 w-4 mr-1 text-blue-500" />
                          SpO2
                        </div>
                        <div className="font-medium">
                          {record.vitals.oxygenSaturation}%
                        </div>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Activity className="h-4 w-4 mr-1 text-purple-500" />
                          Resp. Rate
                        </div>
                        <div className="font-medium">
                          {record.vitals.respiratoryRate} /min
                        </div>
                      </div>
                    </div>

                    <Separator className="my-3" />

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Ruler className="h-4 w-4 mr-1 text-gray-500" />
                          Height
                        </div>
                        <div className="font-medium">
                          {record.vitals.height} cm
                        </div>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Weight className="h-4 w-4 mr-1 text-gray-500" />
                          Weight
                        </div>
                        <div className="font-medium">
                          {record.vitals.weight} kg
                        </div>
                      </div>

                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Activity className="h-4 w-4 mr-1 text-gray-500" />
                          BMI
                        </div>
                        <div className="font-medium">{record.vitals.bmi}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Vitals Records
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          {/* Pending Tasks */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Pending Tasks</CardTitle>
                <CardDescription>
                  Tasks requiring your attention
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <Badge variant="outline" className="bg-red-100 text-red-800">
                  High:{" "}
                  {
                    dashboardData.pendingTasks.filter(
                      (t: any) => t.priority === "high",
                    ).length
                  }
                </Badge>
                <Badge
                  variant="outline"
                  className="bg-yellow-100 text-yellow-800"
                >
                  Medium:{" "}
                  {
                    dashboardData.pendingTasks.filter(
                      (t: any) => t.priority === "medium",
                    ).length
                  }
                </Badge>
                <Badge
                  variant="outline"
                  className="bg-green-100 text-green-800"
                >
                  Low:{" "}
                  {
                    dashboardData.pendingTasks.filter(
                      (t: any) => t.priority === "low",
                    ).length
                  }
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <DataTable
                data={dashboardData.pendingTasks}
                columns={[
                  {
                    key: "type",
                    title: "Task Type",
                    render: (row) => (
                      <div className="flex items-center">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                            row.type === "vitals"
                              ? "bg-blue-100"
                              : row.type === "medication"
                                ? "bg-green-100"
                                : "bg-purple-100"
                          }`}
                        >
                          {row.type === "vitals" ? (
                            <Activity className="h-4 w-4 text-blue-600" />
                          ) : row.type === "medication" ? (
                            <Pill className="h-4 w-4 text-green-600" />
                          ) : (
                            <ClipboardList className="h-4 w-4 text-purple-600" />
                          )}
                        </div>
                        <div>
                          {row.type.charAt(0).toUpperCase() + row.type.slice(1)}
                        </div>
                      </div>
                    ),
                    sortable: true,
                  },
                  {
                    key: "patientName",
                    title: "Patient",
                    sortable: true,
                  },
                  {
                    key: "doctor",
                    title: "Assigned By",
                    sortable: true,
                  },
                  {
                    key: "priority",
                    title: "Priority",
                    render: (row) => (
                      <div
                        className={`text-sm font-medium ${PRIORITY_COLORS[row.priority as keyof typeof PRIORITY_COLORS] || "text-gray-500"}`}
                      >
                        {row.priority.charAt(0).toUpperCase() +
                          row.priority.slice(1)}
                      </div>
                    ),
                    sortable: true,
                  },
                  {
                    key: "actions",
                    title: "",
                    render: () => <Button size="sm">Complete</Button>,
                    align: "right",
                  },
                ]}
                loading={isPageLoading}
                searchable={true}
                searchPlaceholder="Search tasks..."
                exportable={true}
                exportFilename="pending_tasks"
                pagination={true}
                pageSize={5}
                emptyMessage="No pending tasks"
                onRefresh={refetchMetrics}
              />
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Tasks
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
