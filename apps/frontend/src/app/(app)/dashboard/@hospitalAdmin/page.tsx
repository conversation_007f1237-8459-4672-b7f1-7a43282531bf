"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useOrganization } from "@/contexts/organization-context";
import { useBranch } from "@/contexts/branch-context";
import { format } from "date-fns";
import { toast } from "sonner";
import {
  Users,
  Calendar,
  Stethoscope,
  ShieldCheck,
  ArrowRight,
  Download,
} from "lucide-react";

// Import custom components
import { MetricCard } from "@/components/dashboard/metric-card";
import { DashboardChart } from "@/components/dashboard/dashboard-chart";
import { DataTable } from "@/components/dashboard/data-table";
import {
  DashboardFilters,
  TimeRange,
} from "@/components/dashboard/dashboard-filters";
import { BranchOverview } from "@/components/dashboard/branch-overview";
import { useDashboardData } from "@/hooks/use-dashboard-data";
import { useSocketEvents } from "@/hooks/use-socket-events";
import { fetchWithCache } from "@/lib/api-utils";
import { downloadCSV } from "@/lib/export-utils";

export default function HospitalAdminDashboard() {
  const { currentOrganization } = useOrganization();
  const { currentBranch, branches, setCurrentBranch } = useBranch();
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [timeRange, setTimeRange] = useState<TimeRange>("week");
  const [customDateRange, setCustomDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  const [selectedBranchId, setSelectedBranchId] = useState<string | undefined>(
    currentBranch?.id,
  );

  // Use custom hook for dashboard data
  const { isLoading, refetch } = useDashboardData({
    url: `/api/dashboard/metrics?timeRange=${timeRange}${
      selectedBranchId ? `&branchId=${selectedBranchId}` : ""
    }`,
    dependencies: [timeRange, selectedBranchId],
    cacheKey: `hospital_admin_metrics_${timeRange}_${
      selectedBranchId || "all"
    }`,
    onSuccess: (data) => {
      // Additional data fetching after metrics are loaded
      fetchAdditionalData(data);
    },
    onError: (error) => {
      if (error.message.includes("Organization not found")) {
        toast.error(
          "Organization not found. Please ensure you are logged in with an account that has an associated organization.",
        );
      } else {
        toast.error(`Failed to load dashboard data: ${error.message}`);
      }
      // Load fallback data
      setDashboardData(getFallbackData());
    },
  });

  // Update selectedBranchId when currentBranch changes
  useEffect(() => {
    if (currentBranch?.id && currentBranch.id !== selectedBranchId) {
      setSelectedBranchId(currentBranch.id);
    }
  }, [currentBranch?.id, selectedBranchId]);

  // Socket events for real-time updates
  useSocketEvents({
    organizationId: currentOrganization?.id,
    events: [
      {
        name: "appointment-created",
        handler: () => {
          toast.info("New appointment created");
          refetch();
        },
      },
      {
        name: "appointment-updated",
        handler: () => {
          toast.info("Appointment updated");
          refetch();
        },
      },
      {
        name: "queue-status-updated",
        handler: () => {
          toast.info("Queue status updated");
          refetch();
        },
      },
    ],
    showToasts: false,
  });

  // Fetch additional data (doctors, patients)
  const fetchAdditionalData = async (metricsData: any) => {
    try {
      setLoading(true);

      // Fetch doctor statistics
      const doctorsData: any = await fetchWithCache(
        `/api/dashboard/doctors?timeRange=${timeRange}${
          selectedBranchId ? `&branchId=${selectedBranchId}` : ""
        }`,
        undefined,
        `hospital_admin_doctors_${timeRange}_${selectedBranchId || "all"}`,
      );

      // Fetch patient statistics
      const patientsData: any = await fetchWithCache(
        `/api/dashboard/patients?timeRange=${timeRange}${
          selectedBranchId ? `&branchId=${selectedBranchId}` : ""
        }`,
        undefined,
        `hospital_admin_patients_${timeRange}_${selectedBranchId || "all"}`,
      );

      // Combine data
      const combinedData = {
        summary: {
          ...metricsData.summary,
          // Override with patient data
          abhaLinkedPatients: patientsData.summary?.abhaLinkedPatients || 0,
          totalPatients: patientsData.summary?.totalPatients || 0,
          abhaLinkPercentage: patientsData.summary?.abhaLinkPercentage || 0,
        },
        trends: metricsData.trends || {
          patientGrowth: patientsData.summary?.newPatientGrowthRate || 0,
          appointmentGrowth: 0,
          consultationGrowth: 0,
          abhaLinkGrowth: 0,
        },
        appointmentsByBranch: metricsData.appointmentsByBranch || [],
        appointmentsByStatus: metricsData.appointmentsByStatus || [],
        appointmentsByDay: metricsData.appointmentsByDay || [
          { name: "Mon", appointments: 0 },
          { name: "Tue", appointments: 0 },
          { name: "Wed", appointments: 0 },
          { name: "Thu", appointments: 0 },
          { name: "Fri", appointments: 0 },
          { name: "Sat", appointments: 0 },
          { name: "Sun", appointments: 0 },
        ],
        topDoctors: doctorsData.topDoctors || [],
        patientDemographics: patientsData.demographics?.ageGroups || [],
        abhaMetrics: {
          linked: patientsData.summary?.abhaLinkedPatients || 0,
          notLinked:
            patientsData.summary?.totalPatients -
              patientsData.summary?.abhaLinkedPatients || 0,
          consentRequested: metricsData.summary?.consentRequests || 0,
          consentGranted: metricsData.summary?.consentGranted || 0,
          consentDenied: 0,
          consentExpired: 0,
          consentRevoked: 0,
        },
      };

      setDashboardData(combinedData);
    } catch (error) {
      console.error("Error fetching additional dashboard data:", error);
      // If we have metrics data but failed to get additional data,
      // still show what we have rather than falling back completely
      if (!dashboardData) {
        setDashboardData(getFallbackData());
      }
    } finally {
      setLoading(false);
    }
  };

  // Get fallback data for error cases
  const getFallbackData = () => {
    return {
      summary: {
        totalPatients: 0,
        totalDoctors: 0,
        totalAppointments: 0,
        totalConsultations: 0,
        totalBranches: 0,
        abhaLinkedPatients: 0,
        abhaLinkPercentage: 0,
      },
      trends: {
        patientGrowth: 0,
        appointmentGrowth: 0,
        consultationGrowth: 0,
        abhaLinkGrowth: 0,
      },
      appointmentsByBranch: [],
      appointmentsByStatus: [],
      appointmentsByDay: [
        { name: "Sun", appointments: 0 },
        { name: "Mon", appointments: 0 },
        { name: "Tue", appointments: 0 },
        { name: "Wed", appointments: 0 },
        { name: "Thu", appointments: 0 },
        { name: "Fri", appointments: 0 },
        { name: "Sat", appointments: 0 },
      ],
      topDoctors: [],

      abhaMetrics: {
        linked: 0,
        notLinked: 0,
        consentRequested: 0,
        consentGranted: 0,
        consentDenied: 0,
        consentExpired: 0,
        consentRevoked: 0,
      },
      branches: [],
      branchMetrics: {},
    };
  };

  // No longer needed as we're using the DashboardChart component

  // Combined loading state from both hooks
  const isPageLoading = loading || isLoading;

  // Handle branch selection
  const handleBranchChange = async (branchId: string) => {
    const selectedBranchIdValue = branchId === "all" ? undefined : branchId;
    setSelectedBranchId(selectedBranchIdValue);

    // If a specific branch is selected, update the global branch context
    if (selectedBranchIdValue && branches.length > 0) {
      const branch = branches.find((b) => b.id === selectedBranchIdValue);
      if (branch) {
        try {
          await setCurrentBranch(branch);
        } catch (error) {
          console.error("Error updating current branch:", error);
        }
      }
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (range: TimeRange) => {
    setTimeRange(range);
  };

  // Handle custom date range change
  const handleCustomDateRangeChange = (range: {
    from: Date | undefined;
    to: Date | undefined;
  }) => {
    setCustomDateRange(range);
  };

  // Handle export of dashboard data
  const handleExportDashboard = () => {
    if (!dashboardData) return;

    // Prepare data for export
    const exportData = [
      // Summary metrics
      {
        category: "Summary",
        metric: "Total Patients",
        value: dashboardData.summary.totalPatients,
      },
      {
        category: "Summary",
        metric: "Total Doctors",
        value: dashboardData.summary.totalDoctors,
      },
      {
        category: "Summary",
        metric: "Total Appointments",
        value: dashboardData.summary.totalAppointments,
      },
      {
        category: "Summary",
        metric: "Total Consultations",
        value: dashboardData.summary.totalConsultations,
      },
      {
        category: "Summary",
        metric: "ABHA Linked Patients",
        value: dashboardData.summary.abhaLinkedPatients,
      },
      {
        category: "Summary",
        metric: "ABHA Link Percentage",
        value: `${dashboardData.summary.abhaLinkPercentage}%`,
      },
      // Add more metrics as needed
    ];

    // Download as CSV
    downloadCSV(
      exportData,
      `hospital_admin_dashboard_${format(new Date(), "yyyy-MM-dd")}.csv`,
      [
        { key: "category", label: "Category" },
        { key: "metric", label: "Metric" },
        { key: "value", label: "Value" },
      ],
    );

    toast.success("Dashboard data exported successfully");
  };

  if (isPageLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">
            Hospital Admin Dashboard
          </h1>
          <div className="animate-pulse bg-muted h-10 w-32 rounded"></div>
        </div>
        <div className="animate-pulse bg-muted h-12 w-full rounded mb-6"></div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-32 rounded"></div>
          ))}
        </div>
        <div className="grid gap-4 md:grid-cols-2">
          {Array.from({ length: 2 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-muted h-80 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold tracking-tight">
          Hospital Admin Dashboard
        </h1>
        <p className="text-muted-foreground">
          No data available. Please try again later.
        </p>
        <Button onClick={refetch}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            {selectedBranchId
              ? `Overview of ${
                  branches.find((b) => b.id === selectedBranchId)?.name ||
                  "selected branch"
                }`
              : "Overview of all branches"}
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <DashboardFilters
            timeRange={timeRange}
            onTimeRangeChange={handleTimeRangeChange}
            customDateRange={customDateRange}
            onCustomDateRangeChange={handleCustomDateRangeChange}
            branches={
              dashboardData?.branches?.map((branch: any) => ({
                value: branch.id,
                label: branch.name,
              })) || []
            }
            selectedBranchId={selectedBranchId}
            onBranchChange={handleBranchChange}
          />
          <Button variant="outline" size="sm" onClick={handleExportDashboard}>
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Branch Overview - Only show when no specific branch is selected */}
      {!selectedBranchId && (
        <BranchOverview
          branches={
            dashboardData?.branches?.map((branch: any) => ({
              id: branch.id,
              name: branch.name,
              patients: dashboardData.branchMetrics?.[branch.id]?.patients || 0,
              appointments:
                dashboardData.branchMetrics?.[branch.id]?.appointments || 0,
              consultations:
                dashboardData.branchMetrics?.[branch.id]?.consultations || 0,
              abhaLinked:
                dashboardData.branchMetrics?.[branch.id]?.abhaLinked || 0,
              abhaPercentage:
                dashboardData.branchMetrics?.[branch.id]?.abhaPercentage || 0,
              doctors: dashboardData.branchMetrics?.[branch.id]?.doctors || 0,
              staff: dashboardData.branchMetrics?.[branch.id]?.staff || 0,
            })) || []
          }
          loading={isPageLoading}
          onSelectBranch={handleBranchChange}
        />
      )}

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Patients"
          value={dashboardData.summary.totalPatients.toLocaleString()}
          icon={<Users className="h-4 w-4" />}
          trend={dashboardData.trends.patientGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={isPageLoading}
        />

        <MetricCard
          title="Total Appointments"
          value={(
            dashboardData.summary.totalAppointments || 0
          ).toLocaleString()}
          icon={<Calendar className="h-4 w-4" />}
          trend={dashboardData.trends.appointmentGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={isPageLoading}
        />

        <MetricCard
          title="Total Consultations"
          value={(
            dashboardData.summary.totalConsultations || 0
          ).toLocaleString()}
          icon={<Stethoscope className="h-4 w-4" />}
          trend={dashboardData.trends.consultationGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={isPageLoading}
        />

        <MetricCard
          title="ABHA Linked Patients"
          value={(
            dashboardData.summary.abhaLinkedPatients || 0
          ).toLocaleString()}
          description={`${dashboardData.summary.abhaLinkPercentage || 0}% of total patients`}
          icon={<ShieldCheck className="h-4 w-4" />}
          trend={dashboardData.trends.abhaLinkGrowth}
          trendLabel={`from previous ${timeRange}`}
          loading={isPageLoading}
        />
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Appointments by Branch */}
        <DashboardChart
          title="Appointments by Branch"
          data={dashboardData.appointmentsByBranch}
          type="bar"
          nameKey="name"
          valueKey="value"
          height={300}
          loading={isPageLoading}
          allowTypeChange={true}
          exportable={true}
          exportFilename="appointments_by_branch"
          onRefresh={refetch}
        />

        {/* Appointments by Status */}
        <DashboardChart
          title="Appointments by Status"
          data={dashboardData.appointmentsByStatus}
          type="pie"
          nameKey="name"
          valueKey="value"
          height={300}
          loading={isPageLoading}
          allowTypeChange={true}
          exportable={true}
          exportFilename="appointments_by_status"
          onRefresh={refetch}
        />

        {/* Appointments by Day */}
        <DashboardChart
          title="Appointments by Day"
          data={dashboardData.appointmentsByDay}
          type="bar"
          xAxisKey="name"
          dataKey="appointments"
          height={300}
          loading={isPageLoading}
          allowTypeChange={true}
          exportable={true}
          exportFilename="appointments_by_day"
          onRefresh={refetch}
        />
      </div>

      {/* Additional Metrics */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Top Doctors */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Doctors</CardTitle>
            <CardDescription>
              Based on appointment and consultation volume
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              data={dashboardData.topDoctors}
              columns={[
                {
                  key: "name",
                  title: "Doctor",
                  render: (row) => (
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                        <Stethoscope className="h-4 w-4 text-primary" />
                      </div>
                      <div>{row.name}</div>
                    </div>
                  ),
                  sortable: true,
                },
                {
                  key: "appointments",
                  title: "Appointments",
                  sortable: true,
                  align: "center",
                },
                {
                  key: "consultations",
                  title: "Consultations",
                  sortable: true,
                  align: "center",
                },
                {
                  key: "completionRate",
                  title: "Completion",
                  render: (row) => (
                    <div className="text-right font-medium">
                      {((row.consultations / row.appointments) * 100).toFixed(
                        0,
                      )}
                      %
                    </div>
                  ),
                  sortable: true,
                  align: "right",
                },
              ]}
              loading={isPageLoading}
              searchable={true}
              searchPlaceholder="Search doctors..."
              exportable={true}
              exportFilename="top_doctors"
              pagination={false}
              emptyMessage="No doctor data available"
              onRefresh={refetch}
            />
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              View All Doctors
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* ABHA Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>ABDM Integration Metrics</CardTitle>
            <CardDescription>ABHA linking and consent status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-card">
              <h3 className="text-lg font-semibold mb-2">
                ABHA Linking Status
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Patients with ABHA accounts
              </p>

              {isPageLoading ? (
                <div className="flex justify-center items-center h-48">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  {/* Simple pie chart visualization */}
                  <div className="relative w-48 h-48 rounded-full overflow-hidden mb-4">
                    {/* Calculate percentages */}
                    {(() => {
                      // Use summary data directly for more accurate values
                      const linked =
                        dashboardData.summary.abhaLinkedPatients || 0;
                      const total = dashboardData.summary.totalPatients || 0;
                      const notLinked = total - linked;
                      const linkedPercent =
                        total > 0 ? (linked / total) * 100 : 0;

                      // Update the abhaMetrics object to ensure consistency
                      dashboardData.abhaMetrics.linked = linked;
                      dashboardData.abhaMetrics.notLinked = notLinked;

                      return (
                        <>
                          {/* Not Linked segment (background) */}
                          <div className="absolute inset-0 bg-orange-400" />

                          {/* Linked segment (foreground) */}
                          <div
                            className="absolute inset-0 bg-green-400"
                            style={{
                              clipPath: `polygon(0 0, ${linkedPercent}% 0, ${linkedPercent}% 100%, 0 100%)`,
                            }}
                          />

                          {/* Center circle with percentages */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="bg-white rounded-full w-24 h-24 flex items-center justify-center">
                              <div className="text-center">
                                <div className="text-xl font-bold">
                                  {Math.round(linkedPercent)}%
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  Linked
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      );
                    })()}
                  </div>

                  {/* Legend */}
                  <div className="flex justify-center gap-6 mt-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-400 mr-2" />
                      <span className="text-sm">
                        ABHA Linked (
                        {dashboardData.summary.abhaLinkedPatients || 0})
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-orange-400 mr-2" />
                      <span className="text-sm">
                        Not Linked (
                        {dashboardData.summary.totalPatients -
                          dashboardData.summary.abhaLinkedPatients || 0}
                        )
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <MetricCard
                title="Consent Requested"
                value={dashboardData.abhaMetrics.consentRequested.toLocaleString()}
                loading={isPageLoading}
                className="h-auto"
              />
              <MetricCard
                title="Consent Granted"
                value={dashboardData.abhaMetrics.consentGranted.toLocaleString()}
                loading={isPageLoading}
                className="h-auto"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
