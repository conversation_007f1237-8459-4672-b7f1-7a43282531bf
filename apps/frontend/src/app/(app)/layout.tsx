import { AppLayout } from "@/components/app-layout";
import { SidebarProvider } from "@workspace/ui/components/sidebar";
import { SidebarInset } from "@workspace/ui/components/sidebar";

export const metadata = {
  title: "Aran Care - Dashboard",
  description: "A modern healthcare platform for patients and providers",
};

export default function Layout({
  children,
  sidebar,
}: {
  children: React.ReactNode;
  sidebar: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      {sidebar}
      <SidebarInset>
        <AppLayout>{children}</AppLayout>
      </SidebarInset>
    </SidebarProvider>
  );
}
