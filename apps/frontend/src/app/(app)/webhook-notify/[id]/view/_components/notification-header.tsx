"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ir<PERSON>, Clock } from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { HiuConsentNotify } from "@/app/(app)/webhook-notify/(list)/_lib/queries";

interface NotificationHeaderProps {
  notification: HiuConsentNotify;
}

export function NotificationHeader({ notification }: NotificationHeaderProps) {
  const isProcessed = notification.processed;

  return (
    <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
      <div className="flex flex-col space-y-1">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" asChild>
            <Link href="/webhook-notify">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            Webhook Notification
          </h2>
          {isProcessed ? (
            <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-100">
              <CheckCircle className="h-4 w-4 mr-2" />
              Processed
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="ml-2 bg-amber-50 text-amber-800 hover:bg-amber-50"
            >
              <Clock className="h-4 w-4 mr-2" />
              Pending
            </Badge>
          )}
        </div>
        <p className="text-sm text-muted-foreground">
          {notification.webhookType} - {notification.requestId}
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <p className="text-sm text-muted-foreground">
          Received: {new Date(notification.createdAt).toLocaleString()}
        </p>
      </div>
    </div>
  );
}
