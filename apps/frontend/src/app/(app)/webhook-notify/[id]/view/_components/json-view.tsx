"use client";

import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Copy } from "lucide-react";
import type { HiuConsentNotify } from "@/app/(app)/webhook-notify/(list)/_lib/queries";

interface JsonViewProps {
  notification: HiuConsentNotify;
}

export function JsonView({ notification }: JsonViewProps) {
  const [copied, setCopied] = useState(false);

  // Format the notification data as JSON
  const jsonData = JSON.stringify(notification, null, 2);

  // Copy JSON to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonData);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>JSON Data</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={copyToClipboard}
          className="h-8"
        >
          <Copy className="h-4 w-4 mr-2" />
          {copied ? "Copied!" : "Copy"}
        </Button>
      </CardHeader>
      <CardContent>
        <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[500px] text-xs">
          <code>{jsonData}</code>
        </pre>
      </CardContent>
    </Card>
  );
}
