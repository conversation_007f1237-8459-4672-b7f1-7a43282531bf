"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Copy } from "lucide-react";
import type { HiuConsentNotify } from "@/app/(app)/webhook-notify/(list)/_lib/queries";

interface HeadersViewProps {
  notification: HiuConsentNotify;
}

export function HeadersView({ notification }: HeadersViewProps) {
  const [copied, setCopied] = useState(false);

  // Format the headers data as JSON
  const headersData = JSON.stringify(notification.headers, null, 2);

  // Copy headers to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(headersData);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Headers</CardTitle>
          <CardDescription>
            The HTTP headers received with the webhook notification
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={copyToClipboard}
          className="h-8"
        >
          <Copy className="h-4 w-4 mr-2" />
          {copied ? "Copied!" : "Copy"}
        </Button>
      </CardHeader>
      <CardContent>
        <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[300px] text-xs">
          <code>{headersData}</code>
        </pre>
      </CardContent>
    </Card>
  );
}
