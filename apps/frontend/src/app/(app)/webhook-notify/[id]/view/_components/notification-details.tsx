import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { HiuConsentNotify } from "@/app/(app)/webhook-notify/(list)/_lib/queries";

interface NotificationDetailsProps {
  notification: HiuConsentNotify;
}

export function NotificationDetails({
  notification,
}: NotificationDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Details</CardTitle>
        <CardDescription>Details of the webhook notification</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Webhook Type
            </h3>
            <p className="mt-1 text-sm">{notification.webhookType}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Request ID
            </h3>
            <p className="mt-1 text-sm">{notification.requestId}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Created At
            </h3>
            <p className="mt-1 text-sm">
              {format(new Date(notification.createdAt), "PPpp")}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Updated At
            </h3>
            <p className="mt-1 text-sm">
              {format(new Date(notification.updatedAt), "PPpp")}
            </p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
          <div className="mt-1">
            {notification.processed ? (
              <Badge className="bg-green-100 text-green-800">Processed</Badge>
            ) : (
              <Badge variant="outline" className="bg-amber-50 text-amber-800">
                Pending
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
