import { notFound } from "next/navigation";
import { getHiuConsentNotificationById } from "./_lib/queries";
import { NotificationHeader } from "./_components/notification-header";
import { NotificationDetails } from "./_components/notification-details";
import { PayloadView } from "./_components/payload-view";
import { HeadersView } from "./_components/headers-view";
import { JsonView } from "./_components/json-view";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";

interface WebhookNotificationDetailPageProps {
  params: {
    id: string;
  };
}

export default async function WebhookNotificationDetailPage({
  params,
}: WebhookNotificationDetailPageProps) {
  if (!params.id) {
    return notFound();
  }

  try {
    const notification = await getHiuConsentNotificationById(params.id);

    // Ensure payload and headers are objects
    const formattedNotification = {
      ...notification,
      payload:
        typeof notification.payload === "object"
          ? notification.payload
          : JSON.parse(notification.payload as any),
      headers:
        typeof notification.headers === "object"
          ? notification.headers
          : notification.headers
            ? JSON.parse(notification.headers as any)
            : {},
    };

    return (
      <div className="space-y-6">
        <NotificationHeader notification={formattedNotification} />

        <Tabs defaultValue="formatted" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="formatted">Formatted View</TabsTrigger>
            <TabsTrigger value="payload">Payload</TabsTrigger>
            <TabsTrigger value="headers">Headers</TabsTrigger>
            <TabsTrigger value="json">Raw JSON</TabsTrigger>
          </TabsList>

          <TabsContent value="formatted">
            <div className="grid grid-cols-1 gap-6">
              <NotificationDetails notification={formattedNotification} />
              <PayloadView notification={formattedNotification} />
            </div>
          </TabsContent>

          <TabsContent value="payload">
            <PayloadView notification={formattedNotification} />
          </TabsContent>

          <TabsContent value="headers">
            <HeadersView notification={formattedNotification} />
          </TabsContent>

          <TabsContent value="json">
            <JsonView notification={formattedNotification} />
          </TabsContent>
        </Tabs>
      </div>
    );
  } catch (error) {
    console.error("Error fetching webhook notification:", error);
    return notFound();
  }
}
