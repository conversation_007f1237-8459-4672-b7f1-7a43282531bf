"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Copy } from "lucide-react";
import type { HiuConsentNotify } from "@/app/(app)/webhook-notify/(list)/_lib/queries";

interface PayloadViewProps {
  notification: HiuConsentNotify;
}

export function PayloadView({ notification }: PayloadViewProps) {
  const [copied, setCopied] = useState(false);

  // Format the payload data as JSON
  const payloadData = JSON.stringify(notification.payload, null, 2);

  // Copy payload to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(payloadData);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Payload</CardTitle>
          <CardDescription>
            The payload received in the webhook notification
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={copyToClipboard}
          className="h-8"
        >
          <Copy className="h-4 w-4 mr-2" />
          {copied ? "Copied!" : "Copy"}
        </Button>
      </CardHeader>
      <CardContent>
        <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[500px] text-xs">
          <code>{payloadData}</code>
        </pre>
      </CardContent>
    </Card>
  );
}
