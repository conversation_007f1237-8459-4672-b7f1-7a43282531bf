import { Metadata } from "next";
import { PageHeader } from "@/components/page-header";

export const metadata: Metadata = {
  title: "Webhook Notifications",
  description: "View all webhook notifications from ABDM",
};

interface HiuConsentNotifyLayoutProps {
  children: React.ReactNode;
}

export default function HiuConsentNotifyLayout({
  children,
}: HiuConsentNotifyLayoutProps) {
  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        heading="Webhook Notifications"
        subheading="View all webhook notifications from ABDM"
      />
      <div>{children}</div>
    </div>
  );
}
