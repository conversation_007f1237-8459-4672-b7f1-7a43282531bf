"use client";

import * as React from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import Link from "next/link";
import { format } from "date-fns";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ExternalLink, Check, Clock } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { DataTablePagination } from "@workspace/data-table/component/data-table-pagination";
import { DataTableViewOptions } from "@workspace/data-table/component/data-table-view-options";

import { HiuConsentNotify } from "../_lib/queries";

interface WebhookNotificationsTableProps {
  initialData: {
    data: HiuConsentNotify[];
    pageCount: number;
  };
}

export function WebhookNotificationsTable({
  initialData,
}: WebhookNotificationsTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [data] = React.useState(initialData);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [requestIdFilter, setRequestIdFilter] = React.useState<string>(
    searchParams?.get("requestId") || "",
  );
  const [webhookTypeFilter, setWebhookTypeFilter] = React.useState<string>(
    searchParams?.get("webhookType") || "all",
  );

  // Define columns
  const columns: ColumnDef<HiuConsentNotify>[] = [
    {
      accessorKey: "webhookType",
      header: "Webhook Type",
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate font-medium">
          {row.getValue("webhookType")}
        </div>
      ),
    },
    {
      accessorKey: "requestId",
      header: "Request ID",
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.getValue("requestId")}
        </div>
      ),
    },
    {
      accessorKey: "processed",
      header: "Processed",
      cell: ({ row }) => {
        const processed = row.getValue("processed");

        return processed ? (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            <Check className="h-3 w-3 mr-1" />
            Processed
          </Badge>
        ) : (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-800 hover:bg-amber-50"
          >
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }) => (
        <div className="font-medium">
          {format(new Date(row.original.createdAt), "dd/MM/yyyy HH:mm:ss")}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="icon" asChild className="h-8 w-8">
            <Link href={`/webhook-notify/${row.original.id}/view`}>
              <ExternalLink className="h-4 w-4" />
              <span className="sr-only">View</span>
            </Link>
          </Button>
        </div>
      ),
    },
  ];

  // Create table instance
  const table = useReactTable({
    data: data.data,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    manualPagination: true,
    pageCount: data.pageCount,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Handle search
  const handleSearch = () => {
    const params = new URLSearchParams(searchParams?.toString());

    if (requestIdFilter) {
      params.set("requestId", requestIdFilter);
    } else {
      params.delete("requestId");
    }

    if (webhookTypeFilter && webhookTypeFilter !== "all") {
      params.set("webhookType", webhookTypeFilter);
    } else {
      params.delete("webhookType");
    }

    // Reset to page 1 when filtering
    params.set("page", "1");

    router.push(`${pathname}?${params.toString()}`);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set("page", page.toString());
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder="Filter by Request ID"
            value={requestIdFilter}
            onChange={(e) => setRequestIdFilter(e.target.value)}
            className="h-8 w-[250px]"
          />
          <Select
            value={webhookTypeFilter}
            onValueChange={setWebhookTypeFilter}
          >
            <SelectTrigger className="h-8 w-[200px]">
              <SelectValue placeholder="Webhook Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="CONSENT_HIP_NOTIFY">
                CONSENT_HIP_NOTIFY
              </SelectItem>
              <SelectItem value="CONSENT_INIT">CONSENT_INIT</SelectItem>
              <SelectItem value="CONSENT_NOTIFY">CONSENT_NOTIFY</SelectItem>
              <SelectItem value="HEALTH_INFO_REQUEST">
                HEALTH_INFO_REQUEST
              </SelectItem>
              <SelectItem value="HEALTH_INFO_NOTIFY">
                HEALTH_INFO_NOTIFY
              </SelectItem>
              <SelectItem value="TOKEN_GENERATE">TOKEN_GENERATE</SelectItem>
              <SelectItem value="TOKEN_NOTIFY">TOKEN_NOTIFY</SelectItem>
              <SelectItem value="CARE_CONTEXT_NOTIFY">
                CARE_CONTEXT_NOTIFY
              </SelectItem>
              <SelectItem value="UIL_OTP_NOTIFY">UIL_OTP_NOTIFY</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSearch}
            className="h-8"
          >
            Search
          </Button>
        </div>
        <DataTableViewOptions table={table} />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <DataTablePagination
        table={table}
        pageCount={data.pageCount}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
