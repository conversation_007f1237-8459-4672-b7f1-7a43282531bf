import * as React from "react";
import { DataTableSkeleton } from "@workspace/data-table/component/data-table-skeleton";
import { WebhookNotificationsTable } from "./_component/table";
import { getHiuConsentNotifications } from "./_lib/queries";
import { GetWebhookNotificationsSchema } from "./_lib/validations";

interface WebhookNotificationsPageProps {
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}

export default async function WebhookNotificationsPage({
  searchParams,
}: WebhookNotificationsPageProps) {
  // Parse search params
  const parsedParams = GetWebhookNotificationsSchema.parse({
    page: searchParams.page ? Number(searchParams.page) : 1,
    perPage: searchParams.per_page ? Number(searchParams.per_page) : 10,
    sort: searchParams.sort
      ? JSON.parse(decodeURIComponent(searchParams.sort as string))
      : undefined,
    webhookType: searchParams.webhookType,
    requestId: searchParams.requestId,
  });

  // Fetch data
  const notifications = await getHiuConsentNotifications(parsedParams);

  // Ensure the data is properly formatted for the table component
  const formattedNotifications = {
    data: notifications.data.map((notification) => ({
      ...notification,
      payload:
        typeof notification.payload === "object"
          ? notification.payload
          : JSON.parse(notification.payload as any),
      headers:
        typeof notification.headers === "object"
          ? notification.headers
          : notification.headers
            ? JSON.parse(notification.headers as any)
            : {},
    })),
    pageCount: notifications.pageCount,
  };

  return (
    <React.Suspense fallback={<DataTableSkeleton columnCount={5} />}>
      <WebhookNotificationsTable initialData={formattedNotifications} />
    </React.Suspense>
  );
}
