"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";
import { GetWebhookNotificationsInput } from "./validations";

export interface HiuConsentNotify {
  id: string;
  requestId: string;
  webhookType: string;
  payload: any;
  headers: any;
  processed: boolean;
  createdAt: string;
  updatedAt: string;
}

export async function getHiuConsentNotifications(
  input: GetWebhookNotificationsInput,
) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Default pagination values
  const page = input.page || 1;
  const perPage = input.perPage || 10;
  const skip = (page - 1) * perPage;

  // Build where clause for filtering
  const where: any = {};

  // Add filters if provided
  if (input.webhookType) {
    where.webhookType = input.webhookType;
  }

  if (input.requestId) {
    where.requestId = {
      contains: input.requestId,
      mode: "insensitive",
    };
  }

  // Get total count
  const total = await prisma.hiuConsentNotify.count({
    where,
  });

  // Get webhook notifications with pagination
  const notifications = await prisma.hiuConsentNotify.findMany({
    where,
    orderBy: {
      createdAt: "desc",
    },
    skip,
    take: perPage,
  });

  // Calculate total pages
  const totalPages = Math.ceil(total / perPage);

  // Format the data to match the expected interface
  const formattedNotifications = notifications.map((notification) => ({
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  }));

  return {
    data: formattedNotifications,
    pageCount: totalPages,
  };
}

export async function getHiuConsentNotificationById(id: string) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Find the webhook notification
  const notification = await prisma.hiuConsentNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("Webhook notification not found");
  }

  // Format the data to match the expected interface
  return {
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  };
}
