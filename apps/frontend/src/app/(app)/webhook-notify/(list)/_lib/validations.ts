import { z } from "zod";

export const GetWebhookNotificationsSchema = z.object({
  page: z.number().optional().default(1),
  perPage: z.number().optional().default(10),
  sort: z
    .object({
      id: z.string(),
      desc: z.boolean(),
    })
    .optional(),
  webhookType: z.string().optional(),
  requestId: z.string().optional(),
});

export type GetWebhookNotificationsInput = z.infer<
  typeof GetWebhookNotificationsSchema
>;
