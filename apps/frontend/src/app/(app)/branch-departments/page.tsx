"use client";

import { useState, useEffect } from "react";
import { useBranch } from "@/contexts/branch-context";
import { useDepartment } from "@/contexts/department-context";
import { Button } from "@/components/ui/button";
import { PlusIcon, Pencil, Trash2, Phone, Mail } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";

interface BranchDepartment {
  id: string;
  branchId: string;
  departmentId: string;
  locationDetails: string | null;
  phoneNumber: string | null;
  emailAddress: string | null;
  departmentHead: string | null;
  workingHours: string | null;
  branch: any;
  department: any;
}

export default function BranchDepartmentsPage() {
  const { branches, isLoading: isBranchesLoading } = useBranch();
  const { departments, isLoading: isDepartmentsLoading } = useDepartment();
  const [branchDepartments, setBranchDepartments] = useState<
    BranchDepartment[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedBranchDepartment, setSelectedBranchDepartment] =
    useState<BranchDepartment | null>(null);
  const [filterBranchId, setFilterBranchId] = useState<string>("");
  const [filterDepartmentId, setFilterDepartmentId] = useState<string>("");
  const [formData, setFormData] = useState({
    branchId: "",
    departmentId: "",
    locationDetails: "",
    phoneNumber: "",
    emailAddress: "",
    departmentHead: "",
    workingHours: "",
  });

  const fetchBranchDepartments = async (
    branchId?: string,
    departmentId?: string,
  ) => {
    setIsLoading(true);
    try {
      let url = "/api/branch-departments";
      const params = new URLSearchParams();

      if (branchId) {
        params.append("branchId", branchId);
      }

      if (departmentId) {
        params.append("departmentId", departmentId);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setBranchDepartments(data.branchDepartments || []);
      } else {
        console.error("Failed to fetch branch departments");
        setBranchDepartments([]);
      }
    } catch (error) {
      console.error("Error fetching branch departments:", error);
      setBranchDepartments([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!isBranchesLoading && !isDepartmentsLoading) {
      fetchBranchDepartments(
        filterBranchId || undefined,
        filterDepartmentId || undefined,
      );
    }
  }, [
    isBranchesLoading,
    isDepartmentsLoading,
    filterBranchId,
    filterDepartmentId,
  ]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({
      branchId: "",
      departmentId: "",
      locationDetails: "",
      phoneNumber: "",
      emailAddress: "",
      departmentHead: "",
      workingHours: "",
    });
  };

  const handleEditClick = (branchDepartment: BranchDepartment) => {
    setSelectedBranchDepartment(branchDepartment);
    setFormData({
      branchId: branchDepartment.branchId,
      departmentId: branchDepartment.departmentId,
      locationDetails: branchDepartment.locationDetails || "",
      phoneNumber: branchDepartment.phoneNumber || "",
      emailAddress: branchDepartment.emailAddress || "",
      departmentHead: branchDepartment.departmentHead || "",
      workingHours: branchDepartment.workingHours || "",
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (branchDepartment: BranchDepartment) => {
    setSelectedBranchDepartment(branchDepartment);
    setIsDeleteDialogOpen(true);
  };

  const handleAddBranchDepartment = async () => {
    if (!formData.branchId || !formData.departmentId) {
      toast.error("Please select both branch and department");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/branch-departments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Department mapped to branch successfully");
        setIsAddDialogOpen(false);
        resetForm();
        fetchBranchDepartments(
          filterBranchId || undefined,
          filterDepartmentId || undefined,
        );
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to map department to branch");
      }
    } catch (error) {
      console.error("Error mapping department to branch:", error);
      toast.error("An error occurred while mapping the department to branch");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateBranchDepartment = async () => {
    if (!selectedBranchDepartment) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(
        `/api/branch-departments/${selectedBranchDepartment.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            locationDetails: formData.locationDetails,
            phoneNumber: formData.phoneNumber,
            emailAddress: formData.emailAddress,
            departmentHead: formData.departmentHead,
            workingHours: formData.workingHours,
          }),
        },
      );

      if (response.ok) {
        toast.success("Branch department mapping updated successfully");
        setIsEditDialogOpen(false);
        fetchBranchDepartments(
          filterBranchId || undefined,
          filterDepartmentId || undefined,
        );
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to update branch department mapping");
      }
    } catch (error) {
      console.error("Error updating branch department mapping:", error);
      toast.error(
        "An error occurred while updating the branch department mapping",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteBranchDepartment = async () => {
    if (!selectedBranchDepartment) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(
        `/api/branch-departments/${selectedBranchDepartment.id}`,
        {
          method: "DELETE",
        },
      );

      if (response.ok) {
        toast.success("Branch department mapping deleted successfully");
        setIsDeleteDialogOpen(false);
        fetchBranchDepartments(
          filterBranchId || undefined,
          filterDepartmentId || undefined,
        );
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete branch department mapping");
      }
    } catch (error) {
      console.error("Error deleting branch department mapping:", error);
      toast.error(
        "An error occurred while deleting the branch department mapping",
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFilterChange = (type: "branch" | "department", id: string) => {
    if (type === "branch") {
      setFilterBranchId(id === "all" ? "" : id);
    } else {
      setFilterDepartmentId(id === "all" ? "" : id);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Branch & Department Mapping
          </h1>
          <p className="text-muted-foreground">
            Map departments to branches and manage their details
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Map Department to Branch
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Map Department to Branch</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="branchId">Branch*</Label>
                  <Select
                    value={formData.branchId}
                    onValueChange={(value) =>
                      handleSelectChange("branchId", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id}>
                          {branch.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="departmentId">Department*</Label>
                  <Select
                    value={formData.departmentId}
                    onValueChange={(value) =>
                      handleSelectChange("departmentId", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((department) => (
                        <SelectItem key={department.id} value={department.id}>
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="locationDetails">Location Details</Label>
                <Input
                  id="locationDetails"
                  name="locationDetails"
                  placeholder="Floor, Building, or Wing"
                  value={formData.locationDetails}
                  onChange={handleInputChange}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="emailAddress">Email Address</Label>
                  <Input
                    id="emailAddress"
                    name="emailAddress"
                    type="email"
                    value={formData.emailAddress}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="departmentHead">Department Head</Label>
                <Input
                  id="departmentHead"
                  name="departmentHead"
                  value={formData.departmentHead}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="workingHours">Working Hours</Label>
                <Input
                  id="workingHours"
                  name="workingHours"
                  placeholder="e.g., Mon-Fri: 9 AM - 5 PM"
                  value={formData.workingHours}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline" disabled={isSubmitting}>
                  Cancel
                </Button>
              </DialogClose>
              <Button
                onClick={handleAddBranchDepartment}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Mapping..." : "Map Department"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex flex-wrap gap-4 mb-4">
        <div className="space-y-2">
          <Label htmlFor="filterBranch">Filter by Branch</Label>
          <Select
            value={filterBranchId}
            onValueChange={(value) => handleFilterChange("branch", value)}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All Branches" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Branches</SelectItem>
              {branches.map((branch) => (
                <SelectItem key={branch.id} value={branch.id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="filterDepartment">Filter by Department</Label>
          <Select
            value={filterDepartmentId}
            onValueChange={(value) => handleFilterChange("department", value)}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All Departments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((department) => (
                <SelectItem key={department.id} value={department.id}>
                  {department.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Branch</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Working Hours</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  Loading branch department mappings...
                </TableCell>
              </TableRow>
            ) : branchDepartments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  No branch department mappings found. Map a department to a
                  branch to get started.
                </TableCell>
              </TableRow>
            ) : (
              branchDepartments.map((branchDepartment) => (
                <TableRow key={branchDepartment.id}>
                  <TableCell className="font-medium">
                    {branchDepartment.branch.name}
                  </TableCell>
                  <TableCell>
                    {branchDepartment.department.name}
                    <Badge
                      variant={
                        branchDepartment.department.status === "active"
                          ? "default"
                          : "secondary"
                      }
                      className="ml-2"
                    >
                      {branchDepartment.department.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {branchDepartment.locationDetails || "—"}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col space-y-1">
                      {branchDepartment.phoneNumber && (
                        <div className="flex items-center text-sm">
                          <Phone className="h-3 w-3 mr-1" />
                          {branchDepartment.phoneNumber}
                        </div>
                      )}
                      {branchDepartment.emailAddress && (
                        <div className="flex items-center text-sm">
                          <Mail className="h-3 w-3 mr-1" />
                          {branchDepartment.emailAddress}
                        </div>
                      )}
                      {!branchDepartment.phoneNumber &&
                        !branchDepartment.emailAddress &&
                        "—"}
                    </div>
                  </TableCell>
                  <TableCell>{branchDepartment.workingHours || "—"}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditClick(branchDepartment)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(branchDepartment)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Branch Department Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Branch Department Mapping</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Branch</Label>
                <div className="p-2 border rounded-md bg-muted/50">
                  {selectedBranchDepartment?.branch.name}
                </div>
              </div>
              <div className="space-y-2">
                <Label>Department</Label>
                <div className="p-2 border rounded-md bg-muted/50">
                  {selectedBranchDepartment?.department.name}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-locationDetails">Location Details</Label>
              <Input
                id="edit-locationDetails"
                name="locationDetails"
                placeholder="Floor, Building, or Wing"
                value={formData.locationDetails}
                onChange={handleInputChange}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-phoneNumber">Phone Number</Label>
                <Input
                  id="edit-phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-emailAddress">Email Address</Label>
                <Input
                  id="edit-emailAddress"
                  name="emailAddress"
                  type="email"
                  value={formData.emailAddress}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-departmentHead">Department Head</Label>
              <Input
                id="edit-departmentHead"
                name="departmentHead"
                value={formData.departmentHead}
                onChange={handleInputChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-workingHours">Working Hours</Label>
              <Input
                id="edit-workingHours"
                name="workingHours"
                placeholder="e.g., Mon-Fri: 9 AM - 5 PM"
                value={formData.workingHours}
                onChange={handleInputChange}
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              onClick={handleUpdateBranchDepartment}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Updating..." : "Update Mapping"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Branch Department Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Branch Department Mapping</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete the mapping between branch "
              {selectedBranchDepartment?.branch.name}" and department "
              {selectedBranchDepartment?.department.name}"? This action cannot
              be undone.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteBranchDepartment}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete Mapping"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
