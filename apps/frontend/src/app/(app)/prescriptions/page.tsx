import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>Header } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/shell";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import Link from "next/link";

export const dynamic = "force-dynamic";

export const metadata: Metadata = {
  title: "Prescriptions",
  description: "View and manage patient prescriptions",
};

export default async function PrescriptionsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Prescriptions"
        text="View and manage patient prescriptions."
      >
        <Link href="/consultations">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Prescription in Consultation
          </Button>
        </Link>
      </DashboardHeader>
      <div className="grid gap-4">
        <div className="text-center py-8">
          <h2 className="text-xl font-medium mb-2">Prescription Management</h2>
          <p className="text-muted-foreground mb-4">
            Prescriptions are created as part of patient consultations. To
            create a new prescription, please create or open a consultation.
          </p>
          <Link href="/consultations">
            <Button>Go to Consultations</Button>
          </Link>
        </div>
      </div>
    </DashboardShell>
  );
}
