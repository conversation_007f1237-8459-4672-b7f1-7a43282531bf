"use server";

import { db } from "@/lib/db";

/**
 * Fetch uploaded discharge summary bundles for a given consultation.
 * This is a server action.
 */
export async function getUploadedDischargeSummaries(selectedSummary: any) {
  if (!selectedSummary?.consultationId) {
    return [];
  }

  const uploadedDocuments = await db.fhirBundle.findMany({
    where: {
      consultationId: selectedSummary.consultationId,
      bundleType: "DischargeSummary",
      status: "document_uploaded", // Only get documents that were uploaded but not yet processed into bundles
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  // if you need to extract base64Data later

  // base64Data = uploadedDocuments?.[0]?.someField ?? "";

  return uploadedDocuments;
}
