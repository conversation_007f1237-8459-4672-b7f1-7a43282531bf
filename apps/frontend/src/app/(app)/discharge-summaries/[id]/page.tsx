"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Edit,
  Trash2,
  FileText,
  User,
  Stethoscope,
} from "lucide-react";
import Link from "next/link";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import { format } from "date-fns";
import { AzureFhirUpload } from "@/components/azure-fhir-upload";
import { UploadedFilesList } from "@/components/uploaded-files-list";

interface DischargeSummary {
  id: string;
  patientId: string;
  doctorId: string;
  consultationId?: string;
  status: string;
  docStatus: string;
  type: string;
  typeDisplay: string;
  category: string;
  categoryDisplay: string;
  subject?: string;
  date: string;
  author?: string;
  description?: string;
  content: {
    admissionDate?: string;
    dischargeDate?: string;

    // Chief Co<PERSON>laints
    chiefComplaint?: string;

    // Medical History
    medicalHistory?: string;

    // Investigations
    investigations?: string;
    vitalSigns?: string;
    labResults?: string;

    // Procedures
    procedureName?: string;
    procedureCode?: string;
    procedureDate?: string;
    proceduresPerformed?: string;

    // Medications
    medicationName?: string;
    medicationCode?: string;
    medicationInstructions?: string;
    medicationsList?: string;

    // Care Plan
    carePlan?: string;
    followUpDate?: string;
    dischargeInstructions?: string;
    recommendations?: string;

    // Legacy fields
    diagnosisName?: string;
    diagnosisCode?: string;
    dischargeSummary?: string;
    condition?: string;
    treatmentProvided?: string;
  };
  createdAt: string;
  updatedAt: string;
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  doctor?: {
    id: string;
    user: {
      name: string;
    };
  };
  consultation?: {
    patient: {
      firstName: string;
      lastName: string;
    };
    doctor: {
      user: {
        name: string;
      };
    };
  };
}

export default function DischargeSummaryDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [dischargeSummary, setDischargeSummary] =
    useState<DischargeSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params?.id) {
      fetchDischargeSummary(params.id as string);
    }
  }, [params?.id]);

  const fetchDischargeSummary = async (id: string) => {
    try {
      setLoading(true);
      const response = await Fetch.get(`/api/discharge-summary/${id}`);
      if (response.error) {
        throw new Error(response.error);
      }
      setDischargeSummary(response as unknown as DischargeSummary);
    } catch (error) {
      console.error("Error fetching discharge summary:", error);
      toast.error("Failed to fetch discharge summary");
      router.push("/discharge-summaries");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!dischargeSummary) return;

    if (
      confirm(
        "Are you sure you want to delete this discharge summary? This action cannot be undone.",
      )
    ) {
      try {
        const response = await Fetch.delete(
          `/api/discharge-summary/${dischargeSummary.id}`,
        );
        if (response.error) {
          throw new Error(response.error);
        }
        toast.success("Discharge summary deleted successfully");
        router.push("/discharge-summaries");
      } catch (error) {
        console.error("Error deleting discharge summary:", error);
        toast.error("Failed to delete discharge summary");
      }
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading discharge summary...</div>
        </div>
      </div>
    );
  }

  if (!dischargeSummary) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">
            Discharge Summary Not Found
          </h1>
          <Link href="/discharge-summaries">
            <Button>Back to Discharge Summaries</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/discharge-summaries">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Discharge Summaries
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Discharge Summary</h1>
            <p className="text-muted-foreground">
              {dischargeSummary.patient
                ? `${dischargeSummary.patient.firstName} ${dischargeSummary.patient.lastName}`
                : dischargeSummary.consultation
                  ? `${dischargeSummary.consultation.patient.firstName} ${dischargeSummary.consultation.patient.lastName}`
                  : "Unknown Patient"}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge
            variant={
              dischargeSummary.docStatus === "final" ? "default" : "secondary"
            }
          >
            {dischargeSummary.docStatus}
          </Badge>
          <Link href={`/discharge-summaries/${dischargeSummary.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </Link>
          <Button variant="destructive" size="sm" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Patient Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Patient Name
              </p>
              <p className="text-lg">
                {dischargeSummary.consultation
                  ? `${dischargeSummary.consultation.patient.firstName} ${dischargeSummary.consultation.patient.lastName}`
                  : "Unknown Patient"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Attending Doctor
              </p>
              <p className="text-lg">
                {dischargeSummary.consultation?.doctor.user.name ||
                  dischargeSummary.author ||
                  "Unknown Doctor"}
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {dischargeSummary.content.admissionDate && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Admission Date
                  </p>
                  <p>
                    {format(
                      new Date(dischargeSummary.content.admissionDate),
                      "MMM dd, yyyy",
                    )}
                  </p>
                </div>
              )}
              {dischargeSummary.content.dischargeDate && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Discharge Date
                  </p>
                  <p>
                    {format(
                      new Date(dischargeSummary.content.dischargeDate),
                      "MMM dd, yyyy",
                    )}
                  </p>
                </div>
              )}
            </div>
            {dischargeSummary.content.condition && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Condition at Discharge
                </p>
                <Badge variant="outline">
                  {dischargeSummary.content.condition}
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Stethoscope className="h-5 w-5" />
              <span>Clinical Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Chief Complaint
              </p>
              <p>{dischargeSummary.content.chiefComplaint}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Primary Diagnosis
              </p>
              <p>{dischargeSummary.content.diagnosisName}</p>
              {dischargeSummary.content.diagnosisCode && (
                <p className="text-sm text-muted-foreground">
                  Code: {dischargeSummary.content.diagnosisCode}
                </p>
              )}
            </div>
            {dischargeSummary.content.procedureName && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Procedure Performed
                </p>
                <p>{dischargeSummary.content.procedureName}</p>
                {dischargeSummary.content.procedureDate && (
                  <p className="text-sm text-muted-foreground">
                    Date:{" "}
                    {format(
                      new Date(dischargeSummary.content.procedureDate),
                      "MMM dd, yyyy",
                    )}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Discharge Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none">
            <p className="whitespace-pre-wrap">
              {dischargeSummary.content.dischargeSummary}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Additional Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {dischargeSummary.content.vitalSigns && (
          <Card>
            <CardHeader>
              <CardTitle>Vital Signs at Discharge</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">
                {dischargeSummary.content.vitalSigns}
              </p>
            </CardContent>
          </Card>
        )}

        {dischargeSummary.content.labResults && (
          <Card>
            <CardHeader>
              <CardTitle>Lab Results</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">
                {dischargeSummary.content.labResults}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Treatment and Medications */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {dischargeSummary.content.treatmentProvided && (
          <Card>
            <CardHeader>
              <CardTitle>Treatment Provided</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-wrap">
                {dischargeSummary.content.treatmentProvided}
              </p>
            </CardContent>
          </Card>
        )}

        {dischargeSummary.content.medicationName && (
          <Card>
            <CardHeader>
              <CardTitle>Medications</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="font-medium">
                  {dischargeSummary.content.medicationName}
                </p>
                {dischargeSummary.content.medicationCode && (
                  <p className="text-sm text-muted-foreground">
                    Code: {dischargeSummary.content.medicationCode}
                  </p>
                )}
              </div>
              {dischargeSummary.content.medicationInstructions && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Instructions
                  </p>
                  <p className="text-sm">
                    {dischargeSummary.content.medicationInstructions}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Discharge Summary Document Upload */}
      {dischargeSummary.consultationId && (
        <Card className="border-2 border-purple-100 shadow-sm">
          <CardHeader className="bg-purple-50 pb-3">
            <CardTitle className="flex items-center text-purple-700">
              <FileText className="h-5 w-5 mr-2" />
              Upload Discharge Summary Document
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <AzureFhirUpload
              consultationId={dischargeSummary.consultationId}
              patientId={dischargeSummary.patientId}
              bundleType="DischargeSummary"
              title="Upload Discharge Summary Document"
              description="Upload a PDF discharge summary document that will be stored in Azure Blob Storage and automatically generate a DischargeSummary FHIR bundle with DocumentReference for ABDM compliance."
              onUploadSuccess={(result) => {
                console.log(
                  "Discharge summary document uploaded successfully:",
                  result,
                );
                toast.success(
                  "Discharge summary document uploaded and FHIR bundle generated successfully",
                );
                window.location.reload();
              }}
              onUploadError={(error) => {
                console.error(
                  "Discharge summary document upload failed:",
                  error,
                );
                toast.error("Failed to upload discharge summary document");
              }}
              hideWhenFilesExist={true}
              className="mb-4"
            />
          </CardContent>
        </Card>
      )}

      {/* Uploaded Discharge Summary Documents */}
      {dischargeSummary.consultationId && (
        <UploadedFilesList
          consultationId={dischargeSummary.consultationId}
          bundleType="DischargeSummary"
          title="Uploaded Discharge Summary Documents"
          description="Previously uploaded discharge summary documents and generated FHIR bundles"
          className="mb-6"
          showOnlyGenerated={true}
        />
      )}

      {/* Footer Information */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <div>
              Created:{" "}
              {format(
                new Date(dischargeSummary.createdAt),
                "MMM dd, yyyy 'at' HH:mm",
              )}
            </div>
            <div>
              Last updated:{" "}
              {format(
                new Date(dischargeSummary.updatedAt),
                "MMM dd, yyyy 'at' HH:mm",
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
