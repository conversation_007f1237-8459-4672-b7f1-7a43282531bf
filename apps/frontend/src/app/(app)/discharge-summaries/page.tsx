"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import {
  Plus,
  Search,
  FileText,
  Calendar,
  User,
  Stethoscope,
  RefreshCw,
  Upload,
  Loader2,
  Download,
} from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";

interface DischargeSummary {
  id: string;
  patientId: string;
  doctorId: string;
  consultationId?: string;
  status: string;
  docStatus: string;
  type: string;
  typeDisplay: string;
  category: string;
  categoryDisplay: string;
  subject?: string;
  date: string;
  author?: string;
  description?: string;
  content: {
    admissionDate?: string;
    dischargeDate?: string;

    // Chief Complaints
    chiefComplaint?: string;

    // Medical History
    medicalHistory?: string;

    // Investigations
    investigations?: string;
    vitalSigns?: string;
    labResults?: string;

    // Procedures
    procedureName?: string;
    procedureCode?: string;
    procedureDate?: string;
    proceduresPerformed?: string;

    // Medications
    medicationName?: string;
    medicationCode?: string;
    medicationInstructions?: string;
    medicationsList?: string;

    // Care Plan
    carePlan?: string;
    followUpDate?: string;
    dischargeInstructions?: string;
    recommendations?: string;

    // Legacy fields
    diagnosisName?: string;
    diagnosisCode?: string;
    dischargeSummary?: string;
    condition?: string;
    treatmentProvided?: string;
  };
  createdAt: string;
  updatedAt: string;
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
    gender?: string;
    dateOfBirth?: string;
    phone?: string;
  };
  doctor?: {
    id: string;
    user: {
      name: string;
    };
  };
  organization?: {
    id: string;
    name: string;
  };
  consultation?: {
    id: any;
    patient: {
      firstName: string;
      lastName: string;
      gender?: string;
      dateOfBirth?: string;
    };
    doctor: {
      user: {
        name: string;
      };
    };
  };
}

export default function DischargeSummariesPage() {
  const [dischargeSummaries, setDischargeSummaries] = useState<
    DischargeSummary[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [uploadingIds, setUploadingIds] = useState<Set<string>>(new Set());

  // Upload dialog state
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedSummary, setSelectedSummary] =
    useState<DischargeSummary | null>(null);
  const [fetchRequests, setFetchRequests] = useState<any[]>([]);
  const [selectedFetchRequest, setSelectedFetchRequest] = useState<string>("");
  const [loadingFetchRequests, setLoadingFetchRequests] = useState(false);

  useEffect(() => {
    fetchDischargeSummaries();
  }, []);

  // Refresh data when the page becomes visible (e.g., when navigating back)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchDischargeSummaries();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Also refresh when the component mounts
    const handleFocus = () => {
      fetchDischargeSummaries();
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleFocus);
    };
  }, []);

  const fetchDischargeSummaries = async () => {
    try {
      setLoading(true);
      const response = await Fetch.get("/api/discharge-summary");
      console.log("Raw response:", response);

      if (response.error) {
        throw new Error(response.error);
      }

      // Extract only the discharge summaries (object values with numeric keys)
      const summariesData = Object.values(response).filter(
        (item) => typeof item === "object" && item?.id && item?.content,
      );

      console.log("Parsed discharge summaries:", summariesData.length);
      setDischargeSummaries(summariesData);
    } catch (error) {
      console.error("Error fetching discharge summaries:", error);
      toast.error("Failed to fetch discharge summaries");
      setDischargeSummaries([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle upload button click - show dialog to select HI request
  const handleUploadClick = async (summary: DischargeSummary) => {
    setSelectedSummary(summary);
    setLoadingFetchRequests(true);
    setUploadDialogOpen(true);

    try {
      // Load HI requests using the same logic as consultation uploads
      // Pass consultationId if available to match exact consultation like consultation uploads
      const url = summary.consultationId
        ? `/api/patients/${summary.patientId}/hi-requests?consultationId=${summary.consultationId}`
        : `/api/patients/${summary.patientId}/hi-requests`;

      const response = await Fetch.get(url);
      if (response.error) {
        throw new Error(response.error);
      }
      setFetchRequests(response.hiRequests || []);
    } catch (error) {
      console.error("Error loading HI requests:", error);
      toast.error("Failed to load health record requests");
      setFetchRequests([]);
    } finally {
      setLoadingFetchRequests(false);
    }
  };

  // Handle actual upload to selected HI request
  const handleUpload = async () => {
    console.log("Consultation data:", selectedSummary);
    if (!selectedSummary || !selectedFetchRequest) {
      toast.error("Please select a health record request");
      return;
    }

    try {
      setUploadingIds((prev) => new Set(prev).add(selectedSummary.id));

      // Generate FHIR bundle for the discharge summary
      const bundleParams = new URLSearchParams();

      // Add required parameters
      bundleParams.append(
        "patientName",
        selectedSummary.patient
          ? `${selectedSummary.patient.firstName} ${selectedSummary.patient.lastName}`
          : selectedSummary.consultation
            ? `${selectedSummary.consultation.patient.firstName} ${selectedSummary.consultation.patient.lastName}`
            : "Unknown Patient",
      );
      bundleParams.append("patientId", selectedSummary.patientId);
      bundleParams.append(
        "patientGender",
        selectedSummary.patient?.gender || "N/A",
      );
      bundleParams.append(
        "patientBirthDate",
        selectedSummary.patient?.dateOfBirth?.toString().split("T")[0] || "N/A",
      );
      bundleParams.append(
        "organizationName",
        selectedSummary.organization?.name || "N/A",
      );
      bundleParams.append(
        "practitionerName",
        selectedSummary.doctor?.user?.name ||
          selectedSummary.consultation?.doctor.user.name ||
          selectedSummary.author ||
          "Unknown Doctor",
      );
      bundleParams.append("practitionerId", selectedSummary.doctorId);

      // Basic Information
      if (selectedSummary.content.admissionDate) {
        bundleParams.append(
          "admissionDate",
          selectedSummary.content.admissionDate,
        );
      }
      if (selectedSummary.content.dischargeDate) {
        bundleParams.append(
          "dischargeDate",
          selectedSummary.content.dischargeDate,
        );
      }

      // Chief Complaints
      if (selectedSummary.content.chiefComplaint) {
        bundleParams.append(
          "chiefComplaint",
          selectedSummary.content.chiefComplaint,
        );
      }

      // Medical History
      if (selectedSummary.content.medicalHistory) {
        bundleParams.append(
          "medicalHistory",
          selectedSummary.content.medicalHistory,
        );
        bundleParams.append(
          "admissionDetails",
          selectedSummary.content.medicalHistory,
        ); // Map to FHIR field
      }

      // Investigations
      if (selectedSummary.content.investigations) {
        bundleParams.append(
          "investigations",
          selectedSummary.content.investigations,
        );
      }
      if (selectedSummary.content.vitalSigns) {
        bundleParams.append("vitalSigns", selectedSummary.content.vitalSigns);
      }
      if (selectedSummary.content.labResults) {
        bundleParams.append("labResults", selectedSummary.content.labResults);
      }

      // Procedures
      if (selectedSummary.content.proceduresPerformed) {
        bundleParams.append(
          "proceduresPerformed",
          selectedSummary.content.proceduresPerformed,
        );
      }
      if (selectedSummary.content.procedureName) {
        bundleParams.append(
          "procedureName",
          selectedSummary.content.procedureName,
        );
      }
      if (selectedSummary.content.procedureCode) {
        bundleParams.append(
          "procedureCode",
          selectedSummary.content.procedureCode,
        );
      }
      if (selectedSummary.content.procedureDate) {
        bundleParams.append(
          "procedureDate",
          selectedSummary.content.procedureDate,
        );
      }

      // Medications
      if (selectedSummary.content.medicationsList) {
        bundleParams.append(
          "medicationsList",
          selectedSummary.content.medicationsList,
        );
      }
      if (selectedSummary.content.medicationName) {
        bundleParams.append(
          "medicationName",
          selectedSummary.content.medicationName,
        );
      }
      if (selectedSummary.content.medicationCode) {
        bundleParams.append(
          "medicationCode",
          selectedSummary.content.medicationCode,
        );
      }
      if (selectedSummary.content.medicationInstructions) {
        bundleParams.append(
          "medicationInstructions",
          selectedSummary.content.medicationInstructions,
        );
      }

      // Care Plan
      if (selectedSummary.content.carePlan) {
        bundleParams.append("carePlan", selectedSummary.content.carePlan);
      }
      if (selectedSummary.content.followUpDate) {
        bundleParams.append(
          "followUpDate",
          selectedSummary.content.followUpDate,
        );
      }
      bundleParams.append("consultationId", selectedSummary?.consultation?.id);
      if (selectedSummary.content.recommendations) {
        bundleParams.append(
          "recommendations",
          selectedSummary.content.recommendations,
        );
      }

      // Always add dischargeInstructions (required field)
      bundleParams.append(
        "dischargeInstructions",
        selectedSummary.content.dischargeInstructions ||
          "Follow standard discharge protocols and contact your healthcare provider if symptoms worsen.",
      );

      // Fetch uploaded documents for this discharge summary and include base64 data
      try {
        const uploadedDocuments = await Fetch.get(
          `/api/fhir-bundles?consultationId=${selectedSummary.consultationId}&bundleType=DischargeSummary&status=document_uploaded`,
        );

        if (uploadedDocuments && uploadedDocuments.length > 0) {
          // Get the first uploaded document and extract base64 data
          const firstDoc = uploadedDocuments[0];
          const docBundle = firstDoc.bundleJson as any;

          if (docBundle?.entry) {
            const docRefEntry = docBundle.entry.find(
              (entry: any) =>
                entry.resource?.resourceType === "DocumentReference",
            );

            if (docRefEntry?.resource?.content?.[0]?.attachment?.data) {
              const base64Data =
                docRefEntry.resource.content[0].attachment.data;
              bundleParams.append(
                "documentUrl",
                `data:application/pdf;base64,${base64Data}`,
              );
              bundleParams.append(
                "documentTitle",
                docRefEntry.resource.content[0].attachment.title ||
                  "Discharge Summary Document",
              );
              console.log("✅ Added uploaded document to bundle generation");
            }
          }
        }
      } catch (docError) {
        console.warn(
          "Could not fetch uploaded documents for discharge summary:",
          docError,
        );
        // Continue without documents - this is not a critical error
      }

      // Legacy fields
      if (selectedSummary.content.diagnosisName) {
        bundleParams.append(
          "diagnosisName",
          selectedSummary.content.diagnosisName,
        );
      }
      if (selectedSummary.content.diagnosisCode) {
        bundleParams.append(
          "diagnosisCode",
          selectedSummary.content.diagnosisCode,
        );
      }
      if (selectedSummary.content.dischargeSummary) {
        bundleParams.append(
          "dischargeSummary",
          selectedSummary.content.dischargeSummary,
        );
      }
      if (selectedSummary.content.condition) {
        bundleParams.append("condition", selectedSummary.content.condition);
      }
      if (selectedSummary.content.treatmentProvided) {
        bundleParams.append(
          "treatmentProvided",
          selectedSummary.content.treatmentProvided,
        );
      }

      // Step 1: Generate the FHIR bundle
      const bundleResponse = await Fetch.get(
        `/api/fhir/bundles/discharge-summary?${bundleParams.toString()}`,
      );

      if (bundleResponse.error) {
        throw new Error(bundleResponse.error);
      }

      // Step 2: Send To Patient using the same approach as consultation uploads
      const uploadResponse = await Fetch.post(
        "/api/abdm/upload-discharge-summary",
        {
          patientId: selectedSummary.patientId,
          hiRequestId: selectedFetchRequest,
          fhirBundle: bundleResponse,
          consultationId: selectedSummary.consultationId,
        },
      );

      if (uploadResponse.error) {
        throw new Error(uploadResponse.error);
      }

      toast.success("Discharge summary uploaded to ABDM successfully!");
      setUploadDialogOpen(false);
      setSelectedSummary(null);
      setSelectedFetchRequest("");
    } catch (error) {
      console.error("Error uploading discharge summary:", error);
      toast.error(
        `Failed to upload discharge summary: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    } finally {
      setUploadingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(selectedSummary.id);
        return newSet;
      });
    }
  };

  // Handle PDF download
  const handleDownloadPdf = async (consultationId: string) => {
    try {
      const response = await fetch(
        `/api/pdf/discharge-summaries?consultationId=${consultationId}`,
      );

      if (!response.ok) {
        throw new Error("Failed to generate PDF");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `discharge-summary-${consultationId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("PDF downloaded successfully");
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error("Failed to download PDF");
    }
  };

  const filteredSummaries = (dischargeSummaries || []).filter((summary) => {
    const searchLower = searchTerm.toLowerCase();

    // Get patient name from direct patient data or consultation data
    const patientName = summary.patient
      ? `${summary.patient.firstName} ${summary.patient.lastName}`
      : summary.consultation
        ? `${summary.consultation.patient.firstName} ${summary.consultation.patient.lastName}`
        : "";

    // Get doctor name from direct doctor data or consultation data
    const doctorName =
      summary.doctor?.user?.name ||
      summary.consultation?.doctor.user.name ||
      summary.author ||
      "";

    const diagnosis = summary.content.diagnosisName || "";
    const chiefComplaint = summary.content.chiefComplaint || "";

    return (
      patientName.toLowerCase().includes(searchLower) ||
      doctorName.toLowerCase().includes(searchLower) ||
      diagnosis.toLowerCase().includes(searchLower) ||
      chiefComplaint.toLowerCase().includes(searchLower)
    );
  });

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading discharge summaries...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Discharge Summaries</h1>
          <p className="text-muted-foreground">
            Manage patient discharge summaries and documentation
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => fetchDischargeSummaries()}
            disabled={loading}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Link href="/discharge-summaries/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Discharge Summary
            </Button>
          </Link>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search by patient name, doctor, diagnosis, or complaint..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Summaries
                </p>
                <p className="text-2xl font-bold">
                  {(dischargeSummaries || []).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  This Month
                </p>
                <p className="text-2xl font-bold">
                  {
                    (dischargeSummaries || []).filter(
                      (s) =>
                        new Date(s.createdAt).getMonth() ===
                        new Date().getMonth(),
                    ).length
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Filtered Results
                </p>
                <p className="text-2xl font-bold">{filteredSummaries.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discharge Summaries List */}
      <div className="space-y-4">
        {filteredSummaries.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No discharge summaries found
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm
                  ? "No summaries match your search criteria."
                  : "Get started by creating your first discharge summary."}
              </p>
              {!searchTerm && (
                <Link href="/discharge-summaries/new">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Discharge Summary
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredSummaries.map((summary) => (
            <Card
              key={summary.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">
                      {summary.patient
                        ? `${summary.patient.firstName} ${summary.patient.lastName}`
                        : summary.consultation
                          ? `${summary.consultation.patient.firstName} ${summary.consultation.patient.lastName}`
                          : "Unknown Patient"}
                    </CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Stethoscope className="h-4 w-4" />
                        <span>
                          {summary.doctor?.user?.name ||
                            summary.consultation?.doctor.user.name ||
                            summary.author ||
                            "Unknown Doctor"}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Discharged:{" "}
                          {summary.content.dischargeDate
                            ? format(
                                new Date(summary.content.dischargeDate),
                                "MMM dd, yyyy",
                              )
                            : "Not specified"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={
                        summary.docStatus === "final" ? "default" : "secondary"
                      }
                    >
                      {summary.docStatus}
                    </Badge>
                    <Badge variant="outline">
                      {summary.content.condition || "Stable"}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Primary Diagnosis
                    </p>
                    <p className="text-sm">{summary.content.diagnosisName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Chief Complaint
                    </p>
                    <p className="text-sm">{summary.content.chiefComplaint}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Summary
                    </p>
                    <p className="text-sm line-clamp-2">
                      {summary.content.dischargeSummary}
                    </p>
                  </div>
                  <div className="flex justify-between items-center pt-2">
                    <div className="text-xs text-muted-foreground">
                      Created:{" "}
                      {format(
                        new Date(summary.createdAt),
                        "MMM dd, yyyy 'at' HH:mm",
                      )}
                    </div>
                    <div className="space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUploadClick(summary)}
                        disabled={uploadingIds.has(summary.id)}
                        className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                      >
                        {uploadingIds.has(summary.id) ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="mr-2 h-4 w-4" />
                            Send To Patient
                          </>
                        )}
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleDownloadPdf(
                            summary.consultationId || summary.id,
                          )
                        }
                        disabled={!summary.consultationId}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download PDF
                      </Button>

                      <Link href={`/discharge-summaries/${summary.id}`}>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </Link>
                      <Link href={`/discharge-summaries/${summary.id}/edit`}>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Discharge Summary to ABDM</DialogTitle>
            <DialogDescription>
              Select a health record request to upload this discharge summary to
              ABDM.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedSummary && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Patient</Label>
                <p className="text-sm text-muted-foreground">
                  {selectedSummary.patient
                    ? `${selectedSummary.patient.firstName} ${selectedSummary.patient.lastName}`
                    : selectedSummary.consultation
                      ? `${selectedSummary.consultation.patient.firstName} ${selectedSummary.consultation.patient.lastName}`
                      : "Unknown Patient"}
                </p>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="fetch-request">Health Record Request</Label>
              {loadingFetchRequests ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">
                    Loading requests...
                  </span>
                </div>
              ) : fetchRequests.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No active health record requests found for this patient.
                </p>
              ) : (
                <Select
                  value={selectedFetchRequest}
                  onValueChange={setSelectedFetchRequest}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a health record request" />
                  </SelectTrigger>
                  <SelectContent>
                    {fetchRequests.map((request) => (
                      <SelectItem key={request.id} value={request.id}>
                        <div className="flex flex-col">
                          <span>
                            Request{" "}
                            {request.transactionId?.slice(-8) ||
                              request.id.slice(-8)}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(
                              request.requestTimestamp,
                            ).toLocaleDateString()}{" "}
                            - {request.status}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setUploadDialogOpen(false);
                setSelectedSummary(null);
                setSelectedFetchRequest("");
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={
                !selectedFetchRequest ||
                uploadingIds.has(selectedSummary?.id || "")
              }
            >
              {uploadingIds.has(selectedSummary?.id || "") ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Send To Patient
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
