"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useDoctors } from "@/hooks/use-doctors";
import { useCurrentBranch } from "@/hooks/use-current-branch";
import { toast } from "sonner";
import { ArrowLeftIcon, PlusIcon, TrashIcon } from "lucide-react";

// Form schema
const formSchema = z.object({
  doctorId: z.string().min(1, "Doctor is required"),
  branchId: z.string().min(1, "Branch is required"),
  defaultDuration: z
    .number()
    .min(5, "Duration must be at least 5 minutes")
    .max(120, "Duration cannot exceed 120 minutes"),
  slots: z
    .array(
      z.object({
        dayOfWeek: z.number().min(0).max(6),
        startTime: z
          .string()
          .regex(
            /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
            "Invalid time format (HH:MM)",
          ),
        endTime: z
          .string()
          .regex(
            /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
            "Invalid time format (HH:MM)",
          ),
        duration: z
          .number()
          .min(5, "Duration must be at least 5 minutes")
          .max(120, "Duration cannot exceed 120 minutes")
          .optional(),
        isRecurring: z.boolean().default(true),
      }),
    )
    .optional(),
});

export default function NewDoctorSchedulePage() {
  const router = useRouter();
  const { doctors, isLoading: isLoadingDoctors } = useDoctors();
  const { currentBranch } = useCurrentBranch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Using sonner toast directly

  // Initialize form
  const form = useForm<any>({
    resolver: zodResolver(formSchema) as any,
    defaultValues: {
      doctorId: "",
      branchId: "", // Will be set when currentBranch is loaded
      defaultDuration: 15,
      slots: [],
    },
  });

  // Update branch ID when current branch changes
  useEffect(() => {
    if (currentBranch?.id) {
      form.setValue("branchId", currentBranch.id);
    }
  }, [currentBranch, form]);

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Validate all time slots for overlaps
    const slots = values.slots || [];

    if (slots.length === 0) {
      toast.error("Please add at least one time slot");
      return;
    }

    // Check for duplicate/overlapping slots
    for (let i = 0; i < slots.length; i++) {
      const slot = slots[i];

      // Validate that end time is after start time
      const startMinutes = convertTimeToMinutes(slot.startTime);
      const endMinutes = convertTimeToMinutes(slot.endTime);

      if (endMinutes <= startMinutes) {
        toast.error(
          `Invalid Time Range: In slot #${i + 1} (${getDayName(
            slot.dayOfWeek,
          )}), end time must be after start time`,
        );
        return;
      }

      // Check for overlaps with other slots
      for (let j = 0; j < slots.length; j++) {
        if (i === j) continue; // Skip comparing with itself

        const otherSlot = slots[j];
        if (slot.dayOfWeek !== otherSlot.dayOfWeek) continue; // Different days don't overlap

        const otherStartMinutes = convertTimeToMinutes(otherSlot.startTime);
        const otherEndMinutes = convertTimeToMinutes(otherSlot.endTime);

        // Check if slots overlap
        const hasOverlap =
          (startMinutes >= otherStartMinutes &&
            startMinutes < otherEndMinutes) || // Start time is within other slot
          (endMinutes > otherStartMinutes && endMinutes <= otherEndMinutes) || // End time is within other slot
          (startMinutes <= otherStartMinutes && endMinutes >= otherEndMinutes); // Slot completely contains other slot

        if (hasOverlap) {
          toast.error(
            `Overlapping Time Slots: Slot #${i + 1} (${getDayName(
              slot.dayOfWeek,
            )}, ${slot.startTime}-${slot.endTime}) overlaps with slot #${
              j + 1
            } (${getDayName(otherSlot.dayOfWeek)}, ${otherSlot.startTime}-${
              otherSlot.endTime
            })`,
          );
          return;
        }
      }
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/doctor-schedules", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Doctor schedule created successfully");
        router.push(`/doctor-schedules/${data.id}`);
      } else {
        toast.error(data.error || "Failed to create doctor schedule");
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to convert time (HH:MM) to minutes for easier comparison
  const convertTimeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  };

  // Add a new time slot
  const addTimeSlot = () => {
    // Get the default duration from the form
    const defaultDuration = form.getValues("defaultDuration") || 15;

    const newSlot = {
      dayOfWeek: 1, // Monday
      startTime: "09:00",
      endTime: "17:00",
      duration: defaultDuration, // Use the default duration
      isRecurring: true,
    };

    // Find a day that doesn't have a slot yet
    const currentSlots = form.getValues("slots") || [];
    const daysWithSlots = new Set(
      currentSlots.map((slot: any) => slot.dayOfWeek),
    );

    // Try to find an available day
    for (let day = 1; day <= 5; day++) {
      // Try Monday through Friday
      if (!daysWithSlots.has(day)) {
        newSlot.dayOfWeek = day;
        break;
      }
    }

    form.setValue("slots", [...currentSlots, newSlot]);
  };

  // Remove a time slot
  const removeTimeSlot = (index: number) => {
    const currentSlots = form.getValues("slots") || [];
    form.setValue(
      "slots",
      currentSlots.filter((_: any, i: number) => i !== index),
    );
  };

  // Get day name from day of week number
  const getDayName = (dayOfWeek: number) => {
    const days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    return days[dayOfWeek];
  };

  return (
    <div className="container mx-auto">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => router.back()}
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">Create Doctor Schedule</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Schedule Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="doctorId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Doctor</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isLoadingDoctors || isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a doctor" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingDoctors ? (
                            <SelectItem value="loading" disabled>
                              Loading...
                            </SelectItem>
                          ) : (
                            doctors?.map((doctor: any) => (
                              <SelectItem key={doctor.id} value={doctor.id}>
                                {doctor.user?.name || "Unknown"}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="branchId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch</FormLabel>
                      <div className="flex items-center space-x-2">
                        {currentBranch ? (
                          <>
                            <Input
                              value={currentBranch.name}
                              disabled
                              className="bg-muted"
                            />
                            <input
                              type="hidden"
                              {...field}
                              value={currentBranch.id}
                            />
                          </>
                        ) : (
                          <Input
                            value="Loading branch..."
                            disabled
                            className="bg-muted"
                          />
                        )}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="defaultDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Default Appointment Duration (minutes)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="5"
                        max="120"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value))
                        }
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Time Slots</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addTimeSlot}
                    disabled={isSubmitting}
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Slot
                  </Button>
                </div>

                {form.watch("slots")?.length === 0 ? (
                  <div className="text-center py-8 border rounded-md">
                    <p className="text-sm text-gray-500">
                      No time slots added yet. Click "Add Slot" to create a
                      schedule.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {form.watch("slots")?.map((_: any, index: number) => (
                      <div
                        key={index}
                        className="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 border rounded-md"
                      >
                        <FormField
                          control={form.control}
                          name={`slots.${index}.dayOfWeek`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Day</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  const newValue = parseInt(value);
                                  field.onChange(newValue);
                                }}
                                defaultValue={field.value.toString()}
                                disabled={isSubmitting}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select day" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                                    <SelectItem
                                      key={day}
                                      value={day.toString()}
                                    >
                                      {getDayName(day)}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`slots.${index}.startTime`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Start Time</FormLabel>
                              <FormControl>
                                <Input
                                  type="time"
                                  {...field}
                                  onChange={(e) => {
                                    field.onChange(e.target.value);
                                  }}
                                  disabled={isSubmitting}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`slots.${index}.endTime`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>End Time</FormLabel>
                              <FormControl>
                                <Input
                                  type="time"
                                  {...field}
                                  onChange={(e) => {
                                    field.onChange(e.target.value);
                                  }}
                                  disabled={isSubmitting}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`slots.${index}.duration`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Duration (minutes)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="5"
                                  max="120"
                                  placeholder="Use default"
                                  {...field}
                                  value={field.value || ""}
                                  onChange={(e) => {
                                    const value = e.target.value
                                      ? parseInt(e.target.value)
                                      : undefined;
                                    field.onChange(value);
                                  }}
                                  disabled={isSubmitting}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="flex items-end justify-between">
                          <FormField
                            control={form.control}
                            name={`slots.${index}.isRecurring`}
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  Recurring weekly
                                </FormLabel>
                              </FormItem>
                            )}
                          />

                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTimeSlot(index)}
                            disabled={isSubmitting}
                          >
                            <TrashIcon className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Schedule"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
