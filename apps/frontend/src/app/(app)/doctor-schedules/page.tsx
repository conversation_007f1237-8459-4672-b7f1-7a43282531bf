"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CalendarIcon, PlusIcon, Trash2Icon } from "lucide-react";
import { useCurrentBranch } from "@/hooks/use-current-branch";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDate } from "@/lib/utils";

export default function DoctorSchedulesPage() {
  const router = useRouter();
  const [doctorSchedules, setDoctorSchedules] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [scheduleToDelete, setScheduleToDelete] = useState<any>(null);
  const { currentBranch, isLoading: isLoadingBranch } = useCurrentBranch();

  // Fetch doctor schedules
  useEffect(() => {
    const fetchDoctorSchedules = async () => {
      setIsLoading(true);
      try {
        let url = "/api/doctor-schedules";
        const params = new URLSearchParams();

        // Filter by current branch if available
        if (currentBranch?.id) {
          params.append("branchId", currentBranch.id);
        }

        if (params.toString()) {
          url += `?${params.toString()}`;
        }

        const response = await fetch(url);
        const data = await response.json();

        if (response.ok) {
          setDoctorSchedules(data.doctorSchedules || []);
        } else {
        }
      } catch (error) {
      } finally {
        setIsLoading(false);
      }
    };

    fetchDoctorSchedules();
  }, [currentBranch]);

  // Navigate to create new schedule page
  const handleCreateSchedule = () => {
    router.push("/doctor-schedules/new");
  };

  // Navigate to schedule details page
  const handleViewSchedule = (id: string) => {
    router.push(`/doctor-schedules/${id}`);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (schedule: any) => {
    setScheduleToDelete(schedule);
    setDeleteDialogOpen(true);
  };

  // Delete the doctor schedule
  const handleDeleteConfirm = async () => {
    if (!scheduleToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(
        `/api/doctor-schedules/${scheduleToDelete.id}`,
        {
          method: "DELETE",
        },
      );

      if (response.ok) {
        // Remove the deleted schedule from the list
        setDoctorSchedules(
          doctorSchedules.filter((s) => s.id !== scheduleToDelete.id),
        );
        toast.success("Doctor schedule deleted successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete doctor schedule");
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setScheduleToDelete(null);
    }
  };

  // Get day name from day of week number
  const getDayName = (dayOfWeek: number) => {
    const days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    return days[dayOfWeek];
  };

  // Format time slots for display
  const formatTimeSlots = (slots: any[]) => {
    if (!slots || slots.length === 0) return "No slots";

    // Group slots by day of week
    const slotsByDay: Record<number, any[]> = {};
    slots.forEach((slot) => {
      if (!slotsByDay[slot.dayOfWeek]) {
        slotsByDay[slot.dayOfWeek] = [];
      }
      slotsByDay[slot.dayOfWeek].push(slot);
    });

    // Format slots for each day
    return Object.entries(slotsByDay)
      .map(([day, daySlots]) => {
        const dayName = getDayName(parseInt(day));
        const timeRanges = daySlots
          .map((slot) => `${slot.startTime} - ${slot.endTime}`)
          .join(", ");
        return `${dayName}: ${timeRanges}`;
      })
      .join("; ");
  };

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Doctor Schedules</h1>
          {currentBranch ? (
            <p className="text-gray-500 mt-1">{currentBranch.name}</p>
          ) : isLoadingBranch ? (
            <p className="text-gray-500 mt-1 animate-pulse">
              Loading branch...
            </p>
          ) : (
            <p className="text-gray-500 mt-1">No branch selected</p>
          )}
        </div>
        <Button onClick={handleCreateSchedule}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Create Schedule
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Doctor Schedules</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-2">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : doctorSchedules.length === 0 ? (
            <div className="text-center py-8">
              <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium">No schedules found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new doctor schedule.
              </p>
              <div className="mt-6">
                <Button onClick={handleCreateSchedule}>
                  <PlusIcon className="mr-2 h-4 w-4" />
                  Create Schedule
                </Button>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Doctor</TableHead>
                  <TableHead>Branch</TableHead>
                  <TableHead>Schedule</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {doctorSchedules.map((schedule) => (
                  <TableRow key={schedule.id}>
                    <TableCell className="font-medium">
                      {schedule.doctor?.user?.name || "Unknown"}
                    </TableCell>
                    <TableCell>{schedule.branch?.name || "Unknown"}</TableCell>
                    <TableCell className="max-w-md truncate">
                      {formatTimeSlots(schedule.slots)}
                    </TableCell>
                    <TableCell>{formatDate(schedule.createdAt)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewSchedule(schedule.id)}
                        >
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteClick(schedule)}
                        >
                          <Trash2Icon className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Doctor Schedule</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the schedule for{" "}
              <span className="font-semibold">
                {scheduleToDelete?.doctor?.user?.name || "this doctor"}
              </span>
              ? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
