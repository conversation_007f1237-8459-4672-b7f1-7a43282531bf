"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDate, formatTime } from "@/lib/utils";
import {
  ArrowLeftIcon,
  CalendarIcon,
  ClockIcon,
  PlusIcon,
  TrashIcon,
} from "lucide-react";

// Form schema for adding a new slot
const slotFormSchema = z.object({
  dayOfWeek: z.number().min(0).max(6),
  startTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"),
  endTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"),
  duration: z
    .number()
    .min(5, "Duration must be at least 5 minutes")
    .max(120, "Duration cannot exceed 120 minutes")
    .optional(),
  isRecurring: z.boolean().default(true),
});

// Form schema for adding an override
const overrideFormSchema = z.object({
  date: z.string().min(1, "Date is required"),
  startTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"),
  endTime: z
    .string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"),
  duration: z
    .number()
    .min(5, "Duration must be at least 5 minutes")
    .max(120, "Duration cannot exceed 120 minutes")
    .optional(),
  type: z.string().min(1, "Type is required"),
  reason: z.string().optional(),
});

export default function DoctorScheduleDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const [doctorSchedule, setDoctorSchedule] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAddSlotDialog, setShowAddSlotDialog] = useState(false);
  const [showAddOverrideDialog, setShowAddOverrideDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("slots");
  // Using sonner toast directly

  // Initialize forms
  const slotForm = useForm<any>({
    resolver: zodResolver(slotFormSchema) as any,
    defaultValues: {
      dayOfWeek: 1, // Monday
      startTime: "09:00",
      endTime: "17:00",
      duration: 15, // Default duration
      isRecurring: true,
    },
  });

  const overrideForm = useForm<any>({
    resolver: zodResolver(overrideFormSchema) as any,
    defaultValues: {
      date: new Date().toISOString().split("T")[0],
      startTime: "09:00",
      endTime: "17:00",
      duration: 15, // Default duration
      type: "add",
      reason: "",
    },
  });

  // Fetch doctor schedule
  useEffect(() => {
    const fetchDoctorSchedule = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/doctor-schedules/${params.id}`);
        const data = await response.json();

        if (response.ok) {
          setDoctorSchedule(data.doctorSchedule);
        } else {
          console.error("Error fetching doctor schedule:", data.error);
          toast.error(data.error || "Failed to fetch doctor schedule");
        }
      } catch (error) {
        console.error("Error fetching doctor schedule:", error);
        toast.error("An unexpected error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDoctorSchedule();
  }, [params.id]);

  // Helper function to convert time (HH:MM) to minutes for easier comparison
  const convertTimeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  };

  // Check if a new time slot overlaps with existing slots
  const checkTimeSlotOverlap = (
    dayOfWeek: number,
    startTime: string,
    endTime: string,
    excludeSlotId?: string,
  ): boolean => {
    if (!doctorSchedule?.slots) return false;

    const newStartMinutes = convertTimeToMinutes(startTime);
    const newEndMinutes = convertTimeToMinutes(endTime);

    // Validate that end time is after start time
    if (newEndMinutes <= newStartMinutes) {
      toast.error("Invalid Time Range: End time must be after start time");
      return true;
    }

    return doctorSchedule.slots.some((slot: any) => {
      // Skip the slot we're currently editing
      if (excludeSlotId && slot.id === excludeSlotId) return false;

      // Only check slots for the same day
      if (slot.dayOfWeek !== dayOfWeek) return false;

      const slotStartMinutes = convertTimeToMinutes(slot.startTime);
      const slotEndMinutes = convertTimeToMinutes(slot.endTime);

      // Check if the new slot overlaps with an existing slot
      const hasOverlap =
        (newStartMinutes >= slotStartMinutes &&
          newStartMinutes < slotEndMinutes) || // New start time is within existing slot
        (newEndMinutes > slotStartMinutes && newEndMinutes <= slotEndMinutes) || // New end time is within existing slot
        (newStartMinutes <= slotStartMinutes &&
          newEndMinutes >= slotEndMinutes); // New slot completely contains existing slot

      return hasOverlap;
    });
  };

  // Handle adding a new slot
  const handleAddSlot = async (values: z.infer<typeof slotFormSchema>) => {
    // First check for overlaps client-side
    if (
      checkTimeSlotOverlap(values.dayOfWeek, values.startTime, values.endTime)
    ) {
      toast.error(
        "Validation Error: Time slot overlaps with an existing slot for this day",
      );
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/schedule-slots", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          doctorScheduleId: params.id,
          ...values,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Time slot added successfully");
        // Update the doctor schedule with the new slot
        setDoctorSchedule({
          ...doctorSchedule,
          slots: [...doctorSchedule.slots, data],
        });
        setShowAddSlotDialog(false);
        slotForm.reset();
      } else {
        toast.error(data.error || "Failed to add time slot");
      }
    } catch (error) {
      console.error("Error adding time slot:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle adding an override
  const handleAddOverride = async (
    values: z.infer<typeof overrideFormSchema>,
  ) => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/schedule-overrides", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          doctorScheduleId: params.id,
          ...values,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Override added successfully");
        // Update the doctor schedule with the new override
        setDoctorSchedule({
          ...doctorSchedule,
          overrides: [...doctorSchedule.overrides, data],
        });
        setShowAddOverrideDialog(false);
        overrideForm.reset();
      } else {
        toast.error(data.error || "Failed to add override");
      }
    } catch (error) {
      console.error("Error adding override:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting a slot
  const handleDeleteSlot = async (slotId: string) => {
    if (!confirm("Are you sure you want to delete this time slot?")) {
      return;
    }

    try {
      const response = await fetch(`/api/schedule-slots/${slotId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Time slot deleted successfully");
        // Update the doctor schedule by removing the deleted slot
        setDoctorSchedule({
          ...doctorSchedule,
          slots: doctorSchedule.slots.filter((slot: any) => slot.id !== slotId),
        });
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete time slot");
      }
    } catch (error) {
      console.error("Error deleting time slot:", error);
      toast.error("An unexpected error occurred");
    }
  };

  // Handle deleting an override
  const handleDeleteOverride = async (overrideId: string) => {
    if (!confirm("Are you sure you want to delete this override?")) {
      return;
    }

    try {
      const response = await fetch(`/api/schedule-overrides/${overrideId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Override deleted successfully");
        // Update the doctor schedule by removing the deleted override
        setDoctorSchedule({
          ...doctorSchedule,
          overrides: doctorSchedule.overrides.filter(
            (override: any) => override.id !== overrideId,
          ),
        });
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete override");
      }
    } catch (error) {
      console.error("Error deleting override:", error);
      toast.error("An unexpected error occurred");
    }
  };

  // Get day name from day of week number
  const getDayName = (dayOfWeek: number) => {
    const days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    return days[dayOfWeek];
  };

  // Get override type display name
  const getOverrideTypeName = (type: string) => {
    const types: Record<string, string> = {
      add: "Additional Slot",
      remove: "Block/Remove",
      modify: "Modify Time",
    };
    return types[type] || type;
  };

  return (
    <div className="container mx-auto">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => router.push("/doctor-schedules")}
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Schedules
        </Button>
        <h1 className="text-3xl font-bold">Doctor Schedule</h1>
      </div>

      {isLoading ? (
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/3" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          </CardContent>
        </Card>
      ) : doctorSchedule ? (
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Schedule Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Doctor</h3>
                  <p className="mt-1 text-lg">
                    {doctorSchedule.doctor?.user?.name || "Unknown"}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Branch</h3>
                  <p className="mt-1 text-lg">
                    {doctorSchedule.branch?.name || "Unknown"}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Default Appointment Duration
                  </h3>
                  <p className="mt-1">
                    {doctorSchedule.defaultDuration || 15} minutes
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Created</h3>
                  <p className="mt-1">{formatDate(doctorSchedule.createdAt)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Last Updated
                  </h3>
                  <p className="mt-1">{formatDate(doctorSchedule.updatedAt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="slots">
                <ClockIcon className="h-4 w-4 mr-2" />
                Regular Slots
              </TabsTrigger>
              <TabsTrigger value="overrides">
                <CalendarIcon className="h-4 w-4 mr-2" />
                Overrides & Exceptions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="slots">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Regular Time Slots</CardTitle>
                  <Dialog
                    open={showAddSlotDialog}
                    onOpenChange={setShowAddSlotDialog}
                  >
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Slot
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add Time Slot</DialogTitle>
                        <DialogDescription>
                          Add a new regular time slot for this doctor at this
                          branch.
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...slotForm}>
                        <form
                          onSubmit={slotForm.handleSubmit(handleAddSlot)}
                          className="space-y-4"
                        >
                          <FormField
                            control={slotForm.control}
                            name="dayOfWeek"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Day of Week</FormLabel>
                                <Select
                                  onValueChange={(value) =>
                                    field.onChange(parseInt(value))
                                  }
                                  defaultValue={field.value.toString()}
                                  disabled={isSubmitting}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select day" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {[0, 1, 2, 3, 4, 5, 6].map((day) => (
                                      <SelectItem
                                        key={day}
                                        value={day.toString()}
                                      >
                                        {getDayName(day)}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={slotForm.control}
                              name="startTime"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Start Time</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="time"
                                      {...field}
                                      disabled={isSubmitting}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={slotForm.control}
                              name="endTime"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>End Time</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="time"
                                      {...field}
                                      disabled={isSubmitting}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={slotForm.control}
                            name="duration"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Duration (minutes)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="5"
                                    max="120"
                                    placeholder="Use default"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) => {
                                      const value = e.target.value
                                        ? parseInt(e.target.value)
                                        : undefined;
                                      field.onChange(value);
                                    }}
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={slotForm.control}
                            name="isRecurring"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  Recurring weekly
                                </FormLabel>
                              </FormItem>
                            )}
                          />

                          <DialogFooter>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowAddSlotDialog(false)}
                              disabled={isSubmitting}
                            >
                              Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                              {isSubmitting ? "Adding..." : "Add Slot"}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </CardHeader>
                <CardContent>
                  {doctorSchedule.slots?.length === 0 ? (
                    <div className="text-center py-8">
                      <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-lg font-medium">
                        No time slots
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Get started by adding a new time slot.
                      </p>
                      <div className="mt-6">
                        <Button onClick={() => setShowAddSlotDialog(true)}>
                          <PlusIcon className="mr-2 h-4 w-4" />
                          Add Slot
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Day</TableHead>
                          <TableHead>Start Time</TableHead>
                          <TableHead>End Time</TableHead>
                          <TableHead>Duration</TableHead>
                          <TableHead>Recurring</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {doctorSchedule.slots
                          ?.sort((a: any, b: any) => a.dayOfWeek - b.dayOfWeek)
                          .map((slot: any) => (
                            <TableRow key={slot.id}>
                              <TableCell className="font-medium">
                                {getDayName(slot.dayOfWeek)}
                              </TableCell>
                              <TableCell>
                                {formatTime(slot.startTime)}
                              </TableCell>
                              <TableCell>{formatTime(slot.endTime)}</TableCell>
                              <TableCell>
                                {slot.duration ||
                                  doctorSchedule.defaultDuration ||
                                  15}{" "}
                                min
                              </TableCell>
                              <TableCell>
                                {slot.isRecurring ? "Yes" : "No"}
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteSlot(slot.id)}
                                >
                                  <TrashIcon className="h-4 w-4 text-red-500" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="overrides">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Schedule Overrides</CardTitle>
                  <Dialog
                    open={showAddOverrideDialog}
                    onOpenChange={setShowAddOverrideDialog}
                  >
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Add Override
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add Schedule Override</DialogTitle>
                        <DialogDescription>
                          Create an exception or override for a specific date.
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...overrideForm}>
                        <form
                          onSubmit={overrideForm.handleSubmit(
                            handleAddOverride,
                          )}
                          className="space-y-4"
                        >
                          <FormField
                            control={overrideForm.control}
                            name="date"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Date</FormLabel>
                                <FormControl>
                                  <Input
                                    type="date"
                                    {...field}
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={overrideForm.control}
                            name="type"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Override Type</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                  disabled={isSubmitting}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="add">
                                      Additional Slot
                                    </SelectItem>
                                    <SelectItem value="remove">
                                      Block/Remove Slot
                                    </SelectItem>
                                    <SelectItem value="modify">
                                      Modify Time
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={overrideForm.control}
                              name="startTime"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Start Time</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="time"
                                      {...field}
                                      disabled={isSubmitting}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={overrideForm.control}
                              name="endTime"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>End Time</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="time"
                                      {...field}
                                      disabled={isSubmitting}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={overrideForm.control}
                            name="duration"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Duration (minutes)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="5"
                                    max="120"
                                    placeholder="Use default"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) => {
                                      const value = e.target.value
                                        ? parseInt(e.target.value)
                                        : undefined;
                                      field.onChange(value);
                                    }}
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={overrideForm.control}
                            name="reason"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Reason (Optional)</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g., Holiday, Meeting, etc."
                                    {...field}
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <DialogFooter>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowAddOverrideDialog(false)}
                              disabled={isSubmitting}
                            >
                              Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                              {isSubmitting ? "Adding..." : "Add Override"}
                            </Button>
                          </DialogFooter>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </CardHeader>
                <CardContent>
                  {doctorSchedule.overrides?.length === 0 ? (
                    <div className="text-center py-8">
                      <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-lg font-medium">No overrides</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Add overrides for specific dates like holidays or
                        special hours.
                      </p>
                      <div className="mt-6">
                        <Button onClick={() => setShowAddOverrideDialog(true)}>
                          <PlusIcon className="mr-2 h-4 w-4" />
                          Add Override
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Time</TableHead>
                          <TableHead>Duration</TableHead>
                          <TableHead>Reason</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {doctorSchedule.overrides
                          ?.sort(
                            (a: any, b: any) =>
                              new Date(a.date).getTime() -
                              new Date(b.date).getTime(),
                          )
                          .map((override: any) => (
                            <TableRow key={override.id}>
                              <TableCell className="font-medium">
                                {formatDate(override.date)}
                              </TableCell>
                              <TableCell>
                                {getOverrideTypeName(override.type)}
                              </TableCell>
                              <TableCell>
                                {formatTime(override.startTime)} -{" "}
                                {formatTime(override.endTime)}
                              </TableCell>
                              <TableCell>
                                {override.duration ||
                                  doctorSchedule.defaultDuration ||
                                  15}{" "}
                                min
                              </TableCell>
                              <TableCell>{override.reason || "-"}</TableCell>
                              <TableCell>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() =>
                                    handleDeleteOverride(override.id)
                                  }
                                >
                                  <TrashIcon className="h-4 w-4 text-red-500" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <h3 className="text-lg font-medium">Schedule not found</h3>
            <p className="mt-1 text-sm text-gray-500">
              The requested doctor schedule could not be found.
            </p>
            <div className="mt-6">
              <Button onClick={() => router.push("/doctor-schedules")}>
                Back to Schedules
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
