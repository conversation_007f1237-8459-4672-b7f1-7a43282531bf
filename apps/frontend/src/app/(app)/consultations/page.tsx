import { <PERSON>ada<PERSON> } from "next";
import { Dash<PERSON>Header } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/shell";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import Link from "next/link";
import { ConsultationsList } from "@/components/consultations/consultations-list";

export const metadata: Metadata = {
  title: "Consultations",
  description: "Manage patient consultations",
};

export default async function ConsultationsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Consultations"
        text="View and manage patient consultations."
      >
        <Link href="/consultations/new">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Consultation
          </Button>
        </Link>
      </DashboardHeader>
      <div className="grid gap-4">
        <ConsultationsList />
      </div>
    </DashboardShell>
  );
}
