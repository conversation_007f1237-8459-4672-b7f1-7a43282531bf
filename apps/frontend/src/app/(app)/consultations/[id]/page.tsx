import { <PERSON>ada<PERSON> } from "next";
import { DashboardHeader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/shell";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { ConsultationDetail } from "@/components/consultations/consultation-detail";

export const metadata: Metadata = {
  title: "Consultation Details",
  description: "View and manage consultation details",
};

interface ConsultationDetailPageProps {
  params: {
    id: string;
  };
}

export default async function ConsultationDetailPage({
  params,
}: ConsultationDetailPageProps) {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Consultation Details"
        text="View and manage consultation details."
      >
        <Link href="/consultations">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Consultations
          </Button>
        </Link>
      </DashboardHeader>
      <div className="grid gap-4">
        <ConsultationDetail id={params.id} />
      </div>
    </DashboardShell>
  );
}
