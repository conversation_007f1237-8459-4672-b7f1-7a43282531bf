"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { InvoiceForm } from "@/components/invoices/invoice-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import { toast } from "sonner";

export default function CreateInvoicePage() {
  const params = useParams();
  const router = useRouter();
  const [consultation, setConsultation] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const consultationId = params?.id as string;

  useEffect(() => {
    fetchConsultation();
  }, [consultationId]);

  const fetchConsultation = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/consultations/${consultationId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch consultation");
      }

      const data = await response.json();

      // if (data.consultation.status !== "completed") {
      //   toast.error("Invoice can only be created for completed consultations");
      //   router.push(`/consultations/${consultationId}`);
      //   return;
      // }

      // Check if invoice already exists
      const invoiceResponse = await fetch(
        `/api/invoices?consultationId=${consultationId}`,
      );
      if (invoiceResponse.ok) {
        const invoiceData = await invoiceResponse.json();
        if (invoiceData.invoices && invoiceData.invoices.length > 0) {
          toast.error("Invoice already exists for this consultation");
          router.push(`/consultations/${consultationId}`);
          return;
        }
      }

      setConsultation(data.consultation);
    } catch (error) {
      console.error("Error fetching consultation:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load consultation",
      );
      toast.error("Failed to load consultation details");
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = () => {
    toast.success("Invoice created successfully");
    router.push(`/consultations/${consultationId}`);
  };

  const handleCancel = () => {
    router.push(`/consultations/${consultationId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">
            Loading consultation details...
          </p>
        </div>
      </div>
    );
  }

  if (error || !consultation) {
    return (
      <div className="max-w-2xl mx-auto mt-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              {error || "Consultation not found"}
            </p>
            <Button
              onClick={() => router.push("/consultations")}
              variant="outline"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Consultations
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button onClick={handleCancel} variant="ghost" className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Consultation
        </Button>
      </div>

      <InvoiceForm
        consultation={consultation}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
