import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>Header } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/shell";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { NewConsultationForm } from "@/components/consultations/new-consultation-form";

export const metadata: Metadata = {
  title: "New Consultation",
  description: "Create a new patient consultation",
};

export default async function NewConsultationPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="New Consultation"
        text="Create a new patient consultation."
      >
        <Link href="/consultations">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Consultations
          </Button>
        </Link>
      </DashboardHeader>
      <div className="grid gap-4">
        <NewConsultationForm />
      </div>
    </DashboardShell>
  );
}
