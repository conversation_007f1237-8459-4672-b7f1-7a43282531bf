"use client";

import { useState } from "react";
import { useDoctorInvitation } from "@/contexts/doctor-invitation-context";
import { But<PERSON> } from "@/components/ui/button";
import { PlusIcon, Trash2, Refresh<PERSON><PERSON>, Co<PERSON>, CheckCircle } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

export default function DoctorInvitationsPage() {
  const { invitations, isLoading, refreshInvitations } = useDoctorInvitation();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState<any>(null);
  const [email, setEmail] = useState("");
  const [invitationLink, setInvitationLink] = useState("");
  const [showInvitationLink, setShowInvitationLink] = useState(false);
  const [copied, setCopied] = useState(false);

  // Handle delete click
  const handleDeleteClick = (invitation: any) => {
    setSelectedInvitation(invitation);
    setIsDeleteDialogOpen(true);
  };

  // Handle add invitation
  const handleAddInvitation = async () => {
    if (!email) {
      toast.error("Please enter an email address");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/doctor-invitations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("Invitation sent successfully");
        setInvitationLink(data.invitationLink);
        setShowInvitationLink(true);
        refreshInvitations();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to send invitation");
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      toast.error("An error occurred while sending the invitation");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete invitation
  const handleDeleteInvitation = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(
        `/api/doctor-invitations/${selectedInvitation.id}`,
        {
          method: "DELETE",
        },
      );

      if (response.ok) {
        toast.success("Invitation deleted successfully");
        setIsDeleteDialogOpen(false);
        refreshInvitations();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete invitation");
      }
    } catch (error) {
      console.error("Error deleting invitation:", error);
      toast.error("An error occurred while deleting the invitation");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle resend invitation
  const handleResendInvitation = async (invitation: any) => {
    try {
      const response = await fetch("/api/doctor-invitations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: invitation.email }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("Invitation resent successfully");
        setInvitationLink(data.invitationLink);
        setShowInvitationLink(true);
        refreshInvitations();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to resend invitation");
      }
    } catch (error) {
      console.error("Error resending invitation:", error);
      toast.error("An error occurred while resending the invitation");
    }
  };

  // Handle copy invitation link
  const handleCopyInvitationLink = () => {
    navigator.clipboard.writeText(invitationLink);
    setCopied(true);
    toast.success("Invitation link copied to clipboard");
    setTimeout(() => setCopied(false), 2000);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  // Reset form
  const resetForm = () => {
    setEmail("");
    setInvitationLink("");
    setShowInvitationLink(false);
    setCopied(false);
  };

  // Handle dialog close
  const handleDialogClose = () => {
    if (!isSubmitting) {
      resetForm();
      setIsAddDialogOpen(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Doctor Invitations</h1>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Send Invitation
        </Button>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Email</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Expires At</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  Loading...
                </TableCell>
              </TableRow>
            ) : invitations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  No invitations found
                </TableCell>
              </TableRow>
            ) : (
              invitations.map((invitation) => (
                <TableRow key={invitation.id}>
                  <TableCell className="font-medium">
                    {invitation.email}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        invitation.status === "pending"
                          ? "outline"
                          : invitation.status === "accepted"
                            ? "success"
                            : "destructive"
                      }
                    >
                      {invitation.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {invitation.user ? invitation.user.name : "—"}
                  </TableCell>
                  <TableCell>{formatDate(invitation.expiresAt)}</TableCell>
                  <TableCell>{formatDate(invitation.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    {invitation.status === "pending" && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleResendInvitation(invitation)}
                        title="Resend Invitation"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(invitation)}
                      title="Delete Invitation"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Add Invitation Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Send Doctor Invitation</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            {showInvitationLink && (
              <div className="space-y-2 mt-4">
                <Label htmlFor="invitationLink">Invitation Link</Label>
                <div className="flex">
                  <Input
                    id="invitationLink"
                    value={invitationLink}
                    readOnly
                    className="flex-1 pr-10"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="ml-2"
                    onClick={handleCopyInvitationLink}
                  >
                    {copied ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Share this link with the doctor to complete their
                  registration. The link will expire in 24 hours.
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            {showInvitationLink ? (
              <Button onClick={handleDialogClose}>Done</Button>
            ) : (
              <>
                <DialogClose asChild>
                  <Button variant="outline" disabled={isSubmitting}>
                    Cancel
                  </Button>
                </DialogClose>
                <Button onClick={handleAddInvitation} disabled={isSubmitting}>
                  {isSubmitting ? "Sending..." : "Send Invitation"}
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Invitation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Invitation</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete the invitation for "
              {selectedInvitation?.email}"? This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteInvitation}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete Invitation"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
