import { notFound } from "next/navigation";
import { getConsentNotificationById } from "./_lib/queries";
import { NotificationHeader } from "./_components/notification-header";
import { NotificationDetails } from "./_components/notification-details";
import { PatientInfo } from "./_components/patient-info";
import { JsonView } from "./_components/json-view";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";

interface ConsentNotificationDetailPageProps {
  params: {
    id: string;
  };
}

export default async function ConsentNotificationDetailPage({
  params,
}: ConsentNotificationDetailPageProps) {
  if (!params.id) {
    return notFound();
  }

  try {
    const notification = await getConsentNotificationById(params.id);

    if (!notification) {
      return notFound();
    }

    return (
      <div className="space-y-6">
        <NotificationHeader notification={notification} />

        <Tabs defaultValue="formatted" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="formatted">Formatted View</TabsTrigger>
            <TabsTrigger value="json">JSON View</TabsTrigger>
          </TabsList>

          <TabsContent value="formatted">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="md:col-span-2">
                <NotificationDetails notification={notification} />
              </div>

              <div>
                <PatientInfo notification={notification} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="json">
            <JsonView notification={notification} />
          </TabsContent>
        </Tabs>
      </div>
    );
  } catch (error) {
    console.error("Error fetching consent notification:", error);
    return notFound();
  }
}
