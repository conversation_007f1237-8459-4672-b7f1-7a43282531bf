"use client";

import { Badge } from "@workspace/ui/components/badge";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { formatDate } from "@/lib/utils";
import type { ConsentNotification } from "@/app/(app)/abdm-consent-notify/(list)/_lib/queries";

interface NotificationDetailsProps {
  notification: ConsentNotification;
}

export function NotificationDetails({
  notification,
}: NotificationDetailsProps) {
  // Format permission date range
  const formatDateRange = () => {
    const permission = notification.permission;
    if (!permission || !permission.dateRange) return "Not specified";

    const from = permission.dateRange.from
      ? formatDate(permission.dateRange.from)
      : "Not specified";
    const to = permission.dateRange.to
      ? formatDate(permission.dateRange.to)
      : "Not specified";

    return `${from} to ${to}`;
  };

  // Format data erase date
  const formatEraseDate = () => {
    const permission = notification.permission;
    if (!permission || !permission.dataEraseAt) return "Not specified";

    return formatDate(permission.dataEraseAt);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Consent Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">Purpose</h3>
          <p>{notification.purpose?.text || "Not specified"}</p>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Health Information Types
          </h3>
          <div className="flex flex-wrap gap-2">
            {notification.hiTypes && notification.hiTypes.length > 0 ? (
              notification.hiTypes.map((type, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="bg-blue-50 text-blue-700"
                >
                  {type}
                </Badge>
              ))
            ) : (
              <p>No health information types specified</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Permission
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-xs text-muted-foreground">Access Mode</p>
              <p>{notification.permission?.accessMode || "Not specified"}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Date Range</p>
              <p>{formatDateRange()}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Data Erase At</p>
              <p>{formatEraseDate()}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Frequency</p>
              <p>
                {notification.permission?.frequency
                  ? `${notification.permission.frequency.value} ${notification.permission.frequency.unit}(s), Repeats: ${notification.permission.frequency.repeats}`
                  : "Not specified"}
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Care Contexts
          </h3>
          {notification.careContexts &&
          Array.isArray(notification.careContexts) &&
          notification.careContexts.length > 0 ? (
            <div className="space-y-2">
              {notification.careContexts.map((context: any, index: number) => (
                <div key={index} className="rounded-md border p-2">
                  <p className="text-xs text-muted-foreground">
                    Patient Reference
                  </p>
                  <p>{context.patientReference || "Not specified"}</p>
                  <p className="mt-1 text-xs text-muted-foreground">
                    Care Context Reference
                  </p>
                  <p>{context.careContextReference || "Not specified"}</p>
                </div>
              ))}
            </div>
          ) : (
            <p>No care contexts specified</p>
          )}
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Additional Information
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-xs text-muted-foreground">Schema Version</p>
              <p>{notification.schemaVersion || "Not specified"}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">HIP ID</p>
              <p>{notification.hipId || "Not specified"}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">
                Consent Manager ID
              </p>
              <p>{notification.consentManagerId || "Not specified"}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">
                Grant Acknowledgement
              </p>
              <p>{notification.grantAcknowledgement ? "Yes" : "No"}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
