"use server";

import { cookies } from "next/headers";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";

export async function getConsentNotificationById(id: string) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Find the consent notification
  const notification = await prisma.consentNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("Consent notification not found");
  }

  // For now, we're not checking organization ID as it's not stored in the ConsentNotify model
  // In a production environment, you would want to implement proper tenant isolation

  // Convert the Prisma model to the expected interface
  return {
    ...notification,
    organizationId: organizationId, // Use the organizationId from the cookie
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  };
}
