"use client";

import { ActivityLog } from "@/types/activity-log";
import { Badge } from "@workspace/ui/components/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@workspace/ui/components/avatar";
import { CalendarIcon, ClockIcon } from "lucide-react";
import { getInitials } from "@/lib/utils";

interface ActivityHeaderProps {
  activity: ActivityLog;
}

// Helper function to format status
function formatStatus(status: string): string {
  return status
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

// Helper function to get activity type color
function getTypeColor(type: string): string {
  const typeMap: Record<string, string> = {
    NOTE: "bg-blue-50 text-blue-700 hover:bg-blue-100",
    EMAIL: "bg-indigo-50 text-indigo-700 hover:bg-indigo-100",
    CALL: "bg-green-50 text-green-700 hover:bg-green-100",
    MEETING: "bg-purple-50 text-purple-700 hover:bg-purple-100",
    TASK: "bg-yellow-50 text-yellow-700 hover:bg-yellow-100",
    OTHER: "bg-gray-50 text-gray-700 hover:bg-gray-100",
  };

  return typeMap[type] || "bg-gray-50 text-gray-700 hover:bg-gray-100";
}

// Helper function to format date
function formatDate(dateString: string): string {
  if (!dateString) return "—";

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) return "Invalid date";

  // Format the date as "MMM DD, YYYY"
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  });
}

// Helper function to format time
function formatTime(dateString: string): string {
  if (!dateString) return "—";

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) return "Invalid time";

  // Format the time as "HH:MM AM/PM"
  return date.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
}

export function ActivityHeader({ activity }: ActivityHeaderProps) {
  return (
    <div className="rounded-lg border bg-card p-6 shadow-sm">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">{activity.title}</h2>
          <Badge variant="outline" className={getTypeColor(activity.type)}>
            {formatStatus(activity.type)}
          </Badge>
        </div>

        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <CalendarIcon className="h-4 w-4" />
            <span>{formatDate(activity.createdAt)}</span>
          </div>
          <div className="flex items-center space-x-1">
            <ClockIcon className="h-4 w-4" />
            <span>{formatTime(activity.createdAt)}</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={activity.user?.image || ""}
              alt={activity.user?.name || "User"}
            />
            <AvatarFallback>
              {getInitials(activity.user?.name || "User")}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-sm font-medium">
              {activity.user?.name || "Unknown user"}
            </p>
            <p className="text-xs text-muted-foreground">
              {activity.user?.email || ""}
            </p>
          </div>
        </div>

        {activity.description && (
          <div className="mt-4">
            <p className="whitespace-pre-wrap text-sm">
              {activity.description}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
