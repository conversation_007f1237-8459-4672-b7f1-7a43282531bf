"use client";

import { ActivityLog } from "@/types/activity-log";
import { Badge } from "@workspace/ui/components/badge";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { ExternalLink } from "lucide-react";
import Link from "next/link";

interface RelatedEntityProps {
  activity: ActivityLog;
}

// Helper function to get entity type color
function getEntityTypeColor(entityType: string): string {
  const entityTypeMap: Record<string, string> = {
    Lead: "bg-blue-50 text-blue-700 hover:bg-blue-100",
    Contact: "bg-green-50 text-green-700 hover:bg-green-100",
    Account: "bg-purple-50 text-purple-700 hover:bg-purple-100",
    Opportunity: "bg-yellow-50 text-yellow-700 hover:bg-yellow-100",
  };

  return (
    entityTypeMap[entityType] || "bg-gray-50 text-gray-700 hover:bg-gray-100"
  );
}

export function RelatedEntity({ activity }: RelatedEntityProps) {
  // Determine which entity this activity is related to
  const entityType = activity.entityType || "Entity";
  let entityData = null;
  let entityLink = "/activity-log"; // Default fallback link

  if (activity.lead && entityType === "Lead" && activity.lead.id) {
    entityData = {
      name: activity.lead.name || "Unknown Lead",
      email: activity.lead.email,
      company: activity.lead.company,
    };
    entityLink = `/leads/${activity.lead.id}/view`;
  } else if (
    activity.contact &&
    entityType === "Contact" &&
    activity.contact.id
  ) {
    entityData = {
      name: activity.contact.name || "Unknown Contact",
      email: activity.contact.email,
      company: activity.contact.company,
    };
    entityLink = `/contacts/${activity.contact.id}/view`;
  } else if (
    activity.account &&
    entityType === "Account" &&
    activity.account.id
  ) {
    entityData = {
      name: activity.account.name || "Unknown Account",
      industry: activity.account.industry,
      website: activity.account.website,
    };
    entityLink = `/accounts/${activity.account.id}/view`;
  } else if (
    activity.opportunity &&
    entityType === "Opportunity" &&
    activity.opportunity.id
  ) {
    entityData = {
      name: activity.opportunity.name || "Unknown Opportunity",
      value: activity.opportunity.value,
      stage: activity.opportunity.stage,
    };
    entityLink = `/opportunities/${activity.opportunity.id}/view`;
  }

  if (!entityData) {
    return null;
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Related {entityType}</CardTitle>
          <Badge variant="outline" className={getEntityTypeColor(entityType)}>
            {entityType}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium">{entityData.name}</h3>
            {entityData.email && (
              <p className="text-sm text-muted-foreground">
                {entityData.email}
              </p>
            )}
            {entityData.company && (
              <p className="text-sm text-muted-foreground">
                {entityData.company}
              </p>
            )}
            {entityData.industry && (
              <p className="text-sm text-muted-foreground">
                Industry: {entityData.industry}
              </p>
            )}
            {entityData.website && (
              <p className="text-sm text-muted-foreground">
                {entityData.website}
              </p>
            )}
            {entityData.value && (
              <p className="text-sm text-muted-foreground">
                Value: ${entityData.value.toLocaleString()}
              </p>
            )}
            {entityData.stage && (
              <p className="text-sm text-muted-foreground">
                Stage:{" "}
                {entityData.stage
                  .replace(/_/g, " ")
                  .toLowerCase()
                  .replace(/\b\w/g, (l) => l.toUpperCase())}
              </p>
            )}
          </div>

          {entityLink && (
            <Button variant="outline" size="sm" asChild>
              <Link href={entityLink}>
                <ExternalLink className="mr-2 h-4 w-4" />
                View {entityType}
              </Link>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
