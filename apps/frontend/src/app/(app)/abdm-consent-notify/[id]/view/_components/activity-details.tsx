"use client";

import { ActivityLog } from "@/types/activity-log";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";

interface ActivityDetailsProps {
  activity: ActivityLog;
}

// Helper function to format date
function formatDate(dateString: string): string {
  if (!dateString) return "—";

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) return "Invalid date";

  // Format the date as "MMM DD, YYYY HH:MM AM/PM"
  return date.toLocaleString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
}

export function ActivityDetails({ activity }: ActivityDetailsProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">Activity Details</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <p className="text-sm font-medium text-muted-foreground">
              Activity ID
            </p>
            <p className="text-sm">{activity.id}</p>
          </div>

          <div>
            <p className="text-sm font-medium text-muted-foreground">
              Created By
            </p>
            <p className="text-sm">{activity.createdBy?.name || "Unknown"}</p>
          </div>

          <div>
            <p className="text-sm font-medium text-muted-foreground">
              Created At
            </p>
            <p className="text-sm">{formatDate(activity.createdAt)}</p>
          </div>

          <div>
            <p className="text-sm font-medium text-muted-foreground">
              Last Updated
            </p>
            <p className="text-sm">{formatDate(activity.updatedAt)}</p>
          </div>

          {activity.updatedBy && (
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Updated By
              </p>
              <p className="text-sm">{activity.updatedBy.name}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
