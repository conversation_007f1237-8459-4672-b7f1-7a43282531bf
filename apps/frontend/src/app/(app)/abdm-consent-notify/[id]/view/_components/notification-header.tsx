"use client";

import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent } from "@workspace/ui/components/card";
import { formatDate } from "@/lib/utils";
import type { ConsentNotification } from "@/app/(app)/abdm-consent-notify/(list)/_lib/queries";

interface NotificationHeaderProps {
  notification: ConsentNotification;
}

// Helper function to format status
function formatStatus(status: string): string {
  return status
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

// Helper function to get status color
function getStatusColor(status: string): string {
  const statusMap: Record<string, string> = {
    GRANTED: "bg-green-50 text-green-700 hover:bg-green-100",
    REQUESTED: "bg-blue-50 text-blue-700 hover:bg-blue-100",
    DENIED: "bg-red-50 text-red-700 hover:bg-red-100",
    REVOKED: "bg-yellow-50 text-yellow-700 hover:bg-yellow-100",
    EXPIRED: "bg-gray-50 text-gray-700 hover:bg-gray-100",
  };

  return statusMap[status] || "bg-gray-50 text-gray-700 hover:bg-gray-100";
}

export function NotificationHeader({ notification }: NotificationHeaderProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-2xl font-bold">Consent Notification</h2>
            <p className="text-sm text-muted-foreground">
              Consent ID: {notification.consentId}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className={getStatusColor(notification.status)}
            >
              {formatStatus(notification.status)}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {formatDate(notification.createdAt)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
