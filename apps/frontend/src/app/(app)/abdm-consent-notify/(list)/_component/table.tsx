"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import type { ConsentNotification } from "../_lib/queries";
import { DataTable } from "@workspace/data-table/component/data-table";
import { useDataTable } from "@workspace/data-table/hooks/use-data-table";
import { DataTableToolbar } from "@workspace/data-table/component/data-table-toolbar";
import { DataTableSortList } from "@workspace/data-table/component/data-table-sort-list";
import { getConsentNotificationsTableColumns } from "./columns";

interface ConsentNotificationsTableProps {
  initialData: {
    data: ConsentNotification[];
    pageCount: number;
  };
}

export function ConsentNotificationsTable({
  initialData,
}: ConsentNotificationsTableProps) {
  const router = useRouter();
  const { data, pageCount } = initialData;

  const columns = React.useMemo(
    () => getConsentNotificationsTableColumns() as any,
    [],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <DataTable
      table={table}
      onRowClick={(row) => {
        router.push(`/abdm-consent-notify/${row.id}/view`);
      }}
      doctype="consentNotify"
    >
      <DataTableToolbar table={table}>
        <DataTableSortList table={table} align="end" />
      </DataTableToolbar>
    </DataTable>
  );
}
