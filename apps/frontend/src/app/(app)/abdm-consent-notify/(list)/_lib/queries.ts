"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";
import type { GetConsentNotificationsSchemaType } from "./validations";

export interface ConsentNotification {
  id: string;
  requestId: string;
  status: string;
  consentId: string;
  schemaVersion: string;
  patientId: string;
  organizationId?: string;
  careContexts: any;
  purpose: any;
  hipId: string;
  consentManagerId: string;
  hiTypes: string[];
  permission: any;
  signature: string;
  grantAcknowledgement: boolean;
  createdAt: string;
  updatedAt: string;
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export async function getConsentNotifications(
  input: GetConsentNotificationsSchemaType,
) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  const { page = 1, perPage = 10, patientId, status, search } = input;

  console.log("Search query:", search);

  // Calculate pagination
  const skip = (page - 1) * perPage;

  // Build where clause
  const where: any = {};

  // Add filters if provided
  if (patientId) {
    where.patientId = patientId;
  }

  if (status) {
    where.status = status;
  }

  // Add search filter if provided
  if (search) {
    // Log the search term for debugging
    console.log("Searching for:", search);

    // Use a direct SQL query for more flexibility in searching
    // This allows us to search for partial matches in any field
    where.OR = [
      {
        consentId: {
          contains: search,
          mode: "insensitive",
        },
      },
      {
        requestId: {
          contains: search,
          mode: "insensitive",
        },
      },
      {
        status: {
          contains: search,
          mode: "insensitive",
        },
      },
      {
        patientId: {
          contains: search,
          mode: "insensitive",
        },
      },
    ];

    // Log the constructed where clause
    console.log("Search where clause:", JSON.stringify(where, null, 2));
  }

  // Get total count
  const total = await prisma.consentNotify.count({
    where,
  });

  // Get consent notifications with pagination
  const notifications = await prisma.consentNotify.findMany({
    where,
    orderBy: {
      createdAt: "desc",
    },
    skip,
    take: perPage,
  });

  // Log the first notification to see its structure
  if (notifications.length > 0) {
    console.log(
      "First notification sample:",
      JSON.stringify(notifications[0], null, 2),
    );
  } else {
    console.log("No notifications found with the current filters");
  }

  // Calculate total pages
  const totalPages = Math.ceil(total / perPage);

  // Format the data to match the expected interface
  const formattedNotifications = notifications.map((notification) => ({
    ...notification,
    organizationId, // Add organizationId from cookie
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  }));

  // Log the total count and page info
  console.log(
    `Found ${total} notifications, showing page ${page} of ${totalPages}`,
  );

  return {
    data: formattedNotifications,
    pageCount: totalPages,
  };
}

export async function getConsentNotificationById(id: string) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Find the consent notification
  const notification = await prisma.consentNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("Consent notification not found");
  }

  // Format the data to match the expected interface
  return {
    ...notification,
    organizationId, // Add organizationId from cookie
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  };
}
