"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useDepartment } from "@/contexts/department-context";
import { useBranch } from "@/contexts/branch-context";
import { useDoctor } from "@/contexts/doctor-context";
import { useDoctorInvitation } from "@/contexts/doctor-invitation-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function CreateDoctorPage() {
  const router = useRouter();
  const { departments, isLoading: isDepartmentsLoading } = useDepartment();
  const { branches, isLoading: isBranchesLoading } = useBranch();
  const { refreshDoctors } = useDoctor();
  const { refreshInvitations } = useDoctorInvitation();

  // We don't need to manually refresh departments and branches
  // They are already being loaded by their respective context providers
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    profileDescription: "",
    specializations: "",
    qualifications: "",
    contactEmail: "",
    contactPhone: "",
    joiningDate: "",
    yearsOfExperience: 0,
    status: "active",
    departmentId: "",
    branchIds: [] as string[],
  });

  // Handle input change
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle branch selection
  const handleBranchSelection = (branchId: string) => {
    setFormData((prev) => {
      const branchIds = [...prev.branchIds];
      const index = branchIds.indexOf(branchId);

      if (index === -1) {
        branchIds.push(branchId);
      } else {
        branchIds.splice(index, 1);
      }

      return { ...prev, branchIds };
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.name || !formData.email || !formData.departmentId) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.branchIds.length === 0) {
      toast.error("Please select at least one branch");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/doctors", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Doctor added successfully");

        // Refresh the doctors list and invitations before redirecting
        try {
          console.log("Refreshing doctors and invitations...");
          await refreshDoctors();
          await refreshInvitations();
          console.log("Refresh completed");
        } catch (refreshError) {
          console.error("Error refreshing data:", refreshError);
        }

        // Force a hard navigation to the doctors page to ensure a full refresh
        window.location.href = "/doctors";
      } else {
        console.error("Error response:", data);

        if (data.details) {
          toast.error(
            `${data.error}\nDetails: ${JSON.stringify(data.details)}`,
          );
        } else {
          toast.error(data.error || data.message || "Failed to add doctor");
        }
      }
    } catch (error) {
      console.error("Error adding doctor:", error);
      toast.error("An error occurred while adding the doctor");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if data is loading
  const isLoading = isDepartmentsLoading || isBranchesLoading;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/doctors")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Add New Doctor</h1>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4">Loading...</p>
          </div>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Doctor Information</CardTitle>
            <CardDescription>
              Add a new doctor to your organization. If the doctor already has
              an account, they will be linked automatically.
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="profileDescription">Profile Description</Label>
                <Textarea
                  id="profileDescription"
                  name="profileDescription"
                  value={formData.profileDescription}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="specializations">Specializations</Label>
                  <Input
                    id="specializations"
                    name="specializations"
                    placeholder="Comma-separated list"
                    value={formData.specializations}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="qualifications">Qualifications</Label>
                  <Input
                    id="qualifications"
                    name="qualifications"
                    placeholder="Comma-separated list"
                    value={formData.qualifications}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input
                    id="contactEmail"
                    name="contactEmail"
                    type="email"
                    value={formData.contactEmail}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">Contact Phone</Label>
                  <Input
                    id="contactPhone"
                    name="contactPhone"
                    value={formData.contactPhone}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="joiningDate">Joining Date</Label>
                  <Input
                    id="joiningDate"
                    name="joiningDate"
                    type="date"
                    value={formData.joiningDate}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="yearsOfExperience">Years of Experience</Label>
                  <Input
                    id="yearsOfExperience"
                    name="yearsOfExperience"
                    type="number"
                    value={formData.yearsOfExperience.toString()}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="departmentId">Department *</Label>
                  <Select
                    value={formData.departmentId}
                    onValueChange={(value) =>
                      handleSelectChange("departmentId", value)
                    }
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((department) => (
                        <SelectItem key={department.id} value={department.id}>
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) =>
                      handleSelectChange("status", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Branches *</Label>
                <div className="grid grid-cols-3 gap-2">
                  {branches.map((branch) => (
                    <div
                      key={branch.id}
                      className="flex items-center space-x-2"
                    >
                      <input
                        type="checkbox"
                        id={`branch-${branch.id}`}
                        checked={formData.branchIds.includes(branch.id)}
                        onChange={() => handleBranchSelection(branch.id)}
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <Label htmlFor={`branch-${branch.id}`}>
                        {branch.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/doctors")}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Adding..." : "Add Doctor"}
              </Button>
            </CardFooter>
          </form>
        </Card>
      )}
    </div>
  );
}
