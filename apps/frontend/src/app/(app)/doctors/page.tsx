"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useLazyDepartment } from "@/contexts/lazy-context-hooks";
import { useBranch } from "@/contexts/branch-context";
import { useDoctor } from "@/contexts/doctor-context";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusIcon, Pencil, Trash2, FileText } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

export default function DoctorsPage() {
  const router = useRouter();
  const { doctors, isLoading, refreshDoctors } = useDoctor();
  const { departments } = useLazyDepartment();
  const { branches } = useBranch();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<any>(null);
  const [formData, setFormData] = useState({
    profileDescription: "",
    specializations: "",
    qualifications: "",
    contactEmail: "",
    contactPhone: "",
    joiningDate: "",
    yearsOfExperience: 0,
    status: "active",
    departmentId: "",
    branchIds: [] as string[],
  });

  // Handle input change
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle branch selection
  const handleBranchSelection = (branchId: string) => {
    setFormData((prev) => {
      const branchIds = [...prev.branchIds];
      const index = branchIds.indexOf(branchId);

      if (index === -1) {
        branchIds.push(branchId);
      } else {
        branchIds.splice(index, 1);
      }

      return { ...prev, branchIds };
    });
  };

  // Handle edit click
  const handleEditClick = (doctor: any) => {
    setSelectedDoctor(doctor);
    setFormData({
      profileDescription: doctor.profileDescription || "",
      specializations: doctor.specializations || "",
      qualifications: doctor.qualifications || "",
      contactEmail: doctor.contactEmail || "",
      contactPhone: doctor.contactPhone || "",
      joiningDate: doctor.joiningDate
        ? new Date(doctor.joiningDate).toISOString().split("T")[0]
        : "",
      yearsOfExperience: doctor.yearsOfExperience || 0,
      status: doctor.status || "active",
      departmentId: doctor.departmentId || "",
      branchIds: doctor.branches.map((b: any) => b.branchId) || [],
    });
    setIsEditDialogOpen(true);
  };

  // Handle delete click
  const handleDeleteClick = (doctor: any) => {
    setSelectedDoctor(doctor);
    setIsDeleteDialogOpen(true);
  };

  // Handle update doctor
  const handleUpdateDoctor = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/doctors/${selectedDoctor.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Doctor updated successfully");
        setIsEditDialogOpen(false);
        refreshDoctors();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to update doctor");
      }
    } catch (error) {
      console.error("Error updating doctor:", error);
      toast.error("An error occurred while updating the doctor");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete doctor
  const handleDeleteDoctor = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/doctors/${selectedDoctor.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Doctor deleted successfully");
        setIsDeleteDialogOpen(false);
        refreshDoctors();
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete doctor");
      }
    } catch (error) {
      console.error("Error deleting doctor:", error);
      toast.error("An error occurred while deleting the doctor");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Doctors</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push("/doctor-invitations")}
          >
            View Invitations
          </Button>
          <Button onClick={() => router.push("/doctors/create")}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Add Doctor
          </Button>
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Specializations</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Branches</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  Loading...
                </TableCell>
              </TableRow>
            ) : doctors.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  No doctors found
                </TableCell>
              </TableRow>
            ) : (
              doctors.map((doctor) => (
                <TableRow key={doctor.id}>
                  <TableCell className="font-medium">
                    {doctor.user.name}
                  </TableCell>
                  <TableCell>{doctor.user.email}</TableCell>
                  <TableCell>
                    {doctor.specializations ? (
                      <div className="flex flex-wrap gap-1">
                        {doctor.specializations
                          .split(",")
                          .map((spec, index) => (
                            <Badge key={index} variant="outline">
                              {spec.trim()}
                            </Badge>
                          ))}
                      </div>
                    ) : (
                      "—"
                    )}
                  </TableCell>
                  <TableCell>
                    {doctor.department ? doctor.department.name : "—"}
                  </TableCell>
                  <TableCell>
                    {doctor.branches.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {doctor.branches.map((branch) => (
                          <Badge key={branch.id} variant="outline">
                            {branch.branch.name}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      "—"
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        doctor.status === "active" ? "default" : "destructive"
                      }
                      className={
                        doctor.status === "active"
                          ? "bg-green-100 text-green-800"
                          : ""
                      }
                    >
                      {doctor.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditClick(doctor)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(doctor)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" asChild>
                      <a href={`/doctors/${doctor.id}/documents`}>
                        <FileText className="h-4 w-4" />
                      </a>
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Doctor Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Doctor</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-profileDescription">
                Profile Description
              </Label>
              <Textarea
                id="edit-profileDescription"
                name="profileDescription"
                value={formData.profileDescription}
                onChange={handleInputChange}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-specializations">Specializations</Label>
                <Input
                  id="edit-specializations"
                  name="specializations"
                  placeholder="Comma-separated list"
                  value={formData.specializations}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-qualifications">Qualifications</Label>
                <Input
                  id="edit-qualifications"
                  name="qualifications"
                  placeholder="Comma-separated list"
                  value={formData.qualifications}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-contactEmail">Contact Email</Label>
                <Input
                  id="edit-contactEmail"
                  name="contactEmail"
                  type="email"
                  value={formData.contactEmail}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-contactPhone">Contact Phone</Label>
                <Input
                  id="edit-contactPhone"
                  name="contactPhone"
                  value={formData.contactPhone}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-joiningDate">Joining Date</Label>
                <Input
                  id="edit-joiningDate"
                  name="joiningDate"
                  type="date"
                  value={formData.joiningDate}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-yearsOfExperience">
                  Years of Experience
                </Label>
                <Input
                  id="edit-yearsOfExperience"
                  name="yearsOfExperience"
                  type="number"
                  value={formData.yearsOfExperience.toString()}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-departmentId">Department</Label>
                <Select
                  value={formData.departmentId}
                  onValueChange={(value) =>
                    handleSelectChange("departmentId", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((department) => (
                      <SelectItem key={department.id} value={department.id}>
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleSelectChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Branches</Label>
              <div className="grid grid-cols-3 gap-2">
                {branches.map((branch) => (
                  <div key={branch.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`edit-branch-${branch.id}`}
                      checked={formData.branchIds.includes(branch.id)}
                      onChange={() => handleBranchSelection(branch.id)}
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <Label htmlFor={`edit-branch-${branch.id}`}>
                      {branch.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button onClick={handleUpdateDoctor} disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Doctor"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Doctor Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Doctor</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete the doctor profile for "
              {selectedDoctor?.user?.name}"? This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteDoctor}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete Doctor"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
