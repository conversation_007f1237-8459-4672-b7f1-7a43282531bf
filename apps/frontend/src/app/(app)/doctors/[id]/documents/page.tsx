"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  PlusIcon,
  Pencil,
  Trash2,
  ArrowLeft,
  FileIcon,
  ImageIcon,
  FileTextIcon,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

interface Document {
  id: string;
  name: string;
  fileUrl: string;
  fileType: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

interface Doctor {
  id: string;
  user: {
    name: string;
    email: string;
  };
}

export default function DoctorDocumentsPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const doctorId = params.id;

  const [doctor, setDoctor] = useState<Doctor | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [formData, setFormData] = useState({
    name: "",
    fileUrl: "",
    fileType: "",
    description: "",
  });

  // Fetch doctor and documents
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch doctor
        const doctorResponse = await fetch(`/api/doctors/${doctorId}`);
        if (doctorResponse.ok) {
          const data = await doctorResponse.json();
          setDoctor(data.doctor);
        } else {
          toast.error("Failed to fetch doctor details");
          router.push("/doctors");
          return;
        }

        // Fetch documents
        const documentsResponse = await fetch(
          `/api/doctors/${doctorId}/documents`,
        );
        if (documentsResponse.ok) {
          const data = await documentsResponse.json();
          setDocuments(data.documents || []);
        } else {
          toast.error("Failed to fetch documents");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("An error occurred while fetching data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [doctorId, router]);

  // Reset form data
  const resetForm = () => {
    setFormData({
      name: "",
      fileUrl: "",
      fileType: "",
      description: "",
    });
  };

  // Handle input change
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle edit click
  const handleEditClick = (document: Document) => {
    setSelectedDocument(document);
    setFormData({
      name: document.name,
      fileUrl: document.fileUrl,
      fileType: document.fileType,
      description: document.description || "",
    });
    setIsEditDialogOpen(true);
  };

  // Handle delete click
  const handleDeleteClick = (document: Document) => {
    setSelectedDocument(document);
    setIsDeleteDialogOpen(true);
  };

  // Handle add document
  const handleAddDocument = async () => {
    if (!formData.name || !formData.fileUrl || !formData.fileType) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/doctors/${doctorId}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Document added successfully");
        setIsAddDialogOpen(false);
        resetForm();

        // Refresh documents
        const documentsResponse = await fetch(
          `/api/doctors/${doctorId}/documents`,
        );
        if (documentsResponse.ok) {
          const data = await documentsResponse.json();
          setDocuments(data.documents || []);
        }
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to add document");
      }
    } catch (error) {
      console.error("Error adding document:", error);
      toast.error("An error occurred while adding the document");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle update document
  const handleUpdateDocument = async () => {
    if (!formData.name || !formData.fileUrl || !formData.fileType) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(
        `/api/doctors/${doctorId}/documents/${selectedDocument?.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        },
      );

      if (response.ok) {
        toast.success("Document updated successfully");
        setIsEditDialogOpen(false);

        // Refresh documents
        const documentsResponse = await fetch(
          `/api/doctors/${doctorId}/documents`,
        );
        if (documentsResponse.ok) {
          const data = await documentsResponse.json();
          setDocuments(data.documents || []);
        }
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to update document");
      }
    } catch (error) {
      console.error("Error updating document:", error);
      toast.error("An error occurred while updating the document");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete document
  const handleDeleteDocument = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(
        `/api/doctors/${doctorId}/documents/${selectedDocument?.id}`,
        {
          method: "DELETE",
        },
      );

      if (response.ok) {
        toast.success("Document deleted successfully");
        setIsDeleteDialogOpen(false);

        // Refresh documents
        const documentsResponse = await fetch(
          `/api/doctors/${doctorId}/documents`,
        );
        if (documentsResponse.ok) {
          const data = await documentsResponse.json();
          setDocuments(data.documents || []);
        }
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete document");
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error("An error occurred while deleting the document");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get file icon based on file type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith("image/")) {
      return <ImageIcon className="h-5 w-5" />;
    } else if (fileType.includes("pdf")) {
      return <FileTextIcon className="h-5 w-5" />;
    } else {
      return <FileIcon className="h-5 w-5" />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push("/doctors")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            {doctor?.user?.name}'s Documents
          </h1>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Document
        </Button>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Type</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Uploaded</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documents.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  No documents found
                </TableCell>
              </TableRow>
            ) : (
              documents.map((document) => (
                <TableRow key={document.id}>
                  <TableCell>
                    <div className="flex items-center">
                      {getFileIcon(document.fileType)}
                      <span className="ml-2 text-xs text-muted-foreground">
                        {document.fileType.split("/")[1]?.toUpperCase() ||
                          document.fileType}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    <a
                      href={document.fileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:underline text-primary"
                    >
                      {document.name}
                    </a>
                  </TableCell>
                  <TableCell>{document.description || "—"}</TableCell>
                  <TableCell>{formatDate(document.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditClick(document)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(document)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Add Document Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Document</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Document Name *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fileUrl">File URL *</Label>
              <Input
                id="fileUrl"
                name="fileUrl"
                value={formData.fileUrl}
                onChange={handleInputChange}
                placeholder="https://example.com/file.pdf"
                required
              />
              <p className="text-xs text-muted-foreground">
                Enter the URL where the file is stored. In a production
                environment, you would upload the file to a storage service.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fileType">File Type *</Label>
              <Input
                id="fileType"
                name="fileType"
                value={formData.fileType}
                onChange={handleInputChange}
                placeholder="application/pdf, image/jpeg, etc."
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button onClick={handleAddDocument} disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add Document"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Document Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Document</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Document Name *</Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-fileUrl">File URL *</Label>
              <Input
                id="edit-fileUrl"
                name="fileUrl"
                value={formData.fileUrl}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-fileType">File Type *</Label>
              <Input
                id="edit-fileType"
                name="fileType"
                value={formData.fileType}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button onClick={handleUpdateDocument} disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Document"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Document Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete the document "
              {selectedDocument?.name}"? This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" disabled={isSubmitting}>
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteDocument}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete Document"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
