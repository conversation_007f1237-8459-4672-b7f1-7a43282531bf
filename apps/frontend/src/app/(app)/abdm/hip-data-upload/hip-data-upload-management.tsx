"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Eye, Upload, ArrowLeft, Trash2 } from "lucide-react";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import { format } from "date-fns";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ConsentNotification {
  id: string;
  consentId: string;
  status: string;
  careContexts: any[];
  purpose: any;
  permission: any;
  patientId: string;
  hipId: string;
  hiTypes: string[];
  createdAt: string;
  updatedAt: string;
}

interface HealthInfoRequest {
  id: string;
  transactionId: string;
  consentId: string;
  dateRange: any;
  dataPushUrl: string;
  keyMaterial: any;
  acknowledgementSent: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CareContextReference {
  id: string;
  reference: string;
  display?: string;
  consultationExists: boolean;
  consultation?: {
    id: string;
    consultationDate: string;
    status: string;
    startTime?: string;
    endTime?: string;
    followUpDate?: string;
    followUpNotes?: string;
    patient: {
      firstName: string;
      lastName?: string;
    };
    doctor: {
      user: {
        name: string;
      };
    };
    vitals: any[];
    prescriptions: any[];
    clinicalNotes: any[];
  };
}

export function HipDataUploadManagement() {
  const [consentNotifications, setConsentNotifications] = useState<
    ConsentNotification[]
  >([]);
  const [healthInfoRequests, setHealthInfoRequests] = useState<
    HealthInfoRequest[]
  >([]);
  const [selectedConsent, setSelectedConsent] =
    useState<ConsentNotification | null>(null);
  const [careContextReferences, setCareContextReferences] = useState<
    CareContextReference[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);

  // Load consent notifications (GRANTED status with multiple care contexts)
  const loadConsentNotifications = async () => {
    try {
      setLoading(true);
      const response = await Fetch.get("/api/abdm/hip/consent-notifications");

      if (response && response.success) {
        // Filter for GRANTED consents with care contexts
        const grantedConsents = response.data.filter(
          (consent: ConsentNotification) =>
            consent.status === "GRANTED" &&
            consent.careContexts &&
            consent.careContexts.length > 0,
        );
        setConsentNotifications(grantedConsents);
      }
    } catch (error) {
      console.error("Error loading consent notifications:", error);
      toast.error("Failed to load consent notifications");
    } finally {
      setLoading(false);
    }
  };

  // Load health info requests
  const loadHealthInfoRequests = async () => {
    try {
      const response = await Fetch.get("/api/abdm/hip/health-info-requests");

      if (response && response.success) {
        setHealthInfoRequests(response.data);
      }
    } catch (error) {
      console.error("Error loading health info requests:", error);
    }
  };

  // Load care context references and check which consultations exist
  const loadCareContextReferences = async (consent: ConsentNotification) => {
    try {
      setLoading(true);

      // Extract care context references (consultation IDs)
      let careContextRefs: string[] = [];

      if (Array.isArray(consent.careContexts)) {
        careContextRefs = consent.careContexts
          .map((cc) => {
            if (typeof cc === "string") {
              return cc;
            } else if (cc.careContextReference) {
              return cc.careContextReference;
            } else if (cc.id) {
              return cc.id;
            }
            return null;
          })
          .filter(Boolean);
      }

      console.log("🔍 Extracted care context references:", careContextRefs);

      // Check which consultations exist in the database
      const response = await Fetch.post("/api/abdm/hip/match-consultations", {
        careContextReferences: careContextRefs,
        patientAbhaAddress: consent.patientId,
      });

      if (response && response.success) {
        const existingConsultations = response.data;
        // const existingConsultationIds = existingConsultations.map((c: any) => c.id);

        // Create care context reference objects with existence status
        const careContexts: CareContextReference[] = careContextRefs.map(
          (ref, index) => {
            const consultation = existingConsultations.find(
              (c: any) => c.id === ref,
            );
            return {
              id: `${ref}-${index}`,
              reference: ref,
              display: `Care Context ${index + 1}`,
              consultationExists: !!consultation,
              consultation: consultation || undefined,
            };
          },
        );

        setCareContextReferences(careContexts);
        console.log(
          "🔍 Care context references processed:",
          careContexts.length,
        );
      }
    } catch (error) {
      console.error("Error loading care context references:", error);
      toast.error("Failed to load care context references");
    } finally {
      setLoading(false);
    }
  };

  // Check if health info request exists for consent
  const getHealthInfoRequest = (consentId: string) => {
    return healthInfoRequests.find((req) => req.consentId === consentId);
  };

  // Handle consent selection
  const handleConsentSelect = async (consent: ConsentNotification) => {
    setSelectedConsent(consent);
    await loadCareContextReferences(consent);
  };

  // Handle data upload for care context reference
  const handleDataUpload = async (careContextRef: CareContextReference) => {
    if (!selectedConsent) return;

    // Check if consultation exists
    if (!careContextRef.consultationExists) {
      toast.error(
        `Consultation with ID "${careContextRef.reference}" does not exist in the system`,
      );
      return;
    }

    const healthInfoRequest = getHealthInfoRequest(selectedConsent.consentId);
    if (!healthInfoRequest) {
      toast.error("No health information request found for this consent");
      return;
    }

    try {
      setUploadLoading(careContextRef.reference);

      const response = await Fetch.post(
        "/api/abdm/hip/upload-consultation-data",
        {
          consultationId: careContextRef.reference,
          consentId: selectedConsent.consentId,
          transactionId: healthInfoRequest.transactionId,
          dataPushUrl: healthInfoRequest.dataPushUrl,
          keyMaterial: healthInfoRequest.keyMaterial,
        },
      );

      if (response && response.success) {
        toast.success("Health records uploaded successfully");
        // Refresh health info requests
        await loadHealthInfoRequests();
      } else {
        toast.error(response?.error || "Failed to upload health records");
      }
    } catch (error) {
      console.error("Error uploading health records:", error);
      toast.error("Error uploading health records");
    } finally {
      setUploadLoading(null);
    }
  };

  // Handle delete consent notification
  const handleDeleteConsent = async (consentId: string) => {
    try {
      setDeleteLoading(consentId);

      const response = await Fetch.delete(
        `/api/abdm/hip/consent-notifications/${consentId}`,
      );

      if (response && response.success) {
        toast.success("Consent notification deleted successfully");
        await loadConsentNotifications();
      } else {
        toast.error(response?.error || "Failed to delete consent notification");
      }
    } catch (error) {
      console.error("Error deleting consent notification:", error);
      toast.error("Error deleting consent notification");
    } finally {
      setDeleteLoading(null);
    }
  };

  // Load data on mount
  useEffect(() => {
    loadConsentNotifications();
    loadHealthInfoRequests();
  }, []);

  if (selectedConsent) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => {
              setSelectedConsent(null);
              setCareContextReferences([]);
            }}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Consents
          </Button>
          <div>
            <h2 className="text-xl font-semibold">Care Context References</h2>
            <p className="text-sm text-muted-foreground">
              Consent ID: {selectedConsent.consentId}
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Care Context References for Data Upload</CardTitle>
            <CardDescription>
              {careContextReferences.length} care context reference(s) from the
              consent
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : careContextReferences.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  No care context references found
                </p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Care Context Reference</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {careContextReferences.map((careContextRef) => {
                      const healthInfoRequest = getHealthInfoRequest(
                        selectedConsent.consentId,
                      );
                      // const canUpload = !!healthInfoRequest && careContextRef.consultationExists;

                      return (
                        <TableRow key={careContextRef.id}>
                          <TableCell className="font-mono text-sm">
                            {careContextRef.reference}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              size="sm"
                              onClick={() => handleDataUpload(careContextRef)}
                              disabled={
                                !healthInfoRequest ||
                                uploadLoading === careContextRef.reference
                              }
                            >
                              {uploadLoading === careContextRef.reference ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Uploading...
                                </>
                              ) : (
                                <>
                                  <Upload className="h-4 w-4 mr-2" />
                                  Upload
                                </>
                              )}
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Consent Notifications</CardTitle>
          <CardDescription>
            GRANTED consents with care contexts requiring data upload
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : consentNotifications.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No consent notifications found
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Granted consents with care contexts will appear here
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Consent ID</TableHead>
                    <TableHead>Patient ABHA</TableHead>
                    <TableHead>Care Contexts</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Received</TableHead>
                    <TableHead>Health Info Request</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {consentNotifications.map((consent) => {
                    const healthInfoRequest = getHealthInfoRequest(
                      consent.consentId,
                    );

                    return (
                      <TableRow key={consent.id}>
                        <TableCell className="font-mono text-xs">
                          {consent.consentId.substring(0, 8)}...
                        </TableCell>
                        <TableCell>{consent.patientId}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {consent.careContexts.length} contexts
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="default">{consent.status}</Badge>
                        </TableCell>
                        <TableCell>
                          {format(new Date(consent.createdAt), "PPP p")}
                        </TableCell>
                        <TableCell>
                          {healthInfoRequest ? (
                            <Badge variant="secondary">
                              {healthInfoRequest.acknowledgementSent
                                ? "Processed"
                                : "Request Received"}
                            </Badge>
                          ) : (
                            <Badge variant="outline">Pending</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex gap-2 justify-end">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleConsentSelect(consent)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View Care Contexts
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteConsent(consent.id)}
                              disabled={deleteLoading === consent.id}
                            >
                              {deleteLoading === consent.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
