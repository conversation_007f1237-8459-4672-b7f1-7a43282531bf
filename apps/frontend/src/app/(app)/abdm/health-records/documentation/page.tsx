import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon } from "lucide-react";

export const metadata: Metadata = {
  title: "Health Records Documentation",
  description: "Documentation for health record management",
};

export default async function HealthRecordDocumentationPage() {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    redirect("/auth/signin");
  }

  const { organizationId } = session.user as any;
  if (!organizationId) {
    redirect("/auth/signin");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">
          Health Records Documentation
        </h1>
        <p className="text-muted-foreground">
          Learn how to manage and share health records
        </p>
      </div>

      <Alert>
        <InfoIcon className="h-4 w-4" />
        <AlertTitle>ABDM Compliance</AlertTitle>
        <AlertDescription>
          This documentation covers the ABDM-compliant health record management
          features of the system. All health records are stored and shared
          according to ABDM specifications.
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="fhir">FHIR Resources</TabsTrigger>
          <TabsTrigger value="packaging">Packaging</TabsTrigger>
          <TabsTrigger value="sharing">Sharing</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Health Record Management</CardTitle>
              <CardDescription>
                Overview of health record management features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Introduction</h3>
                <p className="text-muted-foreground">
                  The health record management system allows you to create,
                  package, and share patient health records in FHIR-compliant
                  formats according to ABDM specifications. This ensures
                  interoperability with other healthcare systems and compliance
                  with ABDM requirements.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Key Features</h3>
                <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                  <li>
                    <strong>FHIR Compliance:</strong> All health records are
                    stored in FHIR R4-compliant formats.
                  </li>
                  <li>
                    <strong>ABDM Packaging:</strong> Health records are packaged
                    according to ABDM specifications for secure sharing.
                  </li>
                  <li>
                    <strong>Consent Management:</strong> Health records are only
                    shared with valid consent from the patient.
                  </li>
                  <li>
                    <strong>Audit Logging:</strong> All health record operations
                    are logged for audit purposes.
                  </li>
                  <li>
                    <strong>Analytics:</strong> Health record operations can be
                    monitored and analyzed.
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Workflow</h3>
                <ol className="list-decimal pl-6 space-y-2 text-muted-foreground">
                  <li>
                    <strong>Create Health Records:</strong> Health records are
                    created during patient consultations (vitals, prescriptions,
                    clinical notes).
                  </li>
                  <li>
                    <strong>Package Health Records:</strong> Health records are
                    converted to FHIR bundles and packaged for sharing.
                  </li>
                  <li>
                    <strong>Share Health Records:</strong> Packaged health
                    records are shared with other healthcare providers through
                    ABDM.
                  </li>
                </ol>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fhir">
          <Card>
            <CardHeader>
              <CardTitle>FHIR Resources</CardTitle>
              <CardDescription>
                Understanding FHIR resources and bundles
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">FHIR Overview</h3>
                <p className="text-muted-foreground">
                  Fast Healthcare Interoperability Resources (FHIR) is a
                  standard for exchanging healthcare information electronically.
                  The system uses FHIR R4 (Release 4) for storing and sharing
                  health records.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">FHIR Resources</h3>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="patient">
                    <AccordionTrigger>Patient Resource</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-muted-foreground mb-2">
                        The Patient resource contains demographic and other
                        administrative information about an individual receiving
                        healthcare services.
                      </p>
                      <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                        {`{
  "resourceType": "Patient",
  "id": "patient-id",
  "name": [
    {
      "use": "official",
      "family": "Doe",
      "given": ["John"]
    }
  ],
  "gender": "male",
  "birthDate": "1970-01-01"
}`}
                      </pre>
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="observation">
                    <AccordionTrigger>Observation Resource</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-muted-foreground mb-2">
                        The Observation resource is used to record measurements
                        and assertions about a patient, such as vital signs.
                      </p>
                      <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                        {`{
  "resourceType": "Observation",
  "id": "observation-id",
  "status": "final",
  "code": {
    "coding": [
      {
        "system": "http://loinc.org",
        "code": "8480-6",
        "display": "Systolic blood pressure"
      }
    ]
  },
  "subject": {
    "reference": "Patient/patient-id"
  },
  "valueQuantity": {
    "value": 120,
    "unit": "mmHg",
    "system": "http://unitsofmeasure.org",
    "code": "mm[Hg]"
  }
}`}
                      </pre>
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="medication">
                    <AccordionTrigger>
                      MedicationRequest Resource
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-muted-foreground mb-2">
                        The MedicationRequest resource is used to record a
                        request for medication to be dispensed and administered
                        to a patient.
                      </p>
                      <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                        {`{
  "resourceType": "MedicationRequest",
  "id": "medication-request-id",
  "status": "active",
  "intent": "order",
  "medicationCodeableConcept": {
    "coding": [
      {
        "system": "http://snomed.info/sct",
        "code": "27658006",
        "display": "Amoxicillin"
      }
    ],
    "text": "Amoxicillin"
  },
  "subject": {
    "reference": "Patient/patient-id"
  },
  "dosageInstruction": [
    {
      "text": "500mg, three times daily, for 7 days"
    }
  ]
}`}
                      </pre>
                    </AccordionContent>
                  </AccordionItem>
                  <AccordionItem value="condition">
                    <AccordionTrigger>Condition Resource</AccordionTrigger>
                    <AccordionContent>
                      <p className="text-muted-foreground mb-2">
                        The Condition resource is used to record detailed
                        information about a condition, problem, diagnosis, or
                        other event, situation, issue, or clinical concept.
                      </p>
                      <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                        {`{
  "resourceType": "Condition",
  "id": "condition-id",
  "clinicalStatus": {
    "coding": [
      {
        "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
        "code": "active",
        "display": "Active"
      }
    ]
  },
  "code": {
    "coding": [
      {
        "system": "http://snomed.info/sct",
        "code": "38341003",
        "display": "Hypertension"
      }
    ],
    "text": "Hypertension"
  },
  "subject": {
    "reference": "Patient/patient-id"
  }
}`}
                      </pre>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">FHIR Bundles</h3>
                <p className="text-muted-foreground mb-4">
                  A FHIR Bundle is a collection of resources that are related to
                  each other. The system uses document bundles for packaging
                  health records.
                </p>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                  {`{
  "resourceType": "Bundle",
  "id": "bundle-id",
  "type": "document",
  "entry": [
    {
      "fullUrl": "urn:uuid:composition-id",
      "resource": {
        "resourceType": "Composition",
        "id": "composition-id",
        "status": "final",
        "type": {
          "coding": [
            {
              "system": "http://loinc.org",
              "code": "34133-9",
              "display": "Summary of episode note"
            }
          ]
        },
        "subject": {
          "reference": "urn:uuid:patient-id"
        },
        "date": "2023-01-01T00:00:00Z",
        "author": [
          {
            "reference": "urn:uuid:practitioner-id"
          }
        ],
        "title": "Health Record",
        "section": [
          {
            "title": "Vital Signs",
            "entry": [
              {
                "reference": "urn:uuid:observation-id"
              }
            ]
          }
        ]
      }
    },
    {
      "fullUrl": "urn:uuid:patient-id",
      "resource": {
        "resourceType": "Patient",
        "id": "patient-id",
        "name": [
          {
            "use": "official",
            "family": "Doe",
            "given": ["John"]
          }
        ]
      }
    },
    {
      "fullUrl": "urn:uuid:observation-id",
      "resource": {
        "resourceType": "Observation",
        "id": "observation-id",
        "status": "final",
        "code": {
          "coding": [
            {
              "system": "http://loinc.org",
              "code": "8480-6",
              "display": "Systolic blood pressure"
            }
          ]
        },
        "subject": {
          "reference": "urn:uuid:patient-id"
        },
        "valueQuantity": {
          "value": 120,
          "unit": "mmHg"
        }
      }
    }
  ]
}`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="packaging">
          <Card>
            <CardHeader>
              <CardTitle>Health Record Packaging</CardTitle>
              <CardDescription>
                How health records are packaged for sharing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Packaging Process</h3>
                <p className="text-muted-foreground mb-4">
                  Health records are packaged according to ABDM specifications
                  for secure sharing. The packaging process involves the
                  following steps:
                </p>
                <ol className="list-decimal pl-6 space-y-2 text-muted-foreground">
                  <li>
                    <strong>Create FHIR Bundle:</strong> Health records are
                    converted to FHIR resources and bundled together.
                  </li>
                  <li>
                    <strong>Calculate Checksum:</strong> A SHA-256 checksum is
                    calculated for the bundle.
                  </li>
                  <li>
                    <strong>Encrypt Bundle:</strong> The bundle is encrypted
                    using ABDM envelope encryption.
                  </li>
                  <li>
                    <strong>Create Package:</strong> The encrypted bundle,
                    checksum, and encryption metadata are packaged together.
                  </li>
                </ol>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">ABDM Envelope</h3>
                <p className="text-muted-foreground mb-4">
                  The ABDM envelope is a secure container for health records. It
                  includes the following components:
                </p>
                <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                  <li>
                    <strong>Encrypted Content:</strong> The encrypted FHIR
                    bundle.
                  </li>
                  <li>
                    <strong>Checksum:</strong> A SHA-256 checksum of the
                    original bundle.
                  </li>
                  <li>
                    <strong>Key Material:</strong> Encryption metadata including
                    the algorithm, curve, public key, and nonce.
                  </li>
                  <li>
                    <strong>Care Context Reference:</strong> A reference to the
                    care context.
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Package Structure</h3>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                  {`{
  "pageNumber": 0,
  "pageCount": 1,
  "transactionId": "transaction-id",
  "entries": [
    {
      "content": "base64-encoded-encrypted-fhir-bundle",
      "media": "application/fhir+json",
      "checksum": "sha256-checksum",
      "careContextReference": "care-context-reference"
    }
  ],
  "keyMaterial": {
    "cryptoAlg": "ECDH",
    "curve": "Curve25519",
    "dhPublicKey": {
      "expiry": "2023-01-01T01:00:00Z",
      "parameters": "Curve25519/32byte random key",
      "keyValue": "base64-encoded-public-key"
    },
    "nonce": "base64-encoded-nonce"
  }
}`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sharing">
          <Card>
            <CardHeader>
              <CardTitle>Health Record Sharing</CardTitle>
              <CardDescription>
                How health records are shared with other healthcare providers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Consent Management</h3>
                <p className="text-muted-foreground mb-4">
                  Health records are only shared with valid consent from the
                  patient. The consent management process involves the following
                  steps:
                </p>
                <ol className="list-decimal pl-6 space-y-2 text-muted-foreground">
                  <li>
                    <strong>Request Consent:</strong> A healthcare provider
                    requests consent to access a patient's health records.
                  </li>
                  <li>
                    <strong>Patient Approval:</strong> The patient approves or
                    denies the consent request.
                  </li>
                  <li>
                    <strong>Consent Validation:</strong> The system validates
                    the consent before sharing health records.
                  </li>
                  <li>
                    <strong>Audit Logging:</strong> All consent-related
                    activities are logged for audit purposes.
                  </li>
                </ol>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Sharing Process</h3>
                <p className="text-muted-foreground mb-4">
                  Health records are shared through the ABDM network. The
                  sharing process involves the following steps:
                </p>
                <ol className="list-decimal pl-6 space-y-2 text-muted-foreground">
                  <li>
                    <strong>Package Health Record:</strong> The health record is
                    packaged according to ABDM specifications.
                  </li>
                  <li>
                    <strong>Validate Consent:</strong> The system validates that
                    the patient has granted consent for sharing.
                  </li>
                  <li>
                    <strong>Upload Package:</strong> The package is uploaded to
                    the ABDM network.
                  </li>
                  <li>
                    <strong>Notify Recipient:</strong> The recipient is notified
                    that health records are available.
                  </li>
                  <li>
                    <strong>Audit Logging:</strong> All sharing activities are
                    logged for audit purposes.
                  </li>
                </ol>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">API Endpoint</h3>
                <p className="text-muted-foreground mb-2">
                  Health records are shared through the ABDM health record
                  package API:
                </p>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                  {`POST /api/v3/health/record/package

Headers:
  Content-Type: application/json
  REQUEST-ID: request-id
  TIMESTAMP: timestamp
  X-CM-ID: cm-id
  X-HIP-ID: hip-id
  Authorization: Bearer access-token

Body:
  {
    "pageNumber": 0,
    "pageCount": 1,
    "transactionId": "transaction-id",
    "entries": [
      {
        "content": "base64-encoded-encrypted-fhir-bundle",
        "media": "application/fhir+json",
        "checksum": "sha256-checksum",
        "careContextReference": "care-context-reference"
      }
    ],
    "keyMaterial": {
      "cryptoAlg": "ECDH",
      "curve": "Curve25519",
      "dhPublicKey": {
        "expiry": "2023-01-01T01:00:00Z",
        "parameters": "Curve25519/32byte random key",
        "keyValue": "base64-encoded-public-key"
      },
      "nonce": "base64-encoded-nonce"
    }
  }`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
