import { Metada<PERSON> } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { FhirBundleViewer } from "@/components/health-records/fhir-bundle-viewer";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export const metadata: Metadata = {
  title: "FHIR Bundle Details",
  description: "View FHIR bundle details",
};

async function getBundle(bundleId: string, organizationId: string) {
  const bundle = await prisma.fhirBundle.findFirst({
    where: {
      bundleId,
      organizationId,
    },
    // Select specific fields to ensure we only get what we need
    select: {
      id: true,
      bundleId: true,
      bundleType: true,
      bundleJson: true,
      consentId: true,
      transactionId: true,
      packageChecksum: true,
      status: true,
      statusDetails: true,
      createdAt: true,
      updatedAt: true,
      patientId: true,
      organizationId: true,
    },
  });

  if (!bundle) {
    notFound();
  }

  return bundle;
}

export default async function FhirBundleDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    redirect("/auth/signin");
  }

  const { organizationId } = session.user as any;
  if (!organizationId) {
    redirect("/auth/signin");
  }

  const bundle = await getBundle(params.id, organizationId);

  // Get patient information if patientId is available
  let patientName = "";
  if (bundle.patientId) {
    try {
      const patient = await prisma.patient.findUnique({
        where: { id: bundle.patientId },
        select: { firstName: true, lastName: true },
      });

      if (patient) {
        patientName = `${patient.firstName} ${patient.lastName}`;
      }
    } catch (error) {
      console.error("Error fetching patient details:", error);
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            FHIR Bundle Details
          </h1>
          <p className="text-muted-foreground">
            View and analyze FHIR bundle content
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/abdm/health-records/operations">
              <ArrowLeft className="h-4 w-4 mr-2" /> Back to Operations
            </Link>
          </Button>
          {bundle.patientId && (
            <Button asChild>
              <Link href={`/patients/${bundle.patientId}`}>
                {patientName ? `View Patient: ${patientName}` : "View Patient"}
              </Link>
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6">
        <FhirBundleViewer bundleId={params.id} initialBundle={bundle} />
      </div>
    </div>
  );
}
