import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { HealthRecordOperationsList } from "@/components/health-records/health-record-operations-list";

export const metadata: Metadata = {
  title: "Health Record Operations",
  description: "View and manage health record operations",
};

export default async function HealthRecordOperationsPage() {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    redirect("/auth/signin");
  }

  const { organizationId } = session.user as any;
  if (!organizationId) {
    redirect("/auth/signin");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">
          Health Record Operations
        </h1>
        <p className="text-muted-foreground">
          View and manage health record operations
        </p>
      </div>

      <div className="grid gap-6">
        <HealthRecordOperationsList limit={50} />
      </div>
    </div>
  );
}
