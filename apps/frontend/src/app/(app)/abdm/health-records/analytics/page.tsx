import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { HealthRecordDashboard } from "@/components/health-records/health-record-dashboard";

export const metadata: Metadata = {
  title: "Health Record Analytics",
  description: "Analytics for health record operations",
};

export default async function HealthRecordAnalyticsPage() {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    redirect("/auth/signin");
  }

  const { organizationId } = session.user as any;
  if (!organizationId) {
    redirect("/auth/signin");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">
          Health Record Analytics
        </h1>
        <p className="text-muted-foreground">
          Monitor and analyze health record operations
        </p>
      </div>

      <div className="grid gap-6">
        <HealthRecordDashboard organizationId={organizationId} days={30} />
      </div>
    </div>
  );
}
