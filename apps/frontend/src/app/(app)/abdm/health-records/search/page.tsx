import { Metadata } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { HealthRecordSearch } from "@/components/health-records/health-record-search";

export const metadata: Metadata = {
  title: "Health Record Search",
  description: "Search for health records",
};

export default async function HealthRecordSearchPage() {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    redirect("/auth/signin");
  }

  const { organizationId } = session.user as any;
  if (!organizationId) {
    redirect("/auth/signin");
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">
          Health Record Search
        </h1>
        <p className="text-muted-foreground">
          Search for health records by patient, type, status, and date
        </p>
      </div>

      <div className="grid gap-6">
        <HealthRecordSearch />
      </div>
    </div>
  );
}
