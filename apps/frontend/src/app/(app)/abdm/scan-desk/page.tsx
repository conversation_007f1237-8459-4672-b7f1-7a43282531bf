"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Info, QrCode, AlertCircle, Download } from "lucide-react";
import { useCurrentBranch } from "@/hooks/use-current-branch";
import { QRCodeSVG } from "qrcode.react";
import html2canvas from "html2canvas";

export default function AbhaScanDeskPage() {
  const { currentBranch } = useCurrentBranch();
  const [qrUrl, setQrUrl] = useState("");
  const [error, setError] = useState<string | null>(null);

  // Fixed counter ID of 1
  const counterId = "1";

  // Generate QR code when the branch changes
  useEffect(() => {
    if (currentBranch?.hipId) {
      // Generate the QR code URL with fixed counter ID of 1
      const url = `https://phrsbx.abdm.gov.in/share-profile?hip-id=${currentBranch.hipId}&counter-id=${counterId}`;
      setQrUrl(url);
      setError(null);
    } else {
      setError(
        "Facility ID not configured for this branch. Please configure it in the branch settings.",
      );
    }
  }, [currentBranch]);

  const downloadQrCode = () => {
    const qrCodeElement = document.getElementById("abha-qr-code");
    if (!qrCodeElement) return;

    html2canvas(qrCodeElement).then((canvas) => {
      const link = document.createElement("a");
      link.download = `abha-scan-desk-qr-${
        currentBranch?.name || "branch"
      }-counter-${counterId}.png`;
      link.href = canvas.toDataURL("image/png");
      link.click();
    });
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">ABHA Scan Desk</h1>
        <p className="text-muted-foreground">
          QR code for patients to scan and share their ABHA profiles
        </p>
      </div>

      <div className="flex justify-center">
        <div className="max-w-md w-full">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>ABHA QR Code</CardTitle>
              <CardDescription>
                Scan this QR code with the ABHA app to share patient profile
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center justify-center">
              {qrUrl ? (
                <div className="space-y-6">
                  <div
                    id="abha-qr-code"
                    className="bg-white p-6 rounded-lg shadow-md flex flex-col items-center"
                  >
                    <QRCodeSVG
                      value={qrUrl}
                      size={300}
                      level="H"
                      includeMargin={true}
                    />
                    <div className="mt-4 text-center">
                      <p className="font-semibold text-gray-800">
                        ABHA Scan Desk
                      </p>
                      <p className="text-sm text-gray-600">
                        {currentBranch?.name || "Branch"}
                      </p>
                    </div>
                  </div>

                  <Button
                    onClick={downloadQrCode}
                    variant="outline"
                    className="w-full"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download QR Code
                  </Button>
                </div>
              ) : (
                <div className="py-12 text-center text-muted-foreground">
                  <QrCode className="mx-auto h-16 w-16 opacity-20 mb-4" />
                  <p>QR code will appear here once facility ID is configured</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Alert className="mt-4">
            <Info className="h-4 w-4" />
            <AlertTitle>How it works</AlertTitle>
            <AlertDescription>
              <p className="mb-2">
                The QR code allows patients to easily share their ABHA profile
                with your facility:
              </p>
              <ol className="list-decimal pl-5 space-y-1">
                <li>Display this QR code at your registration desk</li>
                <li>Patients scan the QR code using their ABHA app</li>
                <li>They authorize sharing their profile with your facility</li>
                <li>Their ABHA details are securely shared with your system</li>
              </ol>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    </div>
  );
}
