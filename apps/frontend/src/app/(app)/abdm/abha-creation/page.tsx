"use client";

import { useEffect, useState } from "react";
import { AbhaCreationForm } from "@/app/(app)/patients/[id]/abha/create/_components/abha-creation-form";
// Removed unused imports
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { UserPlus } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";

export default function AbhaCreationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [fromPatientSearch, setFromPatientSearch] = useState(false);
  const [prefilledPhone, setPrefilledPhone] = useState<string | null>(null);

  // Check if we're coming from patient search
  useEffect(() => {
    if (searchParams) {
      const phone = searchParams.get("phone");
      const fromPatientSearchParam = searchParams.get("fromPatientSearch");

      if (phone) {
        setPrefilledPhone(phone);
      }

      if (fromPatientSearchParam === "true") {
        setFromPatientSearch(true);
      }
    }
  }, [searchParams]);

  // Handle successful ABHA creation
  const handleSuccess = (abhaDetails: any) => {
    // Show success message
    toast.success("ABHA created successfully");

    // Build the URL with all available ABHA details
    let url = `/patients/register?abhaNumber=${abhaDetails.abhaNumber}&abhaAddress=${abhaDetails.abhaAddress}`;

    // Add name if available
    if (abhaDetails.name) {
      url += `&name=${encodeURIComponent(abhaDetails.name)}`;
    }

    // Add gender if available
    if (abhaDetails.gender) {
      url += `&gender=${encodeURIComponent(abhaDetails.gender)}`;
    }

    // Add yearOfBirth if available
    if (abhaDetails.yearOfBirth) {
      url += `&yearOfBirth=${encodeURIComponent(abhaDetails.yearOfBirth)}`;
    }

    // Add phone if available
    if (abhaDetails.phone) {
      url += `&phone=${encodeURIComponent(abhaDetails.phone)}`;
    } else if (prefilledPhone) {
      url += `&phone=${encodeURIComponent(prefilledPhone)}`;
    }

    // Add flag to indicate coming from ABHA creation flow
    url += `&fromAbhaCreation=true`;

    // Navigate to patient registration with ABHA details
    router.push(url);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">ABHA Creation</h1>
        <p className="text-muted-foreground">
          Create a new ABHA (Ayushman Bharat Health Account) for a patient
        </p>
      </div>

      <div className="grid gap-6">
        <Alert className="bg-green-50 border-green-200">
          <UserPlus className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">
            Automatic Patient Registration
          </AlertTitle>
          <AlertDescription className="text-green-700">
            {fromPatientSearch
              ? "After successful ABHA creation, you will be redirected to the patient registration form with ABHA details pre-filled."
              : "After successful ABHA creation, you will be automatically redirected to the patient details page if a new patient was created or an existing patient was found."}
          </AlertDescription>
        </Alert>

        <AbhaCreationForm
          patient={prefilledPhone ? { phone: prefilledPhone } : null}
          onSuccess={handleSuccess}
          standalone={true}
        />
      </div>
    </div>
  );
}
