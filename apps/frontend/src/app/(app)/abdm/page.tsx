"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowRight, IdCard, Link, Shield } from "lucide-react";
import { useRouter } from "next/navigation";

export default function AbdmPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between bg-card rounded-lg p-4 shadow-sm border">
        <div>
          <h1 className="text-2xl font-bold">ABDM Integration</h1>
          <p className="text-sm text-muted-foreground">
            Manage ABHA profiles, care contexts, and consents
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <IdCard className="h-5 w-5 mr-2 text-primary" />
              ABHA Verification
            </CardTitle>
            <CardDescription>
              Verify ABHA and create patient records
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Verify patients using their ABHA details and automatically create
              patient records.
            </p>
            <Button
              onClick={() => router.push("/abdm/abha/verify")}
              className="w-full"
            >
              Verify ABHA <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Link className="h-5 w-5 mr-2 text-primary" />
              Care Contexts
            </CardTitle>
            <CardDescription>Manage patient care contexts</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Create and manage care contexts for patients to link their health
              records.
            </p>
            <Button
              onClick={() => router.push("/abdm/care-contexts")}
              className="w-full"
            >
              Manage Care Contexts <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-primary" />
              Consents
            </CardTitle>
            <CardDescription>Manage patient consents</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              View and manage patient consents for sharing health records.
            </p>
            <Button
              onClick={() => router.push("/abdm/consents")}
              className="w-full"
            >
              Manage Consents <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
