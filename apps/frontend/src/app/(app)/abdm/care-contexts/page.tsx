"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Link2,
  Search,
  FileText,
  Calendar,
  User,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { useCurrentBranch } from "@/hooks/use-current-branch";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { format } from "date-fns";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface CareContext {
  id: string;
  referenceNumber: string;
  display: string;
  hiType: string;
  status: string;
  createdAt: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    abhaProfile?: {
      abhaNumber?: string;
      abhaAddress?: string;
    } | null;
  };
  consultation: {
    id: string;
    appointmentId: string;
    doctorId: string;
    doctor: {
      firstName: string;
      lastName: string;
    };
  };
}

export default function CareContextsPage() {
  const { currentBranch } = useCurrentBranch();
  const [careContexts, setCareContexts] = useState<CareContext[]>([]);
  const [filteredContexts, setFilteredContexts] = useState<CareContext[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [hiTypeFilter, setHiTypeFilter] = useState("all");

  useEffect(() => {
    fetchCareContexts();
  }, [currentBranch]);

  useEffect(() => {
    // Apply filters
    let filtered = careContexts;

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (context) =>
          context.patient.firstName.toLowerCase().includes(term) ||
          context.patient.lastName.toLowerCase().includes(term) ||
          context.referenceNumber.toLowerCase().includes(term) ||
          (context.patient.abhaProfile?.abhaNumber || "")
            .toLowerCase()
            .includes(term) ||
          (context.patient.abhaProfile?.abhaAddress || "")
            .toLowerCase()
            .includes(term),
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((context) => context.status === statusFilter);
    }

    // Apply hiType filter
    if (hiTypeFilter !== "all") {
      filtered = filtered.filter((context) => context.hiType === hiTypeFilter);
    }

    setFilteredContexts(filtered);
  }, [searchTerm, statusFilter, hiTypeFilter, careContexts]);

  const fetchCareContexts = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/care-contexts/all");
      const data = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 400 && data.error === "Branch not selected") {
          // If branch not selected, try to select a branch first
          await selectDefaultBranch();
          // Then retry the fetch
          const retryResponse = await fetch("/api/care-contexts/all");
          if (retryResponse.ok) {
            const retryData = await retryResponse.json();
            setCareContexts(retryData.careContexts || []);
            return;
          } else {
            throw new Error(data.error || "Failed to fetch care contexts");
          }
        } else {
          throw new Error(data.error || "Failed to fetch care contexts");
        }
      }

      setCareContexts(data.careContexts || []);
    } catch (err) {
      console.error("Error fetching care contexts:", err);
      setError("Failed to load care contexts. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to select a default branch
  const selectDefaultBranch = async () => {
    try {
      // Fetch available branches
      const branchesResponse = await fetch("/api/branches");
      if (!branchesResponse.ok) {
        throw new Error("Failed to fetch branches");
      }

      const branchesData = await branchesResponse.json();
      if (!branchesData.branches || branchesData.branches.length === 0) {
        throw new Error("No branches available");
      }

      // Find head office or use first branch
      const headOffice = branchesData.branches.find((b: any) => b.isHeadOffice);
      const defaultBranch = headOffice || branchesData.branches[0];

      // Set as current branch
      await fetch("/api/branches/default", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ branchId: defaultBranch.id }),
      });

      return defaultBranch;
    } catch (error) {
      console.error("Error selecting default branch:", error);
      throw error;
    }
  };

  const generateLinkToken = async (patientId: string) => {
    try {
      const response = await fetch(
        `/api/patients/${patientId}/care-contexts/generate-link-token`,
        {
          method: "POST",
        },
      );

      if (!response.ok) {
        throw new Error("Failed to generate link token");
      }

      toast.success("Link token generated successfully");
      fetchCareContexts(); // Refresh the list
    } catch (err) {
      console.error("Error generating link token:", err);
      toast.error("Failed to generate link token");
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Linked":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            <CheckCircle2 className="mr-1 h-3 w-3" />
            Linked
          </Badge>
        );
      case "Pending":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200"
          >
            <AlertCircle className="mr-1 h-3 w-3" />
            Pending
          </Badge>
        );
      case "Failed":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200"
          >
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="bg-gray-50 text-gray-700 border-gray-200"
          >
            {status}
          </Badge>
        );
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">
          Care Context Management
        </h1>
        <p className="text-muted-foreground">
          Manage and link care contexts to patient ABHA profiles
        </p>
      </div>

      <Tabs defaultValue="all">
        {/* <TabsList className="mb-6">
          <TabsTrigger value="all">All Care Contexts</TabsTrigger>
          <TabsTrigger value="linked">Linked</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
        </TabsList>  */}

        <TabsContent value="all" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Care Contexts</CardTitle>
              <CardDescription>
                View and manage all care contexts for this branch
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by patient name, ABHA number, or reference number"
                      className="pl-9"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Linked">Linked</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={hiTypeFilter} onValueChange={setHiTypeFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="OPConsultation">
                        OPConsultation
                      </SelectItem>
                      <SelectItem value="Prescription">Prescription</SelectItem>
                      <SelectItem value="DiagnosticReport">
                        DiagnosticReport
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-primary/70" />
                </div>
              ) : error ? (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              ) : filteredContexts.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <Link2 className="h-12 w-12 mx-auto mb-4 opacity-20" />
                  <p className="text-lg font-medium">No care contexts found</p>
                  <p className="text-sm">
                    Try adjusting your filters or create new care contexts
                  </p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Patient</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Doctor</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredContexts.map((context) => (
                        <TableRow key={context.id}>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {context.patient.firstName}{" "}
                                {context.patient.lastName}
                              </span>
                              {context.patient.abhaProfile?.abhaAddress && (
                                <span className="text-xs text-muted-foreground">
                                  {context.patient.abhaProfile.abhaAddress}
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="text-sm font-mono">
                                {context.referenceNumber}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{context.hiType}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span>
                                Dr. {context.consultation.doctor.firstName}{" "}
                                {context.consultation.doctor.lastName}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span>
                                {format(
                                  new Date(context.createdAt),
                                  "dd MMM yyyy",
                                )}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(context.status)}
                          </TableCell>
                          <TableCell>
                            {context.status === "Pending" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  generateLinkToken(context.patient.id)
                                }
                              >
                                <Link2 className="h-4 w-4 mr-2" />
                                Generate Link Token
                              </Button>
                            )}
                            {context.status === "Linked" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Navigate to consultation details
                                  window.location.href = `/consultations/${context.consultation.id}`;
                                }}
                              >
                                <FileText className="h-4 w-4 mr-2" />
                                View Records
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="linked">
          <Card>
            <CardHeader>
              <CardTitle>Linked Care Contexts</CardTitle>
              <CardDescription>
                View all care contexts that have been successfully linked to
                ABHA profiles
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Similar table but filtered to linked contexts */}
              {/* This can be implemented later */}
              <div className="text-center py-12 text-muted-foreground">
                <CheckCircle2 className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>This tab will show only linked care contexts</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending">
          <Card>
            <CardHeader>
              <CardTitle>Pending Care Contexts</CardTitle>
              <CardDescription>
                View care contexts that need to be linked to ABHA profiles
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Similar table but filtered to pending contexts */}
              {/* This can be implemented later */}
              <div className="text-center py-12 text-muted-foreground">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-20" />
                <p>This tab will show only pending care contexts</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
