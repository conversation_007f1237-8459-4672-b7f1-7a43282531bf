"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AbhaVerificationForm } from "@/app/(app)/patients/[id]/abha/verify/_components/abha-verification-form";
import { toast } from "sonner";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export function StandaloneAbhaVerification() {
  const router = useRouter();
  const [, setIsCreatingPatient] = useState(false);

  const handleVerificationSuccess = async (abhaDetails: any) => {
    try {
      setIsCreatingPatient(true);
      console.log("ABHA verification successful:", abhaDetails);

      // Get the current user to use as invitedBy
      const userResponse = await fetch("/api/auth/session");
      if (!userResponse.ok) {
        toast.error("Failed to get current user session");
        return;
      }

      const userSession = await userResponse.json();
      if (!userSession?.user?.id) {
        toast.error("You must be logged in to create a patient");
        return;
      }

      // Create a new patient with the ABHA details
      const response = await fetch("/api/patients", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName: abhaDetails.name?.split(" ")[0] || "",
          lastName: abhaDetails.name?.split(" ").slice(1).join(" ") || "",
          dateOfBirth: abhaDetails.dob
            ? new Date(
                `${abhaDetails.dob.split("-")[2]}-${abhaDetails.dob.split("-")[1]}-${abhaDetails.dob.split("-")[0]}`,
              )
                .toISOString()
                .split("T")[0]
            : abhaDetails.dayOfBirth &&
                abhaDetails.monthOfBirth &&
                abhaDetails.yearOfBirth
              ? new Date(
                  `${abhaDetails.yearOfBirth}-${abhaDetails.monthOfBirth.padStart(2, "0")}-${abhaDetails.dayOfBirth.padStart(2, "0")}`,
                )
                  .toISOString()
                  .split("T")[0]
              : new Date(`${abhaDetails.yearOfBirth || "2000"}-01-01`)
                  .toISOString()
                  .split("T")[0],
          gender:
            abhaDetails.gender === "M"
              ? "male"
              : abhaDetails.gender === "F"
                ? "female"
                : "other",
          phone: abhaDetails.phone || "",
          email: abhaDetails.email || "",
          address: abhaDetails.address || "",
          city: abhaDetails.city || "",
          state: abhaDetails.state || "",
          pincode: abhaDetails.pincode || "",
          // ABHA details
          abhaNumber: abhaDetails.abhaNumber,
          abhaAddress: abhaDetails.abhaAddress,
          healthIdNumber: abhaDetails.healthIdNumber,
          abhaStatus: "VERIFIED",
        }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("Patient created successfully");

        // Redirect to the patient edit page instead of details page
        router.push(`/patients/${data.patient.id}/edit`);
      } else {
        const error = await response.json();
        toast.error(
          `Failed to create patient: ${error.message || "Unknown error"}`,
        );
      }
    } catch (error) {
      console.error("Error creating patient:", error);
      toast.error("Failed to create patient");
    } finally {
      setIsCreatingPatient(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between bg-card rounded-lg p-4 shadow-sm border">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/abdm")}
            className="mr-3"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">ABHA Verification</h1>
            <p className="text-sm text-muted-foreground">
              Verify ABHA and create a patient record
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Verify ABHA</CardTitle>
        </CardHeader>
        <CardContent>
          <AbhaVerificationForm
            patient={null}
            patientId=""
            standalone={true}
            onSuccess={handleVerificationSuccess}
          />
        </CardContent>
      </Card>
    </div>
  );
}
