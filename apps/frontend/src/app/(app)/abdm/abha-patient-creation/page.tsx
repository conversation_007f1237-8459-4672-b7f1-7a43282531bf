"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft, UserPlus } from "lucide-react";
import { AbhaCreationForm } from "@/app/(app)/patients/[id]/abha/create/_components/abha-creation-form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function AbhaPatientCreationPage() {
  const router = useRouter();
  const [creationSuccess, setCreationSuccess] = useState(false);
  const [patientId, setPatientId] = useState<string | null>(null);
  const [abhaDetails, setAbhaDetails] = useState<any>(null);

  const handleSuccess = (details: any) => {
    setAbhaDetails(details);
    setCreationSuccess(true);

    // If a patient ID was returned, store it for redirection
    if (details.patientId) {
      setPatientId(details.patientId);

      // Redirect to patient details page after a short delay
      setTimeout(() => {
        router.push(`/patients/${details.patientId}`);
      }, 2000);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/patients")}
            className="mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Create ABHA & Register Patient</h1>
        </div>
      </div>

      {creationSuccess ? (
        <Alert className="bg-green-50 border-green-200">
          <UserPlus className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">
            ABHA Created Successfully
          </AlertTitle>
          <AlertDescription className="text-green-700">
            {patientId
              ? "Patient has been registered successfully with ABHA details. Redirecting to patient details..."
              : "ABHA has been created successfully. You can now register a patient with these details."}
          </AlertDescription>
          {abhaDetails && (
            <div className="mt-4 p-4 bg-white rounded-md border border-green-100">
              <h3 className="font-medium text-green-800 mb-2">ABHA Details</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="text-gray-500">ABHA Number:</div>
                <div className="font-medium">{abhaDetails.abhaNumber}</div>
                {abhaDetails.abhaAddress && (
                  <>
                    <div className="text-gray-500">ABHA Address:</div>
                    <div className="font-medium">{abhaDetails.abhaAddress}</div>
                  </>
                )}
              </div>
            </div>
          )}
          {!patientId && (
            <div className="mt-4">
              <Button
                onClick={() => router.push("/patients/register")}
                className="bg-green-600 hover:bg-green-700"
              >
                <UserPlus className="mr-2 h-4 w-4" />
                Register New Patient
              </Button>
            </div>
          )}
        </Alert>
      ) : (
        <div className="max-w-3xl">
          <p className="text-muted-foreground mb-6">
            Create an ABHA (Ayushman Bharat Health Account) for a new patient.
            This will automatically register the patient in the system with
            details from their ABHA profile.
          </p>
          <AbhaCreationForm
            patient={null}
            standalone={true}
            onSuccess={handleSuccess}
          />
        </div>
      )}
    </div>
  );
}
