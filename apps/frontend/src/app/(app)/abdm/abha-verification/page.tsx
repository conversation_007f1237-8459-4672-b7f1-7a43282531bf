"use client";

import { useEffect, useState } from "react";
import { AbhaVerificationForm } from "@/app/(app)/patients/[id]/abha/verify/_components/abha-verification-form";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";

export default function AbhaVerificationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [fromPatientRegistration, setFromPatientRegistration] = useState(false);
  const [fromPatientSearch, setFromPatientSearch] = useState(false);
  const [prefilledAbhaNumber, setPrefilledAbhaNumber] = useState<string | null>(
    null,
  );
  const [prefilledPhone, setPrefilledPhone] = useState<string | null>(null);

  // Check if we're coming from patient registration with an ABHA number
  useEffect(() => {
    if (searchParams) {
      const abhaNumber = searchParams.get("abhaNumber");
      const phone = searchParams.get("phone");
      const fromPatientSearchParam = searchParams.get("fromPatientSearch");

      if (abhaNumber) {
        setPrefilledAbhaNumber(abhaNumber);
        setFromPatientRegistration(true);
      }

      if (phone) {
        setPrefilledPhone(phone);
      }

      if (fromPatientSearchParam === "true") {
        setFromPatientSearch(true);
      }
    }
  }, [searchParams]);

  // Handle successful ABHA verification
  const handleSuccess = (abhaDetails: any) => {
    // Show success message
    toast.success("ABHA verified successfully");

    // Build the URL with all available ABHA details
    console.log("abhaDetails", abhaDetails);
    let url = `/patients/register?abhaNumber=${abhaDetails.abhaNumber}&abhaAddress=${abhaDetails.abhaAddress}`;

    // Add name if available
    if (abhaDetails.name) {
      url += `&name=${encodeURIComponent(abhaDetails.name)}`;
    }

    // Add gender if available
    if (abhaDetails.gender) {
      url += `&gender=${encodeURIComponent(abhaDetails.gender)}`;
    }

    // Add yearOfBirth if available
    if (abhaDetails.yearOfBirth) {
      const yearOfBirth = abhaDetails.yearOfBirth;

      // Add separate parameters for day, month, and year
      url += `&yearOfBirth=${encodeURIComponent(yearOfBirth)}`;

      // Add dayOfBirth and monthOfBirth if available
      if (abhaDetails.dayOfBirth) {
        url += `&dayOfBirth=${encodeURIComponent(abhaDetails.dayOfBirth)}`;
      }

      if (abhaDetails.monthOfBirth) {
        url += `&monthOfBirth=${encodeURIComponent(abhaDetails.monthOfBirth)}`;
      }

      // If we have a full DOB, add it as a separate parameter
      if (abhaDetails.dob) {
        url += `&dob=${encodeURIComponent(abhaDetails.dob)}`;
      }
    }

    // Add phone if available
    if (abhaDetails.phone) {
      url += `&phone=${encodeURIComponent(abhaDetails.phone)}`;
    } else if (prefilledPhone) {
      url += `&phone=${encodeURIComponent(prefilledPhone)}`;
    }

    // Add email if available
    if (abhaDetails.email) {
      url += `&email=${encodeURIComponent(abhaDetails.email)}`;
    }

    // Add address if available
    if (abhaDetails.address) {
      url += `&address=${encodeURIComponent(abhaDetails.address)}`;
    }

    // Add city if available
    if (abhaDetails.city) {
      url += `&city=${encodeURIComponent(abhaDetails.city)}`;
    }

    // Add state if available
    if (abhaDetails.state) {
      url += `&state=${encodeURIComponent(abhaDetails.state)}`;
    }

    // Add pincode if available
    if (abhaDetails.pincode) {
      url += `&pincode=${encodeURIComponent(abhaDetails.pincode)}`;
    }

    // If we came from patient registration, add auto-create and verified flags
    if (fromPatientRegistration) {
      url += `&autoCreate=true&verified=true`;
    }

    // Add flag to indicate coming from ABHA verification flow
    url += `&fromAbhaVerification=true`;
    console.log("abhaDetails", abhaDetails);

    // Navigate to patient registration with ABHA details
    router.push(url);
  };
  console;

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">ABHA Verification</h1>
        <p className="text-muted-foreground">
          Verify an existing ABHA (Ayushman Bharat Health Account) for a patient
        </p>
      </div>

      <div className="grid gap-6">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Information</AlertTitle>
          <AlertDescription>
            {fromPatientRegistration
              ? "After successful verification, a patient record will be automatically created with the ABHA details."
              : fromPatientSearch
                ? "After successful verification, you will be redirected to the patient registration form with ABHA details pre-filled."
                : "Verifying an ABHA will allow you to register a new patient with their ABHA details. After successful verification, you will be redirected to the patient registration form."}
          </AlertDescription>
        </Alert>

        <Card>
          <CardContent className="mt-6">
            <AbhaVerificationForm
              patient={null}
              onSuccess={handleSuccess}
              standalone={true}
              initialAbhaNumber={prefilledAbhaNumber}
              initialPhone={prefilledPhone}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
