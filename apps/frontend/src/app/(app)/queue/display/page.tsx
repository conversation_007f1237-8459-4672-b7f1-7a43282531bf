"use client";

import { useState, useEffect } from "react";
import { useQueue } from "@/hooks/useQueue";
import { useBranch } from "@/contexts/branch-context";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, UserCog } from "lucide-react";
import { formatDistanceToNow, parseISO } from "date-fns";
import { useSocket } from "@/components/providers/socket-provider";

export default function QueueDisplayPage() {
  const { currentBranch } = useBranch();
  const { queueItems, isLoading } = useQueue();
  const { isConnected } = useSocket();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Filter and sort queue items
  const displayItems = queueItems
    .filter(
      (item) => item.status === "waiting" || item.status === "in-consultation",
    )
    .sort((a, b) => {
      // First sort by status (waiting first, then in-consultation)
      if (a.status !== b.status) {
        return a.status === "waiting" ? -1 : 1;
      }

      // Then sort by position
      return a.position - b.position;
    });

  // Format estimated wait time
  const formatWaitTime = (estimatedStartTime: string | Date | undefined) => {
    if (!estimatedStartTime) return "Unknown";

    const estimatedDate =
      typeof estimatedStartTime === "string"
        ? parseISO(estimatedStartTime)
        : estimatedStartTime;

    if (estimatedDate < currentTime) {
      return "Now";
    }

    return formatDistanceToNow(estimatedDate, { addSuffix: true });
  };

  // Get status badge styling
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "waiting":
        return {
          variant: "secondary" as const,
          className: "bg-blue-100 text-blue-700 hover:bg-blue-100",
          borderColor: "border-blue-500",
        };
      case "in-consultation":
        return {
          variant: "secondary" as const,
          className: "bg-green-100 text-green-700 hover:bg-green-100",
          borderColor: "border-green-500",
        };
      default:
        return {
          variant: "outline" as const,
          className: "",
          borderColor: "border-gray-300",
        };
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold">Queue Status</h1>
          <p className="text-xl text-muted-foreground">
            {currentBranch?.name || "All Branches"}
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold">
            {currentTime.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
          <div className="text-muted-foreground">
            {currentTime.toLocaleDateString([], {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </div>
          <div className="flex items-center justify-end mt-2">
            <div
              className={`h-3 w-3 rounded-full ${
                isConnected ? "bg-green-500" : "bg-red-500"
              }`}
            ></div>
            <span className="ml-2 text-sm text-muted-foreground">
              {isConnected ? "Live Updates" : "Offline"}
            </span>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-xl">Loading queue information...</p>
        </div>
      ) : displayItems.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-xl">No patients currently in the queue</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayItems.map((item) => (
            <Card
              key={item.id}
              className={`border-l-4 ${
                getStatusBadge(item.status).borderColor
              }`}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-2xl">{item.patientName}</CardTitle>
                  <Badge
                    variant={getStatusBadge(item.status).variant}
                    className={`text-sm px-3 py-1 ${
                      getStatusBadge(item.status).className
                    }`}
                  >
                    {item.status === "waiting" ? "Waiting" : "In Consultation"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center text-muted-foreground">
                    <UserCog className="h-4 w-4 mr-2" />
                    <span>Dr. {item.doctorName}</span>
                  </div>

                  {item.status === "waiting" && item.estimatedStartTime && (
                    <div className="flex items-center text-blue-600">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>
                        Estimated time:{" "}
                        {formatWaitTime(item.estimatedStartTime)}
                      </span>
                    </div>
                  )}

                  <div className="mt-2 text-lg font-semibold">
                    Queue Position: {item.position}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
