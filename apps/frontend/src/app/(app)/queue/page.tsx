import { AdminQueueView } from "@/components/queue/admin-queue-view";

export default async function QueuePage() {
  // In a real implementation, you might want to show different views based on user role
  // For example:
  // const user = await getCurrentUser();
  // if (user?.role === "doctor") return <DoctorQueueView />;

  // For now, we'll just show the admin view for all users
  return <AdminQueueView />;
}
