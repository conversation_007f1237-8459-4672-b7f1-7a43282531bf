"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, RefreshCw, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ApiUsageData {
  oldEndpoint: string;
  newEndpoint: string;
  count: number;
  lastUsed: Date;
}

interface ApiUsageSummary {
  total: number;
  endpoints: number;
  active: number;
}

export default function AbdmMonitorPage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [usageData, setUsageData] = useState<ApiUsageData[]>([]);
  const [summary, setSummary] = useState<ApiUsageSummary | null>(null);
  const [unusedEndpoints, setUnusedEndpoints] = useState<string[]>([]);

  // Function to fetch monitoring data
  const fetchMonitoringData = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/abdm/monitor");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch monitoring data");
      }

      const data = await response.json();

      // Convert lastUsed strings to Date objects
      const formattedData = data.usageData.map((item: any) => ({
        ...item,
        lastUsed: new Date(item.lastUsed),
      }));

      setUsageData(formattedData);
      setSummary(data.summary);
      setUnusedEndpoints(data.unusedEndpoints);
    } catch (error) {
      console.error("Error fetching monitoring data:", error);
      setError(
        error instanceof Error ? error.message : "An unexpected error occurred",
      );
    } finally {
      setLoading(false);
    }
  };

  // Fetch monitoring data on component mount
  useEffect(() => {
    fetchMonitoringData();
  }, []);

  // Format date for display
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    }).format(date);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">
          ABDM API Compatibility Monitoring
        </h1>
        <Button
          onClick={fetchMonitoringData}
          variant="outline"
          disabled={loading}
        >
          {loading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="mr-2 h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Total Requests</CardTitle>
              <CardDescription>
                Total number of requests through compatibility layer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-4xl font-bold">{summary.total}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Active Endpoints</CardTitle>
              <CardDescription>
                Endpoints used in the last 24 hours
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-4xl font-bold">{summary.active}</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Unused Endpoints</CardTitle>
              <CardDescription>
                Endpoints that have never been used
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-4xl font-bold">{unusedEndpoints.length}</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>API Usage Data</CardTitle>
          <CardDescription>
            Usage data for the compatibility layer. This shows which old API
            endpoints are still being used.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : usageData.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No API usage data available. This could mean that no requests have
              been made through the compatibility layer yet.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Old Endpoint</TableHead>
                  <TableHead>New Endpoint</TableHead>
                  <TableHead className="text-right">Count</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {usageData
                  .sort((a, b) => b.count - a.count)
                  .map((item) => {
                    // Check if the endpoint was used in the last 24 hours
                    const now = new Date();
                    const oneDayAgo = new Date(
                      now.getTime() - 24 * 60 * 60 * 1000,
                    );
                    const isActive = item.lastUsed > oneDayAgo;

                    return (
                      <TableRow key={item.oldEndpoint}>
                        <TableCell className="font-mono text-sm">
                          {item.oldEndpoint}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {item.newEndpoint}
                        </TableCell>
                        <TableCell className="text-right">
                          {item.count}
                        </TableCell>
                        <TableCell>{formatDate(item.lastUsed)}</TableCell>
                        <TableCell>
                          <Badge variant={isActive ? "default" : "outline"}>
                            {isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {unusedEndpoints.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Unused Endpoints</CardTitle>
            <CardDescription>
              These endpoints have never been used through the compatibility
              layer. They may be safe to remove.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Old Endpoint</TableHead>
                  <TableHead>New Endpoint</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {unusedEndpoints.map((endpoint) => (
                  <TableRow key={endpoint}>
                    <TableCell className="font-mono text-sm">
                      {endpoint}
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {endpoint.replace("/api/abdm/", "/api/abdm/new/")}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
