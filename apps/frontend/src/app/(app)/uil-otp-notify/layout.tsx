import { Metadata } from "next";
import { PageHeader } from "@/components/page-header";

export const metadata: Metadata = {
  title: "UIL OTP Notifications",
  description: "View User Initiated Linking OTP notifications",
};

interface UILOtpNotifyLayoutProps {
  children: React.ReactNode;
}

export default function UILOtpNotifyLayout({
  children,
}: UILOtpNotifyLayoutProps) {
  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        heading="UIL OTP Notifications"
        subheading="View User Initiated Linking OTP notifications from ABDM"
      />
      <div>{children}</div>
    </div>
  );
}
