import { notFound } from "next/navigation";
import { getUILOtpNotificationById } from "./_lib/queries";
import { NotificationHeader } from "./_components/notification-header";
import { NotificationDetails } from "./_components/notification-details";
import { JsonView } from "./_components/json-view";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";

interface UILOtpNotificationDetailPageProps {
  params: {
    id: string;
  };
}

export default async function UILOtpNotificationDetailPage({
  params,
}: UILOtpNotificationDetailPageProps) {
  if (!params.id) {
    return notFound();
  }

  try {
    const notification = await getUILOtpNotificationById(params.id);

    return (
      <div className="space-y-6">
        <NotificationHeader notification={notification} />

        <Tabs defaultValue="formatted" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="formatted">Formatted View</TabsTrigger>
            <TabsTrigger value="json">JSON View</TabsTrigger>
          </TabsList>

          <TabsContent value="formatted">
            <div className="grid grid-cols-1 gap-6">
              <NotificationDetails notification={notification} />
            </div>
          </TabsContent>

          <TabsContent value="json">
            <JsonView notification={notification} />
          </TabsContent>
        </Tabs>
      </div>
    );
  } catch (error) {
    console.error("Error fetching UIL OTP notification:", error);
    return notFound();
  }
}
