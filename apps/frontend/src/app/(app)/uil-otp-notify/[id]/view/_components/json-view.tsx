"use client";

import { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Copy } from "lucide-react";
import type { UILOtpNotification } from "@/app/(app)/uil-otp-notify/(list)/_lib/queries";

interface JsonViewProps {
  notification: UILOtpNotification;
}

export function JsonView({ notification }: JsonViewProps) {
  const [copied, setCopied] = useState(false);

  // Format the notification data as JSON
  const jsonData = JSON.stringify(notification, null, 2);

  // Copy JSON to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(jsonData);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Raw JSON Data</CardTitle>
        <Button
          variant="outline"
          size="sm"
          className="h-8 px-2 lg:px-3"
          onClick={copyToClipboard}
        >
          <Copy className="h-3.5 w-3.5 lg:mr-2" />
          <span className="hidden lg:inline-block">
            {copied ? "Copied!" : "Copy"}
          </span>
        </Button>
      </CardHeader>
      <CardContent>
        <pre className="mt-2 w-full overflow-auto rounded-md bg-slate-950 p-4">
          <code className="text-white text-xs">{jsonData}</code>
        </pre>
      </CardContent>
    </Card>
  );
}
