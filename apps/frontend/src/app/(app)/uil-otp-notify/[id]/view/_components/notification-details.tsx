import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { UILOtpNotification } from "@/app/(app)/uil-otp-notify/(list)/_lib/queries";

interface NotificationDetailsProps {
  notification: UILOtpNotification;
}

export function NotificationDetails({
  notification,
}: NotificationDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Details</CardTitle>
        <CardDescription>Details of the UIL OTP notification</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Link Ref Number
            </h3>
            <p className="mt-1 text-sm">{notification.linkRefNumber}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">OTP</h3>
            <p className="mt-1 text-sm font-mono bg-gray-50 p-2 rounded border">
              {notification.otp}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Transaction ID
            </h3>
            <p className="mt-1 text-sm">{notification.transactionId}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Patient ID
            </h3>
            <p className="mt-1 text-sm">{notification.patientId}</p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">
            Care Contexts
          </h3>
          <div className="mt-2 space-y-2">
            {notification.careContexts.map((context: any, index: number) => (
              <div key={index} className="rounded-md border p-3 bg-gray-50">
                <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                  <div>
                    <span className="text-xs font-medium text-muted-foreground">
                      Reference Number:
                    </span>
                    <p className="text-sm">{context.referenceNumber}</p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-muted-foreground">
                      Display:
                    </span>
                    <p className="text-sm">{context.display}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Expires At
            </h3>
            <p className="mt-1 text-sm">
              {format(new Date(notification.expiresAt), "PPP p")}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Verification Status
            </h3>
            <p className="mt-1">
              {notification.verified ? (
                <Badge className="bg-green-100 text-green-800">Verified</Badge>
              ) : (
                <Badge variant="outline" className="bg-amber-50 text-amber-800">
                  Pending
                </Badge>
              )}
            </p>
          </div>
        </div>

        {notification.requestId && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Request ID
            </h3>
            <p className="mt-1 text-sm">{notification.requestId}</p>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Created At
            </h3>
            <p className="mt-1 text-sm">
              {format(new Date(notification.createdAt), "PPP p")}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Updated At
            </h3>
            <p className="mt-1 text-sm">
              {format(new Date(notification.updatedAt), "PPP p")}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
