"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";
import { UILOtpNotification } from "@/app/(app)/uil-otp-notify/(list)/_lib/queries";

export async function getUILOtpNotificationById(
  id: string,
): Promise<UILOtpNotification> {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Find the UIL OTP notification
  const notification = await prisma.uILOtpNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("UIL OTP notification not found");
  }

  // Format the data to match the expected interface
  return {
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
    expiresAt:
      notification.expiresAt instanceof Date
        ? notification.expiresAt.toISOString()
        : notification.expiresAt,
    // Ensure careContexts is properly formatted as an array of objects
    careContexts: Array.isArray(notification.careContexts)
      ? notification.careContexts
      : typeof notification.careContexts === "object" &&
          notification.careContexts !== null
        ? [notification.careContexts]
        : [],
  };
}
