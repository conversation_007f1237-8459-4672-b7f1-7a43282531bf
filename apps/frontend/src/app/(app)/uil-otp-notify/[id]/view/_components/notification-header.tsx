"use client";

import Link from "next/link";
import { format } from "date-fns";
import { ArrowLeft, CheckCircle, XCircle } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";

import { UILOtpNotification } from "@/app/(app)/uil-otp-notify/(list)/_lib/queries";

interface NotificationHeaderProps {
  notification: UILOtpNotification;
}

export function NotificationHeader({ notification }: NotificationHeaderProps) {
  const isVerified = notification.verified;

  return (
    <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
      <div className="flex flex-col space-y-1">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" asChild>
            <Link href="/uil-otp-notify">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            UIL OTP Notification
          </h2>
          {isVerified ? (
            <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-100">
              <CheckCircle className="h-4 w-4 mr-2" />
              Verified
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="ml-2 bg-amber-50 text-amber-800 hover:bg-amber-50"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Pending
            </Badge>
          )}
        </div>
        <div className="text-sm text-muted-foreground">
          Link Ref Number: {notification.linkRefNumber} • Created:{" "}
          {format(new Date(notification.createdAt), "PPP p")}
        </div>
      </div>
    </div>
  );
}
