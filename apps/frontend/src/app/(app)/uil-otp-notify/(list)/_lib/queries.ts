"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";
import { GetUILOtpNotificationsInput } from "./validations";

export interface CareContext {
  referenceNumber: string;
  display: string;
}

export interface UILOtpNotification {
  id: string;
  linkRefNumber: string;
  otp: string;
  transactionId: string;
  patientId: string;
  careContexts: CareContext[] | any; // Allow any type for flexibility
  expiresAt: string | Date;
  verified: boolean;
  requestId: string | null;
  createdAt: string;
  updatedAt: string;
}

export async function getUILOtpNotifications(
  input: GetUILOtpNotificationsInput,
) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Default pagination values
  const page = input.page || 1;
  const perPage = input.perPage || 10;
  const skip = (page - 1) * perPage;

  // Build where clause for filtering
  const where: any = {};

  // Add filters if provided
  if (input.linkRefNumber) {
    where.linkRefNumber = {
      contains: input.linkRefNumber,
      mode: "insensitive",
    };
  }

  if (input.verified !== undefined) {
    where.verified = input.verified === "true";
  }

  // Get total count
  const total = await prisma.uILOtpNotify.count({
    where,
  });

  // Get UIL OTP notifications with pagination
  const notifications = await prisma.uILOtpNotify.findMany({
    where,
    orderBy: {
      createdAt: "desc",
    },
    skip,
    take: perPage,
  });

  // Calculate total pages
  const totalPages = Math.ceil(total / perPage);

  // Format the data to match the expected interface
  const formattedNotifications = notifications.map((notification) => ({
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  }));

  return {
    data: formattedNotifications,
    pageCount: totalPages,
  };
}

export async function getUILOtpNotificationById(id: string) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Find the UIL OTP notification
  const notification = await prisma.uILOtpNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("UIL OTP notification not found");
  }

  // Format the data to match the expected interface
  return {
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  };
}
