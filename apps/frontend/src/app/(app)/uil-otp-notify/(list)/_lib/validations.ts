import { z } from "zod";

export const GetUILOtpNotificationsSchema = z.object({
  page: z.number().default(1),
  perPage: z.number().default(10),
  sort: z
    .object({
      field: z.string(),
      direction: z.enum(["asc", "desc"]),
    })
    .optional(),
  linkRefNumber: z.string().optional(),
  verified: z.string().optional(),
});

export type GetUILOtpNotificationsInput = z.infer<
  typeof GetUILOtpNotificationsSchema
>;
