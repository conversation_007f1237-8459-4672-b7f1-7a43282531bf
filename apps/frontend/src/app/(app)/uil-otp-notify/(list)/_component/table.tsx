"use client";

import * as React from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { format } from "date-fns";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Check, X, ExternalLink } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { DataTablePagination } from "@workspace/data-table/component/data-table-pagination";
import { DataTableViewOptions } from "@workspace/data-table/component/data-table-view-options";

import { UILOtpNotification } from "../_lib/queries";

interface UILOtpNotificationsTableProps {
  initialData: {
    data: UILOtpNotification[];
    pageCount: number;
  };
}

export function UILOtpNotificationsTable({
  initialData,
}: UILOtpNotificationsTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [data] = React.useState(initialData);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [linkRefNumberFilter, setLinkRefNumberFilter] = React.useState<string>(
    searchParams?.get("linkRefNumber") || "",
  );
  const [verifiedFilter, setVerifiedFilter] = React.useState<string>(
    searchParams?.get("verified") || "all",
  );

  // Define columns
  const columns: ColumnDef<UILOtpNotification>[] = [
    {
      accessorKey: "linkRefNumber",
      header: "Link Ref Number",
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate font-medium">
          {row.getValue("linkRefNumber")}
        </div>
      ),
    },
    {
      accessorKey: "otp",
      header: "OTP",
      cell: ({ row }) => (
        <div className="font-mono font-medium">{row.getValue("otp")}</div>
      ),
    },
    {
      accessorKey: "verified",
      header: "Verified",
      cell: ({ row }) => {
        const verified = row.getValue("verified");

        return verified ? (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            <Check className="h-3 w-3 mr-1" />
            Verified
          </Badge>
        ) : (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-800 hover:bg-amber-50"
          >
            <X className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      },
    },
    {
      accessorKey: "patientId",
      header: "Patient ID",
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.getValue("patientId")}
        </div>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }) => (
        <div className="font-medium">
          {format(new Date(row.original.createdAt), "dd/MM/yyyy HH:mm:ss")}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="icon" asChild className="h-8 w-8">
            <Link href={`/uil-otp-notify/${row.original.id}/view`}>
              <ExternalLink className="h-4 w-4" />
              <span className="sr-only">View</span>
            </Link>
          </Button>
        </div>
      ),
    },
  ];

  // Create table instance
  const table = useReactTable({
    data: data.data,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    manualPagination: true,
    pageCount: data.pageCount,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Handle search and filter changes
  const handleSearch = React.useCallback(() => {
    const params = new URLSearchParams();

    if (searchParams) {
      searchParams.forEach((value, key) => {
        params.set(key, value);
      });
    }

    if (linkRefNumberFilter) {
      params.set("linkRefNumber", linkRefNumberFilter);
    } else {
      params.delete("linkRefNumber");
    }

    if (verifiedFilter && verifiedFilter !== "all") {
      params.set("verified", verifiedFilter);
    } else {
      params.delete("verified");
    }

    params.set("page", "1");

    router.push(`${pathname}?${params.toString()}`);
  }, [pathname, router, searchParams, linkRefNumberFilter, verifiedFilter]);

  // Handle pagination changes
  const handlePageChange = React.useCallback(
    (page: number) => {
      const params = new URLSearchParams();

      if (searchParams) {
        searchParams.forEach((value, key) => {
          params.set(key, value);
        });
      }

      params.set("page", page.toString());
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams],
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder="Filter by Link Ref Number"
            value={linkRefNumberFilter}
            onChange={(e) => setLinkRefNumberFilter(e.target.value)}
            className="h-8 w-[250px]"
          />
          <Select value={verifiedFilter} onValueChange={setVerifiedFilter}>
            <SelectTrigger className="h-8 w-[150px]">
              <SelectValue placeholder="Verification Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="true">Verified</SelectItem>
              <SelectItem value="false">Pending</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSearch}
            className="h-8"
          >
            Search
          </Button>
        </div>
        <DataTableViewOptions table={table} />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <DataTablePagination
        table={table}
        pageCount={data.pageCount}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
