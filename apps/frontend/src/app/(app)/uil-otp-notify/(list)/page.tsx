import * as React from "react";
import { DataTableSkeleton } from "@workspace/data-table/component/data-table-skeleton";
import { UILOtpNotificationsTable } from "./_component/table";
import { getUILOtpNotifications } from "./_lib/queries";
import { GetUILOtpNotificationsSchema } from "./_lib/validations";

interface UILOtpNotificationsPageProps {
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}

export default async function UILOtpNotificationsPage({
  searchParams,
}: UILOtpNotificationsPageProps) {
  // Parse search params
  const parsedParams = GetUILOtpNotificationsSchema.parse({
    page: searchParams.page ? Number(searchParams.page) : 1,
    perPage: searchParams.per_page ? Number(searchParams.per_page) : 10,
    sort: searchParams.sort
      ? JSON.parse(decodeURIComponent(searchParams.sort as string))
      : undefined,
    linkRefNumber: searchParams.linkRefNumber,
    verified: searchParams.verified,
  });

  // Fetch data
  const notifications = await getUILOtpNotifications(parsedParams);

  // Ensure the data is properly formatted for the table component
  const formattedNotifications = {
    data: notifications.data.map((notification) => ({
      ...notification,
      expiresAt:
        notification.expiresAt instanceof Date
          ? notification.expiresAt.toISOString()
          : notification.expiresAt,
      careContexts: Array.isArray(notification.careContexts)
        ? notification.careContexts
        : typeof notification.careContexts === "object" &&
            notification.careContexts !== null
          ? [notification.careContexts]
          : [],
    })),
    pageCount: notifications.pageCount,
  };

  return (
    <React.Suspense fallback={<DataTableSkeleton columnCount={6} />}>
      <UILOtpNotificationsTable initialData={formattedNotifications} />
    </React.Suspense>
  );
}
