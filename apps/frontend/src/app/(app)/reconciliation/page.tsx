"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Loader2,
  Search,
  FileText,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import { toast } from "sonner";
import { useCurrentBranch } from "@/hooks/use-current-branch";
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface Doctor {
  id: string;
  user: {
    name: string;
  };
}

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  abhaProfile?: {
    abhaNumber: string;
    abhaAddress: string;
  };
}

interface Branch {
  id: string;
  name: string;
}

interface Consultation {
  id: string;
  patientId: string;
  doctorId: string;
  branchId: string;
  consultationDate: string;
  status: string;
  patient: Patient;
  doctor: {
    id: string;
    user: {
      name: string;
    };
  };
  branch: Branch;
  CareContext: any[];
}

export default function ReconsultationPage() {
  const router = useRouter();
  const { currentBranch } = useCurrentBranch();
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  // Initialize with today's date, ensuring it's set to the local date
  const [dateFilter, setDateFilter] = useState<Date | undefined>(() => {
    const today = new Date();
    // Create a new date object with just the year, month, and day to avoid time issues
    return new Date(today.getFullYear(), today.getMonth(), today.getDate());
  });
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [doctorFilter, setDoctorFilter] = useState("all");
  const [processingConsultationId, setProcessingConsultationId] = useState<
    string | null
  >(null);

  useEffect(() => {
    fetchConsultations();
    fetchDoctors();
  }, [currentBranch]);

  const fetchConsultations = async () => {
    try {
      setLoading(true);

      // If branch is still loading, wait for it
      if (currentBranch === undefined) {
        console.log("Branch is still loading, waiting...");
        return; // Don't fetch yet, wait for branch to load
      }

      let url = "/api/reconsultation";

      // Add branch filter if available
      if (currentBranch?.id) {
        url += `?branchId=${currentBranch.id}`;
      }

      console.log(`Fetching consultations with URL: ${url}`);

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch consultations");
      }

      const data = await response.json();
      console.log(`Received ${data.consultations?.length || 0} consultations`);
      setConsultations(data.consultations || []);
    } catch (error) {
      console.error("Error fetching consultations:", error);
      toast.error("Failed to load consultations");
    } finally {
      setLoading(false);
    }
  };

  const fetchDoctors = async () => {
    try {
      const response = await fetch("/api/doctors");

      if (!response.ok) {
        throw new Error("Failed to fetch doctors");
      }

      const data = await response.json();
      setDoctors(data.doctors || []);
    } catch (error) {
      console.error("Error fetching doctors:", error);
    }
  };

  const handleCreateCareContext = async (
    consultationId: string,
    patientId: string,
  ) => {
    try {
      setProcessingConsultationId(consultationId);

      // Get branch ID from cookies or current branch
      let branchId = currentBranch?.id;
      if (!branchId) {
        toast.error("No branch selected. Please select a branch first.");
        return;
      }

      // First check if patient has a link token for this branch
      const linkTokenResponse = await fetch(
        `/api/patients/${patientId}/care-contexts/link-token?branchId=${branchId}`,
      );
      const linkTokenData = await linkTokenResponse.json();

      // If no link token, check if it's in waiting state or needs to be generated
      if (!linkTokenData.hasLinkToken) {
        // If token is in pending state, show waiting message
        if (
          linkTokenData.isPendingToken ||
          linkTokenData.status === "waiting"
        ) {
          toast.info(
            "Waiting for link token from ABDM. Please try again in a few moments.",
          );
          setProcessingConsultationId(null);
          return;
        }

        // Check if there are any token generation errors for this patient
        const tokenErrorsResponse = await fetch(
          `/api/patients/${patientId}/care-contexts/token-errors`,
        );

        if (tokenErrorsResponse.ok) {
          const tokenErrorsData = await tokenErrorsResponse.json();

          // If there are errors, show the most recent one
          if (tokenErrorsData.errors && tokenErrorsData.errors.length > 0) {
            const latestError = tokenErrorsData.errors[0];

            // Only show error if it's an actual error (not a success or pending notification)
            if (latestError.error) {
              toast.error(
                `ABDM Error: ${latestError.error.message || "Unknown error"}`,
                {
                  description: `Code: ${latestError.error.code || "Unknown"}`,
                  duration: 5000,
                },
              );
            }
          }
        }

        toast.info("Generating link token for patient...");

        // Generate a link token
        const generateResponse = await fetch(
          `/api/patients/${patientId}/care-contexts/link-token`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              branchId: branchId,
              createEmpty: true,
            }),
          },
        );

        if (!generateResponse.ok) {
          const errorData = await generateResponse.json();
          throw new Error(errorData.error || "Failed to generate link token");
        }

        const generateData = await generateResponse.json();
        console.log("Token generation response:", generateData);

        // If token generation is asynchronous, we need to wait for it
        if (generateData.async) {
          toast.info("Waiting for link token to be generated...");

          // Poll for token status
          let attempts = 0;
          const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds

          while (attempts < maxAttempts) {
            attempts++;
            await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait 2 seconds

            // Check for token status
            const checkResponse = await fetch(
              `/api/patients/${patientId}/care-contexts/link-token?branchId=${branchId}`,
            );
            if (!checkResponse.ok) continue;

            const checkData = await checkResponse.json();
            if (checkData.hasLinkToken) {
              toast.success("Link token generated successfully");
              break;
            }

            // Check for errors during token generation
            if (attempts % 5 === 0) {
              // Check for errors every 10 seconds
              const errorCheckResponse = await fetch(
                `/api/patients/${patientId}/care-contexts/token-errors`,
              );

              if (errorCheckResponse.ok) {
                const errorData = await errorCheckResponse.json();

                // If there are new errors, show the most recent one
                if (errorData.errors && errorData.errors.length > 0) {
                  const latestError = errorData.errors[0];

                  // Only show error if it's an actual error (not a success or pending notification)
                  if (latestError.error) {
                    toast.error(
                      `ABDM Error: ${latestError.error.message || "Unknown error"}`,
                      {
                        description: `Code: ${latestError.error.code || "Unknown"}`,
                        duration: 5000,
                      },
                    );
                    throw new Error(
                      latestError.error.message ||
                        "Failed to generate link token",
                    );
                  }
                }
              }
            }

            if (attempts === maxAttempts) {
              throw new Error("Timed out waiting for link token");
            }
          }
        }
      }

      // Create care context
      const response = await fetch(
        `/api/patients/${patientId}/care-contexts/link`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            consultationId,
            branchId,
            hiTypes: ["DiagnosticReport", "Prescription", "OPConsultation"],
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create care context");
      }

      const data = await response.json();

      // Notify about linked care context
      const notifyResponse = await fetch(
        `/api/patients/${patientId}/care-contexts/notify`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            careContextId: data.careContext.id,
          }),
        },
      );

      if (!notifyResponse.ok) {
        const errorData = await notifyResponse.json();
        throw new Error(
          errorData.error || "Failed to notify about care context",
        );
      }

      toast.success("Care context created and linked successfully");

      // Refresh the consultations list
      fetchConsultations();
    } catch (error) {
      console.error("Error creating care context:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to create care context",
      );
    } finally {
      setProcessingConsultationId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case "in-progress":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            In Progress
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            <AlertCircle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
    }
  };

  // Log the consultations we received
  useEffect(() => {
    console.log(
      `Processing ${consultations.length} consultations for filtering`,
    );
    if (consultations.length > 0) {
      console.log("Sample consultation:", consultations[0]);
    }
  }, [consultations]);

  const filteredConsultations = consultations.filter((consultation) => {
    // Skip filtering if any required fields are missing
    if (
      !consultation.patient ||
      !consultation.doctor ||
      !consultation.doctor.user
    ) {
      console.log(
        `Skipping consultation ${consultation.id} due to missing data`,
      );
      return false;
    }

    try {
      const patientName =
        `${consultation.patient.firstName || ""} ${consultation.patient.lastName || ""}`.toLowerCase();
      const doctorName = (consultation.doctor.user.name || "").toLowerCase();
      const searchLower = (searchTerm || "").toLowerCase();

      const matchesSearch =
        !searchTerm ||
        patientName.includes(searchLower) ||
        doctorName.includes(searchLower);

      const matchesStatus =
        !statusFilter ||
        statusFilter === "all" ||
        consultation.status === statusFilter;

      const matchesDoctor =
        !doctorFilter ||
        doctorFilter === "all" ||
        consultation.doctorId === doctorFilter;

      const matchesDate =
        !dateFilter ||
        format(new Date(consultation.consultationDate), "yyyy-MM-dd") ===
          format(dateFilter, "yyyy-MM-dd");

      return matchesSearch && matchesStatus && matchesDoctor && matchesDate;
    } catch (error) {
      console.error(`Error filtering consultation ${consultation.id}:`, error);
      return false;
    }
  });

  const handleViewConsultation = (id: string) => {
    router.push(`/consultations/${id}`);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Reconciliation Management</h1>
        <p className="text-muted-foreground">
          Create care contexts for consultations that don't have them
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Consultations Without Care Contexts</CardTitle>
          <CardDescription>
            These consultations are for patients with ABHA profiles but don't
            have care contexts linked
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search by patient name or doctor..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="w-full md:w-[180px]">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-full md:w-[180px]">
              <Select value={doctorFilter} onValueChange={setDoctorFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by doctor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Doctors</SelectItem>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      {doctor.user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="w-full md:w-[180px]">
              <DatePicker
                date={dateFilter}
                setDate={setDateFilter}
                placeholder="Filter by date"
              />
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : filteredConsultations.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>No consultations found</AlertTitle>
              <AlertDescription>
                There are no consultations without care contexts for patients
                with ABHA profiles.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Patient</TableHead>
                    <TableHead>Doctor</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredConsultations.map((consultation) => (
                    <TableRow key={consultation.id}>
                      <TableCell className="font-medium">
                        <div>
                          {consultation.patient.firstName}{" "}
                          {consultation.patient.lastName}
                        </div>
                        {consultation.patient.abhaProfile?.abhaNumber && (
                          <div className="text-xs text-muted-foreground">
                            ABHA: {consultation.patient.abhaProfile.abhaNumber}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>{consultation.doctor.user.name}</TableCell>
                      <TableCell>
                        {format(new Date(consultation.consultationDate), "PPP")}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(consultation.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleViewConsultation(consultation.id)
                            }
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            View
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() =>
                              handleCreateCareContext(
                                consultation.id,
                                consultation.patientId,
                              )
                            }
                            disabled={
                              processingConsultationId === consultation.id
                            }
                          >
                            {processingConsultationId === consultation.id ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                                Processing...
                              </>
                            ) : (
                              <>Create Care Context</>
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
