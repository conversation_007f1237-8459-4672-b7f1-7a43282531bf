import { Doctor<PERSON><PERSON><PERSON>Vie<PERSON> } from "@/components/queue/doctor-queue-view";
import { getCurrentUser } from "@/lib/session";

export default async function DoctorQueuePage() {
  // Get the current user to determine if they are a doctor
  const user = await getCurrentUser();

  // Remove role restriction - all users can access doctor queue page
  // if (!user || user.role !== "doctor") {
  //   redirect("/queue");
  // }

  // For now, we'll just pass the user ID as the doctor ID
  // In a real implementation, you would fetch the doctor ID from the database
  return <DoctorQueueView doctorId={user?.id} />;
}
