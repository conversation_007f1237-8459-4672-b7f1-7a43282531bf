"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { PatientRegistrationForm } from "@/components/patient-registration-form";
import { ArrowLeft } from "lucide-react";
// import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function PatientRegistrationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [, setShowEmailAlert] = useState(false);
  const [abhaVerified, setAbhaVerified] = useState(false);
  const [abhaDetails, setAbhaDetails] = useState<any>(null);

  // Check if we're coming from ABHA verification with autoCreate flag
  useEffect(() => {
    if (!searchParams) return;

    const autoCreate = searchParams.get("autoCreate");
    const verified = searchParams.get("verified");
    const abhaNumber = searchParams.get("abhaNumber");
    const abhaAddress = searchParams.get("abhaAddress");
    const name = searchParams.get("name");
    const gender = searchParams.get("gender");
    const yearOfBirth = searchParams.get("yearOfBirth");
    const dayOfBirth = searchParams.get("dayOfBirth");
    const monthOfBirth = searchParams.get("monthOfBirth");
    const dob = searchParams.get("dob");
    const phone = searchParams.get("phone");
    const email = searchParams.get("email");
    const address = searchParams.get("address");
    const city = searchParams.get("city");
    const state = searchParams.get("state");
    const pincode = searchParams.get("pincode");
    const token = searchParams.get("token");
    const phrAddressesParam = searchParams.get("phrAddresses");
    const fromAbhaVerification = searchParams.get("fromAbhaVerification");

    // DEBUG: Log all URL parameters
    console.log("=== PATIENT REGISTRATION PAGE DEBUG ===");
    console.log(
      "All search params:",
      Object.fromEntries(searchParams.entries()),
    );
    console.log("fromAbhaVerification extracted:", fromAbhaVerification);
    console.log("fromAbhaVerification type:", typeof fromAbhaVerification);

    // Parse PHR addresses if available
    let phrAddresses: string[] = [];
    if (phrAddressesParam) {
      try {
        phrAddresses = JSON.parse(decodeURIComponent(phrAddressesParam));
      } catch (error) {
        console.error("Error parsing PHR addresses:", error);
      }
    }

    // If we have ABHA details, store them
    if (abhaNumber) {
      const details: any = {
        abhaNumber,
        abhaAddress,
        phrAddresses,
        fromAbhaVerification, // Include PHR addresses
      };

      // Add optional fields if they exist
      if (name) details.name = name;
      if (gender) details.gender = gender;
      if (yearOfBirth) details.yearOfBirth = yearOfBirth;
      if (dayOfBirth) details.dayOfBirth = dayOfBirth;
      if (monthOfBirth) details.monthOfBirth = monthOfBirth;
      if (dob) details.dob = dob;

      // If we have day, month, and year, construct a proper date string
      if (yearOfBirth && dayOfBirth && monthOfBirth) {
        // Format as DD-MM-YYYY for consistency with the ABHA API
        details.dob = `${dayOfBirth}-${monthOfBirth}-${yearOfBirth}`;
      } else if (yearOfBirth) {
        // If we only have the year, set it as 14-08-YYYY for the specific case
        details.dob = `${yearOfBirth}`;
      }

      if (phone) details.phone = phone;
      if (email) details.email = email;
      if (address) details.address = address;
      if (city) details.city = city;
      if (state) details.state = state;
      if (pincode) details.pincode = pincode;
      if (token) details.token = token;
      if (fromAbhaVerification)
        details.fromAbhaVerification = fromAbhaVerification;

      // DEBUG: Log details object before passing to form
      console.log("=== ABHA DETAILS OBJECT DEBUG ===");
      console.log("fromAbhaVerification value:", fromAbhaVerification);
      console.log("fromAbhaVerification truthy check:", !!fromAbhaVerification);
      console.log("Details object:", details);
      console.log(
        "Details.fromAbhaVerification:",
        details.fromAbhaVerification,
      );

      setAbhaDetails(details);
      console.log("ABHA details from URL:", details);
    }

    if (autoCreate === "true" && verified === "true") {
      setShowEmailAlert(true);
      setAbhaVerified(true);
    }
  }, [searchParams]);

  const handleSuccess = (patientId: string) => {
    router.push(`/patients/${patientId}`);
  };
  console.log("ABHA details:", abhaDetails);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/patients")}
            className="mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Register New Patient</h1>
        </div>
      </div>

      {/* {showEmailAlert && (
        <Alert className="bg-blue-50 border-blue-200">
          <Mail className="h-4 w-4 text-blue-600" />
          <AlertTitle className="text-blue-800">Email Recommended</AlertTitle>
          <AlertDescription className="text-blue-700">
            ABHA verification successful!
          </AlertDescription>
        </Alert>
      )} */}

      <PatientRegistrationForm
        onSuccess={handleSuccess}
        abhaVerified={abhaVerified}
        abhaDetails={abhaDetails}
      />
    </div>
  );
}
