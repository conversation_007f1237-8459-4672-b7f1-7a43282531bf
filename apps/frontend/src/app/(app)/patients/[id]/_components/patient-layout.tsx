"use client";

import { ReactNode } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Patient, AbhaProfile } from "@prisma/client";
import { cn } from "@/lib/utils";
import { formatGenderDisplay } from "@/lib/gender-utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  User,
  CalendarDays,
  FileText,
  Activity,
  Pill,
  FileCheck,
  AlertTriangle,
  Syringe,
  File,
} from "lucide-react";

interface PatientWithAbha extends Patient {
  abhaProfile?: AbhaProfile | null;
}

interface PatientLayoutProps {
  patient?: PatientWithAbha;
  patientId?: string;
  children: ReactNode;
  title: string;
  description?: string;
}

export function PatientLayout({
  patient,
  patientId: propPatientId,
  children,
  title,
  description,
}: PatientLayoutProps) {
  const pathname = usePathname();
  const patientId = patient?.id || propPatientId || pathname?.split("/")[2];

  const tabs = [
    {
      id: "details",
      label: "Details",
      href: `/patients/${patientId}`,
      icon: <User className="h-4 w-4 mr-2" />,
    },
    {
      id: "appointments",
      label: "Appointments",
      href: `/patients/${patientId}/appointments`,
      icon: <CalendarDays className="h-4 w-4 mr-2" />,
    },
    {
      id: "consultations",
      label: "Consultations",
      href: `/patients/${patientId}/consultations`,
      icon: <FileText className="h-4 w-4 mr-2" />,
    },
    {
      id: "vitals",
      label: "Vitals",
      href: `/patients/${patientId}/vitals`,
      icon: <Activity className="h-4 w-4 mr-2" />,
    },
    {
      id: "allergies",
      label: "Allergies",
      href: `/patients/${patientId}/allergies`,
      icon: <AlertTriangle className="h-4 w-4 mr-2" />,
    },
    {
      id: "immunizations",
      label: "Immunizations",
      href: `/patients/${patientId}/immunizations`,
      icon: <Syringe className="h-4 w-4 mr-2" />,
    },
    {
      id: "documents",
      label: "Documents",
      href: `/patients/${patientId}/documents`,
      icon: <File className="h-4 w-4 mr-2" />,
    },
    {
      id: "prescriptions",
      label: "Prescriptions",
      href: `/patients/${patientId}/prescriptions`,
      icon: <Pill className="h-4 w-4 mr-2" />,
    },
    {
      id: "abha",
      label: "ABHA",
      href: `/patients/${patientId}/abha`,
      icon: <FileCheck className="h-4 w-4 mr-2" />,
    },
  ];

  const activeTab = tabs.find((tab) => pathname === tab.href)?.id || "details";

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {patient
              ? `${patient.firstName} ${patient.lastName}`
              : `Patient ${patientId}`}
          </h1>
          <p className="text-muted-foreground">
            {patient?.dateOfBirth
              ? new Date(patient.dateOfBirth).toLocaleDateString()
              : ""}
            {patient?.gender ? ` • ${formatGenderDisplay(patient.gender)}` : ""}
            {patient?.abhaProfile?.abhaNumber ? (
              <Badge variant="outline" className="ml-2">
                ABHA: {patient.abhaProfile.abhaNumber}
              </Badge>
            ) : null}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild variant="outline">
            <Link href="/patients">Back to Patients</Link>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} className="space-y-4">
        <TabsList className="grid grid-cols-9 h-auto p-1">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className={cn(
                "flex items-center data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
                pathname === tab.href
                  ? "bg-primary text-primary-foreground"
                  : "",
              )}
              asChild
            >
              <Link href={tab.href} className="flex items-center py-2">
                {tab.icon}
                {tab.label}
              </Link>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </CardHeader>
        <CardContent>{children}</CardContent>
      </Card>
    </div>
  );
}
