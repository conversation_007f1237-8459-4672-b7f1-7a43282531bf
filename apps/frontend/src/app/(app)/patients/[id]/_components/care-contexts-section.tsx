"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";

interface CareContext {
  id: string;
  patientId: string;
  consultationId: string;
  display: string;
  hiTypes: string[];
  createdAt: string;
  updatedAt: string;
}

interface CareContextsSectionProps {
  patientId: string;
  hasAbhaProfile: boolean;
  branchId?: string;
}

export function CareContextsSection({
  patientId,
  hasAbhaProfile,
  branchId,
}: CareContextsSectionProps) {
  const router = useRouter();
  const [careContexts, setCareContexts] = useState<CareContext[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch care contexts
  const fetchCareContexts = async () => {
    try {
      setIsLoading(true);

      // Build the URL with optional branch filter
      let url = `/api/patients/${patientId}/care-contexts`;
      if (branchId) {
        url += `?branchId=${branchId}`;
      }

      const response = await fetch(url);

      if (response.ok) {
        const data = await response.json();
        setCareContexts(data.careContexts || []);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to fetch care contexts");
      }
    } catch (error) {
      console.error("Error fetching care contexts:", error);
      toast.error("Failed to fetch care contexts");
    } finally {
      setIsLoading(false);
    }
  };

  // Load care contexts on component mount
  useEffect(() => {
    if (patientId) {
      fetchCareContexts();
    }
  }, [patientId, branchId]);

  if (!hasAbhaProfile) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Care Contexts</CardTitle>
          <CardDescription>
            ABDM care contexts linked to this patient
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            This patient does not have an ABHA profile. Create or link an ABHA
            profile to enable care context linking.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>Care Contexts</CardTitle>
          <CardDescription>
            ABDM care contexts linked to this patient
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={() =>
              router.push(`/patients/${patientId}/care-contexts/create`)
            }
            size="sm"
          >
            Create Care Context
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
        ) : careContexts.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Display</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead>Health Info Types</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {careContexts.map((context) => (
                <TableRow key={context.id}>
                  <TableCell>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium text-left"
                      onClick={() =>
                        router.push(
                          `/patients/${patientId}/care-contexts/${context.id}`,
                        )
                      }
                    >
                      {context.display}
                    </Button>
                  </TableCell>
                  <TableCell className="font-mono text-xs">
                    {context.consultationId}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {context.hiTypes.map((type) => (
                        <Badge key={type} variant="outline">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    {formatDate(new Date(context.createdAt))}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        router.push(
                          `/patients/${patientId}/care-contexts/${context.id}`,
                        )
                      }
                    >
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-6 space-y-3">
            <p className="text-muted-foreground">
              No care contexts found. You can create a care context manually or
              they will be automatically created when a patient with an ABHA
              profile starts a consultation.
            </p>
            <Button
              variant="outline"
              onClick={() =>
                router.push(`/patients/${patientId}/care-contexts/create`)
              }
            >
              Create Care Context
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
