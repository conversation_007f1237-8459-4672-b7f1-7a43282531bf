"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText } from "lucide-react";
import { PatientLayout } from "../_components/patient-layout";
import {
  DocumentReferenceForm,
  DocumentReferenceList,
} from "@/components/document-reference";
// No need to import getPatientById anymore

export default function PatientDocumentsPage() {
  const params = useParams();
  const patientId = params?.id as string;

  return (
    <PatientLayout
      patientId={patientId}
      title="Documents"
      description="Manage patient documents and reports"
    >
      <DocumentsContent patientId={patientId} />
    </PatientLayout>
  );
}

function DocumentsContent({ patientId }: { patientId: string }) {
  const [activeTab, setActiveTab] = useState("all");
  const [showForm, setShowForm] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleDocumentAdded = () => {
    setShowForm(false);
    setRefreshTrigger((prev) => prev + 1);
    setActiveTab("all");
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <FileText className="h-5 w-5 mr-2 text-primary" />
          <h2 className="text-lg font-medium">Patient Documents</h2>
        </div>
        {!showForm && (
          <Button onClick={() => setShowForm(true)}>Upload New Document</Button>
        )}
      </div>

      {showForm ? (
        <Card>
          <CardHeader>
            <CardTitle>Upload New Document</CardTitle>
          </CardHeader>
          <CardContent>
            <DocumentReferenceForm
              patientId={patientId}
              doctorId="" // This will be filled by the API based on the current user
              onCancel={() => setShowForm(false)}
              onSuccess={handleDocumentAdded}
            />
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">All Documents</TabsTrigger>
            <TabsTrigger value="clinical">Clinical</TabsTrigger>
            <TabsTrigger value="lab">Laboratory</TabsTrigger>
            <TabsTrigger value="radiology">Radiology</TabsTrigger>
            <TabsTrigger value="other">Other</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <DocumentReferenceList
                  patientId={patientId}
                  refreshTrigger={refreshTrigger}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="clinical" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <DocumentReferenceList
                  patientId={patientId}
                  category="clinical-document"
                  refreshTrigger={refreshTrigger}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="lab" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <DocumentReferenceList
                  patientId={patientId}
                  category="laboratory"
                  refreshTrigger={refreshTrigger}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="radiology" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <DocumentReferenceList
                  patientId={patientId}
                  category="radiology"
                  refreshTrigger={refreshTrigger}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="other" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <DocumentReferenceList
                  patientId={patientId}
                  refreshTrigger={refreshTrigger}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
