"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertTriangle } from "lucide-react";
import { PatientLayout } from "../_components/patient-layout";
import {
  AllergyIntoleranceForm,
  AllergyIntoleranceList,
} from "@/components/allergy-intolerance";
// No need to import getPatientById anymore

export default function PatientAllergiesPage() {
  const params = useParams();
  const patientId = params?.id as string;

  return (
    <PatientLayout
      patientId={patientId}
      title="Allergies & Intolerances"
      description="Manage patient allergies and intolerances"
    >
      <AllergiesContent patientId={patientId} />
    </PatientLayout>
  );
}

function AllergiesContent({ patientId }: { patientId: string }) {
  const [activeTab, setActiveTab] = useState("list");
  const [showForm, setShowForm] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleAllergyAdded = () => {
    setShowForm(false);
    setRefreshTrigger((prev) => prev + 1);
    setActiveTab("list");
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2 text-amber-500" />
          <h2 className="text-lg font-medium">
            Patient Allergies & Intolerances
          </h2>
        </div>
        {!showForm && (
          <Button onClick={() => setShowForm(true)}>
            Add New Allergy/Intolerance
          </Button>
        )}
      </div>

      {showForm ? (
        <Card>
          <CardHeader>
            <CardTitle>Add New Allergy/Intolerance</CardTitle>
          </CardHeader>
          <CardContent>
            <AllergyIntoleranceForm
              patientId={patientId}
              doctorId="" // This will be filled by the API based on the current user
              onCancel={() => setShowForm(false)}
              onSuccess={handleAllergyAdded}
            />
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="list">Current Allergies</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <AllergyIntoleranceList
                  patientId={patientId}
                  refreshTrigger={refreshTrigger}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  Allergy history and resolved allergies will be shown here
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
