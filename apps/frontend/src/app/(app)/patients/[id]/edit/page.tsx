"use client";

import { useRouter } from "next/navigation";
import { usePatient } from "@/hooks/use-patient";
import { PatientRegistrationForm } from "@/components/patient-registration-form";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function EditPatientPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { patient, isLoading, error } = usePatient(params.id);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">Loading patient details...</p>
        </div>
      </div>
    );
  }

  if (error || !patient) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <h3 className="text-lg font-medium">Error loading patient</h3>
        <p className="text-muted-foreground mt-1">
          {error || "Patient not found"}
        </p>
        <Button onClick={() => router.push("/patients")} className="mt-4">
          Back to Patients
        </Button>
      </div>
    );
  }

  const handleSuccess = (patientId: string) => {
    router.push(`/patients/${patientId}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push(`/patients/${patient.id}`)}
          className="mr-2"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold">Edit Patient</h1>
      </div>

      <PatientRegistrationForm
        patient={patient}
        mode="edit"
        onSuccess={handleSuccess}
      />
    </div>
  );
}
