"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { ConsentRequestForm } from "@/components/consent-request-form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Loader2 } from "lucide-react";
import { Fetch } from "@/services/fetch";

interface PatientConsentsRequestPageProps {
  params: {
    id: string;
  };
}

export default function PatientConsentsRequestPage({
  params,
}: PatientConsentsRequestPageProps) {
  const { id } = params;
  const router = useRouter();
  const [patient, setPatient] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch patient details
  useEffect(() => {
    const fetchPatient = async () => {
      try {
        setLoading(true);
        const response = await Fetch.get(`/api/patients/${id}`);

        if (response && response.patient) {
          setPatient(response.patient);
        } else {
          setError("Patient not found");
        }
      } catch (error) {
        console.error("Error fetching patient:", error);
        setError("Failed to load patient details");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchPatient();
    }
  }, [id]);

  // Handle consent request success
  const handleConsentRequestSuccess = (consentId: string) => {
    router.push(`/patients/${id}/consents/details/${consentId}`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error || !patient) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{error || "Patient not found"}</p>
      </div>
    );
  }

  return (
    <>
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Request Consent</h1>
        <p className="text-muted-foreground">
          Request new consent for {patient.firstName} {patient.lastName}
        </p>
      </div>

      <div className="space-y-6">
        {!patient.abhaProfile?.abhaAddress ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>ABHA Address Required</AlertTitle>
            <AlertDescription>
              This patient does not have an ABHA address. An ABHA address is
              required to request consent.
            </AlertDescription>
          </Alert>
        ) : (
          <ConsentRequestForm
            patientId={patient.id}
            onSuccess={handleConsentRequestSuccess}
          />
        )}
      </div>
    </>
  );
}
