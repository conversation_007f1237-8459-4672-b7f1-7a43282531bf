"use client";

import { useRouter } from "next/navigation";
import { ConsentList } from "@/components/consent-list";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface ConsentManagementProps {
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    abhaProfile?: {
      abhaNumber: string;
      abhaAddress?: string | null;
    } | null;
  };
}

export function ConsentManagement({ patient }: ConsentManagementProps) {
  const router = useRouter();

  // Handle consent selection
  const handleSelectConsent = (consentId: string) => {
    router.push(`/patients/${patient.id}/consents/details/${consentId}`);
  };

  // Handle new consent request
  const handleNewConsentRequest = () => {
    router.push(`/patients/${patient.id}/consents/request`);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Consent List</h2>
          <p className="text-sm text-muted-foreground">
            View and manage consent requests for this patient
          </p>
        </div>
        <Button
          onClick={handleNewConsentRequest}
          disabled={!patient.abhaProfile?.abhaAddress}
        >
          <Plus className="h-4 w-4 mr-2" />
          Request New Consent
        </Button>
      </div>

      <ConsentList
        patientId={patient.id}
        onSelectConsent={handleSelectConsent}
      />
    </div>
  );
}
