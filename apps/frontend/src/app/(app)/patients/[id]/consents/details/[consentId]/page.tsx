import { Metadata } from "next";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { ConsentDetailsWithData } from "../../consent-details-with-data";

export const metadata: Metadata = {
  title: "Patient Consents - Details",
  description: "View consent details and health record data",
};

interface PatientConsentDetailsPageProps {
  params: {
    id: string;
    consentId: string;
  };
}

export default async function PatientConsentDetailsPage({
  params,
}: PatientConsentDetailsPageProps) {
  const { id, consentId } = params;

  // Fetch patient details
  const patient = await prisma.patient.findUnique({
    where: { id },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    notFound();
  }

  // Fetch consent details
  const consent = await prisma.consent.findUnique({
    where: { id: consentId },
    include: {
      patient: true,
    },
  });

  if (!consent || consent.patientId !== id) {
    notFound();
  }

  return (
    <>
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Consent Details</h1>
        <p className="text-muted-foreground">
          View consent details and health record data for {patient.firstName}{" "}
          {patient.lastName}
        </p>
      </div>

      <ConsentDetailsWithData consentId={consentId} patientId={id} />
    </>
  );
}
