"use client";

import { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ConsentDetails } from "@/components/consent-details";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  Download,
  AlertTriangle,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import {
  isConsentDataAccessible,
  isConsentExpired,
} from "@/utils/consent-utils";
import { getBundleTypeDisplayName } from "@/lib/fhir/bundle-display-utils";
import { detectBundleType } from "@/lib/fhir/bundle-type-detector";

interface ConsentDetailsWithDataProps {
  consentId: string;
  patientId: string;
}

interface FhirBundle {
  id: string;
  bundleId: string;
  bundleType: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  consentId: string;
  transactionId: string;
  packageChecksum: string;
  bundleJson: any;
}

export function ConsentDetailsWithData({
  consentId,
  patientId,
}: ConsentDetailsWithDataProps) {
  const [fhirBundles, setFhirBundles] = useState<FhirBundle[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedBundle, setSelectedBundle] = useState<FhirBundle | null>(null);

  const [pullDataLoading, setPullDataLoading] = useState(false);

  const [consentStatus, setConsentStatus] = useState<string | null>(null);
  const [, setConsentStatusLoading] = useState(false);
  const [consentData, setConsentData] = useState<any>(null);
  const [lastPullTime, setLastPullTime] = useState<Date | null>(null);
  const [cooldownRemaining, setCooldownRemaining] = useState<number>(0);
  const [currentBundleIndex, setCurrentBundleIndex] = useState<number>(0);

  // Helper function to get the correct bundle type from FHIR JSON
  const getBundleTypeFromFhir = (bundle: FhirBundle): string => {
    if (bundle.bundleJson) {
      return detectBundleType(bundle.bundleJson);
    }
    return bundle.bundleType; // fallback to database value
  };

  // Check consent status
  const checkConsentStatus = async () => {
    try {
      setConsentStatusLoading(true);
      const response = await Fetch.post("/api/abdm/consent/status", {
        consentId,
      });

      if (response && response.success) {
        setConsentStatus(response.status ? String(response.status) : null);
        return response.status;
      } else {
        console.error("Failed to check consent status:", response?.error);
        return null;
      }
    } catch (error) {
      console.error("Error checking consent status:", error);
      return null;
    } finally {
      setConsentStatusLoading(false);
    }
  };

  // Fetch consent data to check accessibility
  const fetchConsentData = async () => {
    try {
      const response = await Fetch.get(
        `/api/abdm/consent/by-id?id=${consentId}`,
      );
      if (response.success && response.data) {
        setConsentData(response.data);
      }
    } catch (error) {
      console.error("Error fetching consent data:", error);
    }
  };

  // Function to update consent status to expired
  const updateConsentToExpired = async () => {
    try {
      const response = await Fetch.post(
        `/api/abdm/consent/${consentId}/update-status`,
        {
          status: "EXPIRED",
          reason: "Consent automatically expired based on data erase date",
        },
      );

      if (response.success) {
        // Refetch consent data to get the updated status
        await fetchConsentData();
        toast.success("Consent status updated to expired");
      } else {
        console.error("Failed to update consent status:", response.error);
      }
    } catch (error) {
      console.error("Error updating consent status:", error);
    }
  };

  // Load FHIR bundles for this consent
  const loadFhirBundles = async () => {
    try {
      setLoading(true);
      const response = await Fetch.get(
        `/api/abdm/health-record/bundles?patientId=${patientId}&consentId=${consentId}`,
      );

      if (response && response.success) {
        setFhirBundles(response.data || []);
      } else {
        console.error("Failed to load FHIR bundles:", response?.error);
      }
    } catch (error) {
      console.error("Error loading FHIR bundles:", error);
    } finally {
      setLoading(false);
    }
  };

  // Load initial consent status from database
  const loadInitialConsentStatus = async () => {
    try {
      setConsentStatusLoading(true);

      // First get the consent status from database
      const response = await Fetch.get(`/api/abdm/consent/${consentId}`);

      if (response && response.success && response.data) {
        const dbStatus = response.data.status;
        setConsentStatus(dbStatus);
        console.log("Initial consent status from DB:", dbStatus);

        // If status is not REVOKED, also check with ABDM API for latest status
        if (dbStatus !== "REVOKED") {
          await checkConsentStatus();
        }
      } else {
        // Fallback to API check if database fetch fails
        await checkConsentStatus();
      }
    } catch (error) {
      console.error("Error loading initial consent status:", error);
      // Fallback to API check
      await checkConsentStatus();
    } finally {
      setConsentStatusLoading(false);
    }
  };

  // Load bundles and check consent status on mount and when consentId changes
  useEffect(() => {
    if (consentId) {
      loadInitialConsentStatus();
      loadFhirBundles();
      fetchConsentData();
    }
  }, [consentId, patientId]);

  // Check if consent is expired and update status automatically
  useEffect(() => {
    if (consentData && consentData.status === "GRANTED") {
      const expired = isConsentExpired(consentData);
      if (expired) {
        updateConsentToExpired();
      }
    }
  }, [consentData]);

  // View bundle details
  // Navigation functions
  const navigateToPreviousBundle = () => {
    if (fhirBundles.length > 0 && currentBundleIndex > 0) {
      const newIndex = currentBundleIndex - 1;
      setCurrentBundleIndex(newIndex);
      setSelectedBundle(fhirBundles[newIndex]);
    }
  };

  const navigateToNextBundle = () => {
    if (fhirBundles.length > 0 && currentBundleIndex < fhirBundles.length - 1) {
      const newIndex = currentBundleIndex + 1;
      setCurrentBundleIndex(newIndex);
      setSelectedBundle(fhirBundles[newIndex]);
    }
  };

  // Auto-select first bundle when bundles are loaded
  useEffect(() => {
    if (fhirBundles.length > 0 && !selectedBundle) {
      setSelectedBundle(fhirBundles[0]);
      setCurrentBundleIndex(0);
    }
  }, [fhirBundles, selectedBundle]);

  // Cooldown management
  const COOLDOWN_MINUTES = 2;
  const COOLDOWN_MS = COOLDOWN_MINUTES * 60 * 1000;

  // Check if cooldown is active
  const isCooldownActive = () => {
    if (!lastPullTime) return false;
    const timeSinceLastPull = Date.now() - lastPullTime.getTime();
    return timeSinceLastPull < COOLDOWN_MS;
  };

  // Update cooldown timer - optimized to prevent unnecessary re-renders
  useEffect(() => {
    if (!lastPullTime) return;

    const updateCooldown = () => {
      const timeSinceLastPull = Date.now() - lastPullTime.getTime();
      const remaining = Math.max(0, COOLDOWN_MS - timeSinceLastPull);
      const newCooldownRemaining = Math.ceil(remaining / 1000);

      // Only update state if the value actually changed
      setCooldownRemaining(prev => {
        if (prev !== newCooldownRemaining) {
          return newCooldownRemaining;
        }
        return prev;
      });

      return remaining > 0;
    };

    // Initial update
    const shouldContinue = updateCooldown();

    if (!shouldContinue) {
      setCooldownRemaining(0);
      return;
    }

    const interval = setInterval(() => {
      const shouldContinue = updateCooldown();
      if (!shouldContinue) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [lastPullTime]);

  // Manual pull data function
  const handlePullData = async () => {
    // Check cooldown
    if (isCooldownActive()) {
      const remainingSeconds = Math.ceil(
        (COOLDOWN_MS - (Date.now() - lastPullTime!.getTime())) / 1000,
      );
      toast.error(
        `Please wait ${Math.ceil(remainingSeconds / 60)} minutes before requesting data again`,
      );
      return;
    }

    try {
      setPullDataLoading(true);

      const response = await Fetch.post("/api/abdm/health-record/fetch", {
        consentId: consentId,
        patientId: patientId,
      });

      if (response && response.success) {
        toast.success("Health records fetch initiated successfully");

        // Set last pull time for cooldown
        const now = new Date();
        setLastPullTime(now);
        setCooldownRemaining(COOLDOWN_MINUTES * 60);

        // Wait for 5 seconds then refresh bundles
        setTimeout(() => {
          loadFhirBundles();
        }, 5000);
      } else {
        toast.error(
          response?.error || "Failed to initiate health records fetch",
        );
      }
    } catch (error) {
      console.error("Error pulling health records:", error);
      toast.error("Error pulling health records");
    } finally {
      setPullDataLoading(false);
    }
  };

  // Import the new bundle viewer component
  const BundleViewer = dynamic(
    () =>
      import("@/components/fhir/bundle-viewer").then((mod) => ({
        default: mod.BundleViewer,
      })),
    {
      ssr: false,
      loading: () => (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ),
    },
  );

  // Render FHIR data using the new bundle viewer
  const renderFhirData = (bundle: FhirBundle) => {
    return (
      <BundleViewer
        bundle={bundle}
        currentIndex={currentBundleIndex}
        totalBundles={fhirBundles.length}
      />
    );
  };

  // Debug log for tab visibility - commented out to prevent unnecessary re-renders
  // console.log(
  //   "Rendering tabs with consentStatus:",
  //   consentStatus,
  //   "- Data tab visible:",
  //   consentStatus !== "REVOKED",
  // );

  // Check if data is accessible (not revoked and not expired)
  const isDataAccessible = consentData
    ? isConsentDataAccessible(consentData)
    : consentStatus !== "REVOKED";

  // Determine the reason why data is not accessible
  // Use consentData.status if available, otherwise fall back to consentStatus
  const effectiveStatus = consentData?.status || consentStatus;

  const getDataInaccessibilityReason = () => {
    if (effectiveStatus === "REVOKED") return "revoked";
    if (effectiveStatus === "DENIED") return "denied";
    if (effectiveStatus === "REQUESTED") return "pending";
    if (consentData && isConsentExpired(consentData)) return "expired";
    return "unavailable";
  };

  const inaccessibilityReason = getDataInaccessibilityReason();

  // Debug logging - commented out to prevent unnecessary re-renders
  // console.log("UI Debug - Data accessibility:", {
  //   consentStatus,
  //   effectiveStatus,
  //   isDataAccessible,
  //   inaccessibilityReason,
  //   consentData: consentData
  //     ? {
  //         id: consentData.id,
  //         status: consentData.status,
  //         isExpired: isConsentExpired(consentData),
  //       }
  //     : null,
  //   fhirBundlesCount: fhirBundles.length,
  //   loading,
  // });

  return (
    <Tabs defaultValue="overview" className="w-full">
      <TabsList
        className={`grid w-full ${!isDataAccessible ? "grid-cols-1" : "grid-cols-2"}`}
      >
        <TabsTrigger value="overview">Overview</TabsTrigger>
        {isDataAccessible && <TabsTrigger value="data">Data</TabsTrigger>}
      </TabsList>

      <TabsContent value="overview" className="mt-6">
        <div className="space-y-6">
          <ConsentDetails consentId={consentId} />

          {/* Bundle Types Received */}
          {isDataAccessible && fhirBundles.length > 0 && (
            <Card className="border-2 border-green-100 bg-green-50">
              <CardHeader>
                <CardTitle className="text-lg flex items-center text-green-800">
                  <Download className="h-5 w-5 mr-2" />
                  Health Records Received
                </CardTitle>
                <CardDescription className="text-green-700">
                  The following bundle types have been received from ABDM
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {Array.from(
                    new Set(fhirBundles.map((bundle) => getBundleTypeFromFhir(bundle))),
                  ).map((bundleType) => {
                    const bundleCount = fhirBundles.filter(
                      (bundle) => getBundleTypeFromFhir(bundle) === bundleType,
                    ).length;
                    const displayName = getBundleTypeDisplayName(bundleType);
                    return (
                      <div
                        key={bundleType}
                        className="inline-flex items-center px-3 py-2 rounded-lg bg-green-100 border border-green-200"
                      >
                        <span className="text-sm font-medium text-green-800">
                          {displayName}
                        </span>
                        {bundleCount > 1 && (
                          <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-green-200 text-green-800 rounded-full">
                            {bundleCount}
                          </span>
                        )}
                      </div>
                    );
                  })}
                </div>
                <p className="text-sm text-green-600 mt-3">
                  Total: {fhirBundles.length} bundle
                  {fhirBundles.length !== 1 ? "s" : ""} received
                </p>
              </CardContent>
            </Card>
          )}

          {/* Consent Status Alert */}
          {!isDataAccessible && (
            <Card
              className={`border-2 ${
                inaccessibilityReason === "pending"
                  ? "border-yellow-200 bg-yellow-50"
                  : "border-red-200 bg-red-50"
              }`}
            >
              <CardHeader>
                <CardTitle
                  className={`text-lg flex items-center ${
                    inaccessibilityReason === "pending"
                      ? "text-yellow-800"
                      : "text-red-800"
                  }`}
                >
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  {inaccessibilityReason === "revoked" && "Consent Revoked"}
                  {inaccessibilityReason === "denied" && "Consent Denied"}
                  {inaccessibilityReason === "pending" && "Consent Pending"}
                  {inaccessibilityReason === "expired" && "Consent Expired"}
                  {inaccessibilityReason === "unavailable" &&
                    "Data Unavailable"}
                </CardTitle>
                <CardDescription
                  className={
                    inaccessibilityReason === "pending"
                      ? "text-yellow-700"
                      : "text-red-700"
                  }
                >
                  {inaccessibilityReason === "revoked" &&
                    "This consent has been revoked. Health record data access is restricted."}
                  {inaccessibilityReason === "denied" &&
                    "This consent has been denied. Health record data access is not available."}
                  {inaccessibilityReason === "pending" &&
                    "This consent is still pending approval. Health record data will be accessible once the consent is granted."}
                  {inaccessibilityReason === "expired" &&
                    "This consent has expired. Health record data access is no longer available."}
                  {inaccessibilityReason === "unavailable" &&
                    "Health record data is currently not accessible."}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p
                  className={`text-sm ${
                    inaccessibilityReason === "pending"
                      ? "text-yellow-600"
                      : "text-red-600"
                  }`}
                >
                  The "Data" tab is disabled to comply with HIU requirements
                  when consent is {inaccessibilityReason}.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {isDataAccessible && (
        <TabsContent value="data" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={navigateToPreviousBundle}
                  disabled={
                    currentBundleIndex === 0 || fhirBundles.length === 0
                  }
                  className="flex items-center"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                <div className="text-center">
                  {fhirBundles.length > 0 && selectedBundle && (
                    <div>
                      <CardTitle className="text-lg">
                        {getBundleTypeDisplayName(getBundleTypeFromFhir(selectedBundle))}{" "}
                        Bundle
                      </CardTitle>
                      <CardDescription>
                        {currentBundleIndex + 1} of {fhirBundles.length} bundles
                      </CardDescription>
                    </div>
                  )}
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={navigateToNextBundle}
                  disabled={
                    currentBundleIndex === fhirBundles.length - 1 ||
                    fhirBundles.length === 0
                  }
                  className="flex items-center"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : fhirBundles.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    No health record data found
                  </p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Data will appear here when received from ABDM
                  </p>
                  <div className="mt-4 space-y-3">
                    <Button
                      onClick={handlePullData}
                      disabled={pullDataLoading || isCooldownActive()}
                      variant="outline"
                      className="w-full sm:w-auto"
                    >
                      {pullDataLoading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Pulling Data...
                        </>
                      ) : isCooldownActive() ? (
                        <>
                          <Download className="h-4 w-4 mr-2" />
                          Pull Health Records (
                          {Math.floor(cooldownRemaining / 60)}:
                          {(cooldownRemaining % 60).toString().padStart(2, "0")}
                          )
                        </>
                      ) : (
                        <>
                          <Download className="h-4 w-4 mr-2" />
                          Pull Health Records
                        </>
                      )}
                    </Button>
                    {isCooldownActive() && (
                      <p className="text-sm text-orange-600">
                        ⏱️ Cooldown active: Please wait{" "}
                        {Math.floor(cooldownRemaining / 60)} minutes and{" "}
                        {cooldownRemaining % 60} seconds before requesting again
                      </p>
                    )}
                    <p className="text-sm text-muted-foreground">
                      This will request health records from ABDM for this
                      consent. Data will appear above once received and
                      decrypted.
                      {!isCooldownActive() &&
                        " (2-minute cooldown applies after each request)"}
                    </p>
                  </div>
                </div>
              ) : consentStatus === "REVOKED" ? (
                <div className="text-center py-8">
                  <div className="bg-red-100 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                    <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-red-800 mb-2">
                      Health Records Hidden
                    </h3>
                    <p className="text-sm text-red-700 mb-4">
                      FHIR bundles and health record data are hidden because
                      this consent has been revoked.
                    </p>
                    <div className="text-xs text-red-600 space-y-1">
                      <p>
                        • {fhirBundles.length} FHIR bundle(s) exist but are not
                        accessible
                      </p>
                      <p>
                        • Data viewing is restricted per HIU compliance
                        requirements
                      </p>
                      <p>
                        • Contact the patient to request new consent if needed
                      </p>
                    </div>
                  </div>
                </div>
              ) : selectedBundle ? (
                <div className="max-h-[800px] overflow-y-auto">
                  {renderFhirData(selectedBundle)}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No bundle selected</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      )}
    </Tabs>
  );
}
