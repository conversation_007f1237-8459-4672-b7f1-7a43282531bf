import { Metadata } from "next";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { ConsentManagement } from "../consent-management";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export const metadata: Metadata = {
  title: "Patient Consents - List",
  description: "View patient consent list",
};

interface PatientConsentsListPageProps {
  params: {
    id: string;
  };
}

export default async function PatientConsentsListPage({
  params,
}: PatientConsentsListPageProps) {
  const { id } = params;

  // Fetch patient details with ABHA profile
  const patient = await prisma.patient.findUnique({
    where: { id },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    notFound();
  }

  return (
    <>
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">
          Consent Management
        </h1>
        <p className="text-muted-foreground">
          Manage consent requests for {patient.firstName} {patient.lastName}
        </p>
      </div>

      <div className="space-y-6">
        {!patient.abhaProfile?.abhaAddress && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>ABHA Address Required</AlertTitle>
            <AlertDescription>
              This patient does not have an ABHA address. An ABHA address is
              required to request consent.
            </AlertDescription>
          </Alert>
        )}

        <ConsentManagement
          patient={{
            id: patient.id,
            firstName: patient.firstName,
            lastName: patient.lastName,
            abhaProfile: patient.abhaProfile
              ? {
                  abhaNumber: patient.abhaProfile.abhaNumber || "",
                  abhaAddress: patient.abhaProfile.abhaAddress,
                }
              : null,
          }}
        />
      </div>
    </>
  );
}
