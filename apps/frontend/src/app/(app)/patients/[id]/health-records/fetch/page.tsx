import { Metada<PERSON> } from "next";
// import { prisma } from "@/lib/prisma";
// import { notFound } from "next/navigation";

import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Health Records",
  description: "Fetch and view health records from ABDM",
};

// async function getPatient(id: string) {
//   const patient = await prisma.patient.findUnique({
//     where: {
//       id,
//     },
//     include: {
//       abhaProfile: true,
//     },
//   });

//   if (!patient) {
//     notFound();
//   }

//   return patient;
// }

export default async function PatientHealthRecordsFetchPage({
  params,
}: {
  params: { id: string };
}) {
  // Redirect to consents page instead
  redirect(`/patients/${params.id}/consents`);
}
