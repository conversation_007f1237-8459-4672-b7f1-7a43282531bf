import { Metadata } from "next";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { PatientLayout } from "../../_components/patient-layout";
import { HealthRecordTimeline } from "@/components/health-records/health-record-timeline";

export const metadata: Metadata = {
  title: "Health Record Timeline",
  description: "View health records in chronological order",
};

async function getPatient(id: string) {
  const patient = await prisma.patient.findUnique({
    where: {
      id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    notFound();
  }

  return patient;
}

export default async function PatientHealthRecordTimelinePage({
  params,
}: {
  params: { id: string };
}) {
  const patient = await getPatient(params.id);

  return (
    <PatientLayout
      patient={patient}
      title="Health Record Timeline"
      description="View health records in chronological order"
    >
      <div className="space-y-6">
        <HealthRecordTimeline patientId={patient.id} />
      </div>
    </PatientLayout>
  );
}
