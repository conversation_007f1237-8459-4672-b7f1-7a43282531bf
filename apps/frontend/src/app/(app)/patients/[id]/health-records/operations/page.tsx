import { Metadata } from "next";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { PatientLayout } from "../../_components/patient-layout";
import { HealthRecordOperationsList } from "@/components/health-records/health-record-operations-list";

export const metadata: Metadata = {
  title: "Patient Health Record Operations",
  description: "View health record operations for a patient",
};

async function getPatient(id: string) {
  const patient = await prisma.patient.findUnique({
    where: {
      id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    notFound();
  }

  return patient;
}

export default async function PatientHealthRecordOperationsPage({
  params,
}: {
  params: { id: string };
}) {
  const patient = await getPatient(params.id);

  return (
    <PatientLayout
      patient={patient}
      title="Health Record Operations"
      description="View health record operations for this patient"
    >
      <div className="space-y-6">
        <HealthRecordOperationsList patientId={patient.id} limit={50} />
      </div>
    </PatientLayout>
  );
}
