import { Metadata } from "next";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import { PatientLayout } from "../../../_components/patient-layout";
import { HealthRecordDetailViewer } from "@/components/health-records/health-record-detail-viewer";

export const metadata: Metadata = {
  title: "Health Record Details",
  description: "View detailed health record information",
};

async function getPatient(id: string) {
  const patient = await prisma.patient.findUnique({
    where: {
      id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    notFound();
  }

  return patient;
}

export default async function PatientHealthRecordDetailPage({
  params,
}: {
  params: { id: string; resourceId: string };
}) {
  const patient = await getPatient(params.id);

  return (
    <PatientLayout
      patient={patient}
      title="Health Record Details"
      description="View detailed health record information"
    >
      <div className="space-y-6">
        <HealthRecordDetailViewer resourceId={params.resourceId} />
      </div>
    </PatientLayout>
  );
}
