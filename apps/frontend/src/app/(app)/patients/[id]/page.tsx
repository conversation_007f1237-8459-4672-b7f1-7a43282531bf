"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { usePatient } from "@/hooks/use-patient";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import {
  ArrowLeft,
  User,
  Phone,
  Heart,
  AlertCircle,
  FileText,
  ShieldCheck,
  Pencil,
  IdCard,
  Shield,
  Link as LinkIcon,
  Trash2,
  Download,
} from "lucide-react";
import { formatDate } from "@/lib/utils";
import { formatGenderDisplay } from "@/lib/gender-utils";
import { toast } from "sonner";
import { deletePatient } from "@/services/patient-service";
import { ConsultationTimeline } from "@/components/consultations/consultation-timeline";

export default function PatientDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { patient, isLoading, error } = usePatient(params.id);
  const [activeTab, setActiveTab] = useState("personal");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle patient deletion
  const handleDeletePatient = async () => {
    if (!patient) return;

    setIsDeleting(true);
    try {
      const response = await deletePatient(patient.id);

      if (!response.success) {
        throw new Error(response.error || "Failed to delete patient");
      }

      toast.success("Patient deleted successfully");
      router.push("/patients");
    } catch (error) {
      console.error("Error deleting patient:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete patient",
      );
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  // Handle sending PHR app link
  const handleSendPhrLink = async () => {
    if (!patient) return;

    try {
      // Get the primary branch ID or use the current branch
      const branchId = patient.primaryBranchId || (patient as any)?.branch?.id;

      if (!branchId) {
        toast.error("No branch associated with this patient");
        return;
      }

      // Call the API to send the PHR app link
      const response = await fetch(
        `/api/patients/${patient.id}/send-phr-link`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ branchId }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to send PHR app link");
      }

      toast.success("PHR app link sent successfully");
    } catch (error) {
      console.error("Error sending PHR app link:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to send PHR app link",
      );
    }
  };

  // Calculate age if patient exists
  const calculateAge = () => {
    if (!patient) return "";

    const birthDate = new Date(patient.dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return `${age} years`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">Loading patient details...</p>
        </div>
      </div>
    );
  }

  if (error || !patient) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <AlertCircle className="h-12 w-12 text-destructive mb-4" />
        <h3 className="text-lg font-medium">Error loading patient</h3>
        <p className="text-muted-foreground mt-1">
          {error || "Patient not found"}
        </p>
        <Button onClick={() => router.push("/patients")} className="mt-4">
          Back to Patients
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Alert banner for patients without email */}
      {!patient.email && (patient as any).abhaProfile && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md shadow-sm">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Email Required
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  This patient was created from ABHA verification but doesn't
                  have an email address. Please add an email address to enable
                  full functionality.
                </p>
                <div className="mt-3">
                  <Button
                    onClick={() => router.push(`/patients/${patient.id}/edit`)}
                    size="sm"
                    className="bg-yellow-500 hover:bg-yellow-600 text-white"
                  >
                    Add Email
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between bg-card rounded-lg p-4 shadow-sm border">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/patients")}
            className="mr-3"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Patient Details</h1>
            <p className="text-sm text-muted-foreground">
              View and manage patient information
            </p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => router.push(`/patients/${patient.id}/consents`)}
            className="h-9"
          >
            <Shield className="mr-2 h-4 w-4" />
            Consents
          </Button>
          <Button
            variant="outline"
            onClick={() => handleSendPhrLink()}
            className="h-9"
          >
            <Download className="mr-2 h-4 w-4" />
            Send PHR App
          </Button>
          <Button
            onClick={() => router.push(`/patients/${patient.id}/edit`)}
            className="h-9"
          >
            <Pencil className="mr-2 h-4 w-4" />
            Edit Patient
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9 text-red-500 hover:text-red-700 hover:bg-red-50"
            onClick={() => setShowDeleteDialog(true)}
            title="Delete Patient"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Card className="overflow-hidden border-0 shadow-md">
        <CardHeader className="pb-4 bg-gradient-to-r from-primary/5 to-secondary/5">
          <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
            <div className="flex items-start space-x-4">
              <div className="bg-primary/10 rounded-full p-3 hidden md:flex">
                <User className="h-8 w-8 text-primary" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold">
                  {patient.firstName} {patient.lastName}
                </CardTitle>
                <div className="flex flex-wrap items-center gap-2 mt-2">
                  <Badge
                    variant={
                      patient.status === "active" ? "default" : "secondary"
                    }
                    className="px-2 py-1 text-xs font-medium"
                  >
                    {patient.status}
                  </Badge>
                  {/* Patient ID Badge */}
                  <Badge
                    variant="outline"
                    className="px-2 py-1 text-xs font-medium"
                  >
                    Patient ID: {patient.id}
                  </Badge>
                </div>
              </div>
            </div>
            <div className="text-right bg-card/80 p-3 rounded-lg shadow-sm border border-border/50 text-sm">
              <div className="flex flex-col space-y-1">
                <p className="text-muted-foreground flex items-center justify-end">
                  <span className="inline-block w-4 h-4 mr-1 rounded-full bg-primary/20"></span>
                  Registered on {formatDate(patient.registrationDate)}
                </p>

                <p className="flex items-center justify-end">
                  <span className="inline-block w-4 h-4 mr-1 rounded-full bg-secondary/20"></span>
                  Branch: {(patient as any)?.branch?.name}
                </p>
                <p className="flex items-center justify-end">
                  <span className="inline-block w-4 h-4 mr-1 rounded-full bg-green-500/20"></span>
                  Age: {calculateAge()}
                </p>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground mb-6 w-full md:w-auto">
              <TabsTrigger
                value="personal"
                className="data-[state=active]:bg-background data-[state=active]:text-foreground"
              >
                Personal
              </TabsTrigger>
              <TabsTrigger
                value="medical"
                className="data-[state=active]:bg-background data-[state=active]:text-foreground"
              >
                Medical
              </TabsTrigger>
              <TabsTrigger
                value="insurance"
                className="data-[state=active]:bg-background data-[state=active]:text-foreground"
              >
                Insurance
              </TabsTrigger>
              <TabsTrigger
                value="documents"
                className="data-[state=active]:bg-background data-[state=active]:text-foreground"
              >
                Documents
              </TabsTrigger>
              <TabsTrigger
                value="health-records"
                className="data-[state=active]:bg-background data-[state=active]:text-foreground"
              >
                Health Records
              </TabsTrigger>
              <TabsTrigger
                value="abha"
                className="data-[state=active]:bg-background data-[state=active]:text-foreground"
              >
                ABHA
              </TabsTrigger>
            </TabsList>

            {/* Personal Information Tab */}
            <TabsContent value="personal" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Basic Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Full Name
                        </p>
                        <p className="font-medium">
                          {patient.firstName} {patient.lastName}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Gender</p>
                        <p className="font-medium">
                          {formatGenderDisplay(patient.gender)}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Date of Birth
                        </p>
                        <p className="font-medium">
                          {formatDate(patient.dateOfBirth)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Age</p>
                        <p className="font-medium">{calculateAge()}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Blood Group
                        </p>
                        <p className="font-medium">
                          {patient.bloodGroup || "Not specified"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Marital Status
                        </p>
                        <p className="font-medium capitalize">
                          {patient.maritalStatus || "Not specified"}
                        </p>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Occupation
                      </p>
                      <p className="font-medium">
                        {patient.occupation || "Not specified"}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Phone className="h-5 w-5 mr-2" />
                      Contact Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground flex items-center gap-1">
                        <Phone className="h-3 w-3 text-blue-600" />
                        Patient's Communication Mobile Number
                      </p>
                      <p className="font-medium">{patient.phone}</p>
                    </div>
                    {patient.alternatePhone && (
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Alternate Phone
                        </p>
                        <p className="font-medium">{patient.alternatePhone}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p className="font-medium">
                        {patient.email || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Address</p>
                      <p className="font-medium">
                        {patient.address ? (
                          <>
                            {patient.address}
                            {patient.city && `, ${patient.city}`}
                            {patient.state && `, ${patient.state}`}
                            {patient.pincode && ` - ${patient.pincode}`}
                          </>
                        ) : (
                          "Not provided"
                        )}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Medical Information Tab */}
            <TabsContent value="medical" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Heart className="h-5 w-5 mr-2" />
                      Medical Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Allergies</p>
                      <p className="font-medium">
                        {patient.allergies || "None reported"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Chronic Diseases
                      </p>
                      <p className="font-medium">
                        {patient.chronicDiseases || "None reported"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Current Medications
                      </p>
                      <p className="font-medium">
                        {patient.currentMedications || "None reported"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Family Medical History
                      </p>
                      <p className="font-medium">
                        {patient.familyMedicalHistory || "None reported"}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <AlertCircle className="h-5 w-5 mr-2" />
                      Emergency Contact
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Name</p>
                      <p className="font-medium">
                        {patient.emergencyContactName || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Phone Number
                      </p>
                      <p className="font-medium">
                        {patient.emergencyContactPhone || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Relationship
                      </p>
                      <p className="font-medium">
                        {patient.emergencyContactRelation || "Not provided"}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Insurance Details Tab */}
            <TabsContent value="insurance" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <ShieldCheck className="h-5 w-5 mr-2" />
                    Insurance Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Insurance Provider
                      </p>
                      <p className="font-medium">
                        {patient.insuranceProvider || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Policy Number
                      </p>
                      <p className="font-medium">
                        {patient.insurancePolicyNumber || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Expiry Date
                      </p>
                      <p className="font-medium">
                        {patient.insuranceExpiryDate
                          ? formatDate(patient.insuranceExpiryDate)
                          : "Not provided"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Documents Tab */}
            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-lg flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Patient Documents
                  </CardTitle>
                  <Button
                    onClick={() =>
                      router.push(`/patients/${patient.id}/documents/upload`)
                    }
                  >
                    Upload Document
                  </Button>
                </CardHeader>
                <CardContent>
                  {patient.documents && patient.documents.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {patient.documents.map((doc) => (
                        <Card key={doc.id} className="overflow-hidden">
                          <div className="h-40 bg-muted flex items-center justify-center">
                            <FileText className="h-12 w-12 text-muted-foreground" />
                          </div>
                          <CardContent className="p-4">
                            <h4 className="font-medium truncate">{doc.name}</h4>
                            <p className="text-sm text-muted-foreground mt-1 capitalize">
                              {doc.category}
                            </p>
                            <div className="flex justify-between items-center mt-2">
                              <span className="text-xs text-muted-foreground">
                                {formatDate(doc.createdAt)}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  window.open(doc.fileUrl, "_blank")
                                }
                              >
                                View
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 text-center">
                      <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">
                        No documents found
                      </h3>
                      <p className="text-muted-foreground mt-1">
                        Upload documents for this patient
                      </p>
                      <Button
                        onClick={() =>
                          router.push(
                            `/patients/${patient.id}/documents/upload`,
                          )
                        }
                        className="mt-4"
                      >
                        Upload Document
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Health Records Tab */}
            <TabsContent value="health-records" className="space-y-6">
              <ConsultationTimeline patientId={patient.id} />
            </TabsContent>

            {/* ABHA Tab */}
            <TabsContent value="abha" className="space-y-6">
              <Card className="overflow-hidden border shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-primary/5 to-secondary/5 pb-4">
                  <CardTitle className="text-lg flex items-center">
                    <IdCard className="h-5 w-5 mr-2 text-primary" />
                    ABHA Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {(patient as any)?.abhaProfile ? (
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-900/50 rounded-lg p-4 shadow-sm">
                        <div className="flex items-start">
                          <div className="bg-white dark:bg-gray-800 rounded-full p-2 text-green-600 dark:text-green-400 mr-3 shadow-sm">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="20"
                              height="20"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                          </div>
                          <div>
                            <h3 className="text-green-800 dark:text-green-400 font-medium">
                              ABHA Verified
                            </h3>
                            <p className="text-green-700 dark:text-green-500 text-sm mt-1">
                              This patient has a verified ABHA account
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-card border rounded-lg p-4 shadow-sm transition-all hover:shadow-md">
                          <div className="text-sm text-muted-foreground mb-1">
                            ABHA Number
                          </div>
                          <div className="font-medium text-foreground flex items-center">
                            <span className="inline-block w-2 h-2 mr-2 rounded-full bg-primary"></span>
                            {(patient as any).abhaProfile?.abhaNumber ||
                              "Not available"}
                          </div>
                        </div>

                        {(patient as any).abhaProfile?.abhaAddress && (
                          <div className="bg-card border rounded-lg p-4 shadow-sm transition-all hover:shadow-md">
                            <div className="text-sm text-muted-foreground mb-1">
                              ABHA Address
                            </div>
                            <div className="font-medium text-foreground flex items-center">
                              <span className="inline-block w-2 h-2 mr-2 rounded-full bg-secondary"></span>
                              {(patient as any).abhaProfile?.abhaAddress}
                            </div>
                          </div>
                        )}

                        {(patient as any).abhaProfile?.healthIdNumber && (
                          <div className="bg-card border rounded-lg p-4 shadow-sm transition-all hover:shadow-md">
                            <div className="text-sm text-muted-foreground mb-1">
                              Health ID
                            </div>
                            <div className="font-medium text-foreground flex items-center">
                              <span className="inline-block w-2 h-2 mr-2 rounded-full bg-green-500"></span>
                              {(patient as any).abhaProfile?.healthIdNumber}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3 pt-2">
                        <Button
                          variant="outline"
                          className="flex-1 h-10 border-primary/30 hover:border-primary hover:bg-primary/5"
                          onClick={() =>
                            router.push(`/patients/${patient.id}/abha/view`)
                          }
                        >
                          <IdCard className="mr-2 h-4 w-4" />
                          View ABHA Details
                        </Button>
                        <Button
                          variant="outline"
                          className="flex-1 h-10 border-primary/30 hover:border-primary hover:bg-primary/5"
                          onClick={() =>
                            router.push(`/patients/${patient.id}/consents`)
                          }
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          Manage Consents
                        </Button>
                        <Button
                          variant="outline"
                          className="flex-1 h-10 border-primary/30 hover:border-primary hover:bg-primary/5"
                          onClick={() =>
                            router.push(`/patients/${patient.id}/care-contexts`)
                          }
                        >
                          <LinkIcon className="mr-2 h-4 w-4" />
                          Care Context Dashboard
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-10 px-4 text-center bg-muted/30 rounded-lg border border-dashed border-muted-foreground/30">
                      <div className="bg-background rounded-full p-4 shadow-sm mb-4">
                        <IdCard className="h-10 w-10 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium">
                        No ABHA information found
                      </h3>
                      <p className="text-muted-foreground mt-2 max-w-md">
                        Create an ABHA (Ayushman Bharat Health Account) for this
                        patient to enable health data sharing and access
                      </p>
                      <div className="flex flex-col sm:flex-row gap-3 mt-6 w-full max-w-md">
                        <Button
                          onClick={() =>
                            router.push(`/patients/${patient.id}/abha/create`)
                          }
                          className="flex-1"
                        >
                          Create New ABHA
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() =>
                            router.push(`/patients/${patient.id}/abha/verify`)
                          }
                          className="flex-1"
                        >
                          Verify Existing ABHA
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Delete Patient Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Patient</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {patient.firstName}{" "}
              {patient.lastName}? This action is irreversible and will remove
              all patient data including medical records, appointments, and ABHA
              links.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeletePatient();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
