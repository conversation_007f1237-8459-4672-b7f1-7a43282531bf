"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Syringe } from "lucide-react";
import { PatientLayout } from "../_components/patient-layout";
import { ImmunizationForm, ImmunizationList } from "@/components/immunization";
// No need to import getPatientById anymore

export default function PatientImmunizationsPage() {
  const params = useParams();
  const patientId = (params?.id as string) || "";

  return (
    <PatientLayout
      patientId={patientId}
      title="Immunizations"
      description="Manage patient immunization records"
    >
      <ImmunizationsContent patientId={patientId} />
    </PatientLayout>
  );
}

function ImmunizationsContent({ patientId }: { patientId: string }) {
  const [activeTab, setActiveTab] = useState("list");
  const [showForm, setShowForm] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleImmunizationAdded = () => {
    setShowForm(false);
    setRefreshTrigger((prev) => prev + 1);
    setActiveTab("list");
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Syringe className="h-5 w-5 mr-2 text-primary" />
          <h2 className="text-lg font-medium">Patient Immunization Records</h2>
        </div>
        {!showForm && (
          <Button onClick={() => setShowForm(true)}>
            Add New Immunization
          </Button>
        )}
      </div>

      {showForm ? (
        <Card>
          <CardHeader>
            <CardTitle>Add New Immunization</CardTitle>
          </CardHeader>
          <CardContent>
            <ImmunizationForm
              patientId={patientId}
              doctorId="" // This will be filled by the API based on the current user
              onCancel={() => setShowForm(false)}
              onSuccess={handleImmunizationAdded}
            />
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="list">Current Immunizations</TabsTrigger>
            <TabsTrigger value="schedule">Immunization Schedule</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <ImmunizationList
                  patientId={patientId}
                  refreshTrigger={refreshTrigger}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  Upcoming immunizations and recommended schedule will be shown
                  here
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  Immunization history and past records will be shown here
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
