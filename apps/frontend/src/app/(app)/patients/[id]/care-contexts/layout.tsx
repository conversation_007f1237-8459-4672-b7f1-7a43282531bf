"use client";

import { useEffect, useState } from "react";
import { LinkTokenAlert } from "@/components/care-context/link-token-alert";
import { useCurrentBranch } from "@/hooks/use-current-branch";

export default function CareContextsLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { id: string };
}) {
  const [hasAbhaProfile, setHasAbhaProfile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { currentBranch } = useCurrentBranch();

  // Check if patient has ABHA profile
  useEffect(() => {
    const checkAbhaProfile = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/patients/${params.id}/abha-profile`);

        if (response.ok) {
          const data = await response.json();
          setHasAbhaProfile(!!data.abhaProfile?.abhaNumber);
        }
      } catch (error) {
        console.error("Error checking ABHA profile:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      checkAbhaProfile();
    }
  }, [params.id]);

  return (
    <div className="space-y-6">
      {!isLoading && hasAbhaProfile && currentBranch && (
        <LinkTokenAlert patientId={params.id} branchId={currentBranch.id} />
      )}
      {children}
    </div>
  );
}
