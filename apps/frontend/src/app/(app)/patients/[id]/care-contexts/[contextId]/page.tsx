"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";
import { ArrowLeft, LinkIcon, Unlink, FileText } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface CareContext {
  id: string;
  patientId: string;
  consultationId: string;
  display: string;
  hiTypes: string[];
  createdAt: string;
  updatedAt: string;
  additionalInfo?: any;
}

export default function CareContextDetailPage({
  params,
}: {
  params: { id: string; contextId: string };
}) {
  const router = useRouter();
  const [careContext, setCareContext] = useState<CareContext | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUnlinking, setIsUnlinking] = useState(false);
  const [showUnlinkDialog, setShowUnlinkDialog] = useState(false);

  // Fetch care context details
  const fetchCareContext = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/patients/${params.id}/care-contexts/${params.contextId}`,
      );

      if (response.ok) {
        const data = await response.json();
        setCareContext(data.careContext);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to fetch care context details");
        router.push(`/patients/${params.id}`);
      }
    } catch (error) {
      console.error("Error fetching care context details:", error);
      toast.error("Failed to fetch care context details");
      router.push(`/patients/${params.id}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Unlink care context
  const unlinkCareContext = async () => {
    try {
      setIsUnlinking(true);
      const response = await fetch(
        `/api/patients/${params.id}/care-contexts/${params.contextId}/unlink`,
        {
          method: "POST",
        },
      );

      if (response.ok) {
        toast.success("Care context unlinked successfully");
        router.push(`/patients/${params.id}`);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to unlink care context");
      }
    } catch (error) {
      console.error("Error unlinking care context:", error);
      toast.error("Failed to unlink care context");
    } finally {
      setIsUnlinking(false);
      setShowUnlinkDialog(false);
    }
  };

  // Load care context on component mount
  useEffect(() => {
    if (params.id && params.contextId) {
      fetchCareContext();
    }
  }, [params.id, params.contextId]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/patients/${params.id}`)}
              className="mr-3"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-48 mt-2" />
            </div>
          </div>
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64 mt-2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!careContext) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/patients/${params.id}`)}
              className="mr-3"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Care Context Not Found</h1>
              <p className="text-sm text-muted-foreground">
                The requested care context could not be found
              </p>
            </div>
          </div>
        </div>

        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center py-8">
              <p>
                The care context you are looking for does not exist or has been
                removed.
              </p>
              <Button
                onClick={() => router.push(`/patients/${params.id}`)}
                className="mt-4"
              >
                Back to Patient
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push(`/patients/${params.id}`)}
            className="mr-3"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Care Context Details</h1>
            <p className="text-sm text-muted-foreground">
              View details of the linked care context
            </p>
          </div>
        </div>
        <Dialog open={showUnlinkDialog} onOpenChange={setShowUnlinkDialog}>
          <DialogTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Unlink className="h-4 w-4" />
              Unlink Context
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Unlink Care Context</DialogTitle>
              <DialogDescription>
                Are you sure you want to unlink this care context? This action
                cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowUnlinkDialog(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={unlinkCareContext}
                disabled={isUnlinking}
              >
                {isUnlinking ? "Unlinking..." : "Unlink"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LinkIcon className="h-5 w-5 text-primary" />
            {careContext.display}
          </CardTitle>
          <CardDescription>
            Consultation ID: {careContext.consultationId}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">
                  Health Information Types
                </h3>
                <div className="flex flex-wrap gap-2">
                  {careContext.hiTypes.map((type) => (
                    <Badge key={type} variant="outline">
                      {type}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">
                  Created At
                </h3>
                <p>{formatDate(new Date(careContext.createdAt))}</p>
              </div>
            </div>

            {careContext.additionalInfo && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  Additional Information
                </h3>
                <Card className="bg-muted/30">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      {careContext.additionalInfo.appointmentId && (
                        <div>
                          <span className="text-sm font-medium">
                            Appointment ID:
                          </span>{" "}
                          <span className="font-mono text-xs">
                            {careContext.additionalInfo.appointmentId}
                          </span>
                        </div>
                      )}
                      {careContext.additionalInfo.doctorId && (
                        <div>
                          <span className="text-sm font-medium">
                            Doctor ID:
                          </span>{" "}
                          <span className="font-mono text-xs">
                            {careContext.additionalInfo.doctorId}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">
                Associated Consultation
              </h3>
              <Card className="bg-muted/30">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm">
                        View the consultation details associated with this care
                        context
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        router.push(
                          `/consultations/${careContext.consultationId}`,
                        )
                      }
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      View Consultation
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
