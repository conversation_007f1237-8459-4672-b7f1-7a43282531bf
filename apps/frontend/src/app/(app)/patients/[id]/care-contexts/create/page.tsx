"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { CareContextCreator } from "@/components/care-context/care-context-creator";
import { Skeleton } from "@/components/ui/skeleton";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  abhaProfile?: {
    abhaNumber: string;
    abhaAddress: string;
  } | null;
}

export default function CreateCareContextPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPatient = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/patients/${params.id}`);

        if (response.ok) {
          const data = await response.json();
          setPatient(data.patient);
        } else {
          console.error("Failed to fetch patient");
        }
      } catch (error) {
        console.error("Error fetching patient:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchPatient();
    }
  }, [params.id]);

  const handleSuccess = () => {
    // Navigate to care contexts list after successful creation
    router.push(`/patients/${params.id}/care-contexts`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push(`/patients/${params.id}/care-contexts`)}
          className="mr-3"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Create Care Context</h1>
          {!isLoading && patient && (
            <p className="text-sm text-muted-foreground">
              Create a new care context for {patient.firstName}{" "}
              {patient.lastName}
            </p>
          )}
        </div>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      ) : patient ? (
        <CareContextCreator
          patientId={params.id}
          hasAbhaProfile={!!patient.abhaProfile?.abhaNumber}
          onSuccess={handleSuccess}
        />
      ) : (
        <div className="p-4 text-center">
          <p>Patient not found</p>
        </div>
      )}
    </div>
  );
}
