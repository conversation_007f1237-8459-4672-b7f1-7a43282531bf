"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { <PERSON>Lef<PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { CareContextsSection } from "../_components/care-contexts-section";
import { useCurrentBranch } from "@/hooks/use-current-branch";

interface CareContextMetrics {
  total: number;
  linked: number;
  unlinked: number;
  byHiType: Record<string, number>;
  byMonth: Record<string, number>;
}

export default function CareContextDashboardPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { currentBranch } = useCurrentBranch();
  const [metrics, setMetrics] = useState<CareContextMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasAbhaProfile, setHasAbhaProfile] = useState(false);

  // Fetch care context metrics
  const fetchMetrics = async () => {
    try {
      setIsLoading(true);

      // Build the URL with optional branch filter
      let url = `/api/patients/${params.id}/care-contexts/metrics`;
      if (currentBranch) {
        url += `?branchId=${currentBranch.id}`;
      }

      const response = await fetch(url);

      if (response.ok) {
        const data = await response.json();
        setMetrics(data.metrics);
        setHasAbhaProfile(data.hasAbhaProfile);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to fetch care context metrics");
      }
    } catch (error) {
      console.error("Error fetching care context metrics:", error);
      toast.error("Failed to fetch care context metrics");
    } finally {
      setIsLoading(false);
    }
  };

  // Load metrics on component mount
  useEffect(() => {
    if (params.id) {
      fetchMetrics();
    }
  }, [params.id, currentBranch]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push(`/patients/${params.id}`)}
              className="mr-3"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-48 mt-2" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
          <Skeleton className="h-32" />
        </div>

        <Skeleton className="h-64" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push(`/patients/${params.id}`)}
            className="mr-3"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Care Context Dashboard</h1>
            <p className="text-sm text-muted-foreground">
              View and manage ABDM care contexts for this patient
            </p>
          </div>
        </div>
        <Button
          onClick={() => router.push(`/patients/${params.id}/abha/view`)}
          variant="outline"
        >
          View ABHA Profile
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contexts">Care Contexts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {metrics ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Care Contexts
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{metrics.total}</div>
                    <p className="text-xs text-muted-foreground mt-1">
                      All care contexts linked to this patient
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Linked Contexts
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {metrics.linked}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Active care contexts linked to ABHA
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Unlinked Contexts
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-amber-600">
                      {metrics.unlinked}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Care contexts that have been unlinked
                    </p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart className="h-5 w-5 text-primary" />
                    Health Information Types
                  </CardTitle>
                  <CardDescription>
                    Distribution of care contexts by health information type
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(metrics.byHiType).map(([type, count]) => (
                      <div key={type} className="flex items-center">
                        <div className="w-40 font-medium">{type}</div>
                        <div className="flex-1">
                          <div className="bg-muted h-3 rounded-full overflow-hidden">
                            <div
                              className="bg-primary h-full rounded-full"
                              style={{
                                width: `${(count / metrics.total) * 100}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="w-10 text-right font-mono text-sm">
                          {count}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center justify-center py-8">
                  <p>No care context metrics available for this patient.</p>
                  {hasAbhaProfile ? (
                    <p className="text-muted-foreground mt-2">
                      This patient has an ABHA profile but no care contexts have
                      been linked yet.
                    </p>
                  ) : (
                    <p className="text-muted-foreground mt-2">
                      This patient does not have an ABHA profile. Create or link
                      an ABHA profile to enable care context linking.
                    </p>
                  )}
                  <Button
                    onClick={() => router.push(`/patients/${params.id}/abha`)}
                    className="mt-4"
                  >
                    Manage ABHA Profile
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="contexts">
          <CareContextsSection
            patientId={params.id}
            hasAbhaProfile={hasAbhaProfile}
            branchId={currentBranch?.id}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
