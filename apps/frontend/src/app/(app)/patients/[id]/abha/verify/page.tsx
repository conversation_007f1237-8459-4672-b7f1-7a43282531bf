import { notFound, redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { AbhaVerificationForm } from "./_components/abha-verification-form";

export default async function PatientAbhaVerifyPage({
  params,
}: {
  params: { id: string };
}) {
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    return notFound();
  }

  // If patient already has an ABHA profile, redirect to view page
  if (patient.abhaProfile && patient.abhaProfile.abhaNumber) {
    redirect(`/patients/${patient.id}/abha/view`);
  }

  return <AbhaVerificationForm patient={patient} />;
}
