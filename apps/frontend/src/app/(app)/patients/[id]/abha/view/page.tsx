import { notFound, redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { AbhaCardViewer } from "./_components/abha-card-viewer";

export default async function PatientAbhaViewPage({
  params,
}: {
  params: { id: string };
}) {
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    return notFound();
  }

  // Check if the patient has an ABHA profile

  // If patient doesn't have an ABHA profile, redirect to create page
  if (!patient.abhaProfile || !patient.abhaProfile.abhaNumber) {
    redirect(`/patients/${patient.id}/abha/create`);
  }

  return (
    <div className="space-y-6">
      <AbhaCardViewer patient={patient} abhaProfile={patient.abhaProfile} />
    </div>
  );
}
