import { notFound, redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { UpdateContactForm } from "./_components/update-contact-form";

export default async function PatientAbhaUpdateContactPage({
  params,
}: {
  params: { id: string };
}) {
  // Get the patient with ABHA profile
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    return notFound();
  }

  // If patient doesn't have an ABHA profile, redirect to create page
  if (!patient.abhaProfile || !patient.abhaProfile.abhaNumber) {
    redirect(`/patients/${patient.id}/abha/create`);
  }

  // If patient doesn't have an active ABHA session, redirect to login page
  if (!patient.abhaProfile.xToken) {
    redirect(
      `/patients/${patient.id}/abha/login?returnUrl=${encodeURIComponent(`/patients/${patient.id}/abha/update-contact`)}`,
    );
  }

  // Check if token is expired
  if (
    patient.abhaProfile.xTokenExpiresAt &&
    new Date() > new Date(patient.abhaProfile.xTokenExpiresAt)
  ) {
    redirect(
      `/patients/${patient.id}/abha/login?returnUrl=${encodeURIComponent(`/patients/${patient.id}/abha/update-contact`)}`,
    );
  }

  return (
    <div className="space-y-6">
      <UpdateContactForm patient={patient} />
    </div>
  );
}
