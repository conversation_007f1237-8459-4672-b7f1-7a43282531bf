"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AbhaNumberInput } from "@/components/ui/abha-number-input";
import {
  AlertCircle,
  Loader2,
  CreditCard,
  Smartphone,
  Fingerprint,
  ArrowRight,
  ArrowLeft,
  RefreshCw,
  CheckCircle,
  XCircle,
  AtSign,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { toast } from "sonner";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { parseAbdmError } from "@/lib/abdm-error-utils";
import { maskAadhaarNumber } from "@/lib/aadhaar-utils";
// Direct fetch calls instead of using makeAbdmApiRequest
import {
  trackVerificationMethodSelected,
  trackVerificationStep,
  trackVerificationError,
  trackVerificationSuccess,
  VerificationMethod,
} from "@/lib/analytics-utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";
import { SimpleCaptcha } from "@/components/ui/simple-captcha";

interface AbhaVerificationFormProps {
  patient: any;
  patientId?: string;
  onSuccess?: (abhaDetails: {
    abhaNumber: string;
    abhaAddress: string;
    healthIdNumber: string;
    stateName?: string;
    districtName?: string;
    abhaStatus?: string;
    name?: string;
    gender?: string;
    yearOfBirth?: string;
    dayOfBirth?: string;
    monthOfBirth?: string;
    dob?: string;
    phone?: string;
    email?: string;
    address?: string;
    city?: string;
    state?: string;
    pincode?: string;
    token?: string;
  }) => void;
  standalone?: boolean;
  initialAbhaNumber?: string | null;
  initialPhone?: string | null;
}

type VerificationMode = "aadhaar" | "mobile" | "abha" | "abhaAddress";
type VerificationStep = "select" | "input" | "otp-method" | "otp" | "success";
type OtpMethod = "aadhaar-linked" | "abha-linked";

export function AbhaVerificationForm({
  patient,
  patientId,
  onSuccess,
  standalone = false,
  initialAbhaNumber = null,
  initialPhone = null,
}: AbhaVerificationFormProps) {
  // If patient is provided, use its ID, otherwise use the provided patientId
  const effectivePatientId = patient?.id || patientId || "";

  // Set initial verification mode based on initialAbhaNumber
  const initialMode = initialAbhaNumber ? "abha" : "aadhaar";
  const [mode, setMode] = useState<VerificationMode>(initialMode);

  // State for verification flow
  const [aadhaar, setAadhaar] = useState("");
  const [mobile, setMobile] = useState(initialPhone || "");
  const [abhaId, setAbhaId] = useState(initialAbhaNumber || "");
  const [abhaAddress, setAbhaAddress] = useState("");
  const [otp, setOtp] = useState("");
  const [txnId, setTxnId] = useState("");

  // State for ABHA find flow
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedIndex, setSelectedIndex] = useState("");
  const [searchTxnId, setSearchTxnId] = useState("");

  // Set initial step based on initialAbhaNumber
  const initialStep = initialAbhaNumber ? "input" : "select";
  const [step, setStep] = useState<VerificationStep>(initialStep);

  const [isGeneratingOtp, setIsGeneratingOtp] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<number>(600); // 10 minutes in seconds
  const [isTimerActive, setIsTimerActive] = useState<boolean>(false);
  const [resendTimeRemaining, setResendTimeRemaining] = useState<number>(60); // 60 seconds for resend cooldown
  const [isResendTimerActive, setIsResendTimerActive] =
    useState<boolean>(false);
  const [resendAttempts, setResendAttempts] = useState<number>(0);
  const [otpSentMessage, setOtpSentMessage] = useState<string | null>(null);
  const [isCaptchaValid, setIsCaptchaValid] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [otpMethod, setOtpMethod] = useState<OtpMethod>("aadhaar-linked");

  // Multiple consent states for Aadhaar verification (copied from ABHA creation)
  const [consent1Accepted, setConsent1Accepted] = useState(false); // ABHA creation consent
  const [consent2Accepted, setConsent2Accepted] = useState(false); // Document linking consent (should NOT be checked for Aadhaar)
  const [consent3Accepted, setConsent3Accepted] = useState(false); // Health records sharing consent
  const [consent4Accepted, setConsent4Accepted] = useState(false); // Healthcare provider sharing consent
  const [consent5Accepted, setConsent5Accepted] = useState(false); // Anonymization consent
  const [consent6Accepted, setConsent6Accepted] = useState(false); // Healthcare worker consent
  const [consent7Accepted, setConsent7Accepted] = useState(false); // Beneficiary consent
  const [beneficiaryName, setBeneficiaryName] = useState(""); // Beneficiary name for consent

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const resendTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Multiple consent texts for Aadhaar verification (copied from ABHA creation)
  const aadhaarConsentTexts = {
    // First consent - ABHA creation using Aadhaar
    consent1: {
      en: `I am voluntarily sharing my Aadhaar Number / Virtual ID issued by the Unique Identification Authority of India ("UIDAI"), and my demographic information for the purpose of creating an Ayushman Bharat Health Account number ("ABHA number") and Ayushman Bharat Health Account address ("ABHA Address"). I authorize NHA to use my Aadhaar number / Virtual ID for performing Aadhaar based authentication with UIDAI as per the provisions of the Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016 for the aforesaid purpose. I understand that UIDAI will share my e-KYC details, or response of "Yes" with NHA upon successful authentication.`,
    },
    // Second consent - Document linking (should be unchecked for Aadhaar creation)
    consent2: {
      en: `I intend to create Ayushman Bharat Health Account Number ("ABHA number") and Ayushman Bharat Health Account address ("ABHA Address") using document other than Aadhaar. (Click here to proceed further)`,
    },
    // Third consent - Health records sharing
    consent3: {
      en: `I consent to usage of my ABHA address and ABHA number for linking of my legacy (past) health records and those which will be generated during this encounter.`,
    },
    // Fourth consent - Anonymization
    consent4: {
      en: `I authorize the sharing of all my health records with healthcare provider(s) for the purpose of providing healthcare services to me during this encounter.`,
    },
    // Fifth consent - Public health purposes
    consent5: {
      en: `I consent to the anonymization and subsequent use of my health records for public health purposes.`,
    },
    // Sixth consent - Healthcare worker
    consent6: {
      en: `I consent to the healthcare worker(s) accessing my health records for the purpose of providing healthcare services to me.`,
    },
    // Seventh consent - Beneficiary
    consent7: {
      en: `I, [Beneficiary Name], hereby consent to the creation of my ABHA number and ABHA address.`,
    },
  };

  // Effect to handle the OTP countdown timer
  useEffect(() => {
    if (isTimerActive && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining((prevTime) => {
          if (prevTime <= 1) {
            clearInterval(timerRef.current!);
            setIsTimerActive(false);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isTimerActive, timeRemaining]);

  // Effect to handle the resend OTP cooldown timer
  useEffect(() => {
    if (isResendTimerActive && resendTimeRemaining > 0) {
      resendTimerRef.current = setInterval(() => {
        setResendTimeRemaining((prevTime) => {
          if (prevTime <= 1) {
            clearInterval(resendTimerRef.current!);
            setIsResendTimerActive(false);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (resendTimerRef.current) {
        clearInterval(resendTimerRef.current);
      }
    };
  }, [isResendTimerActive, resendTimeRemaining]);

  // Format the remaining time as MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Function to generate OTP based on the selected mode
  const handleGenerateOtp = async () => {
    setError(null);
    setIsGeneratingOtp(true);

    // Track the start of verification
    if (!standalone) {
      trackVerificationStep(
        mode as VerificationMethod,
        "started",
        effectivePatientId,
      );
    }

    try {
      let endpoint;
      let payload;

      switch (mode) {
        case "aadhaar":
          // Validate Aadhaar number
          if (!aadhaar || aadhaar.length !== 12 || !/^\d+$/.test(aadhaar)) {
            const errorMsg = "Aadhaar Number is not valid";
            setError(errorMsg);
            if (!standalone) {
              trackVerificationError(
                mode as VerificationMethod,
                "started",
                "VALIDATION_ERROR",
                errorMsg,
                effectivePatientId,
              );
            }
            setIsGeneratingOtp(false);
            return;
          }
          endpoint = "/api/abdm/abha-verify/aadhaar/otp";
          payload = { aadhaar };
          break;

        case "mobile":
          // Import and use proper validation
          const { validateMobile } = await import("@/lib/validation");
          const mobileValidation = validateMobile(mobile);

          if (!mobileValidation.isValid) {
            const errorMsg =
              mobileValidation.message || "Please enter a valid mobile number";
            setError(errorMsg);
            if (!standalone) {
              trackVerificationError(
                mode as VerificationMethod,
                "started",
                "VALIDATION_ERROR",
                errorMsg,
                effectivePatientId,
              );
            }
            setIsGeneratingOtp(false);
            return;
          }

          // For mobile verification, we need to check if this is for ABHA mobile verification
          // If abhaId is provided, we'll use that for mobile verification
          if (abhaId && abhaId.length > 0) {
            endpoint = "/api/abdm/abha-verify/abha-id/otp";
            payload = { abhaNumber: abhaId };
            toast.info("Using ABHA number for mobile verification", {
              duration: 3000,
              position: "top-center",
            });
          } else {
            // Use the new ABHA find flow
            endpoint = "/api/abdm/abha-find/mobile/search";
            payload = { mobile };
          }
          break;

        case "abha":
          // Import and use proper ABHA validation
          const { validateAbhaNumber } = await import("@/lib/validation");
          const abhaValidation = validateAbhaNumber(abhaId);

          if (!abhaValidation.isValid) {
            const errorMsg =
              abhaValidation.message || "Please enter a valid ABHA number";
            setError(errorMsg);
            if (!standalone) {
              trackVerificationError(
                mode as VerificationMethod,
                "started",
                "VALIDATION_ERROR",
                errorMsg,
                effectivePatientId,
              );
            }
            setIsGeneratingOtp(false);
            return;
          }
          endpoint = "/api/abdm/abha-verify/abha-id/otp";
          payload = { abhaNumber: abhaId, otpMethod };
          break;

        case "abhaAddress":
          // Import and use proper ABHA Address validation
          const { validateAbhaAddress } = await import("@/lib/validation");
          const abhaAddressValidation = validateAbhaAddress(abhaAddress);

          if (!abhaAddressValidation.isValid) {
            const errorMsg =
              abhaAddressValidation.message ||
              "Please enter a valid ABHA Address";
            setError(errorMsg);
            if (!standalone) {
              trackVerificationError(
                mode as VerificationMethod,
                "started",
                "VALIDATION_ERROR",
                errorMsg,
                effectivePatientId,
              );
            }
            setIsGeneratingOtp(false);
            return;
          }
          endpoint = "/api/abdm/abha-verify/abha-address/otp";
          payload = { abhaAddress, otpMethod };
          break;

        default:
          const errorMsg = "Invalid verification mode";
          setError(errorMsg);
          if (!standalone) {
            trackVerificationError(
              mode as VerificationMethod,
              "started",
              "VALIDATION_ERROR",
              errorMsg,
              effectivePatientId,
            );
          }
          setIsGeneratingOtp(false);
          return;
      }

      // Track OTP request
      if (!standalone) {
        trackVerificationStep(
          mode as VerificationMethod,
          "otp_requested",
          effectivePatientId,
          payload,
        );
      }

      // Make direct fetch call to the API
      try {
        console.log("Making API request to:", endpoint);
        console.log("With payload:", payload);

        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        console.log("API response status:", response.status);
        const data = await response.json();
        console.log("API response data:", data);

        if (!response.ok) {
          // Handle error response
          const err = data.error;
          if (
            err &&
            err ===
              "The mobile number you have entered does not match with any of the records. Please enter a different number."
          ) {
            const customMsg = [
              "ABHA Number not found.",
              "We did not find any ABHA number linked to this mobile number.",
              "Please use ABHA linked mobile number",
            ].join("\n");

            // set the inline error state
            setError(customMsg);

            // show the toast
            toast.error(customMsg, {
              duration: 5000,
              position: "top-center",
              icon: <XCircle className="h-5 w-5 text-red-500" />,
            });

            // track the error if you want
            if (!standalone) {
              trackVerificationError(
                mode as VerificationMethod,
                "otp_requested",
                "ABDM-1115",
                customMsg,
                effectivePatientId,
              );
            }

            // stop here
            setIsGeneratingOtp(false);
            return;
          }

          // fallback for any other non-OK
          throw new Error(data.error);
        }

        // Handle different responses based on the endpoint
        if (endpoint === "/api/abdm/abha-find/mobile/search") {
          // The response format is { txnId: "...", ABHA: [{ index: 1, ... }] }
          console.log("Search response:", data);
          console.log(
            "Search response type:",
            typeof data,
            "isArray:",
            Array.isArray(data),
          );

          // Handle search response - check if data is valid
          if (
            data &&
            ((Array.isArray(data) && data.length > 0) ||
              (typeof data === "object" && data.txnId && data.ABHA))
          ) {
            // Convert to array if it's a single object
            const dataArray = Array.isArray(data) ? data : [data];
            setSearchResults(dataArray);

            // If there's only one result, automatically select it
            if (dataArray.length === 1) {
              // Extract the index and txnId from the response
              // The response format is { txnId: "...", ABHA: [{ index: 1, ... }] }
              console.log("Search response (single result):", dataArray);
              if (dataArray[0]?.ABHA && dataArray[0]?.ABHA.length > 0) {
                setSelectedIndex(dataArray[0].ABHA[0].index.toString());
                setSearchTxnId(dataArray[0].txnId);
              } else {
                console.error("Unexpected search response format:", data);
                throw new Error("Unexpected search response format");
              }

              // Proceed to request OTP with the index
              console.log(
                "Requesting OTP with index:",
                dataArray[0].ABHA[0].index,
                "type:",
                typeof dataArray[0].ABHA[0].index,
              );
              console.log(
                "Requesting OTP with searchTxnId:",
                dataArray[0].txnId,
              );

              const requestPayload = {
                index: dataArray[0].ABHA[0].index,
                searchTxnId: dataArray[0].txnId,
              };

              console.log("Request payload:", JSON.stringify(requestPayload));

              const otpResponse = await fetch(
                "/api/abdm/abha-find/mobile/request-otp",
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify(requestPayload),
                },
              );

              const otpData = await otpResponse.json();

              if (!otpResponse.ok) {
                throw new Error(otpData.error || "Failed to request OTP");
              }

              // Extract masked mobile number from the response message if available
              if (otpData.message && typeof otpData.message === "string") {
                console.log("OTP message:", otpData.message);

                // Try to extract the masked mobile number using regex
                const [, maskedPart] =
                  otpData.message.match(/ending with (\*+\d{4})/) || [];

                // Build the final message if masked part is found
                if (maskedPart) {
                  setOtpSentMessage(
                    `We just sent an OTP on the Mobile Number ${maskedPart}. ` +
                      `Enter the OTP below to proceed with ABHA verification`,
                  );
                } else {
                  // Fallback message
                  setOtpSentMessage(
                    `We just sent an OTP to your mobile number. ` +
                      `Enter the OTP below to proceed with ABHA verification`,
                  );
                }
              } else {
                // Clear any previous message if no message in response
                setOtpSentMessage(null);
              }

              setTxnId(otpData.txnId || "");
              setStep("otp");
              // Reset and start the OTP timer
              setTimeRemaining(600); // 10 minutes
              setIsTimerActive(true);

              // Reset resend attempts when generating OTP for the first time
              setResendAttempts(0);

              // Start the resend cooldown timer (60 seconds)
              setResendTimeRemaining(60);
              setIsResendTimerActive(true);

              toast.success("OTP sent successfully to your mobile number", {
                duration: 4000,
                position: "top-center",
                icon: <CheckCircle className="h-5 w-5 text-green-500" />,
              });
            } else {
              // TODO: Show UI to select from multiple results
              // For now, just use the first result
              // Extract the index and txnId from the response
              // The response format is { txnId: "...", ABHA: [{ index: 1, ... }] }
              console.log("Search response (multiple results):", dataArray);
              if (dataArray[0]?.ABHA && dataArray[0]?.ABHA.length > 0) {
                setSelectedIndex(dataArray[0].ABHA[0].index.toString());
                setSearchTxnId(dataArray[0].txnId);
              } else {
                console.error("Unexpected search response format:", data);
                throw new Error("Unexpected search response format");
              }

              // Proceed to request OTP with the index
              console.log(
                "Requesting OTP with index:",
                dataArray[0].ABHA[0].index,
                "type:",
                typeof dataArray[0].ABHA[0].index,
              );
              console.log(
                "Requesting OTP with searchTxnId:",
                dataArray[0].txnId,
              );

              const requestPayload = {
                index: dataArray[0].ABHA[0].index,
                searchTxnId: dataArray[0].txnId,
              };

              console.log("Request payload:", JSON.stringify(requestPayload));

              const otpResponse = await fetch(
                "/api/abdm/abha-find/mobile/request-otp",
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify(requestPayload),
                },
              );

              const otpData = await otpResponse.json();

              if (!otpResponse.ok) {
                throw new Error(otpData.error || "Failed to request OTP");
              }

              // Extract masked mobile number from the response message if available
              if (otpData.message && typeof otpData.message === "string") {
                console.log("OTP message:", otpData.message);

                // Try to extract the masked mobile number using regex
                const [, maskedPart] =
                  otpData.message.match(/ending with (\*+\d{4})/) || [];

                // Build the final message if masked part is found
                if (maskedPart) {
                  setOtpSentMessage(
                    `We just sent an OTP on the Mobile Number ${maskedPart}. ` +
                      `Enter the OTP below to proceed with ABHA verification`,
                  );
                } else {
                  // Fallback message
                  setOtpSentMessage(
                    `We just sent an OTP to your mobile number. ` +
                      `Enter the OTP below to proceed with ABHA verification`,
                  );
                }
              } else {
                // Clear any previous message if no message in response
                setOtpSentMessage(null);
              }

              setTxnId(otpData.txnId || "");
              setStep("otp");
              // Reset and start the OTP timer
              setTimeRemaining(600); // 10 minutes
              setIsTimerActive(true);

              // Reset resend attempts when generating OTP for the first time
              setResendAttempts(0);

              // Start the resend cooldown timer (60 seconds)
              setResendTimeRemaining(60);
              setIsResendTimerActive(true);

              toast.success("OTP sent successfully to your mobile number", {
                duration: 4000,
                position: "top-center",
                icon: <CheckCircle className="h-5 w-5 text-green-500" />,
              });
            }
          } else {
            // No results found
            const customMsg = [
              "ABHA Number not found.",
              "We did not find any ABHA number linked to this mobile number.",
              "Please use ABHA linked mobile number",
            ].join("\n");

            setError(customMsg);

            toast.error(customMsg, {
              duration: 5000,
              position: "top-center",
              icon: <XCircle className="h-5 w-5 text-red-500" />,
            });

            if (!standalone) {
              trackVerificationError(
                mode as VerificationMethod,
                "otp_requested",
                "ABDM-1115",
                customMsg,
                effectivePatientId,
              );
            }
          }
        } else {
          // Handle regular OTP response for other verification methods
          if (!data.txnId) {
            console.warn("No txnId received in response:", data);
          }

          // Extract masked mobile number from the response message if available
          if (data.message && typeof data.message === "string") {
            console.log("OTP message:", data.message);

            // Try to extract the masked mobile number using regex
            const [, maskedPart] =
              data.message.match(/ending with (\*+\d{4})/) || [];

            // Build the final message if masked part is found
            if (maskedPart) {
              setOtpSentMessage(
                `We just sent an OTP on the Mobile Number ${maskedPart} linked with Aadhaar. ` +
                  `Enter the OTP below to proceed with ABHA creation`,
              );
            } else {
              // Fallback message if we can't extract the masked number
              setOtpSentMessage(
                `We just sent an OTP to your Aadhaar-linked mobile number. ` +
                  `Enter the OTP below to proceed with ABHA creation`,
              );
            }
          } else {
            // Clear any previous message if no message in response
            setOtpSentMessage(null);
          }

          setTxnId(data.txnId || "");
          setStep("otp");
          // Reset and start the OTP timer
          setTimeRemaining(600); // 10 minutes
          setIsTimerActive(true);

          // Reset resend attempts when generating OTP for the first time
          setResendAttempts(0);

          // Start the resend cooldown timer (60 seconds)
          setResendTimeRemaining(60);
          setIsResendTimerActive(true);

          // Show success toast with method-specific message
          const successMessage = {
            aadhaar:
              "OTP sent successfully to your Aadhaar-linked mobile number",
            mobile: "OTP sent successfully to your mobile number",
            abha: "OTP sent successfully to your ABHA-linked mobile number",
            abhaAddress:
              "OTP sent successfully to your ABHA Address-linked mobile number",
          }[mode];

          toast.success(successMessage, {
            duration: 4000,
            position: "top-center",
            icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          });
        }
      } catch (error) {
        // Parse the error for better user feedback
        const parsedError = parseAbdmError(error);

        // Track the error with the parsed error code
        if (!standalone) {
          trackVerificationError(
            mode as VerificationMethod,
            "otp_requested",
            parsedError.code,
            parsedError.message,
            effectivePatientId,
          );
        }

        throw new Error(parsedError.message);
      }
    } catch (error) {
      console.error("Error generating OTP:", error);

      // Parse the error for better user feedback
      const parsedError = parseAbdmError(error);

      // Special handling for ABDM-1115 error
      if (parsedError.code === "ABDM-1115") {
        const customMessage =
          "ABHA Number not found. We did not find any ABHA number linked to this mobile number. Please use ABHA linked mobile number.";
        setError(customMessage);

        // Show error toast with the custom message
        toast.error(customMessage, {
          duration: 5000,
          position: "top-center",
          icon: <XCircle className="h-5 w-5 text-red-500" />,
        });
      } else {
        // Handle specific invalid Aadhaar error
        let errorMessage = parsedError.message;
        if (
          errorMessage.toLowerCase().includes("invalid loginid") ||
          errorMessage.toLowerCase().includes("invalid login id") ||
          errorMessage.includes("HTTP 400: Bad Request")
        ) {
          errorMessage = "Aadhaar Number is not valid";
        }

        setError(errorMessage);

        // Show error toast with the parsed error message
        toast.error(errorMessage, {
          duration: 5000,
          position: "top-center",
          icon: <XCircle className="h-5 w-5 text-red-500" />,
        });
      }
    } finally {
      setIsGeneratingOtp(false);
    }
  };

  // Function to verify OTP based on the selected mode
  const handleVerifyOtp = async () => {
    // Import and use proper OTP validation
    const { validateOTP } = await import("@/lib/validation");
    const otpValidation = validateOTP(otp);

    if (!otpValidation.isValid) {
      const errorMsg = otpValidation.message || "Please enter a valid OTP";
      setError(errorMsg);
      if (!standalone) {
        trackVerificationError(
          mode as VerificationMethod,
          "otp_verified",
          "VALIDATION_ERROR",
          errorMsg,
          effectivePatientId,
        );
      }
      return;
    }

    // For Aadhaar mode, we need the mobile number
    if (
      mode === "aadhaar" &&
      (!mobile || mobile.length !== 10 || !/^\d+$/.test(mobile))
    ) {
      const errorMsg = "Please enter a valid 10-digit Indian mobile number";
      setError(errorMsg);
      if (!standalone) {
        trackVerificationError(
          mode as VerificationMethod,
          "otp_verified",
          "VALIDATION_ERROR",
          errorMsg,
          effectivePatientId,
        );
      }
      return;
    }

    setError(null);
    setIsVerifyingOtp(true);

    // Track OTP verification attempt
    if (!standalone) {
      trackVerificationStep(
        mode as VerificationMethod,
        "otp_verified",
        effectivePatientId,
      );
    }

    try {
      let endpoint;
      let payload;

      switch (mode) {
        case "aadhaar":
          endpoint = "/api/abdm/abha-verify/aadhaar/verify";
          payload = {
            otp,
            mobile,
            txnId,
            patientId: standalone ? "" : effectivePatientId,
            standalone,
          };
          break;

        case "mobile":
          // For mobile verification, we need to check if this is for ABHA mobile verification
          // If abhaId is provided, we'll use the ABHA ID verification endpoint
          if (abhaId && abhaId.length > 0) {
            endpoint = "/api/abdm/abha-verify/abha-id/verify";
            payload = {
              otp,
              txnId,
              patientId: standalone ? "" : effectivePatientId,
              standalone,
              isMobileVerification: true, // Flag to indicate this is for mobile verification
            };
          } else if (searchResults.length > 0) {
            // Use the new ABHA find flow for verification
            endpoint = "/api/abdm/abha-find/mobile/verify-otp";
            payload = {
              otp,
              txnId,
              patientId: standalone ? "" : effectivePatientId,
              standalone,
            };
          } else {
            endpoint = "/api/abdm/abha-verify/mobile/verify";
            payload = {
              otp,
              txnId,
              patientId: standalone ? "" : effectivePatientId,
              mobile, // Pass the mobile number to the API
              standalone,
            };
          }
          break;

        case "abha":
          endpoint = "/api/abdm/abha-verify/abha-id/verify";
          payload = {
            otp,
            txnId,
            patientId: standalone ? "" : effectivePatientId,
            standalone,
            otpMethod,
          };
          break;

        case "abhaAddress":
          endpoint = "/api/abdm/abha-verify/abha-address/verify";
          payload = {
            otp,
            txnId,
            patientId: standalone ? "" : effectivePatientId,
            standalone,
            otpMethod,
          };
          break;

        default:
          const errorMsg = "Invalid verification mode";
          setError(errorMsg);
          if (!standalone) {
            trackVerificationError(
              mode as VerificationMethod,
              "otp_verified",
              "VALIDATION_ERROR",
              errorMsg,
              effectivePatientId,
            );
          }
          setIsVerifyingOtp(false);
          return;
      }

      // Make direct fetch call to the API
      try {
        // Ensure txnId is not empty
        if (!txnId) {
          throw new Error(
            "Transaction ID is missing. Please try generating the OTP again.",
          );
        }

        console.log("Starting OTP verification with payload:", {
          endpoint,
          otp: "REDACTED",
          txnId,
          mode,
        });

        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const data = await response.json();

        console.log("OTP verification response:", {
          ok: response.ok,
          status: response.status,
          hasData: !!data,
          authResult: data?.authResult,
        });

        // Enhanced error checking - ensure we properly handle all failure cases
        if (!response.ok) {
          const errorMessage =
            data.error ||
            data.message ||
            `HTTP ${response.status}: Failed to verify OTP`;
          console.error("OTP verification failed:", errorMessage);
          throw new Error(errorMessage);
        }

        // Additional validation - check if the response contains expected success indicators
        if (!data || (data.authResult && data.authResult === "Failed")) {
          const errorMessage =
            data.message ||
            "OTP verification failed. Please check your OTP and try again.";
          console.error(
            "OTP verification failed with authResult:",
            data.authResult,
          );
          throw new Error(errorMessage);
        }

        // Ensure we have the required data for successful verification
        if (!data.abhaNumber && !data.abhaAddress) {
          console.error(
            "OTP verification response missing required data:",
            data,
          );
          throw new Error(
            "Invalid response from verification service. Please try again.",
          );
        }

        console.log(
          "OTP verification successful, proceeding with success flow",
        );

        // Track verification success
        if (!standalone) {
          trackVerificationSuccess(
            mode as VerificationMethod,
            effectivePatientId,
            data.abhaNumber || "unknown",
          );

          // Track completion of verification flow
          trackVerificationStep(
            mode as VerificationMethod,
            "completed",
            effectivePatientId,
            {
              abhaNumber: data.abhaNumber,
              abhaAddress: data.abhaAddress,
            },
          );
        }

        // Show success toast with animation
        toast.success(
          <div className="flex flex-col space-y-1">
            <span className="font-medium">ABHA Verified Successfully!</span>
            <span className="text-sm opacity-90">Refreshing page...</span>
          </div>,
          {
            duration: 3000,
            position: "top-center",
            icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          },
        );

        // If in standalone mode, call the onSuccess callback
        if (standalone && onSuccess) {
          // Log the data for debugging
          console.log("ABHA verification data:", data);

          // For Aadhaar verification, we need to extract the DOB from the response
          // The DOB format in the verification response is "DD-MM-YYYY"
          let dob = "";
          if (
            mode === "aadhaar" &&
            data.accounts &&
            data.accounts[0] &&
            data.accounts[0].dob
          ) {
            dob = data.accounts[0].dob;
          }

          // Add a small delay to ensure all processing is complete before navigation
          setTimeout(() => {
            console.log("Calling onSuccess callback after verification");
            onSuccess({
              abhaNumber: data.abhaNumber,
              abhaAddress: data.abhaAddress,
              healthIdNumber: data.healthIdNumber || "",
              name: data.name,
              gender: data.gender,
              dayOfBirth: data.dayOfBirth,
              monthOfBirth: data.monthOfBirth,
              yearOfBirth: data.yearOfBirth,
              dob: dob, // Include the full DOB if available
              phone: data.phone,
              email: data.email,
              address: data.address,
              city: data.city,
              state: data.state,
              pincode: data.pincode,
              token: data.token,
              stateName: data.stateName,
              districtName: data.districtName,
              abhaStatus: data.abhaStatus,
            });
          }, 500); // Small delay to ensure API response is fully processed
        } else {
          // Wait a moment for the toast to be visible
          setTimeout(() => {
            // Refresh the page to show the updated ABHA information
            window.location.reload();
          }, 2000);
        }
      } catch (error) {
        // Parse the error for better user feedback
        const parsedError = parseAbdmError(error);

        // Track the error with the parsed error code
        if (!standalone) {
          trackVerificationError(
            mode as VerificationMethod,
            "otp_verified",
            parsedError.code,
            parsedError.message,
            effectivePatientId,
          );
        }

        // Show a user-friendly error message
        toast.error(parsedError.message, {
          duration: 5000,
          position: "top-center",
          icon: <XCircle className="h-5 w-5 text-red-500" />,
        });

        throw new Error(parsedError.message);
      }

      // No need to call onSuccess callback as we're refreshing the page
    } catch (error) {
      console.error("Error verifying OTP:", error);

      // Parse the error for better user feedback
      const parsedError = parseAbdmError(error);
      setError(parsedError.message);

      // Show error toast with the parsed error message
      toast.error(parsedError.message, {
        duration: 5000,
        position: "top-center",
        icon: <XCircle className="h-5 w-5 text-red-500" />,
      });
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  // Function to resend OTP
  const handleResendOtp = async () => {
    // Don't allow resend if timer is still active or maximum attempts reached
    if (isResendTimerActive) {
      toast.error(
        `Please wait ${resendTimeRemaining} seconds before resending OTP`,
        {
          duration: 3000,
          position: "top-center",
        },
      );
      return;
    }

    if (resendAttempts >= 2) {
      toast.error("Maximum resend attempts reached", {
        duration: 3000,
        position: "top-center",
      });
      return;
    }

    setError(null);
    setIsGeneratingOtp(true);

    // Track OTP resend attempt
    if (!standalone) {
      trackVerificationStep(
        mode as VerificationMethod,
        "otp_requested",
        effectivePatientId,
        { resend: true },
      );
    }

    try {
      let endpoint;
      let payload;

      switch (mode) {
        case "aadhaar":
          endpoint = "/api/abdm/abha-verify/aadhaar/otp";
          payload = { aadhaar };
          break;

        case "mobile":
          // For resend, use the appropriate endpoint based on whether we're using the new flow
          if (searchResults.length > 0 && selectedIndex && searchTxnId) {
            endpoint = "/api/abdm/abha-find/mobile/request-otp";
            console.log(
              "Resend OTP with index:",
              selectedIndex,
              "type:",
              typeof selectedIndex,
            );
            console.log("Resend OTP with searchTxnId:", searchTxnId);
            payload = {
              index: Number(selectedIndex), // Convert to number as the API expects
              searchTxnId: searchTxnId,
            };
          } else {
            endpoint = "/api/abdm/abha-verify/mobile/otp";
            payload = { mobile, captchaToken };
          }
          break;

        case "abha":
          endpoint = "/api/abdm/abha-verify/abha-id/otp";
          payload = { abhaNumber: abhaId, otpMethod };
          break;

        case "abhaAddress":
          endpoint = "/api/abdm/abha-verify/abha-address/otp";
          payload = { abhaAddress, otpMethod };
          break;

        default:
          const errorMsg = "Invalid verification mode";
          setError(errorMsg);
          if (!standalone) {
            trackVerificationError(
              mode as VerificationMethod,
              "otp_requested",
              "VALIDATION_ERROR",
              errorMsg,
              effectivePatientId,
            );
          }
          setIsGeneratingOtp(false);
          return;
      }

      // Make direct fetch call to the API
      try {
        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to request OTP");
        }

        if (!data.txnId) {
          console.warn("No txnId received in resend response:", data);
        }

        setTxnId(data.txnId || "");

        // Extract masked mobile number from the response message if available
        if (data.message && typeof data.message === "string") {
          console.log("Resend OTP message:", data.message);

          // Try to extract the masked mobile number using regex
          const [, maskedPart] =
            data.message.match(/ending with (\*+\d{4})/) || [];

          // Build the final message if masked part is found
          if (maskedPart) {
            setOtpSentMessage(
              `We just sent an OTP on the Mobile Number ${maskedPart} linked with Aadhaar. ` +
                `Enter the OTP below to proceed with ABHA creation`,
            );
          } else {
            // Fallback message if we can't extract the masked number
            setOtpSentMessage(
              `We just sent an OTP to your Aadhaar-linked mobile number. ` +
                `Enter the OTP below to proceed with ABHA creation`,
            );
          }
        }

        // Increment resend attempts
        const newResendAttempts = resendAttempts + 1;
        setResendAttempts(newResendAttempts);

        // Reset the OTP validity timer (10 minutes)
        setTimeRemaining(600);
        setIsTimerActive(true);

        // Start the resend cooldown timer (60 seconds)
        setResendTimeRemaining(60);
        setIsResendTimerActive(true);

        // Show appropriate toast message based on remaining attempts
        const remainingAttempts = 2 - newResendAttempts;
        const baseMessage = {
          aadhaar:
            "OTP resent successfully to your Aadhaar-linked mobile number",
          mobile: "OTP resent successfully to your mobile number",
          abha: "OTP resent successfully to your ABHA-linked mobile number",
          abhaAddress:
            "OTP resent successfully to your ABHA Address-linked mobile number",
        }[mode];

        const successMessage =
          remainingAttempts > 0
            ? `${baseMessage}. ${remainingAttempts} resend attempt${remainingAttempts === 1 ? "" : "s"} remaining.`
            : `${baseMessage}. No more resend attempts available.`;

        toast.success(successMessage, {
          duration: 4000,
          position: "top-center",
          icon: <RefreshCw className="h-5 w-5 text-green-500" />,
        });
      } catch (error) {
        // Parse the error for better user feedback
        const parsedError = parseAbdmError(error);

        // Track the error with the parsed error code
        if (!standalone) {
          trackVerificationError(
            mode as VerificationMethod,
            "otp_requested",
            parsedError.code,
            parsedError.message,
            effectivePatientId,
          );
        }

        throw new Error(parsedError.message);
      }
    } catch (error) {
      console.error("Error resending OTP:", error);

      // Parse the error for better user feedback
      const parsedError = parseAbdmError(error);

      // Special handling for ABDM-1115 error
      if (parsedError.code === "ABDM-1115") {
        const customMessage =
          "ABHA Number not found. We did not find any ABHA number linked to this mobile number. Please use ABHA linked mobile number.";
        setError(customMessage);

        // Show error toast with the custom message
        toast.error(customMessage, {
          duration: 5000,
          position: "top-center",
          icon: <XCircle className="h-5 w-5 text-red-500" />,
        });
      } else {
        // Handle specific invalid Aadhaar error
        let errorMessage = parsedError.message;
        if (
          errorMessage.toLowerCase().includes("invalid loginid") ||
          errorMessage.toLowerCase().includes("invalid login id") ||
          errorMessage.includes("HTTP 400: Bad Request")
        ) {
          errorMessage = "Aadhaar Number is not valid";
        }

        setError(errorMessage);

        // Show error toast with the parsed error message
        toast.error(errorMessage, {
          duration: 5000,
          position: "top-center",
          icon: <XCircle className="h-5 w-5 text-red-500" />,
        });
      }
    } finally {
      setIsGeneratingOtp(false);
    }
  };

  // Function to handle mode change
  const handleModeChange = (newMode: VerificationMode) => {
    setMode(newMode);
    setError(null);
    setOtp("");
    setTxnId("");
    setOtpMethod("aadhaar-linked"); // Reset OTP method to default
    // Clear the timer if active
    if (timerRef.current) {
      clearInterval(timerRef.current);
      setIsTimerActive(false);
    }

    // Track the verification method selection
    if (!standalone) {
      trackVerificationMethodSelected(
        newMode as VerificationMethod,
        effectivePatientId,
      );
    }
  };

  // Function to proceed to input step
  const handleProceedToInput = () => {
    setStep("input");
  };

  // Function to handle next step from input
  const handleNextFromInput = () => {
    // For ABHA and ABHA Address modes, go to OTP method selection
    if (mode === "abha" || mode === "abhaAddress") {
      setStep("otp-method");
    } else {
      // For other modes, directly generate OTP
      handleGenerateOtp();
    }
  };

  // Function to proceed to OTP generation after method selection
  const handleProceedToOtpGeneration = () => {
    handleGenerateOtp();
  };

  // Calculate the progress percentage based on the current step
  const getProgressPercentage = () => {
    switch (step) {
      case "select":
        return 20;
      case "input":
        return 40;
      case "otp-method":
        return 60;
      case "otp":
        return 80;
      case "success":
        return 100;
      default:
        return 0;
    }
  };

  // Get the step label based on the current step
  const getStepLabel = () => {
    switch (step) {
      case "select":
        return "Step 1: Select Verification Method";
      case "input":
        return "Step 2: Enter Details";
      case "otp-method":
        return "Step 3: Select OTP Method";
      case "otp":
        return mode === "abha" || mode === "abhaAddress"
          ? "Step 4: Verify OTP"
          : "Step 3: Verify OTP";
      case "success":
        return mode === "abha" || mode === "abhaAddress"
          ? "Step 5: Verification Complete"
          : "Step 4: Verification Complete";
      default:
        return "";
    }
  };

  // Get the icon for the verification method
  const getMethodIcon = (methodType: VerificationMode) => {
    switch (methodType) {
      case "aadhaar":
        return <Fingerprint className="h-5 w-5" />;
      case "mobile":
        return <Smartphone className="h-5 w-5" />;
      case "abha":
        return <CreditCard className="h-5 w-5" />;
      case "abhaAddress":
        return <AtSign className="h-5 w-5" />;
      default:
        return null;
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="space-y-6 mb-6 bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10 p-6">
        <div className="space-y-1">
          <CardTitle className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary mr-2"
            >
              <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
              <path d="M3 9h18"></path>
              <path d="M9 21V9"></path>
            </svg>
            Verify ABHA
          </CardTitle>
          <CardDescription>
            Verify your ABHA (Ayushman Bharat Health Account) using one of the
            available methods
          </CardDescription>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">{getStepLabel()}</span>
            <Badge
              variant={step === "success" ? "success" : "outline"}
              className="text-xs"
            >
              {step === "input" && "Details"}
              {step === "otp-method" && "OTP Method"}
              {step === "otp" && "Verification"}
              {step === "success" && "Complete"}
            </Badge>
          </div>
          <Progress value={getProgressPercentage()} className="h-2" />
        </div>
      </div>

      <CardContent className="p-6">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Step 1: Verification Mode Selection */}
        {step === "select" && (
          <div className="space-y-6">
            <div className="mb-8">
              <Label
                htmlFor="verification-mode"
                className="mb-4 block text-lg font-semibold"
              >
                Select Verification Method
              </Label>
              <RadioGroup
                value={mode}
                onValueChange={(value) =>
                  handleModeChange(value as VerificationMode)
                }
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                <Label
                  htmlFor="aadhaar-mode"
                  className={`cursor-pointer flex items-center p-5 rounded-lg border-2 transition-all ${
                    mode === "aadhaar"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-muted hover:border-muted-foreground/20 hover:shadow-sm"
                  }`}
                >
                  <RadioGroupItem
                    value="aadhaar"
                    id="aadhaar-mode"
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div
                      className={`mr-4 p-3 rounded-full ${
                        mode === "aadhaar"
                          ? "bg-primary/10 text-primary"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {getMethodIcon("aadhaar")}
                    </div>
                    <div>
                      <span className="text-base font-semibold">
                        Aadhaar OTP
                      </span>
                      <p className="text-sm text-muted-foreground mt-1">
                        Verify using your Aadhaar-linked mobile number
                      </p>
                    </div>
                  </div>
                </Label>

                <Label
                  htmlFor="mobile-mode"
                  className={`cursor-pointer flex items-center p-5 rounded-lg border-2 transition-all ${
                    mode === "mobile"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-muted hover:border-muted-foreground/20 hover:shadow-sm"
                  }`}
                >
                  <RadioGroupItem
                    value="mobile"
                    id="mobile-mode"
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div
                      className={`mr-4 p-3 rounded-full ${
                        mode === "mobile"
                          ? "bg-primary/10 text-primary"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {getMethodIcon("mobile")}
                    </div>
                    <div>
                      <span className="text-base font-semibold">
                        Mobile OTP
                      </span>
                      <p className="text-sm text-muted-foreground mt-1">
                        Verify using your registered mobile number
                      </p>
                    </div>
                  </div>
                </Label>

                <Label
                  htmlFor="abha-mode"
                  className={`cursor-pointer flex items-center p-5 rounded-lg border-2 transition-all ${
                    mode === "abha"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-muted hover:border-muted-foreground/20 hover:shadow-sm"
                  }`}
                >
                  <RadioGroupItem
                    value="abha"
                    id="abha-mode"
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div
                      className={`mr-4 p-3 rounded-full ${
                        mode === "abha"
                          ? "bg-primary/10 text-primary"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {getMethodIcon("abha")}
                    </div>
                    <div>
                      <span className="text-base font-semibold">ABHA ID</span>
                      <p className="text-sm text-muted-foreground mt-1">
                        Verify using your existing ABHA ID
                      </p>
                    </div>
                  </div>
                </Label>

                <Label
                  htmlFor="abhaAddress-mode"
                  className={`cursor-pointer flex items-center p-5 rounded-lg border-2 transition-all ${
                    mode === "abhaAddress"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-muted hover:border-muted-foreground/20 hover:shadow-sm"
                  }`}
                >
                  <RadioGroupItem
                    value="abhaAddress"
                    id="abhaAddress-mode"
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div
                      className={`mr-4 p-3 rounded-full ${
                        mode === "abhaAddress"
                          ? "bg-primary/10 text-primary"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      {getMethodIcon("abhaAddress")}
                    </div>
                    <div>
                      <span className="text-base font-semibold">
                        ABHA Address
                      </span>
                      <p className="text-sm text-muted-foreground mt-1">
                        Verify using your ABHA Address (e.g., john.doe@sbx)
                      </p>
                    </div>
                  </div>
                </Label>
              </RadioGroup>
            </div>

            <div className="flex justify-end mt-8">
              <Button
                onClick={handleProceedToInput}
                className="h-10 px-4 shadow-sm transition-all hover:shadow-md"
              >
                <ArrowRight className="mr-2 h-4 w-4" />
                Continue
              </Button>
            </div>
          </div>
        )}

        {/* Input Step */}
        {step === "input" && (
          <div className="space-y-4 px-6">
            {/* Aadhaar Input */}
            {mode === "aadhaar" && (
              <div className="mr-auto max-w-[420px] space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="aadhaar">Aadhaar Number</Label>
                  <Input
                    id="aadhaar"
                    placeholder="Enter 12-digit Aadhaar number"
                    value={maskAadhaarNumber(aadhaar) || aadhaar}
                    onChange={(e) => {
                      const value = e.target.value;
                      // If it's masked, don't allow editing
                      if (value.includes("X")) return;
                      // Only allow digits and limit to 12
                      const cleanValue = value.replace(/\D/g, "").slice(0, 12);
                      setAadhaar(cleanValue);
                    }}
                    onFocus={(e) => {
                      // Show actual value when focused
                      e.target.value = aadhaar;
                    }}
                    onBlur={(e) => {
                      // Show masked value when blurred and complete
                      const masked = maskAadhaarNumber(aadhaar);
                      if (masked) {
                        e.target.value = masked;
                      }
                    }}
                    maxLength={14}
                    disabled={isGeneratingOtp}
                  />
                  <p className="text-sm text-muted-foreground">
                    You will need your Aadhaar-linked mobile number in the next
                    step
                  </p>
                </div>

                {/* Beneficiary Name Input for Aadhaar verification */}
                <div className="space-y-2">
                  <Label htmlFor="beneficiaryName">Beneficiary Name</Label>
                  <Input
                    id="beneficiaryName"
                    placeholder="Enter beneficiary name"
                    value={beneficiaryName}
                    onChange={(e) => setBeneficiaryName(e.target.value)}
                    disabled={isGeneratingOtp}
                  />
                  <p className="text-sm text-muted-foreground">
                    This name will be used in the consent declaration
                  </p>
                </div>
              </div>
            )}

            {/* Mobile Input */}
            {mode === "mobile" && (
              <div className="mr-auto max-w-[420px] space-y-4">
                {/* Mobile Number Input */}
                <div className="space-y-2">
                  <Label htmlFor="mobile-input">Mobile Number</Label>
                  <div className="flex">
                    <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                      +91
                    </div>
                    <Input
                      id="mobile-input"
                      placeholder="Enter your 10-digit mobile number"
                      value={mobile}
                      onChange={(e) =>
                        setMobile(
                          e.target.value.replace(/\D/g, "").slice(0, 10),
                        )
                      }
                      maxLength={10}
                      disabled={isGeneratingOtp}
                      className="rounded-l-none"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {abhaId
                      ? "Enter the mobile number linked with your ABHA account"
                      : "Enter your mobile number for verification"}
                  </p>
                </div>

                {/* Captcha for mobile verification */}
                <SimpleCaptcha
                  onValidationChange={setIsCaptchaValid}
                  onCaptchaChange={setCaptchaToken}
                  disabled={isGeneratingOtp}
                  required
                />
              </div>
            )}

            {/* ABHA ID Input */}
            {mode === "abha" && (
              <div className="mr-auto max-w-[420px] space-y-2">
                <AbhaNumberInput
                  value={abhaId}
                  onChange={setAbhaId}
                  disabled={isGeneratingOtp}
                  label="ABHA Number"
                  required
                />
              </div>
            )}

            {/* ABHA Address Input */}
            {mode === "abhaAddress" && (
              <div className="mr-auto max-w-[420px] space-y-2">
                <Label htmlFor="abha-address">ABHA Address</Label>
                <Input
                  id="abha-address"
                  placeholder="Enter your ABHA Address (e.g., john.doe@sbx)"
                  value={abhaAddress}
                  onChange={(e) => setAbhaAddress(e.target.value)}
                  disabled={isGeneratingOtp}
                />
                <p className="text-sm text-muted-foreground">
                  Enter your ABHA Address in the format username@domain (e.g.,
                  john.doe@sbx, user123@abdm)
                </p>
              </div>
            )}

            {/* <div className="flex items-center justify-between">
                <Label
                  htmlFor="language-select"
                  className="text-sm font-medium"
                >
                  Consent Language
                </Label>
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                  <Select
                    value={selectedLanguage}
                    onValueChange={setSelectedLanguage}
                    disabled={isGeneratingOtp}
                  >
                    <SelectTrigger
                      id="language-select"
                      className="w-[140px] h-8"
                    >
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="hi">हिन्दी (Hindi)</SelectItem>
                      <SelectItem value="ta">தமிழ் (Tamil)</SelectItem>
                      <SelectItem value="te">తెలుగు (Telugu)</SelectItem>
                      <SelectItem value="kn">ಕನ್ನಡ (Kannada)</SelectItem>
                      <SelectItem value="ml">മലയാളം (Malayalam)</SelectItem>
                      <SelectItem value="bn">বাংলা (Bengali)</SelectItem>
                      <SelectItem value="gu">ગુજરાતી (Gujarati)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div> */}

            {/* Consent section - only show for Aadhaar verification */}
            {mode === "aadhaar" && (
              <div className="mr-auto w-full space-y-4 mt-6 p-4 border rounded-md bg-muted/30">
                {/* Multiple consent checkboxes as per ABDM requirements */}
                <div className="space-y-4 mt-4">
                  <p className="text-sm font-medium text-gray-900 mb-3">
                    I hereby declare that:
                  </p>

                  {/* Consent 1 - ABHA creation using Aadhaar (checked) */}
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="consent1"
                      checked={consent1Accepted}
                      onCheckedChange={(c) => setConsent1Accepted(c as boolean)}
                      disabled={isGeneratingOtp}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor="consent1"
                        className="text-sm leading-relaxed cursor-pointer"
                      >
                        {aadhaarConsentTexts.consent1.en}
                      </Label>
                    </div>
                  </div>

                  {/* Consent 2 - Document linking (should NOT be checked for Aadhaar-based creation) */}
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="consent2"
                      checked={consent2Accepted}
                      onCheckedChange={(c) => setConsent2Accepted(c as boolean)}
                      disabled={isGeneratingOtp}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor="consent2"
                        className="text-sm leading-relaxed cursor-pointer"
                      >
                        {aadhaarConsentTexts.consent2.en}
                      </Label>
                    </div>
                  </div>

                  {/* Consent 3 - Health records sharing */}
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="consent3"
                      checked={consent3Accepted}
                      onCheckedChange={(c) => setConsent3Accepted(c as boolean)}
                      disabled={isGeneratingOtp}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor="consent3"
                        className="text-sm leading-relaxed cursor-pointer"
                      >
                        {aadhaarConsentTexts.consent3.en}
                      </Label>
                    </div>
                  </div>

                  {/* Consent 4 - Healthcare provider sharing */}
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="consent4"
                      checked={consent4Accepted}
                      onCheckedChange={(c) => setConsent4Accepted(c as boolean)}
                      disabled={isGeneratingOtp}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor="consent4"
                        className="text-sm leading-relaxed cursor-pointer"
                      >
                        {aadhaarConsentTexts.consent4.en}
                      </Label>
                    </div>
                  </div>

                  {/* Consent 5 - Anonymization */}
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="consent5"
                      checked={consent5Accepted}
                      onCheckedChange={(c) => setConsent5Accepted(c as boolean)}
                      disabled={isGeneratingOtp}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor="consent5"
                        className="text-sm leading-relaxed cursor-pointer"
                      >
                        {aadhaarConsentTexts.consent5.en}
                      </Label>
                    </div>
                  </div>

                  {/* Consent 6 - Healthcare Worker */}
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="consent6"
                      checked={consent6Accepted}
                      onCheckedChange={(c) => setConsent6Accepted(c as boolean)}
                      disabled={isGeneratingOtp}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor="consent6"
                        className="text-sm leading-relaxed cursor-pointer"
                      >
                        I, (name of healthcare worker- depending on the username
                        used for logging in to the system), confirm that I have
                        duly informed and explained the beneficiary of the
                        contents of consent for aforementioned purposes.
                      </Label>
                    </div>
                  </div>

                  {/* Consent 7 - Beneficiary */}
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="consent7"
                      checked={consent7Accepted}
                      onCheckedChange={(c) => setConsent7Accepted(c as boolean)}
                      disabled={isGeneratingOtp}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <Label
                        htmlFor="consent7"
                        className="text-sm leading-relaxed cursor-pointer"
                      >
                        I, ({beneficiaryName || "beneficiary name"}), have been
                        explained about the consent as stated above and hereby
                        provide my consent for the aforementioned purposes.
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3 mt-8">
              <Button
                variant="outline"
                onClick={() => setStep("select")}
                disabled={isGeneratingOtp}
                className="h-10 px-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                onClick={handleNextFromInput}
                disabled={
                  isGeneratingOtp ||
                  // Aadhaar verification - exact same validation as ABHA creation
                  (mode === "aadhaar" &&
                    (aadhaar.length !== 12 ||
                      beneficiaryName.trim() === "" || // Require beneficiary name
                      consent2Accepted || // Disable if consent2 is checked (document-based creation)
                      !consent1Accepted || // Require consent1
                      !consent3Accepted || // Require consent3
                      !consent4Accepted || // Require consent4
                      !consent5Accepted || // Require consent5
                      !consent6Accepted || // Require consent6
                      !consent7Accepted)) || // Require consent7
                  // Other verification methods - no consent required
                  (mode === "mobile" &&
                    (!mobile ||
                      !/^(\+91|0)?[1-9][0-9]{9}$/.test(mobile) ||
                      !isCaptchaValid)) ||
                  (mode === "abha" &&
                    (!abhaId || !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(abhaId))) ||
                  (mode === "abhaAddress" &&
                    (!abhaAddress ||
                      !/^[a-zA-Z0-9]+([._]?[a-zA-Z0-9]+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z0-9]+)*$/.test(
                        abhaAddress,
                      )))
                }
                className="h-10 px-4 shadow-sm transition-all hover:shadow-md"
              >
                {isGeneratingOtp ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <ArrowRight className="mr-2 h-4 w-4" />
                )}
                {isGeneratingOtp
                  ? "Generating OTP..."
                  : mode === "abha" || mode === "abhaAddress"
                    ? "Next"
                    : "Generate OTP"}
              </Button>
            </div>
          </div>
        )}

        {/* OTP Method Selection Step */}
        {step === "otp-method" && (
          <div className="space-y-6 px-6">
            <div className="mr-auto max-w-[500px] space-y-4">
              <div>
                <Label className="text-base font-semibold">
                  Select OTP Delivery Method
                </Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Choose how you want to receive the OTP for verification
                </p>
              </div>

              <RadioGroup
                value={otpMethod}
                onValueChange={(value) => setOtpMethod(value as OtpMethod)}
                className="space-y-3"
              >
                <Label
                  htmlFor="aadhaar-linked"
                  className={`cursor-pointer flex items-center p-4 rounded-lg border-2 transition-all ${
                    otpMethod === "aadhaar-linked"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-muted hover:border-muted-foreground/20 hover:shadow-sm"
                  }`}
                >
                  <RadioGroupItem
                    value="aadhaar-linked"
                    id="aadhaar-linked"
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div
                      className={`mr-4 p-3 rounded-full ${
                        otpMethod === "aadhaar-linked"
                          ? "bg-primary/10 text-primary"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      <Fingerprint className="h-5 w-5" />
                    </div>
                    <div>
                      <span className="text-base font-semibold">
                        Aadhaar-linked Mobile Number
                      </span>
                      <p className="text-sm text-muted-foreground mt-1">
                        Send OTP to the mobile number linked with your Aadhaar
                      </p>
                    </div>
                  </div>
                </Label>

                <Label
                  htmlFor="abha-linked"
                  className={`cursor-pointer flex items-center p-4 rounded-lg border-2 transition-all ${
                    otpMethod === "abha-linked"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-muted hover:border-muted-foreground/20 hover:shadow-sm"
                  }`}
                >
                  <RadioGroupItem
                    value="abha-linked"
                    id="abha-linked"
                    className="mr-3"
                  />
                  <div className="flex items-center">
                    <div
                      className={`mr-4 p-3 rounded-full ${
                        otpMethod === "abha-linked"
                          ? "bg-primary/10 text-primary"
                          : "bg-muted text-muted-foreground"
                      }`}
                    >
                      <Smartphone className="h-5 w-5" />
                    </div>
                    <div>
                      <span className="text-base font-semibold">
                        ABHA-linked Mobile Number
                      </span>
                      <p className="text-sm text-muted-foreground mt-1">
                        Send OTP to the mobile number linked with your ABHA
                        account
                      </p>
                    </div>
                  </div>
                </Label>
              </RadioGroup>
            </div>

            <div className="flex justify-end space-x-3 mt-8">
              <Button
                variant="outline"
                onClick={() => setStep("input")}
                disabled={isGeneratingOtp}
                className="h-10 px-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                onClick={handleProceedToOtpGeneration}
                disabled={isGeneratingOtp}
                className="h-10 px-4 shadow-sm transition-all hover:shadow-md"
              >
                {isGeneratingOtp ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <ArrowRight className="mr-2 h-4 w-4" />
                )}
                {isGeneratingOtp ? "Generating OTP..." : "Generate OTP"}
              </Button>
            </div>
          </div>
        )}

        {/* OTP Verification Step */}
        {step === "otp" && (
          <div className="space-y-6">
            <div className="mr-auto max-w-[420px] space-y-3">
              <Label htmlFor="otp" className="text-base font-semibold">
                Enter OTP
              </Label>
              {otpSentMessage && (
                <div className="p-3 bg-blue-50 border border-blue-100 rounded-md text-sm text-blue-800 mb-2">
                  {otpSentMessage}
                </div>
              )}
              <Input
                id="otp"
                placeholder="Enter OTP sent to your mobile"
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/\D/g, ""))}
                maxLength={6}
                disabled={isVerifyingOtp}
                className="h-14 text-xl font-medium tracking-wider text-center shadow-sm"
              />

              <div className="flex justify-between items-center">
                {isTimerActive && (
                  <span className="text-sm text-muted-foreground">
                    OTP expires in {formatTime(timeRemaining)}
                  </span>
                )}

                {isResendTimerActive ? (
                  <span className="text-sm text-muted-foreground italic">
                    Wait {formatTime(resendTimeRemaining)} to resend (
                    {2 - resendAttempts} attempt
                    {2 - resendAttempts === 1 ? "" : "s"} left)
                  </span>
                ) : resendAttempts >= 2 ? (
                  <span className="text-sm text-red-500 italic">
                    Maximum resend attempts reached
                  </span>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleResendOtp}
                    disabled={
                      isGeneratingOtp ||
                      isResendTimerActive ||
                      resendAttempts >= 2
                    }
                    className="h-8 px-3 text-xs font-medium"
                  >
                    {isGeneratingOtp ? (
                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-3 w-3" />
                    )}
                    Resend OTP
                  </Button>
                )}
              </div>
            </div>

            {/* Mobile number input for Aadhaar mode */}
            {mode === "aadhaar" && (
              <div className="mr-auto max-w-[420px] space-y-3">
                <Label htmlFor="mobile" className="text-base font-semibold">
                  Mobile Number
                </Label>
                <div className="flex">
                  <div className="flex items-center justify-center px-4 border border-r-0 rounded-l-md bg-muted text-muted-foreground font-medium">
                    +91
                  </div>
                  <Input
                    id="mobile"
                    placeholder="Enter your 10-digit mobile number"
                    value={mobile}
                    onChange={(e) =>
                      setMobile(e.target.value.replace(/\D/g, "").slice(0, 10))
                    }
                    maxLength={10}
                    disabled={isVerifyingOtp}
                    className="rounded-l-none h-12 text-base shadow-sm"
                  />
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3 mt-8">
              <Button
                variant="outline"
                onClick={() => {
                  // Go back to the appropriate step based on mode
                  const previousStep =
                    mode === "abha" || mode === "abhaAddress"
                      ? "otp-method"
                      : "input";
                  setStep(previousStep);
                  setOtp("");
                  setTxnId("");
                  setError(null);
                  // Clear the timers
                  if (timerRef.current) {
                    clearInterval(timerRef.current);
                  }
                  if (resendTimerRef.current) {
                    clearInterval(resendTimerRef.current);
                  }
                  setIsTimerActive(false);
                  setIsResendTimerActive(false);
                }}
                disabled={isVerifyingOtp}
                className="h-10 px-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                onClick={(e) => {
                  // Prevent multiple clicks during verification
                  if (isVerifyingOtp) {
                    e.preventDefault();
                    return;
                  }
                  handleVerifyOtp();
                }}
                disabled={
                  isVerifyingOtp ||
                  !otp ||
                  !/^[0-9]{6}$/.test(otp) ||
                  (mode === "aadhaar" &&
                    (!mobile || !/^(\+91|0)?[1-9][0-9]{9}$/.test(mobile)))
                }
                className="h-10 px-4 shadow-sm transition-all hover:shadow-md"
              >
                {isVerifyingOtp ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                {isVerifyingOtp ? "Verifying..." : "Verify OTP"}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
