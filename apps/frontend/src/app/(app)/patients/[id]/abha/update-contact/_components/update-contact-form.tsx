"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, Mail, Phone, ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { AbhaProfile, Patient } from "@prisma/client";
import { useRouter } from "next/navigation";

interface UpdateContactFormProps {
  patient: Patient & { abhaProfile: AbhaProfile | null };
}

export function UpdateContactForm({ patient }: UpdateContactFormProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"email" | "mobile">("email");

  // Email update state
  const [email, setEmail] = useState("");
  const [emailOtp, setEmailOtp] = useState("");
  const [emailTxnId, setEmailTxnId] = useState("");
  const [emailStep, setEmailStep] = useState<"input" | "otp">("input");
  const [isGeneratingEmailOtp, setIsGeneratingEmailOtp] = useState(false);
  const [isVerifyingEmailOtp, setIsVerifyingEmailOtp] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);

  // Mobile update state
  const [mobile, setMobile] = useState("");
  const [mobileOtp, setMobileOtp] = useState("");
  const [mobileTxnId, setMobileTxnId] = useState("");
  const [mobileStep, setMobileStep] = useState<"input" | "otp">("input");
  const [isGeneratingMobileOtp, setIsGeneratingMobileOtp] = useState(false);
  const [isVerifyingMobileOtp, setIsVerifyingMobileOtp] = useState(false);
  const [mobileError, setMobileError] = useState<string | null>(null);

  // Handle email OTP generation
  const handleGenerateEmailOtp = async () => {
    // Validate email
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    setEmailError(null);
    setIsGeneratingEmailOtp(true);

    try {
      const response = await fetch(
        "/api/abdm/abha-profile/update/email/request-otp",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            patientId: patient.id,
            email,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to generate OTP");
      }

      setEmailTxnId(data.txnId);
      setEmailStep("otp");

      toast.success("OTP sent successfully to your email", {
        duration: 4000,
        position: "top-center",
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      });
    } catch (error: any) {
      setEmailError(error.message || "Failed to generate OTP");
      toast.error(error.message || "Failed to generate OTP", {
        duration: 4000,
        position: "top-center",
      });
    } finally {
      setIsGeneratingEmailOtp(false);
    }
  };

  // Handle email OTP verification
  const handleVerifyEmailOtp = async () => {
    // Validate OTP
    if (!emailOtp || emailOtp.length < 4) {
      setEmailError("Please enter a valid OTP");
      return;
    }

    setEmailError(null);
    setIsVerifyingEmailOtp(true);

    try {
      const response = await fetch(
        "/api/abdm/abha-profile/update/email/verify-otp",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            patientId: patient.id,
            otp: emailOtp,
            txnId: emailTxnId,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to verify OTP");
      }

      toast.success("Email updated successfully", {
        duration: 4000,
        position: "top-center",
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      });

      // Reset form
      setEmail("");
      setEmailOtp("");
      setEmailTxnId("");
      setEmailStep("input");

      // Redirect to ABHA view page
      router.push(`/patients/${patient.id}/abha/view`);
    } catch (error: any) {
      setEmailError(error.message || "Failed to verify OTP");
      toast.error(error.message || "Failed to verify OTP", {
        duration: 4000,
        position: "top-center",
      });
    } finally {
      setIsVerifyingEmailOtp(false);
    }
  };

  // Handle mobile OTP generation
  const handleGenerateMobileOtp = async () => {
    // Validate mobile
    if (!mobile || !/^[0-9]{10}$/.test(mobile)) {
      setMobileError("Please enter a valid 10-digit mobile number");
      return;
    }

    setMobileError(null);
    setIsGeneratingMobileOtp(true);

    try {
      const response = await fetch(
        "/api/abdm/abha-profile/update/mobile/request-otp",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            patientId: patient.id,
            mobile,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to generate OTP");
      }

      setMobileTxnId(data.txnId);
      setMobileStep("otp");

      toast.success("OTP sent successfully to your mobile", {
        duration: 4000,
        position: "top-center",
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      });
    } catch (error: any) {
      setMobileError(error.message || "Failed to generate OTP");
      toast.error(error.message || "Failed to generate OTP", {
        duration: 4000,
        position: "top-center",
      });
    } finally {
      setIsGeneratingMobileOtp(false);
    }
  };

  // Handle mobile OTP verification
  const handleVerifyMobileOtp = async () => {
    // Validate OTP
    if (!mobileOtp || mobileOtp.length < 4) {
      setMobileError("Please enter a valid OTP");
      return;
    }

    setMobileError(null);
    setIsVerifyingMobileOtp(true);

    try {
      const response = await fetch(
        "/api/abdm/abha-profile/update/mobile/verify-otp",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            patientId: patient.id,
            otp: mobileOtp,
            txnId: mobileTxnId,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to verify OTP");
      }

      toast.success("Mobile number updated successfully", {
        duration: 4000,
        position: "top-center",
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      });

      // Reset form
      setMobile("");
      setMobileOtp("");
      setMobileTxnId("");
      setMobileStep("input");

      // Redirect to ABHA view page
      router.push(`/patients/${patient.id}/abha/view`);
    } catch (error: any) {
      setMobileError(error.message || "Failed to verify OTP");
      toast.error(error.message || "Failed to verify OTP", {
        duration: 4000,
        position: "top-center",
      });
    } finally {
      setIsVerifyingMobileOtp(false);
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="space-y-6 mb-6 bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10 p-6">
        <div className="space-y-1">
          <CardTitle className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary mr-2"
            >
              <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
              <path d="M3 9h18"></path>
              <path d="M9 21V9"></path>
            </svg>
            Update ABHA Contact Information
          </CardTitle>
          <CardDescription>
            Update your email or mobile number in your ABHA profile
          </CardDescription>
        </div>
      </div>

      <CardContent className="p-6">
        <Tabs
          defaultValue="email"
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as "email" | "mobile")}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="email" className="flex items-center">
              <Mail className="mr-2 h-4 w-4" />
              Update Email
            </TabsTrigger>
            <TabsTrigger value="mobile" className="flex items-center">
              <Phone className="mr-2 h-4 w-4" />
              Update Mobile
            </TabsTrigger>
          </TabsList>

          {/* Email Update Tab */}
          <TabsContent value="email" className="space-y-6">
            {emailError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{emailError}</AlertDescription>
              </Alert>
            )}

            {emailStep === "input" ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">New Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your new email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="max-w-md"
                  />
                </div>
                <div className="flex justify-end">
                  <Button
                    onClick={handleGenerateEmailOtp}
                    disabled={isGeneratingEmailOtp}
                    className="w-full max-w-[200px]"
                  >
                    {isGeneratingEmailOtp ? (
                      <>
                        <span className="animate-spin mr-2">⟳</span>
                        Sending OTP...
                      </>
                    ) : (
                      "Send OTP"
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="emailOtp">Enter OTP</Label>
                  <Input
                    id="emailOtp"
                    type="text"
                    placeholder="Enter the OTP sent to your email"
                    value={emailOtp}
                    onChange={(e) => setEmailOtp(e.target.value)}
                    className="max-w-md"
                  />
                  <p className="text-sm text-muted-foreground">
                    An OTP has been sent to {email}
                  </p>
                </div>
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setEmailStep("input");
                      setEmailError(null);
                    }}
                    className="max-w-[200px]"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button
                    onClick={handleVerifyEmailOtp}
                    disabled={isVerifyingEmailOtp}
                    className="max-w-[200px]"
                  >
                    {isVerifyingEmailOtp ? (
                      <>
                        <span className="animate-spin mr-2">⟳</span>
                        Verifying...
                      </>
                    ) : (
                      "Verify OTP"
                    )}
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          {/* Mobile Update Tab */}
          <TabsContent value="mobile" className="space-y-6">
            {mobileError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{mobileError}</AlertDescription>
              </Alert>
            )}

            {mobileStep === "input" ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="mobile">New Mobile Number</Label>
                  <Input
                    id="mobile"
                    type="tel"
                    placeholder="Enter your new 10-digit mobile number"
                    value={mobile}
                    onChange={(e) => setMobile(e.target.value)}
                    className="max-w-md"
                  />
                </div>
                <div className="flex justify-end">
                  <Button
                    onClick={handleGenerateMobileOtp}
                    disabled={isGeneratingMobileOtp}
                    className="w-full max-w-[200px]"
                  >
                    {isGeneratingMobileOtp ? (
                      <>
                        <span className="animate-spin mr-2">⟳</span>
                        Sending OTP...
                      </>
                    ) : (
                      "Send OTP"
                    )}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="mobileOtp">Enter OTP</Label>
                  <Input
                    id="mobileOtp"
                    type="text"
                    placeholder="Enter the OTP sent to your mobile"
                    value={mobileOtp}
                    onChange={(e) => setMobileOtp(e.target.value)}
                    className="max-w-md"
                  />
                  <p className="text-sm text-muted-foreground">
                    An OTP has been sent to {mobile}
                  </p>
                </div>
                <div className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setMobileStep("input");
                      setMobileError(null);
                    }}
                    className="max-w-[200px]"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button
                    onClick={handleVerifyMobileOtp}
                    disabled={isVerifyingMobileOtp}
                    className="max-w-[200px]"
                  >
                    {isVerifyingMobileOtp ? (
                      <>
                        <span className="animate-spin mr-2">⟳</span>
                        Verifying...
                      </>
                    ) : (
                      "Verify OTP"
                    )}
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
