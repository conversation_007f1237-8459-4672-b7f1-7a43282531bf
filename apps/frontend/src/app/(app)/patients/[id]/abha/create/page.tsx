import { notFound, redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { AbhaCreationForm } from "./_components/abha-creation-form";

export default async function PatientAbhaCreatePage({
  params,
}: {
  params: { id: string };
}) {
  // Get the patient with ABHA profile
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    return notFound();
  }

  // If patient already has an ABHA profile, redirect to view page
  if (patient.abhaProfile && patient.abhaProfile.abhaNumber) {
    redirect(`/patients/${patient.id}/abha/view`);
  }

  return <AbhaCreationForm patient={patient} />;
}
