"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle, Unlink, LogIn, Eye, Edit } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AbhaProfile, Patient } from "@prisma/client";
import dynamic from "next/dynamic";
import Link from "next/link";

// Dynamically import the delete confirmation dialog
const DelinkProfileModal = dynamic(() => import("./delink-profile-modal"), {
  ssr: false,
});

interface AbhaCardViewerProps {
  patient: Patient;
  abhaProfile: AbhaProfile;
}

export function AbhaCardViewer({ patient, abhaProfile }: AbhaCardViewerProps) {
  const [error, setError] = useState<string | null>(null);
  const [showDelinkDialog, setShowDelinkDialog] = useState(false);

  return (
    <>
      <Card className="w-full overflow-hidden border shadow-sm">
        <CardHeader className="bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10">
          <CardTitle className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary mr-2"
            >
              <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
              <path d="M3 9h18"></path>
              <path d="M9 21V9"></path>
            </svg>
            ABHA Details
          </CardTitle>
          <CardDescription>
            View and manage ABHA (Ayushman Bharat Health Account) details
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <>
            <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border border-green-200 dark:border-green-900/50 rounded-lg p-4 shadow-sm">
              <div className="flex items-start">
                <div className="bg-white dark:bg-gray-800 rounded-full p-2 text-green-600 dark:text-green-400 mr-3 shadow-sm">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <div>
                  <h3 className="text-green-800 dark:text-green-400 font-medium">
                    ABHA Verified
                  </h3>
                  <p className="text-green-700 dark:text-green-500 text-sm mt-1">
                    This patient has a verified ABHA account
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-card border rounded-lg p-4 shadow-sm transition-all hover:shadow-md">
                <div className="text-sm text-muted-foreground mb-1">
                  ABHA Number
                </div>
                <div className="font-medium text-foreground flex items-center">
                  <span className="inline-block w-2 h-2 mr-2 rounded-full bg-primary"></span>
                  {abhaProfile?.abhaNumber}
                </div>
              </div>

              {abhaProfile?.abhaAddress && (
                <div className="bg-card border rounded-lg p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="text-sm text-muted-foreground mb-1">
                    ABHA Address
                  </div>
                  <div className="font-medium text-foreground flex items-center">
                    <span className="inline-block w-2 h-2 mr-2 rounded-full bg-secondary"></span>
                    {abhaProfile?.abhaAddress}
                  </div>
                </div>
              )}

              {abhaProfile?.healthIdNumber && (
                <div className="bg-card border rounded-lg p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="text-sm text-muted-foreground mb-1">
                    Health ID
                  </div>
                  <div className="font-medium text-foreground flex items-center">
                    <span className="inline-block w-2 h-2 mr-2 rounded-full bg-green-500"></span>
                    {abhaProfile?.healthIdNumber}
                  </div>
                </div>
              )}
            </div>
          </>
        </CardContent>
        <CardFooter className="flex justify-between p-6 border-t bg-muted/10">
          <div className="flex items-center gap-2">
            <div className="text-sm text-muted-foreground">
              Last updated: {new Date().toLocaleDateString()}
            </div>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setShowDelinkDialog(true)}
              className="flex items-center"
            >
              <Unlink className="mr-2 h-4 w-4" />
              Delink ABHA
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link
                href={`/patients/${
                  patient.id
                }/abha/login?returnUrl=${encodeURIComponent(
                  `/patients/${patient.id}/abha/view`,
                )}`}
              >
                <LogIn className="mr-2 h-4 w-4" />
                Login to ABHA
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/patients/${patient.id}/abha/update-contact`}>
                <Edit className="mr-2 h-4 w-4" />
                Update Contact
              </Link>
            </Button>
            {abhaProfile?.abhaNumber &&
            abhaProfile.abhaNumber !== "undefined" ? (
              <Button variant="default" size="sm" asChild>
                <Link href={`/patients/${patient.id}/abha/view-card`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View ABHA Card
                </Link>
              </Button>
            ) : (
              <div className="text-sm text-muted-foreground">
                KYC verification required to download ABHA card
              </div>
            )}
          </div>
        </CardFooter>
      </Card>

      <DelinkProfileModal
        patient={patient}
        showDelinkDialog={showDelinkDialog}
        setShowDelinkDialog={setShowDelinkDialog}
        error={error}
        setError={setError}
      />
    </>
  );
}
