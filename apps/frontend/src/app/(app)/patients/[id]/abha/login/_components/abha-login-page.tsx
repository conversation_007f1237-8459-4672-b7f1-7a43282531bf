"use client";

import { useState } from "react";
import { AbhaLoginForm } from "@/components/abha-login-form";
import { useRouter } from "next/navigation";
import { AbhaPageLayout } from "../../_components/abha-page-layout";

interface AbhaLoginPageProps {
  patientId: string;
  abhaNumber: string;
  returnUrl: string;
}

export function AbhaLoginPage({
  patientId,
  abhaNumber,
  returnUrl,
}: AbhaLoginPageProps) {
  const router = useRouter();
  // We'll keep the error state for future use
  const [_error, _setError] = useState<string | null>(null);

  // Function to handle successful login
  const handleLoginSuccess = () => {
    // Redirect to the return URL
    router.push(returnUrl);
  };

  // Function to handle cancel login
  const handleCancelLogin = () => {
    // Redirect to the patient ABHA view page
    router.push(`/patients/${patientId}/abha/view`);
  };

  return (
    <AbhaPageLayout
      patientId={patientId}
      title="ABHA Login"
      description="Login to your ABHA (Ayushman Bharat Health Account)"
    >
      <AbhaLoginForm
        abhaNumber={abhaNumber}
        patientId={patientId}
        onLoginSuccess={handleLoginSuccess}
        onCancel={handleCancelLogin}
      />
    </AbhaPageLayout>
  );
}
