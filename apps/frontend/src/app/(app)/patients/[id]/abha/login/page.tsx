import { notFound, redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { AbhaLoginPage } from "./_components/abha-login-page";

export default async function PatientAbhaLoginPage({
  params,
  searchParams,
}: {
  params: { id: string };
  searchParams: { returnUrl?: string };
}) {
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    return notFound();
  }

  // If patient doesn't have an ABHA profile, redirect to create page
  if (!patient.abhaProfile || !patient.abhaProfile.abhaNumber) {
    redirect(`/patients/${patient.id}/abha/create`);
  }

  // Get the return URL from the query parameters or default to the ABHA view page
  const returnUrl =
    searchParams?.returnUrl || `/patients/${patient.id}/abha/view`;

  return (
    <AbhaLoginPage
      patientId={patient.id}
      abhaNumber={patient.abhaProfile.abhaNumber}
      returnUrl={returnUrl}
    />
  );
}
