import { notFound } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import { PageHeader } from "@/components/page-header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IdCard, Plus, CheckCircle, Eye } from "lucide-react";
import Link from "next/link";

export default async function PatientAbhaPage({
  params,
}: {
  params: { id: string };
}) {
  const user = await getCurrentUser();

  if (!user) {
    return notFound();
  }

  // Get the patient with ABHA profile
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    return notFound();
  }

  const hasAbha = !!(patient.abhaProfile && patient.abhaProfile.abhaNumber);
  const isKycVerified = patient.abhaProfile?.kycVerified || false;

  return (
    <div className="container mx-auto space-y-6" id="abha">
      <PageHeader
        heading="ABHA Management"
        subheading={`Manage ABHA for ${patient.firstName} ${patient.lastName}`}
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* View ABHA Card */}
        <Card
          className={`overflow-hidden border shadow-md ${
            !hasAbha ? "opacity-60" : ""
          }`}
        >
          <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardTitle className="flex items-center text-lg">
              <Eye className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
              View ABHA
            </CardTitle>
            <CardDescription>
              View and download ABHA card details
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <p className="text-sm text-muted-foreground mb-6">
              {hasAbha
                ? isKycVerified
                  ? "View your ABHA card and download it for future reference."
                  : "ABHA card available but KYC verification required for download."
                : "No ABHA card available. Create or verify an ABHA first."}
            </p>
            {hasAbha && isKycVerified ? (
              <Button className="w-full" asChild>
                <Link href={`/patients/${patient.id}/abha/view`}>
                  <IdCard className="h-4 w-4 mr-2" />
                  View ABHA Card
                </Link>
              </Button>
            ) : hasAbha && !isKycVerified ? (
              <Button className="w-full" disabled>
                <IdCard className="h-4 w-4 mr-2" />
                KYC Verification Required
              </Button>
            ) : (
              <Button className="w-full" disabled>
                <IdCard className="h-4 w-4 mr-2" />
                View ABHA Card
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Create ABHA */}
        <Card
          className={`overflow-hidden border shadow-md ${
            hasAbha ? "opacity-60" : ""
          }`}
        >
          <CardHeader className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
            <CardTitle className="flex items-center text-lg">
              <Plus className="h-5 w-5 mr-2 text-green-600 dark:text-green-400" />
              Create ABHA
            </CardTitle>
            <CardDescription>Create a new ABHA using Aadhaar</CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <p className="text-sm text-muted-foreground mb-6">
              {hasAbha
                ? "This patient already has an ABHA card."
                : "Create a new ABHA card using Aadhaar verification."}
            </p>
            <Button className="w-full" asChild disabled={hasAbha}>
              <Link href={`/patients/${patient.id}/abha/create`}>
                <Plus className="h-4 w-4 mr-2" />
                Create New ABHA
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Verify ABHA */}
        <Card
          className={`overflow-hidden border shadow-md ${
            hasAbha ? "opacity-60" : ""
          }`}
        >
          <CardHeader className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
            <CardTitle className="flex items-center text-lg">
              <CheckCircle className="h-5 w-5 mr-2 text-purple-600 dark:text-purple-400" />
              Verify ABHA
            </CardTitle>
            <CardDescription>Verify an existing ABHA</CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            <p className="text-sm text-muted-foreground mb-6">
              {hasAbha
                ? "This patient already has a verified ABHA card."
                : "Verify an existing ABHA using verification methods."}
            </p>
            <Button className="w-full" asChild disabled={hasAbha}>
              <Link href={`/patients/${patient.id}/abha/verify`}>
                <CheckCircle className="h-4 w-4 mr-2" />
                Verify Existing ABHA
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
