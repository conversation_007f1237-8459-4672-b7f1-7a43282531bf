"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useState } from "react";

export const DelinkProfileModal = ({
  showDelinkDialog,
  setShowDelinkDialog,
  error,
  setError,
  patient,
}: any) => {
  const router = useRouter();
  const [isDeLinking, setIsDeLinking] = useState(false);
  const handleDelink = async () => {
    try {
      setIsDeLinking(true);
      setError(null);

      const response = await fetch(`/api/patients/${patient?.id}/abha`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || "Failed to delink ABHA");
      }

      toast.success("ABHA profile successfully delinked");
      setShowDelinkDialog(false);

      // Redirect to the patient page
      router.push(`/patients/${patient?.id}`);
      router.refresh();
    } catch (error) {
      console.error("Error delinking ABHA:", error);
      setError(
        error instanceof Error ? error.message : "Failed to delink ABHA",
      );
      setShowDelinkDialog(false);
    } finally {
      setIsDeLinking(false);
    }
  };

  return (
    <Dialog open={showDelinkDialog} onOpenChange={setShowDelinkDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delink ABHA Profile</DialogTitle>
          <DialogDescription>
            Are you sure you want to delink this ABHA profile from the patient?
            This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setShowDelinkDialog(false)}
            disabled={isDeLinking}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelink}
            disabled={isDeLinking}
          >
            {isDeLinking ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Delinking...
              </>
            ) : (
              "Delink ABHA"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Also export as default for dynamic imports
export default DelinkProfileModal;
