"use client";

import { useState, useEffect, useRef } from "react";
import { parseAbdmError } from "@/lib/abdm-error-utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertCircle,
  Loader2,
  ArrowRight,
  ArrowLeft,
  RefreshCw,
  CheckCircle,
  SkipForward,
  XCircle,
  Plus,
  Shield,
  Phone,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

import { Fetch } from "@/services/fetch";
import { MobileUpdateVerificationDialog } from "@/components/mobile-update-verification-dialog";
import { maskAadhaarNumber } from "@/lib/aadhaar-utils";

interface AbhaCreationFormProps {
  patient: any;
  onSuccess?: (abhaDetails: any) => void;
  standalone?: boolean;
}

interface AbhaDetails {
  abhaNumber: string;
  abhaAddress: string;
  healthIdNumber: string;
  xToken?: string;
  xTokenExpiresAt?: string;
  txnId?: string;
  phrAddresses?: string[]; // Add PHR addresses from verify OTP response
  ABHAProfile?: {
    firstName?: string;
    lastName?: string;
    middleName?: string;
    dob?: string;
    gender?: string;
    mobile?: string;
    email?: string;
    phrAddress?: string[];
    abhaStatus?: string;
    ABHANumber?: string;
    address?: string;
    stateName?: string;
    districtName?: string;
    townName?: string;
    pinCode?: string;
    pincode?: string;
    yearOfBirth?: string;
    monthOfBirth?: string;
    dayOfBirth?: string;
    districtCode?: string;
    stateCode?: string;
  };
}

export function AbhaCreationForm({
  patient,
  standalone = false,
}: AbhaCreationFormProps) {
  const router = useRouter();
  const [aadhaar, setAadhaar] = useState("");
  const [otp, setOtp] = useState("");
  const [aadhaarLinkedMobile, setAadhaarLinkedMobile] = useState(""); // Read-only, from ABHA response
  const [communicationMobile, setCommunicationMobile] = useState(
    patient?.communicationMobile || patient?.phone || "",
  ); // User input
  const [beneficiaryName, setBeneficiaryName] = useState(""); // Beneficiary name for consent
  const [txnId, setTxnId] = useState("");
  const [step, setStep] = useState<
    "aadhaar" | "otp" | "abhaAddress" | "existingAddresses" | "success"
  >("aadhaar");
  const [isGeneratingOtp, setIsGeneratingOtp] = useState(false);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeRemaining, setTimeRemaining] = useState(600); // OTP validity timer (10 minutes)
  const [cameFromExistingAddresses, setCameFromExistingAddresses] =
    useState(false); // Track navigation source
  const [resendTimeRemaining, setResendTimeRemaining] = useState(60); // Resend timer (60 seconds)
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [isResendTimerActive, setIsResendTimerActive] = useState(false);
  const [resendAttempts, setResendAttempts] = useState(0);
  const [consent1Accepted, setConsent1Accepted] = useState(false); // ABHA creation consent
  const [consent2Accepted, setConsent2Accepted] = useState(false); // Document linking consent (should NOT be checked for Aadhaar)
  const [consent3Accepted, setConsent3Accepted] = useState(false); // Health records sharing consent
  const [consent4Accepted, setConsent4Accepted] = useState(false); // Healthcare provider sharing consent
  const [consent5Accepted, setConsent5Accepted] = useState(false); // Anonymization consent
  const [consent6Accepted, setConsent6Accepted] = useState(false); // Healthcare worker consent
  const [consent7Accepted, setConsent7Accepted] = useState(false); // Beneficiary consent
  const [healthcareWorkerName, setHealthcareWorkerName] = useState(""); // Healthcare worker name from user info
  const [abhaAddressSuggestions, setAbhaAddressSuggestions] = useState<
    string[]
  >([]);
  const [existingAbhaAddresses, setExistingAbhaAddresses] = useState<string[]>(
    [],
  );
  const [selectedAbhaAddress, setSelectedAbhaAddress] = useState("");
  const [customAbhaAddress, setCustomAbhaAddress] = useState("");
  const [customAbhaAddressError, setCustomAbhaAddressError] = useState("");
  const [abhaDetails, setAbhaDetails] = useState<AbhaDetails | null>(null);
  const [createdPatientId, setCreatedPatientId] = useState<string | null>(null);

  const [otpSentMessage, setOtpSentMessage] = useState<string | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const resendTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Mobile update states
  const [showMobileUpdateDialog, setShowMobileUpdateDialog] = useState(false);
  const [mobileUpdateData, setMobileUpdateData] = useState<any>(null);

  // Consent texts for different checkboxes
  const consentTexts = {
    // First consent - ABHA creation using Aadhaar
    consent1: {
      en: `I am voluntarily sharing my Aadhaar Number / Virtual ID issued by the Unique Identification Authority of India ("UIDAI"), and my demographic information for the purpose of creating an Ayushman Bharat Health Account number ("ABHA number") and Ayushman Bharat Health Account address ("ABHA Address"). I authorize NHA to use my Aadhaar number / Virtual ID for performing Aadhaar based authentication with UIDAI as per the provisions of the Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016 for the aforesaid purpose. I understand that UIDAI will share my e-KYC details, or response of "Yes" with NHA upon successful authentication.`,
    },
    // Second consent - Document linking (should be unchecked for Aadhaar creation)
    consent2: {
      en: `I intend to create Ayushman Bharat Health Account Number ("ABHA number") and Ayushman Bharat Health Account address ("ABHA Address") using document other than Aadhaar. (Click here to proceed further)`,
    },
    // Third consent - Health records sharing
    consent3: {
      en: `I consent to usage of my ABHA address and ABHA number for linking of my legacy (past) health records and those which will be generated during this encounter.`,
    },
    // Fourth consent - Anonymization
    consent4: {
      en: `I authorize the sharing of all my health records with healthcare provider(s) for the purpose of providing healthcare services to me during this encounter.`,
    },
    // Fifth consent - Public health purposes
    consent5: {
      en: `I consent to the anonymization and subsequent use of my health records for public health purposes.`,
    },

    ta: `நான் இதன் மூலம் அறிவிக்கிறேன்: நான் UIDAI வழங்கிய என் ஆதார் எண் மற்றும் ஜனதாரியல் தகவல்களை ABHA எண் உருவாக்குவதற்கான ஒரே நோக்கத்திற்காக தேசிய சுகாதார அங்கீகாரம் (NHA) உடன் தன்னார்வமாக பகிர்கிறேன். ABDM கால நிலையில் அறிவிக்கும் நோக்கங்களுக்காக, அவற்றில் சுகாதார சேவைகள் வழங்குவது உட்பட, என் ABHA எண்ணை பயன்படுத்தவோ பகிரவோ முடியும் என்பதை நான் புரிந்துகொள்கிறேன். மேலும், என் பெயர், முகவரி, வயது, பிறந்த தேதி, பாலினம் மற்றும் புகைப்படம் போன்ற என் தனிப்பட்ட அடையாளத் தகவல்கள் தேசிய டிஜிட்டல் சுகாதார சூழலில் (NDHE) பணியாற்றும் மருத்துவ வல்லுநர்கள் (எ.கா. மருத்துவர்), வசதிகள் (எ.கா. மருத்துவமனை, ஆய்வகம்) மற்றும் தரவு நம்பகையாளர்கள் (எ.கா. சுகாதார திட்டம்) போன்ற ABDM-இன் பதிவு செய்யப்பட்ட அல்லது இணைக்கப்பட்ட நிறுவனங்களுக்கு வழங்கப்படலாம் என்பதை எனக்கு அறிவாகியுள்ளது. நான் Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016 பிரிவுகள் படி UIDAI உடன் ஆதார் அடிப்படையிலான அங்கீகாரத்திற்காக என் ஆதார் எண்ணை NHA பயன்படுத்த அனுமதிக்கிறேன். வெற்றிகரமான அங்கீகாரத்தின் போது UIDAI என் e-KYC விவரங்களையோ “Yes” என்ற பதிலையோ NHA உடன் பகிரும் என்பதை நான் புரிந்துகொள்கிறேன். ஆதாருக்கு மாற்றாக பிற அடையாளங்களைப் பயன்படுத்தும் விருப்பம் எனக்கு வழங்கப்பட்டிருந்தாலும், NDHE முழுவதிலும் கிடைக்கும் பலன்களைப் பெற நான் ஆதார் எண்ணை தேர்தெடுத்துள்ளேன். ஆதார் எண்/VID எண்ணைத் தவிர என் பிற தனிப்பட்ட அடையாளத் தகவல்கள் மேலே குறிப்பிடப்பட்ட நோக்கங்களுக்காக பயன்படுத்தப்படலாம் மற்றும் பகிரப்படலாம் என்பதை நான் உறுதியாக அறிந்துள்ளேன். Aadhaar Act மற்றும் Regulations படி எந்த நேரத்திலும் இந்த ஒப்புதலை நான் ரத்து செய்யும் உரிமை எனக்கு உண்டு.`,
    te: `నేను ఇక్కడ ప్రకటిస్తున్నాను: UIDAI విడుదల చేసిన నా ఆధార్ నంబర్ మరియు జనాభా సమాచారాన్ని ABHA నంబర్ సృష్టించడానికి ఏకైక ఉద్దేశ్యార్థం జాతీయ ఆరోగ్య అథారిటీ (NHA)తో నేను స్వేచ్ఛగా పంచుకుంటున్నాను. ABDM సమయానుకూలంగా ప్రకటించే ప్రయోజనాల కోసం, వాటిలో ఆరోగ్య సేవల అందుబాటు కూడా ఉన్నది, నా ABHA నంబర్ ఉపయోగించి పంచుకోవచ్చు అని నేను అర్థం చేసుకుంటున్నాను. అదనంగా, నా పేరు, చిరునామా, వయసు, జన్మతేదీ, లింగం మరియు ఫోటో వంటి నా వ్యక్తిగత గుర్తింపు సమాచారాన్ని జాతీయ డిజిటల్ ఆరోగ్య పర్యావరణ వ్యవస్థ (NDHE)లో పనిచేస్తున్న వైద్య నిపుణులు (ఉదా. వైద్యులు), సౌకర్యాలు (ఉదా. ఆసుపత్రులు, ప్రయోగశాలలు) మరియు డేటా విశ్వాస్యులు (ఉదా. ఆరోగ్య కార్యక్రమాలు) వంటి ABDMకు నమోదు చేయబడ్డ లేదా లింక్ చేయబడ్డ సంస్థలకు అందజేయవచ్చునని నేను తెలుసుకున్నాను. నేను Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016 ప్రకారం UIDAIతో ఆధార్ ఆధారిత ప్రమాణీకరణ చేయడానికి నా ఆధార్ నంబర్ ఉపయోగించడానికి NHAని అనుమతిస్తున్నాను. విజయవంతమైన ప్రమాణీకరణ తరువాత UIDAI నా e-KYC వివరాలు లేదా “Yes” స్పందనను NHAతో పంచుకుంటుందని నేను అర్థం చేసుకుంటున్నాను. ఆధార్ కాకుండా ఇతర IDs ఉపయోగించే అవకాశం ఉందని నాకు తెలియజేయబడింది; అయినప్పటికీ, NDHE ద్వారా లభించే లాభాల కోసం ఆధార్ నంబర్ వాడడం నేను ఎన్నుకున్నాను. ఆధార్/VID సంఖ్య తప్ప నా ఇతర వ్యక్తిగత గుర్తింపు సమాచారాన్ని ఫై పేర్కొన్న ఉద్దేశ్యాలకు ఉపయోగించి పంచుకోవచ్చు అని నేను తెలుసుకున్నాను. Aadhaar Act & Regulations ప్రకారం నేను ఎప్పుడైనా ఈ సమ్మతి రద్దు చేసుకునే హక్కు కలిగి ఉన్నాను.`,
    kn: `ನಾನು ಈ ಮೂಲಕ ಘೋಷಿಸುತ್ತೇನೆ: UIDAI ಬಿಡುಗಡೆ ಮಾಡಿದ ನನ್ನ ಆಧಾರ್ ಸಂಖ್ಯೆ ಮತ್ತು ಡೆಮೋಗ್ರಾಫಿಕ್ ಮಾಹಿತಿಯನ್ನು ABHA ಸಂಖ್ಯೆ ರಚಿಸುವ ಏಕೈಕ ಉದ್ದೇಶಕ್ಕಾಗಿ ರಾಷ್ಟ್ರೀಯ ಆರೋಗ್ಯ ಪ್ರಾಧಿಕಾರ (NHA) ಜೊತೆ ನಾನು ಸ್ವೈಚ್ಛಿಕವಾಗಿ ಹಂಚಿಕೊಳ್ಳುತ್ತಿದ್ದೇನೆ. ABDM ಸಮಯಕಾಲದಲ್ಲಿ ಪ್ರಕಟಿಸಬಹುದಾದ ಉದ್ದೇಶಗಳಿಗೆ, ಅದರಲ್ಲಿ ಆರೋಗ್ಯ ಸೇವೆಗಳ ಒದಗಿಸುವುದೂ ಸೇರಿಬಿಡುತ್ತದೆ, ನನ್ನ ABHA ಸಂಖ್ಯೆಯನ್ನು ಬಳಸಬಹುದು ಹಾಗೂ ಹಂಚಿಕೊಳ್ಳಬಹುದು ಎಂದು ನಾನು ಅರಿತಿದ್ದೇನೆ. ಜೊತೆಗೆ, ನನ್ನ ಹೆಸರು, ವಿಳಾಸ, ವಯಸ್ಸು, ಜನ್ಮತಾರೀಕೆ, ಲಿಂಗ ಮತ್ತು ಛಾಯಾಚಿತ್ರ ಸೇರಿದಂತೆ ನನ್ನ ವೈಯಕ್ತिक ಗುರುತಿಸಬಹುದಾದ ಮಾಹಿತಿಯನ್ನು ರಾಷ್ಟ್ರೀಯ ಡಿಜಿಟಲ್ ಆರೋಗ್ಯ ಪರಿಸರ (NDHE)ಯಲ್ಲಿ ಕಾರ್ಯನಿರ್ವಹಿಸುವ ವೈದ್ಯಕೀಯ ವೃತ್ತಿಪರರು (ಉದಾ. ವೈದ್ಯರು), ಸೌಲಭ್ಯಗಳು (ಉದಾ. ಆಸ್ಪತ್ರೆಗೆ, ಪ್ರಯೋಗಾಲಯ) ಮತ್ತು ಡೇಟಾ ಫಿಡುಸಿಯಾರಿಗಳು (ಉದಾ. ಆರೋಗ್ಯ ಕಾರ್ಯಕ್ರಮಗಳು) ಆಗಿ ABDMಗೆ ನೋಂದಾಯಿತ ಅಥವಾ ಲಿಂಕ್ ಹೊಂದಿರುವ ಸಂಸ್ಥೆಗಳಿಗೆ ಒದಗಿಸಬಹುದು ಎಂಬುದು ನನಗೆ ತಿಳಿದಿದೆ. ನಾನು Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016 ಪ್ರಾವಧಾನಗಳ ಪ್ರಕಾರ UIDAI ಜೊತೆಗೆ ಆಧಾರ್ ಆಧಾರಿತ ಪ್ರಮಾಣಿೀಕರಣ ನಡೆಸಲು ನನ್ನ ಆಧಾರ್ ಸಂಖ್ಯೆಯನ್ನು NHA ಬಳಸಲು ಅನುಮತಿಸುತ್ತೇನೆ. ಯಶಸ್ವೀ ಪ್ರಮಾಣಿೀಕರಣದ ನಂತರ UIDAI ನನ್ನ e-KYC ವಿವರಗಳು ಅಥವಾ “Yes” ಪ್ರತಿಕ್ರಿಯೆಯನ್ನು NHA ಜೊತೆ ಹಂಚಿಕೊಳ್ಳುತ್ತದೆ ಎಂದು ನಾನು ಹೇಳಬಹುದು. ಆಧಾರ್ ಹೊರತುಪಡಿಸಿ ಇತರ IDs ಬಳಸುವ ಆಯ್ಕೆಯನ್ನು ನನಗೆ ನೀಡಲಾಗಿದೆ; ಆದಾದರೂ, NDHE ಮೂಲಕ ಲಭಿಸುವ ಲಾಭಗಳಿಗಾಗಿ ಆಧಾರ್ ಸಂಖ್ಯೆಯನ್ನು ನಾನು ಆಯ್ಕೆ ಮಾಡಿಕೊಂಡಿದ್ದೇನೆ. ಆಧಾರ್ ಸಂಖ್ಯೆ/VID ಹೊರತು ನನ್ನ ಇತರ ವೈಯಕ್ತಿಕ ಗುರುತಿಸಬಹುದಾದ ಮಾಹಿತಿಯನ್ನು ಮೇಲ್ಕಂಡ ಉದ್ದೇಶಗಳಿಗಾಗಿ ಬಳಸಬಹುದು ಮತ್ತು ಹಂಚಬಹುದು ಎಂಬ ಅರಿವು ನನಗಿದೆ. Aadhaar Act ಮತ್ತು Regulations ಪ್ರಕಾರ ನಾನು ಯಾವುದೇ ಸಮಯದಲ್ಲಿ ಈ ಒಪ್ಪಿಸುವಿಕೆಯನ್ನು ರದ್ದುಪಡಿಸಲು ಹಕ್ಕಿದೆ.`,
    ml: `ഞാൻ ഇതുവഴി പ്രഖ്യാപിക്കുന്നു: UIDAI പുറത്തിറക്കുന്ന എനിക്ക് ലഭിക്കുന്ന ആധാർ നമ്പർ ആൻഡ് ജനസംഖ്യ വിവരങ്ങൾ ABHA നമ്പർ സൃഷ്ടിക്കുന്ന ഏക ലക്ഷ്യാർത്ഥം ദേശീയ ആരോഗ്യ അതോറിറ്റിക്കൊപ്പം (NHA) ഞാൻ സ്വമേവ പങ്കുവയ്ക്കുകയാണ്. ABDM സമയംപ്രകാരം അറിയിക്കുന്ന ലക്ഷ്യങ്ങൾക്ക്, അവയിൽ ആരോഗ്യ സേവനങ്ങൾ നൽകലും ഉൾപ്പെടെ, എന്റെ ABHA നമ്പർ ഉപയോഗിക്കപ്പെട്ട് പങ്കിടപ്പെടാമെന്നത് ഞാൻ മനസിൽ വെക്കുന്നു. കൂടാതെ, എന്റെ പേര്, വിലാസം, വയസ്സ്, ജന്മതിയ്യതി, ലിംഗം, ഫോട്ടോ എന്നിവയടങ്ങിയ വ്യക്തിഗത തിരിച്ചറിയൽ വിവരങ്ങൾ NDHE യിലെ ആരോഗ്യ വിദഗ്ധർ (ഉദാ. ഡോക്ടർമാർ), സൗകര്യങ്ങൾ (ഉദാ. ആശുപത്രികൾ, ലബോറട്ടറികൾ) മൊത്തമ്മാരായ ഡേറ്റാ ഫിഡുശ്യറി ഏജൻസികൾ (ഉദാ. ആരോഗ്യ പരിപാടികൾ) എന്നിവരുമായി രജിസ്റ്റർ ചെയ്തോ ബന്ധിപ്പിച്ചോ ഉള്ള ABDM സ്ഥാപനങ്ങൾക്ക് ലഭ്യമാക്കാവുന്നതായും ഞാൻ അറിഞ്ഞിരിക്കുന്നു. ഞാൻ Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016 നിബന്ധനകൾ അനുസരിച്ച് UIDAIയുമായി ആധാർ അടിസ്ഥാനAUTH നടത്താൻ എന്റെ ആധാർ നമ്പർ ഉപയോഗിക്കാൻ NHA യെ അംഗീകരിക്കുന്നു. വിജയകരമായ AUTH കഴിഞ്ഞ് UIDAI എന്റെ e-KYC വിവരങ്ങളോ “Yes” എന്ന പ്രതികരണവുമാണ് NHAയുമായി പങ്കിടുക എന്നുമാണ് എനിക്കറിയുന്നത്. ആധാർ ഒഴികെയുള്ള മറ്റ് IDs ഉപയോഗിക്കുന്ന അവസരം എനിക്ക് ഉണ്ടെന്ന് അറിയിച്ചിട്ടുണ്ടെങ്കിലും, NDHE വഴി ലഭിക്കുന്ന ആനുകൂല്യങ്ങൾക്കായി ആധാർ നമ്പർ തിരഞ്ഞെടുക്കുന്നത് എനിക്ക് നിർബന്ധമായ തീരുമാനം. ആധാർ/VID നമ്പർ ഒഴികെയുള്ള എന്റെ മറ്റു വ്യക്തിഗത തിരിച്ചറിയൽ വിവരങ്ങൾ മുൻകൂട്ടി പരാമർശിച്ച ലക്ഷ്യങ്ങൾക്ക് ഉപയോഗിച്ച് പങ്കിടാവുന്നതായും ഞാൻ ബോധ്യപ്പെടുത്തുന്നു. Aadhaar Act & Regulations പ്രകാരം ഞാൻ എപ്പോൾ വേണമെങ്കിലും ഈ സമ്മതം റദ്ദാക്കാനുള്ള അവകാശം നിലനിർത്തുന്നു.`,
    bn: `আমি ঘোষণা করছি যে আমি UIDAI কর্তৃক ইস্যুকৃত আমার আধার নম্বর এবং জনসংখ্যাগত তথ্য শুধুমাত্র ABHA নম্বর তৈরির একমাত্র উদ্দেশ্যে জাতীয় স্বাস্থ্য কর্তৃপক্ষ (NHA)-এর সাথে স্বেচ্ছায় ভাগ করে নিচ্ছি। আমি বুঝি যে আমার ABHA নম্বর ABDM কর্তৃক সময়ে সময়ে ঘোষিত যে কোনো উদ্দেশ্যে, যার মধ্যে স্বাস্থ্যসেবা প্রদানও অন্তর্ভুক্ত, সেভাবে ব্যবহার ও ভাগ করা যেতে পারে। পাশাপাশি, আমার ব্যক্তিগত শনাক্তযোগ্য তথ্য (নাম, ঠিকানা, বয়স, জন্মতারিখ, লিঙ্গ এবং ছবি) জাতীয় ডিজিটাল স্বাস্থ্য ইকোসিস্টেম (NDHE)-এ নিবন্ধিত বা সংযুক্ত সংস্থাগুলো যেমন স্বাস্থ্য পেশাজীবী (ডাক্তার), সুবিধাসমূহ (হাসপাতাল, ল্যাব) এবং ডেটা বিশ্বাসী (স্বাস্থ্য কর্মসূচি)দের কাছে বিতরণ করা হতে পারে আমি এ বিষয়ে সচেতন। আমি NHA-কে অনুমোদন দিচ্ছি যে তারা Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016-এর বিধান অনুযায়ী UIDAI-এর সাথে আধার-ভিত্তিক প্রমাণীকরণ করার জন্য আমার আধার নম্বর ব্যবহার করতে পারে। সফল প্রমাণীকরণের পর UIDAI আমার e-KYC তথ্য বা “Yes” উত্তর NHA-এর সাথে শেয়ার করবে বলে আমি বুঝি। আধার ছাড়াও অন্যান্য শনাক্তকারী IDs ব্যবহারের বিকল্প আমাকে জানানো হয়েছে; তবে NDHE-এর মাধ্যমে সুবিধা অর্জনের জন্য আধার নম্বর ব্যবহার করাই আমি বেছে নিয়েছি। আধার নম্বর/VID নম্বর ছাড়া আমার অন্যান্য ব্যক্তিগত শনাক্তযোগ্য তথ্য উল্লিখিত উদ্দেশ্যে ব্যবহার ও ভাগ করা যেতে পারে এ বিষয়ে আমি নিশ্চিত। আমি Aadhaar Act এবং Regulations অনুযায়ী যে কোনো সময় এই সম্মতি প্রত্যাহার করার অধিকার রাখি।`,
    gu: `હું અહીંથી ઘોષણા કરું છું કે હું UIDAI દ્વારા જારી થયેલું મારું આધાર નંબર અને લોકસંખ્યા સંબંધિત માહિતી માત્ર ABHA નંબર બનાવવાનો એકમાત્ર હેતુ માટે રાષ્ટ્રીય આરોગ્ય અધિકરણ (NHA) સાથે સ્વૈચ્છિક રીતે શેર કરી રહ્યો/છું. હું સમજું છું કે મારું ABHA નંબર ABDM દ્વારા સમયાંતરે સૂચવાયેલા હેતુઓ માટે, જેમાં આરોગ્ય સેવાઓનો સમાવેશ થાય છે, માટે ઉપયોગ કરી શકાય અને શેર કરી શકાય છે. આગળ, મારું નામ, સરનામું, ઉંમર, જન્મતારીખ, લિંગ અને ફોટો જેવા મારી વ્યક્તિગત ઓળખી શકાય તેવી માહિતી રાષ્ટ્રીય ડિજિટલ આરોગ્ય ઇકોસિસ્ટમ (NDHE)માં નોંધાયેલ કે લિંક થયેલા સંસ્થાઓ—જેમ કે આરોગ્ય વ્યાવસાયિકો (ડોક્ટર્સ), સુવિધાઓ (હસ્પિટલ, પરીક્ષણશાળા) અને ડેટા ફિડ્યુશિયરીઝ (આરોગ્ય કાર્યક્રમો)—ને ઉપલબ્ધ કરાવવામાં આવી શકે છે તે હું જાણું છું. હું NHA ને Aadhaar (Targeted Delivery of Financial and other Subsidies, Benefits and Services) Act, 2016ના પ્રાવધાન મુજબ UIDAI સાથે આધાર આધારિત પ્રમાણિકરણ કરવા માટે મારું આધાર નંબર ઉપયોગ કરવા માટે અનકાર્ય આપું છું. સફળ પ્રમાણિકરણ પછી UIDAI મારું e-KYC વિગતો અથવા “Yes”નો જવાબ NHA સાથે શેર કરશે તે મને સ્પષ્ટ છે. મને આધાર સિવાય અન્ય ઓળખપત્રો ઉપયોગ કરવાની વિકલ્પના બાબતે માહિતગાર કરવામાં આવ્યો હતો; તેમ છતાં, NDHE દ્વારા લાભ લેવા માટે હું આધાર નંબર પસંદ કરું છું. હું જાણું છું કે આધાર નંબર/VID સિવાય મારી અન્ય વ્યક્તિગત ઓળખી શકાય તેવા માહિતી ઉપરોક્ત હેતુઓ માટે ઉપયોગ અને શેર કરી શકાય છે. હું Aadhaar Act અને Regulations મુજબ કોઈપણ સમય પર આ સંમતિ પાછી ખેંચવાનો અધિકાર રાખું છું।`,
  };

  // OTP validity timer (10 minutes)
  useEffect(() => {
    if (isTimerActive && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(timerRef.current!);
            setIsTimerActive(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [isTimerActive, timeRemaining]);

  // Resend OTP timer (60 seconds)
  useEffect(() => {
    if (isResendTimerActive && resendTimeRemaining > 0) {
      resendTimerRef.current = setInterval(() => {
        setResendTimeRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(resendTimerRef.current!);
            setIsResendTimerActive(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (resendTimerRef.current) clearInterval(resendTimerRef.current);
    };
  }, [isResendTimerActive, resendTimeRemaining]);

  // Initialize healthcare worker name from user info cookie
  useEffect(() => {
    try {
      const userInfoCookie = document.cookie
        .split("; ")
        .find((row) => row.startsWith("user-info="));

      if (userInfoCookie) {
        const userInfoValue = decodeURIComponent(userInfoCookie.split("=")[1]);
        const userInfo = JSON.parse(userInfoValue);
        if (userInfo.name) {
          setHealthcareWorkerName(userInfo.name);
        }
      }
    } catch (error) {
      console.error("Error parsing user info from cookie:", error);
    }
  }, []);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Validate custom ABHA address
  const validateCustomAbhaAddress = (address: string) => {
    // Check if it contains @ symbol
    if (!address.includes("@")) {
      return false;
    }

    const [username, domain] = address.split("@");

    // Check username part (before @) - must be 8-18 characters
    if (!username || username.length < 8 || username.length > 18) {
      return false;
    }

    // Check domain part (after @)
    if (!domain || domain.length < 1) {
      return false;
    }

    // Check if special characters are only dot (.) and/or underscore (_)
    const allowedSpecialChars = /^[a-zA-Z0-9._]*$/;
    if (!allowedSpecialChars.test(username)) {
      return false;
    }

    // Check that special characters are not at the beginning or end
    if (
      username.startsWith(".") ||
      username.startsWith("_") ||
      username.endsWith(".") ||
      username.endsWith("_")
    ) {
      return false;
    }

    // Check that username contains only alphanumeric and allowed special characters
    const usernameRegex = /^[a-zA-Z0-9]+([._][a-zA-Z0-9]+)*$/;
    if (!usernameRegex.test(username)) {
      return false;
    }

    // Check domain format (basic validation)
    const domainRegex = /^[a-zA-Z][a-zA-Z0-9]*$/;
    if (!domainRegex.test(domain)) {
      return false;
    }

    return true;
  };

  // Handle custom ABHA address change
  const handleCustomAbhaAddressChange = (value: string) => {
    setCustomAbhaAddress(value);

    if (value.trim() === "") {
      setCustomAbhaAddressError("");
      return;
    }

    if (!validateCustomAbhaAddress(value)) {
      setCustomAbhaAddressError(
        "Invalid ABHA address format. Please check the requirements below.",
      );
    } else {
      setCustomAbhaAddressError("");
      setSelectedAbhaAddress(value); // Set as selected when valid
    }
  };

  const getProgressPercentage = () => {
    switch (step) {
      case "aadhaar":
        return 20;
      case "otp":
        return 40;
      case "abhaAddress":
        return 60;
      case "existingAddresses":
        return 80;
      case "success":
        return 100;
      default:
        return 0;
    }
  };

  const getStepLabel = () => {
    switch (step) {
      case "aadhaar":
        return "Step 1: Enter Aadhaar Number";
      case "otp":
        return "Step 2: Verify OTP";
      case "abhaAddress":
        return "Step 3: Choose ABHA Address";
      case "existingAddresses":
        return "Step 3: Choose ABHA Address";
      case "success":
        return "Step 4: Completed";
      default:
        return "";
    }
  };

  const handleGenerateOtp = async () => {
    if (!/^[0-9]{12}$/.test(aadhaar)) {
      setError("Aadhaar Number is not valid");
      return;
    }
    if (!beneficiaryName.trim()) {
      setError("Please enter the beneficiary name");
      return;
    }
    // Check consent logic - consent2 should NOT be selected for Aadhaar-based ABHA creation
    if (consent2Accepted) {
      setError(
        "Document-based ABHA creation is not supported in this flow. Please uncheck the second consent to proceed with Aadhaar-based creation.",
      );
      return;
    }

    // Check that other required consents are accepted
    if (
      !consent1Accepted ||
      !consent3Accepted ||
      !consent4Accepted ||
      !consent5Accepted ||
      !consent6Accepted ||
      !consent7Accepted
    ) {
      setError(
        "Please accept all required consents (except the second one) to proceed",
      );
      return;
    }
    setError(null);
    setIsGeneratingOtp(true);
    try {
      const response = await fetch(
        "/api/abdm/abha-create/aadhaar/request-otp",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ aadhaar }),
        },
      );
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || "Failed to generate OTP");
      if (response.ok) {
        // Extract the last 4 digits of Aadhaar
        // Set the OTP message to display above the OTP input field
        // data.message === "OTP sent to Aadhaar registered mobile number ending with ******5935"

        // 1. grab the masked part ("******5935")
        const [, maskedPart] =
          data.message.match(/ending with (\*+\d{4})/) || [];

        // 2. build your final message
        setOtpSentMessage(
          `We just sent an OTP on the Mobile Number ${maskedPart} linked with Aadhaar. ` +
            `Enter the OTP below to proceed with ABHA creation`,
        );
      }

      if (!data.txnId) {
        console.warn("No txnId received in OTP request response:", data);
      }

      setTxnId(data.txnId || "");
      setStep("otp");

      // Reset resend attempts when generating OTP for the first time
      setResendAttempts(0);

      // Set the OTP validity timer (10 minutes)
      setTimeRemaining(600);
      setIsTimerActive(true);

      // Start the resend cooldown timer (60 seconds)
      setResendTimeRemaining(60);
      setIsResendTimerActive(true);

      // toast.success("OTP sent successfully to your Aadhaar-linked mobile number", {
      //   duration: 4000,
      //   position: "top-center",
      //   icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      // });
    } catch (err) {
      console.error("Error generating OTP:", err);
      const parsed = parseAbdmError(err);

      // Handle specific invalid Aadhaar error
      let errorMessage = parsed.message;
      if (
        errorMessage.toLowerCase().includes("invalid loginid") ||
        errorMessage.toLowerCase().includes("invalid login id") ||
        errorMessage.includes("HTTP 400: Bad Request")
      ) {
        errorMessage = "Aadhaar Number is not valid";
      }

      setError(errorMessage);
      toast.error(errorMessage, {
        duration: 5000,
        position: "top-center",
        icon: <XCircle className="h-5 w-5 text-red-500" />,
      });
    } finally {
      setIsGeneratingOtp(false);
    }
  };

  const handleResendOtp = async () => {
    // Don't allow resend if timer is still active or maximum attempts reached
    if (isResendTimerActive) {
      toast.error(
        `Please wait ${resendTimeRemaining} seconds before resending OTP`,
        {
          duration: 3000,
          position: "top-center",
        },
      );
      return;
    }

    if (resendAttempts >= 2) {
      toast.error("Maximum resend attempts reached", {
        duration: 3000,
        position: "top-center",
      });
      return;
    }

    setError(null);
    setIsGeneratingOtp(true);
    try {
      const response = await fetch(
        "/api/abdm/abha-create/aadhaar/request-otp",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ aadhaar }),
        },
      );
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || "Failed to resend OTP");

      if (!data.txnId) {
        console.warn("No txnId received in resend response:", data);
      }

      setTxnId(data.txnId || "");

      // Increment resend attempts
      const newResendAttempts = resendAttempts + 1;
      setResendAttempts(newResendAttempts);

      // Reset the OTP validity timer (10 minutes)
      setTimeRemaining(600);
      setIsTimerActive(true);

      // Start the resend cooldown timer (60 seconds)
      setResendTimeRemaining(60);
      setIsResendTimerActive(true);

      // Show appropriate toast message based on remaining attempts
      const remainingAttempts = 2 - newResendAttempts;
      const successMessage =
        remainingAttempts > 0
          ? `OTP resent successfully. ${remainingAttempts} resend attempt${remainingAttempts === 1 ? "" : "s"} remaining.`
          : "OTP resent successfully. No more resend attempts available.";

      toast.success(successMessage, {
        duration: 4000,
        position: "top-center",
        icon: <RefreshCw className="h-5 w-5 text-green-500" />,
      });
    } catch (err) {
      console.error("Error resending OTP:", err);
      const parsed = parseAbdmError(err);
      setError(parsed.message);
      toast.error(parsed.message, {
        duration: 5000,
        position: "top-center",
        icon: <XCircle className="h-5 w-5 text-red-500" />,
      });
    } finally {
      setIsGeneratingOtp(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!/^[0-9]{6}$/.test(otp)) {
      setError("Please enter a valid OTP");
      return;
    }
    if (!/^[0-9]{10}$/.test(aadhaarLinkedMobile)) {
      setError("Please enter a valid 10-digit Aadhaar-linked mobile number");
      return;
    }
    setError(null);
    setIsVerifyingOtp(true);
    try {
      console.log("Starting ABHA creation OTP verification with payload:", {
        otp: "REDACTED",
        aadhaarLinkedMobile,
        communicationMobile,
        txnId,
      });

      const createResp = await fetch(
        "/api/abdm/abha-create/aadhaar/verify-otp",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            otp,
            mobile: aadhaarLinkedMobile, // Use Aadhaar-linked mobile for ABDM API
            txnId,
            patientId: standalone ? null : patient?.id,
            userMobile: communicationMobile || aadhaarLinkedMobile, // Communication mobile or fallback to Aadhaar-linked
          }),
        },
      );
      const createData = await createResp.json();

      console.log("ABHA creation OTP verification response:", {
        ok: createResp.ok,
        status: createResp.status,
        hasData: !!createData,
        mobileUpdateRequired: createData?.mobileUpdateRequired,
      });

      // Check if mobile update is required (only if communication mobile is different from Aadhaar-linked)
      if (createData.mobileUpdateRequired) {
        console.log("Mobile update required, showing dialog");
        setMobileUpdateData(createData);
        setShowMobileUpdateDialog(true);
        setIsVerifyingOtp(false);
        return;
      }

      // Enhanced error checking
      if (!createResp.ok) {
        const errorMessage =
          createData.error || createData.message || "Failed to create ABHA";
        console.error("ABHA creation OTP verification failed:", errorMessage);
        throw new Error(errorMessage);
      }

      // Additional validation - ensure we have the required data
      // Note: abhaAddress might be empty if user needs to select from suggestions
      if (!createData.abhaNumber) {
        console.error(
          "ABHA creation response missing ABHA number:",
          createData,
        );
        throw new Error(
          "Invalid response from ABHA creation service. Please try again.",
        );
      }

      // If abhaAddress is empty but we have phrAddresses, use the first one
      let finalAbhaAddress = createData.abhaAddress;
      if (
        !finalAbhaAddress &&
        createData.phrAddresses &&
        createData.phrAddresses.length > 0
      ) {
        finalAbhaAddress = createData.phrAddresses[0];
        console.log(
          "Using first PHR address as ABHA address:",
          finalAbhaAddress,
        );
      }

      console.log(
        "ABHA creation OTP verification successful, proceeding with creation flow",
      );
      console.log("Final ABHA address to use:", finalAbhaAddress);

      // Extract ABHA profile data - it can be in ABHAProfile field or directly in the response
      console.log("createData in frontend:", createData);
      console.log("createData.ABHAProfile:", createData.ABHAProfile);

      const abhaProfile = createData.ABHAProfile || createData;
      console.log("abhaProfile extracted:", abhaProfile);

      const details: AbhaDetails = {
        abhaNumber: createData.abhaNumber,
        abhaAddress: finalAbhaAddress,
        healthIdNumber: createData.healthIdNumber,
        txnId: createData.txnId || txnId,
        phrAddresses: createData.phrAddresses || [], // Include PHR addresses from verify OTP response
        ABHAProfile: {
          firstName: abhaProfile.firstName || "",
          lastName: abhaProfile.lastName || "",
          middleName: abhaProfile.middleName || "",
          dob: abhaProfile.dob || "",
          gender: abhaProfile.gender || "",
          mobile:
            abhaProfile.mobile || aadhaarLinkedMobile || communicationMobile,
          email: abhaProfile.email || "",
          address: abhaProfile.address || "",
          stateName: abhaProfile.stateName || "",
          districtName: abhaProfile.districtName || "",
          townName: abhaProfile.townName || "",
          pincode: abhaProfile.pincode || "",
          yearOfBirth: abhaProfile.yearOfBirth || "",
          monthOfBirth: abhaProfile.monthOfBirth || "",
          dayOfBirth: abhaProfile.dayOfBirth || "",
          phrAddress: abhaProfile.phrAddress || [],
          abhaStatus: abhaProfile.abhaStatus || "ACTIVE",
          ABHANumber: abhaProfile.ABHANumber || createData.abhaNumber,
        },
      };

      console.log("Final abhaDetails being set:", details);
      console.log("Final ABHAProfile:", details.ABHAProfile);
      setAbhaDetails(details);

      // IMPORTANT: Store the patient ID if this is standalone mode
      if (standalone && createData.patientId) {
        setCreatedPatientId(createData.patientId);
      }

      // Check for PHR addresses from the verify OTP response first, then from ABHAProfile
      const phrAddresses =
        createData.phrAddresses || abhaProfile.phrAddress || [];

      if (phrAddresses && phrAddresses.length > 0) {
        setExistingAbhaAddresses(phrAddresses);
        setSelectedAbhaAddress(phrAddresses[0]); // Select the first address by default
        setStep("existingAddresses");
      } else {
        // Otherwise, go to the address suggestions step
        setCameFromExistingAddresses(false); // Reset flag when coming from OTP verification
        setStep("abhaAddress");
        await fetchAddressSuggestions();
      }
    } catch (err) {
      const parsed = parseAbdmError(err);
      setError(parsed.message);
      toast.error(parsed.message, { duration: 5000, position: "top-center" });
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  const fetchAddressSuggestions = async () => {
    if (!txnId) {
      setError("Transaction ID missing");
      return;
    }
    setIsLoadingSuggestions(true);
    setError(null);
    try {
      console.log("Fetching ABHA address suggestions for txnId:", txnId);
      const resp = await fetch(
        `/api/abdm/abha-create/aadhaar/suggestions?txnId=${txnId}`,
      );
      const data = await resp.json();
      console.log("Suggestions API response:", data);

      if (!resp.ok) throw new Error(data.error || "Failed to load suggestions");

      // Ensure we have an array of suggestions and limit to 3 items
      const suggestions = data.suggestions || [];
      const limitedSuggestions = suggestions.slice(0, 3); // Show only first 3 suggestions
      console.log("Extracted suggestions:", suggestions);
      console.log("Limited suggestions (first 3):", limitedSuggestions);
      setAbhaAddressSuggestions(limitedSuggestions);

      // If we have suggestions, select the first one by default
      if (suggestions.length > 0) {
        setSelectedAbhaAddress(suggestions[0]);
        console.log("Selected first suggestion:", suggestions[0]);
      } else {
        console.warn("No suggestions received from API");
      }
    } catch (err) {
      console.error("Error fetching suggestions:", err);
      const parsed = parseAbdmError(err);
      setError(parsed.message);
      toast.error(parsed.message, { duration: 5000, position: "top-center" });
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleCreateAbha = async () => {
    if (!selectedAbhaAddress || !abhaDetails) {
      setError("Please select an ABHA address");
      return;
    }
    setError(null);
    setIsVerifyingOtp(true);
    try {
      // First, create the ABHA address
      const createAddressPayload = {
        txnId: txnId,
        abhaAddress: selectedAbhaAddress,
        preferred: 1,
      };

      const createAddressResp = await fetch(
        "/api/abdm/abha-create/aadhaar/create-abha-address",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(createAddressPayload),
        },
      );

      const createAddressData = await createAddressResp.json();
      if (!createAddressResp.ok)
        throw new Error(
          createAddressData.error || "Failed to create ABHA address",
        );

      // Update the ABHA details with the response data
      const updatedAbhaDetails = {
        ...abhaDetails,
        abhaAddress: createAddressData.abhaAddress || selectedAbhaAddress,
        abhaNumber: createAddressData.abhaNumber || abhaDetails.abhaNumber,
        healthIdNumber:
          createAddressData.healthIdNumber || abhaDetails.healthIdNumber,
      };

      setAbhaDetails(updatedAbhaDetails);

      // If in standalone mode, create a patient directly without calling verify-otp
      if (standalone) {
        try {
          // Debug: Log the abhaDetails to see what data is available
          console.log("abhaDetails in handleCreateAbha:", abhaDetails);
          console.log("abhaDetails.ABHAProfile:", abhaDetails.ABHAProfile);

          // Create a patient directly using the patients API
          // Ensure we have at least some name data
          const firstName = abhaDetails.ABHAProfile?.firstName || "Unknown";
          const lastName = abhaDetails.ABHAProfile?.lastName || "";

          // Extract date of birth with multiple fallback options
          const profile = abhaDetails.ABHAProfile as any;
          let dateOfBirth = "";

          if (profile?.dateOfBirth) {
            // Use ISO format dateOfBirth if available
            dateOfBirth = profile.dateOfBirth;
          } else if (profile?.dob) {
            // Convert DD-MM-YYYY to YYYY-MM-DD format
            const dobParts = profile.dob.split("-");
            if (dobParts.length === 3) {
              const [day, month, year] = dobParts;
              dateOfBirth = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
            }
          } else if (profile?.yearOfBirth) {
            // Construct from individual parts
            const year = profile.yearOfBirth;
            const month = profile.monthOfBirth || "01";
            const day = profile.dayOfBirth || "01";
            dateOfBirth = `${year}-${month.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
          }

          console.log("Date of birth extraction:", {
            originalDob: profile?.dob,
            dateOfBirth: profile?.dateOfBirth,
            yearOfBirth: profile?.yearOfBirth,
            finalDateOfBirth: dateOfBirth,
          });

          const patientData = {
            firstName,
            lastName,
            dateOfBirth,
            gender: abhaDetails.ABHAProfile?.gender === "M" ? "male" : "female",
            phone: aadhaarLinkedMobile || abhaDetails.ABHAProfile?.mobile, // Aadhaar-linked mobile
            communicationMobile:
              communicationMobile !== aadhaarLinkedMobile
                ? communicationMobile
                : undefined, // Communication mobile (only if different)
            email: abhaDetails.ABHAProfile?.email || "",
            address: abhaDetails.ABHAProfile?.address || "",
            state: abhaDetails.ABHAProfile?.stateName || "",
            city: abhaDetails.ABHAProfile?.districtName || "",
            pincode:
              abhaDetails.ABHAProfile?.pincode ||
              abhaDetails.ABHAProfile?.pinCode ||
              "",
            abhaNumber: abhaDetails.abhaNumber,
            healthIdNumber: abhaDetails.healthIdNumber,
            abhaAddress:
              selectedAbhaAddress +
              (selectedAbhaAddress.includes("@") ? "" : "@sbx"),
            abhaStatus: "verified",
            fromAbhaVerification: true, // Important: This tells the API that data comes from ABHA verification
          };

          console.log("Patient data being sent:", patientData);

          // Call the patients API to create a new patient
          const createPatientResp = await Fetch.post(
            "/api/patients",
            patientData,
          );

          if (!createPatientResp.success) {
            throw new Error(
              createPatientResp.error || "Failed to create patient",
            );
          }

          const createPatientData = createPatientResp;

          // Check for patient ID in the response
          let patientId = null;

          // The API returns different structures based on whether it's a new or existing patient
          if (createPatientData.patient && createPatientData.patient.id) {
            // This is the case when an existing patient is found
            patientId = createPatientData.patient.id;
          } else if (createPatientData.id) {
            // This is the case for a new patient
            patientId = createPatientData.id;
          }

          if (patientId) {
            // Store the created patient ID for later use
            setCreatedPatientId(patientId);

            // Now create the ABHA profile for the patient
            console.log("Creating ABHA profile for patient:", patientId);
            try {
              const abhaProfilePayload = {
                abhaNumber: updatedAbhaDetails.abhaNumber,
                abhaAddress: updatedAbhaDetails.abhaAddress,
                healthIdNumber: updatedAbhaDetails.healthIdNumber,
                abhaStatus: "verified",
                kycVerified: true, // Set to true since this comes from ABHA verification
                xToken: abhaDetails.xToken, // Include X-token for future operations
                xTokenExpiresAt: abhaDetails.xTokenExpiresAt, // Include X-token expiry
              };

              console.log("ABHA profile payload:", abhaProfilePayload);

              const abhaProfileResp = await fetch(
                `/api/patients/${patientId}/abha`,
                {
                  method: "PATCH",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify(abhaProfilePayload),
                },
              );

              if (!abhaProfileResp.ok) {
                const errorText = await abhaProfileResp.text();
                console.error("Failed to create ABHA profile:", errorText);
                console.error(
                  "ABHA profile response status:",
                  abhaProfileResp.status,
                );
                // Don't throw error here, just log it - patient creation was successful
                console.warn(
                  "ABHA profile creation failed, but patient was created successfully",
                );
              } else {
                const abhaProfileData = await abhaProfileResp.json();
                console.log(
                  "ABHA profile created successfully:",
                  abhaProfileData,
                );
              }
            } catch (abhaProfileError) {
              console.error("Error creating ABHA profile:", abhaProfileError);
              // Don't throw error here, just log it - patient creation was successful
            }

            setStep("success");

            // Show success message
            toast.success(
              <div className="flex flex-col space-y-1">
                <span className="font-medium">
                  ABHA Created Successfully and patient registered!
                </span>
                <span className="text-sm opacity-90">
                  ABHA Number: {updatedAbhaDetails.abhaNumber}
                </span>
                <span className="text-sm opacity-90">
                  ABHA Address: {updatedAbhaDetails.abhaAddress}
                </span>
              </div>,
              {
                duration: 5000,
                position: "top-center",
                icon: <CheckCircle className="h-5 w-5 text-green-500" />,
              },
            );
          } else {
            throw new Error("No patient ID found in response");
          }
        } catch (error) {
          console.error("Error creating patient:", error);
          throw error;
        }
      } else {
        setStep("success");

        // Show success message
        toast.success(
          <div className="flex flex-col space-y-1">
            <span className="font-medium">ABHA Created Successfully!</span>
            <span className="text-sm opacity-90">
              ABHA Number: {updatedAbhaDetails.abhaNumber}
            </span>
            <span className="text-sm opacity-90">
              ABHA Address: {updatedAbhaDetails.abhaAddress}
            </span>
          </div>,
          {
            duration: 5000,
            position: "top-center",
            icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          },
        );
      }

      // Handle patient context mode (when not standalone)
      if (!standalone && patient?.id) {
        try {
          // PATIENT CONTEXT MODE: Create ABHA profile for existing patient
          const patientUpdatePayload = {
            abhaNumber: updatedAbhaDetails.abhaNumber,
            healthIdNumber: updatedAbhaDetails.healthIdNumber,
            abhaAddress: updatedAbhaDetails.abhaAddress,
            abhaStatus: "verified",
          };

          const updateResponse = await Fetch.post(
            `/api/patients/${patient.id}/abha`,
            patientUpdatePayload,
          );

          if (!updateResponse.success) {
            throw new Error(
              updateResponse.error || "Failed to create patient ABHA profile",
            );
          }

          setStep("success");

          // Show success message
          toast.success(
            <div className="flex flex-col space-y-1">
              <span className="font-medium">
                ABHA Profile Created Successfully!
              </span>
              <span className="text-sm opacity-90">
                ABHA Number: {updatedAbhaDetails.abhaNumber}
              </span>
              <span className="text-sm opacity-90">
                ABHA Address: {updatedAbhaDetails.abhaAddress}
              </span>
            </div>,
            {
              duration: 5000,
              position: "top-center",
              icon: <CheckCircle className="h-5 w-5 text-green-500" />,
            },
          );
        } catch (error) {
          console.error("Error creating ABHA profile:", error);
          throw error; // Re-throw to be caught by outer try-catch
        }
      }
    } catch (err) {
      const parsed = parseAbdmError(err);
      setError(parsed.message);
      toast.error(parsed.message, { duration: 5000, position: "top-center" });
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  // Handle mobile update verification success
  const handleMobileUpdateSuccess = async (result: any) => {
    console.log("Mobile update verification result:", result);

    // Update the communication mobile number in the form
    setCommunicationMobile(mobileUpdateData?.userMobile || communicationMobile);

    // Set the Aadhaar-linked mobile from the mobile update data
    if (mobileUpdateData?.abhaResponseMobile) {
      setAadhaarLinkedMobile(mobileUpdateData.abhaResponseMobile);
    }

    // Set the ABHA details from the result (without abhaAddress since we need to create it)
    // Use the ABHAProfile returned from the API which includes name and other details
    const details: AbhaDetails = {
      abhaNumber: result.abhaNumber,
      healthIdNumber: result.healthIdNumber,
      abhaAddress: "", // Will be set after address selection
      ABHAProfile: result.ABHAProfile || {
        mobile: mobileUpdateData?.abhaResponseMobile || aadhaarLinkedMobile, // Use Aadhaar-linked mobile
        abhaStatus: "verified",
        ABHANumber: result.abhaNumber,
      },
    };

    setAbhaDetails(details);

    // Store the transaction data for address creation
    setTxnId(result.txnId);

    // If we're in standalone mode and got a patient ID, store it
    if (result.patientId) {
      setCreatedPatientId(result.patientId);
    }

    // Close the mobile update dialog first
    setShowMobileUpdateDialog(false);
    setMobileUpdateData(null);

    // Show success toast for mobile update
    toast.success(
      "Mobile number updated successfully! Now choose your ABHA address.",
      {
        duration: 5000,
        position: "top-center",
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      },
    );

    // Proceed to address suggestion step
    setStep("abhaAddress");
    await fetchAddressSuggestions();
  };

  return (
    <Card className="overflow-hidden">
      <div className="space-y-6 mb-6 bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10 p-6">
        <div className="space-y-1">
          <CardTitle className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary mr-2"
            >
              <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
              <path d="M3 9h18"></path>
              <path d="M9 21V9"></path>
            </svg>
            Create ABHA
          </CardTitle>
          <CardDescription>Create a new ABHA using Aadhaar</CardDescription>
        </div>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">{getStepLabel()}</span>
            <Badge
              variant={step === "success" ? "success" : "outline"}
              className="text-xs"
            >
              {step === "aadhaar" && "Details"}
              {step === "otp" && "Verification"}
              {step === "abhaAddress" && "Address"}
              {step === "existingAddresses" && "Existing Addresses"}
              {step === "success" && "Complete"}
            </Badge>
          </div>
          <Progress value={getProgressPercentage()} className="h-2" />
        </div>
      </div>

      <CardContent className="p-6">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {step === "aadhaar" && (
          <div className="space-y-6">
            <div className="mr-auto max-w-[420px] space-y-2">
              <Label htmlFor="aadhaar">Aadhaar Number</Label>
              <Input
                id="aadhaar"
                placeholder="Enter 12-digit Aadhaar number"
                value={maskAadhaarNumber(aadhaar) || aadhaar}
                onChange={(e) => {
                  const value = e.target.value;
                  // If it's masked, don't allow editing
                  if (value.includes("X")) return;
                  // Only allow digits and limit to 12
                  const cleanValue = value.replace(/\D/g, "").slice(0, 12);
                  setAadhaar(cleanValue);
                }}
                onFocus={(e) => {
                  // Show actual value when focused
                  e.target.value = aadhaar;
                }}
                onBlur={(e) => {
                  // Show masked value when blurred and complete
                  const masked = maskAadhaarNumber(aadhaar);
                  if (masked) {
                    e.target.value = masked;
                  }
                }}
                maxLength={14}
                disabled={isGeneratingOtp}
              />
              <p className="text-sm text-muted-foreground">
                You will need your Aadhaar-linked mobile number in the next step
              </p>
            </div>

            <div className="mr-auto max-w-[420px] space-y-2">
              <Label htmlFor="beneficiary-name">Beneficiary Name</Label>
              <Input
                id="beneficiary-name"
                placeholder="Enter beneficiary name"
                value={beneficiaryName}
                onChange={(e) => setBeneficiaryName(e.target.value)}
                disabled={isGeneratingOtp}
              />
              <p className="text-sm text-muted-foreground">
                Name of the person for whom ABHA is being created
              </p>
            </div>

            <div className="mr-auto w-full space-y-4 mt-6 p-4 border rounded-md bg-muted/30">
              {/* Multiple consent checkboxes as per ABDM requirements */}
              <div className="space-y-4 mt-4">
                <p className="text-sm font-medium text-gray-900 mb-3">
                  I hereby declare that:
                </p>

                {/* Consent 1 - ABHA creation using Aadhaar (checked) */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent1"
                    checked={consent1Accepted}
                    onCheckedChange={(c) => setConsent1Accepted(c as boolean)}
                    disabled={isGeneratingOtp}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="consent1"
                      className="text-sm leading-relaxed cursor-pointer"
                    >
                      {consentTexts.consent1.en}
                    </Label>
                  </div>
                </div>

                {/* Consent 2 - Document linking (should NOT be checked for Aadhaar-based creation) */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent2"
                    checked={consent2Accepted}
                    onCheckedChange={(c) => setConsent2Accepted(c as boolean)}
                    disabled={isGeneratingOtp}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="consent2"
                      className="text-sm leading-relaxed cursor-pointer"
                    >
                      {consentTexts.consent2.en}
                    </Label>
                  </div>
                </div>

                {/* Consent 3 - Health records sharing */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent3"
                    checked={consent3Accepted}
                    onCheckedChange={(c) => setConsent3Accepted(c as boolean)}
                    disabled={isGeneratingOtp}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="consent3"
                      className="text-sm leading-relaxed cursor-pointer"
                    >
                      {consentTexts.consent3.en}
                    </Label>
                  </div>
                </div>

                {/* Consent 4 - Healthcare provider sharing */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent4"
                    checked={consent4Accepted}
                    onCheckedChange={(c) => setConsent4Accepted(c as boolean)}
                    disabled={isGeneratingOtp}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="consent4"
                      className="text-sm leading-relaxed cursor-pointer"
                    >
                      {consentTexts.consent4.en}
                    </Label>
                  </div>
                </div>

                {/* Consent 5 - Anonymization */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent5"
                    checked={consent5Accepted}
                    onCheckedChange={(c) => setConsent5Accepted(c as boolean)}
                    disabled={isGeneratingOtp}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="consent5"
                      className="text-sm leading-relaxed cursor-pointer"
                    >
                      {consentTexts.consent5.en}
                    </Label>
                  </div>
                </div>

                {/* Consent 6 - Healthcare Worker */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent6"
                    checked={consent6Accepted}
                    onCheckedChange={(c) => setConsent6Accepted(c as boolean)}
                    disabled={isGeneratingOtp}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="consent6"
                      className="text-sm leading-relaxed cursor-pointer"
                    >
                      I, (
                      {healthcareWorkerName ||
                        "name of healthcare worker- depending on the username used for logging in to the system"}
                      ), confirm that I have duly informed and explained the
                      beneficiary of the contents of consent for aforementioned
                      purposes.
                    </Label>
                  </div>
                </div>

                {/* Consent 7 - Beneficiary */}
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent7"
                    checked={consent7Accepted}
                    onCheckedChange={(c) => setConsent7Accepted(c as boolean)}
                    disabled={isGeneratingOtp}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="consent7"
                      className="text-sm leading-relaxed cursor-pointer"
                    >
                      I, ({beneficiaryName || "beneficiary name"}), have been
                      explained about the consent as stated above and hereby
                      provide my consent for the aforementioned purposes.
                    </Label>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-8">
              <Button
                onClick={handleGenerateOtp}
                disabled={
                  isGeneratingOtp ||
                  aadhaar.length !== 12 ||
                  beneficiaryName.trim() === "" || // Require beneficiary name
                  consent2Accepted || // Disable if consent2 is checked (document-based creation)
                  !consent1Accepted || // Require consent1
                  !consent3Accepted || // Require consent3
                  !consent4Accepted || // Require consent4
                  !consent5Accepted || // Require consent5
                  !consent6Accepted || // Require consent6
                  !consent7Accepted // Require consent7
                }
                className="h-10 px-4 shadow-sm transition-all hover:shadow-md"
              >
                {isGeneratingOtp ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <ArrowRight className="mr-2 h-4 w-4" />
                )}
                {isGeneratingOtp ? "Generating OTP..." : "Generate OTP"}
              </Button>
            </div>
          </div>
        )}

        {step === "otp" && (
          <div className="space-y-6">
            <div className="mr-auto max-w-[420px] space-y-3">
              <Label htmlFor="otp" className="text-base font-semibold">
                Enter OTP
              </Label>
              {otpSentMessage && (
                <div className="p-3 bg-blue-50 border border-blue-100 rounded-md text-sm text-blue-800 mb-2">
                  {otpSentMessage}
                </div>
              )}
              <Input
                id="otp"
                placeholder="Enter OTP sent to your mobile"
                value={otp}
                onChange={(e) =>
                  setOtp(e.target.value.replace(/\D/g, "").slice(0, 6))
                }
                maxLength={6}
                disabled={isVerifyingOtp}
                className="h-14 text-xl font-medium tracking-wider text-center shadow-sm"
              />

              <div className="flex justify-between items-center">
                {isTimerActive && (
                  <span className="text-sm text-muted-foreground">
                    OTP expires in {formatTime(timeRemaining)}
                  </span>
                )}

                {isResendTimerActive ? (
                  <span className="text-sm text-muted-foreground italic">
                    Wait {formatTime(resendTimeRemaining)} to resend (
                    {2 - resendAttempts} attempt
                    {2 - resendAttempts === 1 ? "" : "s"} left)
                  </span>
                ) : resendAttempts >= 2 ? (
                  <span className="text-sm text-red-500 italic">
                    Maximum resend attempts reached
                  </span>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleResendOtp}
                    disabled={
                      isGeneratingOtp ||
                      isResendTimerActive ||
                      resendAttempts >= 2
                    }
                    className="h-8 px-3 text-xs font-medium"
                  >
                    {isGeneratingOtp ? (
                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-2 h-3 w-3" />
                    )}
                    Resend OTP
                  </Button>
                )}
              </div>
            </div>

            <div className="mr-auto max-w-[420px] space-y-6">
              {/* Aadhaar Linked Mobile - Read Only */}
              <div className="space-y-2">
                <Label
                  htmlFor="aadhaar-mobile"
                  className="text-base font-semibold flex items-center gap-2"
                >
                  <Shield className="h-4 w-4 text-green-600" />
                  Aadhaar Linked Mobile Number
                </Label>
                <div className="flex">
                  <div className="flex items-center justify-center px-4 border border-r-0 rounded-l-md bg-muted text-muted-foreground font-medium">
                    +91
                  </div>
                  <Input
                    id="aadhaar-mobile"
                    placeholder="Enter 10-digit Aadhaar-linked mobile"
                    value={aadhaarLinkedMobile}
                    onChange={(e) => {
                      const cleanValue = e.target.value
                        .replace(/\D/g, "")
                        .slice(0, 10);
                      setAadhaarLinkedMobile(cleanValue);
                    }}
                    disabled={isVerifyingOtp}
                    className="rounded-l-none h-12 text-base shadow-sm"
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  Enter your Aadhaar-linked mobile number for ABHA creation
                </p>
              </div>

              {/* Communication Mobile - User Input */}
              <div className="space-y-2">
                <Label
                  htmlFor="communication-mobile"
                  className="text-base font-semibold flex items-center gap-2"
                >
                  <Phone className="h-4 w-4 text-blue-600" />
                  Communication Mobile Number
                </Label>
                <div className="flex">
                  <div className="flex items-center justify-center px-4 border border-r-0 rounded-l-md bg-muted text-muted-foreground font-medium">
                    +91
                  </div>
                  <Input
                    id="communication-mobile"
                    placeholder="Enter your preferred mobile number"
                    value={communicationMobile}
                    onChange={(e) => {
                      const cleanValue = e.target.value
                        .replace(/\D/g, "")
                        .slice(0, 10);
                      setCommunicationMobile(cleanValue);
                    }}
                    maxLength={10}
                    disabled={isVerifyingOtp}
                    className="rounded-l-none h-12 text-base shadow-sm"
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  This number will be used for communication. Leave same as
                  Aadhaar-linked mobile or enter a different number.
                </p>
                {aadhaarLinkedMobile &&
                  communicationMobile &&
                  communicationMobile !== aadhaarLinkedMobile && (
                    <div className="mt-2">
                      <p className="text-sm text-blue-600">
                        ✓ Different communication mobile detected. Mobile
                        verification will be required.
                      </p>
                    </div>
                  )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-8">
              <Button
                variant="outline"
                onClick={() => {
                  setStep("aadhaar");
                  setOtp("");
                  setError(null);
                  // Clear the timer
                  if (timerRef.current) {
                    clearInterval(timerRef.current);
                  }
                  if (resendTimerRef.current) {
                    clearInterval(resendTimerRef.current);
                  }
                  setIsTimerActive(false);
                  setIsResendTimerActive(false);
                }}
                disabled={isVerifyingOtp}
                className="h-10 px-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button
                onClick={handleVerifyOtp}
                disabled={
                  isVerifyingOtp ||
                  otp.length < 6 ||
                  aadhaarLinkedMobile.length < 10
                }
                className="h-10 px-4 shadow-sm transition-all hover:shadow-md"
              >
                {isVerifyingOtp ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                {isVerifyingOtp ? "Verifying..." : "Verify OTP"}
              </Button>
            </div>
          </div>
        )}

        {step === "abhaAddress" && (
          <div className="space-y-6">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold">
                {cameFromExistingAddresses
                  ? "Create New ABHA Address"
                  : "Select your ABHA Address"}
              </h3>
              <p className="text-sm text-muted-foreground mt-1">
                {cameFromExistingAddresses
                  ? "Choose from the suggested ABHA addresses below to create a new address for your account."
                  : "Choose from the suggested ABHA addresses below or view your existing addresses."}
              </p>
              {abhaDetails &&
                abhaDetails.ABHAProfile &&
                (() => {
                  // Debug: Log the ABHAProfile to see what fields are available
                  console.log(
                    "ABHAProfile data for name display:",
                    abhaDetails.ABHAProfile,
                  );

                  // Extract name with fallbacks
                  const profile = abhaDetails.ABHAProfile as any;
                  const displayName =
                    profile.firstName && profile.lastName
                      ? `${profile.firstName} ${profile.lastName}`.trim()
                      : profile.name ||
                        profile.fullName ||
                        profile.firstName ||
                        "Name not available";

                  return (
                    <div className="mt-3 p-3 bg-blue-50 rounded-md text-blue-700 text-sm">
                      <p className="font-semibold">ABHA Profile Information:</p>
                      <p>Name: {displayName}</p>
                      <p>ABHA Number: {abhaDetails.abhaNumber}</p>
                      {profile.mobile && <p>Mobile: {profile.mobile}</p>}
                    </div>
                  );
                })()}
            </div>

            {/* Custom ABHA Address Input */}
            <div className="space-y-3 p-4 border rounded-md bg-muted/30">
              <div className="flex items-center space-x-2">
                <Plus className="h-4 w-4 text-primary" />
                <Label
                  htmlFor="custom-abha-address"
                  className="text-sm font-medium"
                >
                  Enter a custom ABHA address
                </Label>
              </div>
              <Input
                id="custom-abha-address"
                placeholder="Enter custom ABHA address (e.g., john.doe@abdm)"
                value={customAbhaAddress}
                onChange={(e) => handleCustomAbhaAddressChange(e.target.value)}
                className={`${customAbhaAddressError ? "border-red-500" : ""}`}
              />
              {customAbhaAddressError && (
                <p className="text-sm text-red-500 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {customAbhaAddressError}
                </p>
              )}
              <div className="text-xs text-muted-foreground space-y-1">
                <p className="font-medium">ABHA Address Requirements:</p>
                <ul className="list-disc list-inside space-y-0.5 ml-2">
                  <li>Minimum length - 8 characters</li>
                  <li>Maximum length - 18 characters</li>
                  <li>
                    Special characters allowed - 1 dot (.) and/or 1 underscore
                    (_)
                  </li>
                  <li>
                    Special character dot and underscore should be in between.
                    Special characters cannot be in the beginning or at the end
                  </li>
                  <li>
                    Alphanumeric - only numbers, only letters or any combination
                    of numbers and letters is allowed.
                  </li>
                </ul>
              </div>
            </div>

            {/* Separator */}
            <div className="flex items-center space-x-4 my-6">
              <div className="flex-1 border-t border-muted"></div>
              <span className="text-sm text-muted-foreground">OR</span>
              <div className="flex-1 border-t border-muted"></div>
            </div>

            {/* Suggestions Section */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                Choose from suggested addresses:
              </Label>
            </div>

            {isLoadingSuggestions ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-3">Loading suggestions...</span>
              </div>
            ) : abhaAddressSuggestions.length > 0 ? (
              <div className="space-y-3">
                {abhaAddressSuggestions.map((addr, idx) => (
                  <div
                    key={idx}
                    className={`p-4 border rounded-md cursor-pointer ${selectedAbhaAddress === addr ? "border-primary bg-primary/5" : "hover:border-primary/50"}`}
                    onClick={() => {
                      setSelectedAbhaAddress(addr);
                      setCustomAbhaAddress(""); // Clear custom input when selecting suggestion
                      setCustomAbhaAddressError(""); // Clear any custom input errors
                    }}
                  >
                    <div className="flex items-center">
                      <span
                        className={`w-5 h-5 rounded-full border mr-3 ${selectedAbhaAddress === addr ? "border-primary" : "border-muted"}`}
                      >
                        {" "}
                        {selectedAbhaAddress === addr && (
                          <div className="w-3 h-3 rounded-full bg-primary" />
                        )}
                      </span>
                      <span>
                        {addr}
                        {addr.includes("@") ? "" : "@sbx"}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center p-8 text-muted-foreground">
                <p>No address suggestions available.</p>
                <p className="text-sm mt-2">
                  Please try refreshing or contact support if the issue
                  persists.
                </p>
                <Button
                  variant="outline"
                  onClick={fetchAddressSuggestions}
                  className="mt-4"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Retry
                </Button>
              </div>
            )}
            <div className="flex flex-col space-y-2">
              <Button
                onClick={handleCreateAbha}
                disabled={
                  !selectedAbhaAddress ||
                  Boolean(customAbhaAddress && customAbhaAddressError)
                }
                className="w-full"
              >
                <CheckCircle className="mr-2 h-4 w-4" /> Create ABHA
              </Button>
              {!cameFromExistingAddresses && (
                <Button
                  variant="outline"
                  onClick={async () => {
                    if (!abhaDetails) {
                      setError("ABHA details are missing. Please try again.");
                      return;
                    }

                    setIsVerifyingOtp(true);
                    try {
                      // Use PHR addresses from the verify OTP response first, then fallback to suggestions
                      let phrAddresses: string[] = [];

                      // Check if we have PHR addresses from the verify OTP response
                      if (
                        abhaDetails.phrAddresses &&
                        Array.isArray(abhaDetails.phrAddresses) &&
                        abhaDetails.phrAddresses.length > 0
                      ) {
                        phrAddresses = abhaDetails.phrAddresses;
                      } else if (
                        abhaAddressSuggestions &&
                        abhaAddressSuggestions.length > 0
                      ) {
                        // Fallback to suggestions if available
                        phrAddresses = abhaAddressSuggestions;
                      } else {
                        // If no suggestions available, try to fetch them again

                        await fetchAddressSuggestions();

                        if (
                          abhaAddressSuggestions &&
                          abhaAddressSuggestions.length > 0
                        ) {
                          phrAddresses = abhaAddressSuggestions;
                        } else {
                          // If still no suggestions, create a default one based on the ABHA number
                          // const defaultAddress = `${abhaDetails.abhaNumber.replace(/-/g, "").toLowerCase()}@abdm`;
                          // console.log("No suggestions found, using default:", defaultAddress);
                          // phrAddresses = [defaultAddress];
                        }
                      }

                      if (phrAddresses.length > 0) {
                        setExistingAbhaAddresses(phrAddresses);
                        setSelectedAbhaAddress(phrAddresses[0]); // Select the first address by default
                      } else {
                        throw new Error("No ABHA addresses available");
                      }

                      // Always go to the existing addresses step
                      setStep("existingAddresses");
                    } catch (error) {
                      console.error("Error setting up ABHA addresses:", error);
                      setError(
                        error instanceof Error
                          ? error.message
                          : "Failed to load ABHA addresses",
                      );
                      toast.error(
                        error instanceof Error
                          ? error.message
                          : "Failed to load ABHA addresses. Please try again.",
                        {
                          duration: 5000,
                          position: "top-center",
                        },
                      );
                    } finally {
                      setIsVerifyingOtp(false);
                    }
                  }}
                  className="w-full"
                >
                  <SkipForward className="mr-2 h-4 w-4" /> View My ABHA
                  Addresses
                </Button>
              )}
              {cameFromExistingAddresses ? (
                <Button
                  variant="outline"
                  onClick={() => {
                    setCameFromExistingAddresses(false);
                    setStep("existingAddresses");
                  }}
                  className="w-full"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back to Existing
                  Addresses
                </Button>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => setStep("otp")}
                  className="w-full"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Back
                </Button>
              )}
            </div>
          </div>
        )}

        {step === "existingAddresses" && (
          <div className="space-y-6">
            <div className="text-center mb-4">
              <h3 className="text-lg font-semibold">
                Choose Your ABHA Address
              </h3>
              <p className="text-sm text-muted-foreground mt-1">
                We found existing PHR addresses associated with your ABHA
                number. You can select one of these or create a new ABHA
                address.
              </p>
              {abhaDetails && abhaDetails.abhaNumber && (
                <div className="mt-2 p-2 bg-blue-50 rounded-md text-blue-700 text-sm">
                  ABHA Number:{" "}
                  <span className="font-semibold">
                    {abhaDetails.abhaNumber}
                  </span>
                </div>
              )}
            </div>

            {existingAbhaAddresses.length > 0 ? (
              <div className="space-y-3">
                {existingAbhaAddresses.map((addr, idx) => (
                  <div
                    key={idx}
                    className={`p-4 border rounded-md cursor-pointer ${selectedAbhaAddress === addr ? "border-primary bg-primary/5" : "hover:border-primary/50"}`}
                    onClick={() => {
                      setSelectedAbhaAddress(addr);
                    }}
                  >
                    <div className="flex items-center">
                      <span
                        className={`w-5 h-5 rounded-full border mr-3 ${selectedAbhaAddress === addr ? "border-primary" : "border-muted"}`}
                      >
                        {selectedAbhaAddress === addr && (
                          <div className="w-3 h-3 rounded-full bg-primary m-auto" />
                        )}
                      </span>
                      <span>
                        {addr}
                        {addr.includes("@") ? "" : "@sbx"}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center">
                <p className="text-muted-foreground">
                  No existing PHR addresses found for this ABHA number
                </p>
              </div>
            )}

            <div className="flex flex-col space-y-2 mt-4">
              <Button
                onClick={async () => {
                  if (!selectedAbhaAddress || !abhaDetails) {
                    setError("Please select an ABHA address");
                    return;
                  }

                  setIsVerifyingOtp(true);
                  try {
                    // Update the ABHA profile with the selected address
                    const updatedAbhaDetails = {
                      ...abhaDetails,
                      abhaAddress: selectedAbhaAddress,
                    };

                    setAbhaDetails(updatedAbhaDetails);

                    // Create ABHA profile for the patient
                    const targetPatientId = standalone
                      ? createdPatientId
                      : patient?.id;
                    const finalAbhaAddress =
                      selectedAbhaAddress +
                      (selectedAbhaAddress.includes("@") ? "" : "@sbx");

                    if (targetPatientId) {
                      // Update the ABHA address for the patient

                      // Create complete ABHA profile with all details from verify OTP response
                      const updatePayload = {
                        abhaNumber: abhaDetails.abhaNumber,
                        abhaAddress: finalAbhaAddress,
                        healthIdNumber: abhaDetails.healthIdNumber,
                        xToken: abhaDetails.xToken,
                        xTokenExpiresAt: abhaDetails.xTokenExpiresAt,
                        abhaStatus: "verified",
                      };

                      const updateResp = await fetch(
                        `/api/patients/${targetPatientId}/abha`,
                        {
                          method: "PATCH",
                          headers: { "Content-Type": "application/json" },
                          body: JSON.stringify(updatePayload),
                        },
                      );

                      if (!updateResp.ok) {
                        const errorText = await updateResp.text();
                        console.error(
                          "Failed to create ABHA profile:",
                          errorText,
                        );
                        console.error("Response status:", updateResp.status);
                        throw new Error(
                          `Failed to create ABHA profile: ${errorText}`,
                        );
                      }

                      await updateResp.json();

                      // SECOND: Automatically trigger link token generation with correct ABHA address
                      try {
                        const linkTokenResp = await fetch(
                          `/api/patients/${targetPatientId}/care-contexts/link-token`,
                          {
                            method: "POST",
                            headers: {
                              "Content-Type": "application/json",
                            },
                          },
                        );

                        if (!linkTokenResp.ok) {
                          console.error(
                            "Link token generation failed:",
                            await linkTokenResp.text(),
                          );
                        }
                      } catch (linkTokenError) {
                        console.error(
                          "Error triggering link token generation:",
                          linkTokenError,
                        );
                      }

                      // THIRD: Show success message and move to success step
                      toast.success(
                        <div className="flex flex-col space-y-1">
                          <span className="font-medium">
                            ABHA Profile Created Successfully!
                          </span>
                          <span className="text-sm opacity-90">
                            ABHA Number: {abhaDetails.abhaNumber}
                          </span>
                          <span className="text-sm opacity-90">
                            ABHA Address: {finalAbhaAddress}
                          </span>
                        </div>,
                        {
                          duration: 5000,
                          position: "top-center",
                          icon: (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ),
                        },
                      );

                      // Move to success step
                      setStep("success");
                    } else {
                      throw new Error(
                        "Patient ID not found. Cannot create ABHA profile.",
                      );
                    }

                    // For existing patient mode, also update the ABHA profile via the patient API
                    if (!standalone && patient?.id) {
                      // In patient mode, just update the existing patient's ABHA profile
                      const patientUpdatePayload = {
                        abhaNumber: abhaDetails.abhaNumber,
                        healthIdNumber: abhaDetails.healthIdNumber,
                        abhaAddress:
                          selectedAbhaAddress +
                          (selectedAbhaAddress.includes("@") ? "" : "@sbx"),
                        abhaStatus: "verified",
                      };

                      console.log(
                        "Creating ABHA profile for existing patient:",
                        patientUpdatePayload,
                      );

                      const updateResponse = await Fetch.post(
                        `/api/patients/${patient.id}/abha`,
                        patientUpdatePayload,
                      );

                      if (!updateResponse.success) {
                        console.error(
                          "Failed to create patient ABHA profile:",
                          updateResponse,
                        );
                        throw new Error(
                          updateResponse.error ||
                            "Failed to create patient ABHA profile",
                        );
                      }

                      // Automatically trigger link token generation for existing patient
                      try {
                        const linkTokenResp = await fetch(
                          `/api/patients/${patient.id}/care-contexts/link-token`,
                          {
                            method: "POST",
                            headers: {
                              "Content-Type": "application/json",
                            },
                          },
                        );

                        if (!linkTokenResp.ok) {
                          console.error(
                            "Link token generation failed for existing patient:",
                            await linkTokenResp.text(),
                          );
                        }
                      } catch (linkTokenError) {
                        console.error(
                          "Error triggering link token generation for existing patient:",
                          linkTokenError,
                        );
                      }
                    }
                  } catch (error) {
                    console.error("Error updating ABHA address:", error);
                    setError(
                      error instanceof Error
                        ? error.message
                        : "Failed to update ABHA address",
                    );
                    toast.error(
                      error instanceof Error
                        ? error.message
                        : "Failed to update ABHA address. Please try again.",
                      {
                        duration: 5000,
                        position: "top-center",
                      },
                    );
                  } finally {
                    setIsVerifyingOtp(false);
                  }
                }}
                disabled={!selectedAbhaAddress || isVerifyingOtp}
                className="w-full"
              >
                {isVerifyingOtp ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <ArrowRight className="mr-2 h-4 w-4" />
                )}
                {isVerifyingOtp ? "Processing..." : "Use Selected Address"}
              </Button>

              <Button
                variant="outline"
                onClick={async () => {
                  setCameFromExistingAddresses(true);
                  setStep("abhaAddress");
                  // Fetch address suggestions when navigating to the suggestions step
                  await fetchAddressSuggestions();
                }}
                disabled={isVerifyingOtp}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" /> Create New ABHA Address
              </Button>

              <Button
                variant="outline"
                onClick={() => setStep("otp")}
                disabled={isVerifyingOtp}
                className="w-full"
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back
              </Button>
            </div>
          </div>
        )}

        {step === "success" && (
          <div className="space-y-4 text-center">
            <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold">
              ABHA Created Successfully!
            </h3>
            {abhaDetails && <p>ABHA Number: {abhaDetails.abhaNumber}</p>}
            <Button
              onClick={() => {
                if (standalone) {
                  if (createdPatientId) {
                    // If we have a created patient ID, redirect to that patient's details page with ABHA tab
                    router.push(`/patients/${createdPatientId}/abha/view`);
                  } else {
                    // Fallback to patients list if no patient ID is available
                    router.push("/patients");
                  }
                } else if (patient?.id) {
                  // If we're in patient context, redirect to that patient's details page with ABHA tab
                  router.push(`/patients/${patient.id}/abha/view`);
                }
              }}
              className="w-full"
            >
              Continue
            </Button>
          </div>
        )}
      </CardContent>

      {/* Mobile Update Verification Dialog */}
      {mobileUpdateData && (
        <MobileUpdateVerificationDialog
          open={showMobileUpdateDialog}
          onOpenChange={setShowMobileUpdateDialog}
          userMobile={mobileUpdateData.userMobile}
          abhaResponseMobile={mobileUpdateData.abhaResponseMobile}
          txnId={mobileUpdateData.txnId}
          abhaNumber={mobileUpdateData.abhaNumber}
          abhaAddress={mobileUpdateData.abhaAddress}
          healthIdNumber={mobileUpdateData.healthIdNumber}
          patientId={mobileUpdateData.patientId}
          xToken={mobileUpdateData.xToken}
          onVerificationSuccess={handleMobileUpdateSuccess}
        />
      )}
    </Card>
  );
}
