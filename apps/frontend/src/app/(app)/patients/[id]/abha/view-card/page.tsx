import { notFound, redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { AbhaCardFullPage } from "./_components/abha-card-full-page";

export default async function PatientAbhaViewCardPage({
  params,
}: {
  params: { id: string };
}) {
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    include: {
      abhaProfile: true,
    },
  });

  if (!patient) {
    return notFound();
  }

  // If patient doesn't have an ABHA profile, redirect to create page
  if (!patient.abhaProfile || !patient.abhaProfile.abhaNumber) {
    redirect(`/patients/${patient.id}/abha/create`);
  }

  return (
    <AbhaCardFullPage
      patientId={patient.id}
      abhaNumber={patient.abhaProfile.abhaNumber}
      xToken={patient.abhaProfile.xToken || ""}
    />
  );
}
