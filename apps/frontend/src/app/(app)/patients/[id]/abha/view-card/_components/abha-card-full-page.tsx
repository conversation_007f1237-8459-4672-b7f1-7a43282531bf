"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertCircle,
  RefreshCw,
  LogIn,
  ArrowLeft,
  Download,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AbhaCardIframe } from "@/components/abha-card-iframe";
import Link from "next/link";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { AbhaPageLayout } from "../../_components/abha-page-layout";

interface AbhaCardFullPageProps {
  patientId: string;
  abhaNumber: string;
  xToken?: string;
}

export function AbhaCardFullPage({
  patientId,
  abhaNumber,
  xToken,
}: AbhaCardFullPageProps) {
  const router = useRouter();
  const [showCard, setShowCard] = useState(!!xToken);
  const [error, setError] = useState<string | null>(null);

  // Function to handle download button click
  const handleDownload = async () => {
    try {
      toast.info("Preparing ABHA card for download...");

      // Call our API to get the card data
      const response = await fetch(
        `/api/abdm/abha-card/download-stream?patientId=${encodeURIComponent(
          patientId,
        )}`,
      );

      if (!response.ok) {
        const errorData = await response.json();

        // Check for token expiration
        if (errorData.code === "TOKEN_EXPIRED") {
          toast.error("Your ABHA session has expired. Please login again.");
          handleTokenExpired();
          return;
        }

        toast.error(errorData.error || "Failed to download ABHA card");
        return;
      }

      const data = await response.json();

      if (!data.cardData) {
        toast.error("Failed to download ABHA card. No data received.");
        return;
      }

      // Create a link element to trigger the browser's native save dialog
      const link = document.createElement("a");
      link.href = data.cardData;
      link.download = `ABHA_Card_${abhaNumber}.${
        data.contentType.includes("pdf") ? "pdf" : "png"
      }`;

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();

      // Small delay before removing the link
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);

      toast.success("ABHA card downloaded successfully");
    } catch (error) {
      console.error("Error downloading ABHA card:", error);
      toast.error("Failed to download ABHA card");
    }
  };

  // Function to handle token expiration
  const handleTokenExpired = () => {
    setShowCard(false);
    setError("Your ABHA session has expired. Please login again.");
    // Redirect to login page with return URL
    const returnUrl = encodeURIComponent(
      `/patients/${patientId}/abha/view-card`,
    );
    router.push(`/patients/${patientId}/abha/login?returnUrl=${returnUrl}`);
  };

  // Function to handle card viewing errors
  const handleCardError = (errorMessage: string) => {
    console.error("Error viewing ABHA card:", errorMessage);

    // Check for token expiration
    if (
      errorMessage.toLowerCase().includes("token expired") ||
      errorMessage.includes("TOKEN_EXPIRED") ||
      errorMessage.includes("ABDM-1094")
    ) {
      setError("Your ABHA session has expired. Please login again.");
      handleTokenExpired();
      return;
    }

    setError("Failed to load ABHA card. Please try again.");
    setShowCard(false);
    toast.error("Failed to load ABHA card");

    // Redirect to login page with return URL if it's a token issue
    if (
      errorMessage.toLowerCase().includes("token") ||
      errorMessage.toLowerCase().includes("authentication") ||
      errorMessage.toLowerCase().includes("auth")
    ) {
      const returnUrl = encodeURIComponent(
        `/patients/${patientId}/abha/view-card`,
      );
      router.push(`/patients/${patientId}/abha/login?returnUrl=${returnUrl}`);
    }
  };

  // Add effect to check token expiration on mount
  useEffect(() => {
    if (xToken) {
      // Check if the token is valid by making a test request
      const checkToken = async () => {
        try {
          const response = await fetch(
            `/api/abdm/abha-card/download?patientId=${patientId}`,
            {
              method: "HEAD",
            },
          );

          if (!response.ok && response.status === 401) {
            // Token is expired
            handleTokenExpired();
          }
        } catch (error) {
          console.error("Error checking token:", error);
        }
      };

      checkToken();
    }
  }, [xToken, patientId]);

  return (
    <AbhaPageLayout
      patientId={patientId}
      title="ABHA Card"
      description="View your ABHA (Ayushman Bharat Health Account) Card"
      fullWidth={true}
    >
      {error && (
        <div className="p-6">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="flex justify-end mt-4">
            <Button
              variant="outline"
              onClick={() => {
                setError(null);
                if (xToken) {
                  setShowCard(true);
                } else {
                  // Redirect to login page
                  const returnUrl = encodeURIComponent(
                    `/patients/${patientId}/abha/view-card`,
                  );
                  router.push(
                    `/patients/${patientId}/abha/login?returnUrl=${returnUrl}`,
                  );
                }
              }}
              className="mr-2"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <Button variant="default" asChild>
              <Link href={`/patients/${patientId}/abha/view`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to ABHA Details
              </Link>
            </Button>
          </div>
        </div>
      )}
      {!xToken && (
        <div className="p-6 flex flex-col items-center justify-center h-[600px]">
          <div className="text-center mb-6">
            <h3 className="text-lg font-medium mb-2">ABHA Login Required</h3>
            <p className="text-muted-foreground mb-4">
              You need to login to your ABHA account to view your ABHA card.
            </p>
            <Button asChild>
              <Link
                href={`/patients/${patientId}/abha/login?returnUrl=${encodeURIComponent(
                  `/patients/${patientId}/abha/view-card`,
                )}`}
              >
                <LogIn className="h-4 w-4 mr-2" />
                Login to ABHA
              </Link>
            </Button>
          </div>
        </div>
      )}

      {showCard && (
        <>
          <div className="h-[80vh] min-h-[600px]">
            <AbhaCardIframe
              abhaNumber={abhaNumber}
              patientId={patientId}
              onTokenExpired={handleTokenExpired}
              onError={handleCardError}
              fullPage={true}
            />
          </div>
          <div className="p-4 border-t flex justify-end">
            <Button
              variant="outline"
              onClick={handleDownload}
              className="border-primary/30 hover:border-primary hover:bg-primary/5"
            >
              <Download className="h-4 w-4 mr-2" />
              Download ABHA Card
            </Button>
          </div>
        </>
      )}

      {xToken && !showCard && (
        <div className="flex items-center justify-center p-6 h-[600px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
    </AbhaPageLayout>
  );
}
