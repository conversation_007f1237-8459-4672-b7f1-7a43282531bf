"use client";

import { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { CardDescription, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

interface AbhaPageLayoutProps {
  patientId: string;
  title: string;
  description: string;
  children: ReactNode;
  backUrl?: string;
  backLabel?: string;
  actions?: ReactNode;
  fullWidth?: boolean;
}

export function AbhaPageLayout({
  patientId,
  title,
  description,
  children,
  backUrl = `/patients/${patientId}/abha/view`,
  backLabel = "Back",
  actions,
  fullWidth = false,
}: AbhaPageLayoutProps) {
  return (
    <Card className={`overflow-hidden border shadow-sm`}>
      <div className="space-y-6 mb-6 bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/10 p-6">
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <CardTitle className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary mr-2"
              >
                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                <path d="M3 9h18"></path>
                <path d="M9 21V9"></path>
              </svg>
              {title}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {actions}
            <Button variant="outline" size="sm" asChild>
              <Link href={backUrl}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                {backLabel}
              </Link>
            </Button>
          </div>
        </div>
      </div>
      <CardContent className={fullWidth ? "p-0" : "p-6"}>
        {children}
      </CardContent>
    </Card>
  );
}
