import { PageHeader } from "@/components/page-header";
import { notFound } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

export default async function PatientAbhaLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { id: string };
}) {
  // Get the patient with ABHA profile
  const patient = await prisma.patient.findUnique({
    where: {
      id: params.id,
    },
    select: {
      firstName: true,
      lastName: true,
    },
  });

  if (!patient) {
    return notFound();
  }
  return (
    <>
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" asChild className="mr-4">
          <Link href={`/patients`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Link>
        </Button>
        <PageHeader
          heading="ABHA Management"
          subheading={`Manage ABHA for ${patient.firstName} ${patient.lastName}`}
        />
      </div>

      {children}
    </>
  );
}
