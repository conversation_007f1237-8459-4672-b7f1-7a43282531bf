"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { usePatient } from "@/contexts/patient-context";
import { usePatientSearchWithABHA } from "@/hooks/use-patient-search-with-abha";
import { isP<PERSON><PERSON><PERSON><PERSON>, isAbhaAddress } from "@/services/patient-service";
import { formatGenderDisplay } from "@/lib/gender-utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PatientSearchBanner } from "@/components/patient-search-banner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  PlusIcon,
  Search,
  FileText,
  User,
  MoreVertical,
  IdCard,
  Link,
  Unlink,
  Phone,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDate } from "@/lib/utils";

export default function PatientsPage() {
  const router = useRouter();
  const { patients, isLoading, refreshPatients } = usePatient();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all"); // Changed default to "all"
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced search with ABHA integration
  const {
    searchResult,
    bannerStates,
    isSearching,
    searchPhoneWithABHA,
    clearSearch,
    hideBanner,
  } = usePatientSearchWithABHA();

  // Apply filters when they change
  useEffect(() => {
    const status = statusFilter === "all" ? "" : statusFilter;

    // If we have a phone search result and patient found, show only that patient
    if (
      searchResult?.hasPatient &&
      searchResult.patientHasAbhaProfile &&
      isPhoneNumber(searchQuery)
    ) {
      // Don't call refreshPatients, we'll show the filtered result
      return;
    }

    refreshPatients(status, searchQuery);
  }, [statusFilter, searchQuery, searchResult]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Clear previous search results when input changes
    if (searchResult) {
      clearSearch();
    }

    // If input is a phone number and has 10 digits, trigger ABHA search
    if (isPhoneNumber(value)) {
      const cleanPhone = value.replace(/\D/g, "");
      if (cleanPhone.length === 10) {
        // Clear existing timeout
        if (searchTimeoutRef.current) {
          clearTimeout(searchTimeoutRef.current);
        }

        // Debounce the search
        searchTimeoutRef.current = setTimeout(() => {
          searchPhoneWithABHA(cleanPhone);
        }, 500);
      }
    }
    // If input is an ABHA address, search in local database only
    else if (isAbhaAddress(value)) {
      // Clear existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Debounce the search - search local database for ABHA address
      searchTimeoutRef.current = setTimeout(() => {
        // Search local database for patients with this ABHA address
        // This will trigger the useEffect that calls refreshPatients
        // No need to call a separate function, just let the normal search flow work
      }, 500);
    }
  };

  // Handle status filter change
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

  // Handle view patient details
  const handleViewPatient = (patientId: string) => {
    router.push(`/patients/${patientId}`);
  };

  const displayPatients = (() => {
    const isPhoneSearch = isPhoneNumber(searchQuery);
    if (isPhoneSearch && searchResult?.hasPatient) {
      // Prefer to prepend the exact match (if not already in patients)
      const patientAlreadyListed = patients.some(
        (p) => p.id === searchResult.patient.id,
      );
      return patientAlreadyListed
        ? patients
        : [searchResult.patient, ...patients];
    }

    return patients;
  })();

  // Handle create ABHA
  const handleCreateAbha = (patientId: string) => {
    router.push(`/patients/${patientId}/abha/create`);
  };

  // Handle link/delink ABHA
  const handleLinkAbha = (patientId: string, hasAbha: boolean) => {
    if (hasAbha) {
      // If patient already has ABHA, go to view tab
      router.push(`/patients/${patientId}/abha/view`);
    } else {
      // If patient doesn't have ABHA, go to verify tab
      router.push(`/patients/${patientId}/abha/verify`);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Patients</h1>
        <div className="flex space-x-2">
          <Button onClick={() => router.push("/patients/register")}>
            <PlusIcon className="mr-2 h-4 w-4" />
            Register Patient
          </Button>
        </div>
      </div>

      <Card className="border-0 shadow-md rounded-xl overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-primary/5 via-background to-secondary/5">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle>Patient Management</CardTitle>
              <CardDescription>
                View and manage all patients in the system
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* 🔍 Search Banner for ABHA Integration - Always visible */}
          {bannerStates.length > 0 && (
            <div className="mb-6 space-y-4">
              {bannerStates.map((banner, index) =>
                banner.show ? (
                  <div key={`${banner.type}-${index}`} className="mb-4">
                    <PatientSearchBanner
                      banner={banner}
                      onHide={() => hideBanner(index)}
                    />
                  </div>
                ) : null,
              )}
            </div>
          )}

          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, abha address or phone..."
                className="pl-8"
                value={searchQuery}
                onChange={handleSearchChange}
                disabled={isSearching}
              />
              {isSearching && (
                <div className="absolute right-2.5 top-2.5">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                </div>
              )}
            </div>
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4">Loading patients...</p>
              </div>
            </div>
          ) : displayPatients.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <User className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No patients found</h3>
              <p className="text-muted-foreground mt-1">
                {searchQuery
                  ? "Try adjusting your search or filters"
                  : "Register your first patient to get started"}
              </p>
              {!searchQuery && (
                <div className="flex space-x-2 mt-4">
                  <Button onClick={() => router.push("/patients/register")}>
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Register Patient
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Gender</TableHead>
                    <TableHead>Age</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Branch</TableHead>
                    <TableHead>ABHA Status</TableHead>
                    {/* <TableHead>Link Status</TableHead> */}
                    {/* <TableHead>KYC Status</TableHead> */}
                    <TableHead>Registration Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {displayPatients.map((patient) => {
                    const birthDate = new Date(patient.dateOfBirth);
                    const today = new Date();
                    let age = today.getFullYear() - birthDate.getFullYear();
                    const monthDiff = today.getMonth() - birthDate.getMonth();
                    if (
                      monthDiff < 0 ||
                      (monthDiff === 0 && today.getDate() < birthDate.getDate())
                    ) {
                      age--;
                    }

                    return (
                      <TableRow key={patient.id}>
                        <TableCell className="font-medium">
                          {patient.firstName} {patient.lastName}
                        </TableCell>
                        <TableCell>
                          {formatGenderDisplay(patient.gender)}
                        </TableCell>
                        <TableCell>{age} years</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3 text-blue-600" />
                              <span className="font-medium">
                                {patient.phone}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {(patient as any)?.primaryBranch?.name ||
                            "Not assigned"}
                        </TableCell>
                        <TableCell>
                          {patient.abhaProfile?.abhaNumber &&
                          patient.abhaProfile.abhaNumber !== "undefined" ? (
                            // Condition 1: ABHA Number is present and valid
                            <>
                              <Badge
                                variant="outline"
                                className="bg-green-100 text-green-800 hover:bg-green-200 border border-green-200"
                              >
                                {patient.abhaProfile.abhaAddress}
                                <br></br> ({patient.abhaProfile.abhaNumber})
                              </Badge>
                              {/* <Badge
                              variant="outline"
                              className="bg-green-100 text-green-800 hover:bg-green-200 border border-green-200"
                            >
                               {patient.abhaProfile.abhaNumber}
                            </Badge> */}
                            </>
                          ) : patient.abhaProfile?.abhaAddress ? (
                            // Condition 2: No ABHA Number, but ABHA Address is present
                            <Badge
                              variant="outline"
                              className="bg-orange-100 text-orange-800 hover:bg-orange-200 border border-orange-200"
                            >
                              {patient.abhaProfile.abhaAddress}
                            </Badge>
                          ) : (
                            // Condition 3: Neither ABHA Number nor ABHA Address is present
                            <Badge
                              variant="outline"
                              className="bg-gray-100 text-gray-800 hover:bg-gray-200 border border-gray-200"
                            >
                              No ABHA
                            </Badge>
                          )}
                        </TableCell>
                        {/* <TableCell>
                          {(() => {
                            // Determine link status based on both LinkTokenRequest and AbhaLinkToken
                            const linkToken = patient.abhaLinkTokens?.[0];
                            const linkRequest = patient.linkTokenRequests?.[0];

                            // If there's an active link token with valid expiry
                            if (linkToken && linkToken.status === "active" &&
                                linkToken.linkToken && linkToken.linkToken !== "pending" &&
                                new Date(linkToken.linkTokenExpiry) > new Date()) {
                              return (
                                <Badge
                                  variant="outline"
                                  className="bg-green-100 text-green-800 hover:bg-green-200 border border-green-200"
                                >
                                  Completed
                                </Badge>
                              );
                            }

                            // If there's a pending link token or pending request
                            if ((linkToken && linkToken.status === "pending") ||
                                (linkRequest && linkRequest.status === "pending")) {
                              return (
                                <Badge
                                  variant="outline"
                                  className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border border-yellow-200"
                                >
                                  Pending
                                </Badge>
                              );
                            }

                            // No link token request or token found
                            return (
                              <Badge
                                variant="outline"
                                className="bg-gray-100 text-gray-800 hover:bg-gray-200 border border-gray-200"
                              >
                                No Request
                              </Badge>
                            );
                          })()}
                        </TableCell> */}
                        {/* <TableCell>
                          {patient.abhaProfile ? (
                            <Badge
                              variant="outline"
                              className={
                                patient.abhaProfile.kycVerified
                                  ? "bg-green-100 text-green-800 hover:bg-green-200 border border-green-200"
                                  : "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border border-yellow-200"
                              }
                            >
                              {patient.abhaProfile.kycVerified ? "KYC Verified" : "KYC Pending"}
                            </Badge>
                          ) : (
                            <Badge
                              variant="outline"
                              className="bg-gray-100 text-gray-800 hover:bg-gray-200 border border-gray-200"
                            >
                              N/A
                            </Badge>
                          )}
                        </TableCell> */}
                        <TableCell>
                          {formatDate(patient.registrationDate)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              patient.status === "active"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {patient.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => handleViewPatient(patient.id)}
                              >
                                <FileText className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              {patient.abhaProfile ? (
                                <>
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleLinkAbha(patient.id, true)
                                    }
                                  >
                                    <IdCard className="mr-2 h-4 w-4" />
                                    View ABHA
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() =>
                                      router.push(
                                        `/patients/${patient.id}/abha/view`,
                                      )
                                    }
                                  >
                                    <Unlink className="mr-2 h-4 w-4" />
                                    Delink ABHA
                                  </DropdownMenuItem>
                                </>
                              ) : (
                                <>
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleLinkAbha(patient.id, false)
                                    }
                                  >
                                    <Link className="mr-2 h-4 w-4" />
                                    Link Existing ABHA
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleCreateAbha(patient.id)}
                                  >
                                    <IdCard className="mr-2 h-4 w-4" />
                                    Create ABHA
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
