import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export interface CareContextNotificationDetail {
  id: string;
  abhaAddress: string;
  status: string;
  requestId: string;
  response: any;
  createdAt: string;
  updatedAt: string;
}

export async function getCareContextNotificationById(
  id: string,
): Promise<CareContextNotificationDetail> {
  try {
    const notification = await prisma.careContextNotify.findUnique({
      where: {
        id,
      },
    });

    if (!notification) {
      return notFound();
    }

    return notification as unknown as CareContextNotificationDetail;
  } catch (error) {
    console.error("Error fetching care context notification:", error);
    throw error;
  }
}
