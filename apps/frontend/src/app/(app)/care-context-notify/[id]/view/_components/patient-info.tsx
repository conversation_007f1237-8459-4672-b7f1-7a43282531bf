"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/card";
import { formatDate } from "@/lib/utils";
import type { CareContextNotificationDetail } from "../_lib/queries";
import Link from "next/link";
import { Button } from "@workspace/ui/components/button";
import { ExternalLink } from "lucide-react";
import { useEffect, useState } from "react";

interface PatientInfoProps {
  notification: CareContextNotificationDetail;
}

interface PatientData {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth?: string;
  gender?: string;
  phone?: string;
  email?: string;
  abhaProfile?: {
    abhaNumber?: string;
    abhaAddress?: string;
  };
}

export function PatientInfo({ notification }: PatientInfoProps) {
  const [patient, setPatient] = useState<PatientData | null>(null);
  const [loading, setLoading] = useState(true);

  // The abhaAddress from the notification
  const abhaAddress = notification.abhaAddress;

  useEffect(() => {
    async function fetchPatientByAbhaAddress() {
      try {
        setLoading(true);
        // Fetch patient data by ABHA address
        const response = await fetch(
          `/api/patients/by-abha-address?abhaAddress=${encodeURIComponent(abhaAddress)}`,
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data) {
            setPatient(data.data);
          }
        }
      } catch (error) {
        console.error("Error fetching patient data:", error);
      } finally {
        setLoading(false);
      }
    }

    if (abhaAddress) {
      fetchPatientByAbhaAddress();
    }
  }, [abhaAddress]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Patient Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {loading ? (
          <p className="text-muted-foreground">
            Loading patient information...
          </p>
        ) : !patient ? (
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Patient information not available in the system
            </p>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                ABHA Address
              </h3>
              <p>{abhaAddress}</p>
            </div>
          </div>
        ) : (
          <>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Name
              </h3>
              <p>
                {patient.firstName} {patient.lastName}
              </p>
            </div>

            {patient.dateOfBirth && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">
                  Date of Birth
                </h3>
                <p>{formatDate(patient.dateOfBirth)}</p>
              </div>
            )}

            {patient.gender && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">
                  Gender
                </h3>
                <p>{patient.gender}</p>
              </div>
            )}

            {patient.phone && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">
                  Phone
                </h3>
                <p>{patient.phone}</p>
              </div>
            )}

            {patient.email && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">
                  Email
                </h3>
                <p>{patient.email}</p>
              </div>
            )}

            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">
                ABHA Information
              </h3>
              <div>
                <p className="text-xs text-muted-foreground">ABHA Address</p>
                <p>{abhaAddress}</p>
              </div>
              {patient.abhaProfile?.abhaNumber && (
                <div>
                  <p className="text-xs text-muted-foreground">ABHA Number</p>
                  <p>{patient.abhaProfile.abhaNumber}</p>
                </div>
              )}
            </div>

            <div className="pt-4">
              <Button asChild variant="outline" size="sm" className="w-full">
                <Link href={`/patients/${patient.id}`}>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Patient Profile
                </Link>
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
