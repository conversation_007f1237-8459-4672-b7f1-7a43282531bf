"use client";

import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent } from "@workspace/ui/components/card";
import { formatDate } from "@/lib/utils";
import type { CareContextNotificationDetail } from "../_lib/queries";

interface NotificationHeaderProps {
  notification: CareContextNotificationDetail;
}

export function NotificationHeader({ notification }: NotificationHeaderProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-2xl font-bold">Care Context Notification</h2>
            <p className="text-sm text-muted-foreground">
              Request ID: {notification.requestId}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Badge
              variant={
                notification.status.toLowerCase().includes("success")
                  ? "success"
                  : "default"
              }
              className="whitespace-nowrap"
            >
              {notification.status}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {formatDate(notification.createdAt)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
