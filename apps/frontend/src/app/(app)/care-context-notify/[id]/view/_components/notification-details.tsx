"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/card";
import { formatDate } from "@/lib/utils";
import type { CareContextNotificationDetail } from "../_lib/queries";

interface NotificationDetailsProps {
  notification: CareContextNotificationDetail;
}

export function NotificationDetails({
  notification,
}: NotificationDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-xs text-muted-foreground">ABHA Address</p>
              <p className="break-all">{notification.abhaAddress}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Status</p>
              <p>{notification.status}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Request ID</p>
              <p className="break-all">{notification.requestId}</p>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Response Data
          </h3>
          {notification.response ? (
            <div className="rounded-md bg-muted p-4 overflow-auto max-h-[300px]">
              <pre className="text-xs">
                {JSON.stringify(notification.response, null, 2)}
              </pre>
            </div>
          ) : (
            <p>No response data available</p>
          )}
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            Timestamps
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <p className="text-xs text-muted-foreground">Created At</p>
              <p>{formatDate(notification.createdAt)}</p>
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Updated At</p>
              <p>{formatDate(notification.updatedAt)}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
