import * as React from "react";
import { DataTableSkeleton } from "@workspace/data-table/component/data-table-skeleton";
import { CareContextNotificationsTable } from "./_component/table";
import { getCareContextNotifications } from "./_lib/queries";
import { GetCareContextNotificationsSchema } from "./_lib/validations";

interface CareContextNotificationsPageProps {
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}

export default async function CareContextNotificationsPage({
  searchParams,
}: CareContextNotificationsPageProps) {
  // Parse search params
  const parsedParams = GetCareContextNotificationsSchema.parse({
    page: searchParams.page ? Number(searchParams.page) : 1,
    perPage: searchParams.per_page ? Number(searchParams.per_page) : 10,
    sort: searchParams.sort
      ? JSON.parse(decodeURIComponent(searchParams.sort as string))
      : undefined,
    abhaAddress: searchParams.abhaAddress,
    status: searchParams.status,
    requestId: searchParams.requestId,
  });

  // Fetch data
  const notifications = await getCareContextNotifications(parsedParams);

  return (
    <React.Suspense fallback={<DataTableSkeleton columnCount={6} />}>
      <CareContextNotificationsTable initialData={notifications} />
    </React.Suspense>
  );
}
