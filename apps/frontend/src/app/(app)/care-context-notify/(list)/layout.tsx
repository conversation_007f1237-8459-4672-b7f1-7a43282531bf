import React from "react";

export const metadata = {
  title: "Care Context Notifications",
};

export default function CareContextNotifyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">
            Care Context Notifications
          </h2>
        </div>
        {children}
      </div>
    </>
  );
}
