"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";
import { z } from "zod";
import { GetCareContextNotificationsSchema } from "./validations";

export interface CareContextNotification {
  id: string;
  abhaAddress: string;
  status: string;
  requestId: string;
  response: any;
  createdAt: string;
  updatedAt: string;
}

export async function getCareContextNotifications(
  input: z.infer<typeof GetCareContextNotificationsSchema>,
) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Default pagination values
  const page = input.page || 1;
  const perPage = input.perPage || 10;
  const skip = (page - 1) * perPage;

  // Build where clause for filtering
  const where: any = {};

  // Add filters if provided
  if (input.abhaAddress) {
    where.abhaAddress = {
      contains: input.abhaAddress,
      mode: "insensitive",
    };
  }

  if (input.status) {
    where.status = input.status;
  }

  if (input.requestId) {
    where.requestId = {
      contains: input.requestId,
      mode: "insensitive",
    };
  }

  // Get total count
  const total = await prisma.careContextNotify.count({
    where,
  });

  // Get care context notifications with pagination
  const notifications = await prisma.careContextNotify.findMany({
    where,
    orderBy: {
      createdAt: "desc",
    },
    skip,
    take: perPage,
  });

  // Calculate total pages
  const totalPages = Math.ceil(total / perPage);

  // Format the data to match the expected interface
  const formattedNotifications = notifications.map((notification) => ({
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  }));

  return {
    data: formattedNotifications,
    pageCount: totalPages,
  };
}
