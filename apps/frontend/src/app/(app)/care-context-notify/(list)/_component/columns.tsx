"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@workspace/ui/components/badge";
import { formatDate } from "@/lib/utils";
import type { CareContextNotification } from "../_lib/queries";

export function getCareContextNotificationsTableColumns(): ColumnDef<CareContextNotification>[] {
  return [
    {
      accessorKey: "abhaAddress",
      header: "ABHA Address",
      cell: ({ row }) => {
        return <div className="font-medium">{row.getValue("abhaAddress")}</div>;
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;

        let variant: "default" | "outline" | "secondary" | "destructive" =
          "default";

        switch (status) {
          case "Success":
            variant = "default";
            break;
          case "Pending":
            variant = "secondary";
            break;
          case "Failed":
            variant = "destructive";
            break;
          default:
            variant = "outline";
        }

        return <Badge variant={variant}>{status}</Badge>;
      },
    },
    {
      accessorKey: "requestId",
      header: "Request ID",
      cell: ({ row }) => {
        return <div className="font-medium">{row.getValue("requestId")}</div>;
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }) => {
        return formatDate(row.getValue("createdAt"));
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      cell: ({ row }) => {
        return formatDate(row.getValue("updatedAt"));
      },
    },
  ];
}
