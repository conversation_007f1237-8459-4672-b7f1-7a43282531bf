"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import type { CareContextNotification } from "../_lib/queries";
import { DataTable } from "@workspace/data-table/component/data-table";
import { useDataTable } from "@workspace/data-table/hooks/use-data-table";
import { DataTableToolbar } from "@workspace/data-table/component/data-table-toolbar";
import { DataTableSortList } from "@workspace/data-table/component/data-table-sort-list";
import { getCareContextNotificationsTableColumns } from "./columns";

interface CareContextNotificationsTableProps {
  initialData: {
    data: CareContextNotification[];
    pageCount: number;
  };
}

export function CareContextNotificationsTable({
  initialData,
}: CareContextNotificationsTableProps) {
  const router = useRouter();
  const { data, pageCount } = initialData;

  const columns = React.useMemo(
    () => getCareContextNotificationsTableColumns() as any,
    [],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <DataTable
      table={table}
      onRowClick={(row) => {
        router.push(`/care-context-notify/${row.id}/view`);
      }}
      doctype="careContextNotify"
    >
      <DataTableToolbar table={table}>
        <DataTableSortList table={table} align="end" />
      </DataTableToolbar>
    </DataTable>
  );
}
