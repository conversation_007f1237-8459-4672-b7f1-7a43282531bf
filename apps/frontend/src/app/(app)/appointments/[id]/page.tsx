"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { format } from "date-fns";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { useSocketClient } from "@/hooks/useSocket";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function AppointmentDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const [appointment, setAppointment] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const [activeTab, setActiveTab] = useState("details");

  // Initialize Socket.io connection
  const { socket, isConnected } = useSocketClient(
    appointment?.branchId,
    appointment?.doctorId,
  );

  // Fetch appointment details
  const fetchAppointment = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/appointments/${params.id}`);
      const data = await response.json();

      if (response.ok) {
        setAppointment(data.appointment);
      } else {
        toast.error(data.error || "Failed to fetch appointment details");
      }
    } catch (error) {
      console.error("Error fetching appointment:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch appointment on initial load
  useEffect(() => {
    fetchAppointment();
  }, [params.id]);

  // Set up Socket.io event listeners
  useEffect(() => {
    if (socket && isConnected && appointment) {
      // Listen for queue status updates
      socket.on("queue-status-updated", (data) => {
        if (data.appointmentId === appointment.id) {
          // Refresh the appointment details
          fetchAppointment();
        }
      });

      return () => {
        // Clean up event listeners
        socket.off("queue-status-updated");
      };
    }
  }, [socket, isConnected, appointment]);

  // Update appointment status
  const updateAppointmentStatus = async (status: string, reason?: string) => {
    setIsUpdating(true);
    try {
      const data: any = { status };

      if (reason) {
        data.cancellationReason = reason;
      }

      const response = await fetch(`/api/appointments/${params.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Appointment status updated to ${status}`);

        // Update queue status if needed
        if (status === "cancelled" && appointment.queueStatus) {
          await updateQueueStatus("cancelled");
        }

        // Refresh the appointment details
        fetchAppointment();
      } else {
        toast.error(result.error || "Failed to update appointment status");
      }
    } catch (error) {
      console.error("Error updating appointment status:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsUpdating(false);
      setShowCancelDialog(false);
      setCancellationReason("");
    }
  };

  // Update queue status
  const updateQueueStatus = async (status: string, reason?: string) => {
    if (!appointment.queueStatus) return;

    try {
      const data: any = { status };

      if (reason) {
        data.pauseReason = reason;
      }

      // Add timestamps based on status
      if (status === "in-consultation") {
        data.actualStartTime = new Date().toISOString();
      } else if (status === "completed") {
        data.completionTime = new Date().toISOString();
      }

      const response = await fetch(`/api/queue/${appointment.queueStatus.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Queue status updated to ${status}`);

        // Emit socket event for real-time updates
        if (socket && isConnected) {
          socket.emit("queue-status-update", {
            ...result.queueStatus,
            organizationId: appointment.organizationId,
            branchId: appointment.branchId,
          });
        }

        // Refresh the appointment details
        fetchAppointment();
      } else {
        toast.error(result.error || "Failed to update queue status");
      }
    } catch (error) {
      console.error("Error updating queue status:", error);
      toast.error("An unexpected error occurred");
    }
  };

  // Handle cancel appointment
  const handleCancelAppointment = () => {
    updateAppointmentStatus("cancelled", cancellationReason);
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline">Scheduled</Badge>;
      case "confirmed":
        return <Badge variant="secondary">Confirmed</Badge>;
      case "completed":
        return <Badge variant="success">Completed</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      case "no-show":
        return <Badge variant="warning">No Show</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get queue status badge color
  const getQueueStatusBadge = (status: string) => {
    switch (status) {
      case "waiting":
        return <Badge>Waiting</Badge>;
      case "in-consultation":
        return <Badge variant="secondary">In Consultation</Badge>;
      case "paused":
        return <Badge variant="warning">Paused</Badge>;
      case "completed":
        return <Badge variant="success">Completed</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <p>Loading appointment details...</p>
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="flex flex-col justify-center items-center h-96">
        <p className="text-muted-foreground">Appointment not found</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push("/appointments")}
        >
          Back to Appointments
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Appointment Details
          </h1>
          <p className="text-muted-foreground">
            View and manage appointment information
          </p>
        </div>
        <div className="flex space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push("/appointments")}
          >
            Back
          </Button>

          {appointment.status !== "cancelled" &&
            appointment.status !== "completed" && (
              <>
                <Button
                  variant="destructive"
                  onClick={() => setShowCancelDialog(true)}
                  disabled={isUpdating}
                >
                  Cancel Appointment
                </Button>
              </>
            )}
        </div>
      </div>

      <Tabs
        defaultValue="details"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="queue">Queue Status</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Appointment Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Status
                  </h3>
                  <div className="mt-1">
                    {getStatusBadge(appointment.status)}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Type
                  </h3>
                  <p className="mt-1 capitalize">{appointment.type}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Date
                  </h3>
                  <p className="mt-1">
                    {format(
                      new Date(appointment.appointmentDate),
                      "EEEE, MMMM d, yyyy",
                    )}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Time
                  </h3>
                  <p className="mt-1">
                    {appointment.startTime} - {appointment.endTime} (
                    {appointment.duration} minutes)
                  </p>
                </div>

                {appointment.cancellationReason && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Cancellation Reason
                    </h3>
                    <p className="mt-1">{appointment.cancellationReason}</p>
                  </div>
                )}

                {appointment.notes && (
                  <div className="col-span-2">
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Notes
                    </h3>
                    <p className="mt-1">{appointment.notes}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Patient Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Name
                    </h3>
                    <p className="mt-1">
                      {appointment.patient.firstName}{" "}
                      {appointment.patient.lastName}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Contact
                    </h3>
                    <p className="mt-1">{appointment.patient.phone}</p>
                    {appointment.patient.email && (
                      <p className="mt-1">{appointment.patient.email}</p>
                    )}
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Date of Birth
                    </h3>
                    <p className="mt-1">
                      {format(
                        new Date(appointment.patient.dateOfBirth),
                        "MMMM d, yyyy",
                      )}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Gender
                    </h3>
                    <p className="mt-1 capitalize">
                      {appointment.patient.gender}
                    </p>
                  </div>

                  {appointment.patient.allergies && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Allergies
                      </h3>
                      <p className="mt-1">{appointment.patient.allergies}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Doctor Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Name
                    </h3>
                    <p className="mt-1">{appointment.doctor.user.name}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Specialization
                    </h3>
                    <p className="mt-1">{appointment.doctor.specialization}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Qualification
                    </h3>
                    <p className="mt-1">{appointment.doctor.qualification}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Consultation Fee
                    </h3>
                    <p className="mt-1">
                      {appointment.doctor.consultationFee
                        ? `₹${appointment.doctor.consultationFee}`
                        : "Not specified"}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">
                      Branch
                    </h3>
                    <p className="mt-1">{appointment.branch.name}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="queue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Queue Status</CardTitle>
            </CardHeader>
            <CardContent>
              {appointment.queueStatus ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Queue Number
                      </h3>
                      <p className="mt-1 text-2xl font-bold">
                        {appointment.queueStatus.queueNumber}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Status
                      </h3>
                      <div className="mt-1">
                        {getQueueStatusBadge(appointment.queueStatus.status)}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Estimated Start
                      </h3>
                      <p className="mt-1">
                        {appointment.queueStatus.estimatedStartTime
                          ? format(
                              new Date(
                                appointment.queueStatus.estimatedStartTime,
                              ),
                              "h:mm a",
                            )
                          : "Not estimated"}
                      </p>
                    </div>
                  </div>

                  {appointment.queueStatus.pauseReason && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Pause Reason
                      </h3>
                      <p className="mt-1">
                        {appointment.queueStatus.pauseReason}
                      </p>
                    </div>
                  )}

                  {appointment.queueStatus.notes && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">
                        Notes
                      </h3>
                      <p className="mt-1">{appointment.queueStatus.notes}</p>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <h3 className="text-sm font-medium mb-2">Queue Actions</h3>
                    <div className="flex flex-wrap gap-2">
                      {appointment.queueStatus.status === "waiting" && (
                        <Button
                          onClick={() => updateQueueStatus("in-consultation")}
                          disabled={isUpdating}
                        >
                          Start Consultation
                        </Button>
                      )}

                      {appointment.queueStatus.status === "in-consultation" && (
                        <>
                          <Button
                            variant="outline"
                            onClick={() => updateQueueStatus("paused")}
                            disabled={isUpdating}
                          >
                            Pause
                          </Button>
                          <Button
                            onClick={() => updateQueueStatus("completed")}
                            disabled={isUpdating}
                          >
                            Complete
                          </Button>
                        </>
                      )}

                      {appointment.queueStatus.status === "paused" && (
                        <Button
                          onClick={() => updateQueueStatus("in-consultation")}
                          disabled={isUpdating}
                        >
                          Resume
                        </Button>
                      )}

                      {(appointment.queueStatus.status === "waiting" ||
                        appointment.queueStatus.status === "paused") && (
                        <Button
                          variant="destructive"
                          onClick={() => updateQueueStatus("cancelled")}
                          disabled={isUpdating}
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h3 className="text-sm font-medium mb-2">Timeline</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created</span>
                        <span>
                          {format(
                            new Date(appointment.queueStatus.createdAt),
                            "MMM d, yyyy h:mm a",
                          )}
                        </span>
                      </div>

                      {appointment.queueStatus.actualStartTime && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Started</span>
                          <span>
                            {format(
                              new Date(appointment.queueStatus.actualStartTime),
                              "MMM d, yyyy h:mm a",
                            )}
                          </span>
                        </div>
                      )}

                      {appointment.queueStatus.completionTime && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            Completed
                          </span>
                          <span>
                            {format(
                              new Date(appointment.queueStatus.completionTime),
                              "MMM d, yyyy h:mm a",
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-6">
                  <p className="text-muted-foreground">
                    No queue status found for this appointment
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Cancel Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Appointment</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this appointment? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Cancellation Reason</h3>
              <Textarea
                placeholder="Enter reason for cancellation"
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowCancelDialog(false);
                setCancellationReason("");
              }}
            >
              Keep Appointment
            </Button>
            <Button
              variant="destructive"
              onClick={handleCancelAppointment}
              disabled={isUpdating}
            >
              {isUpdating ? "Cancelling..." : "Cancel Appointment"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
