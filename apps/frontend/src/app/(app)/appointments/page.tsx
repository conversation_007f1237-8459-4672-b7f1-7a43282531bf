"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PlusIcon } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import { useBranch } from "@/contexts/branch-context";

import { Badge } from "@/components/ui/badge";

export default function AppointmentsPage() {
  const router = useRouter();
  const { currentBranch } = useBranch();
  const [appointments, setAppointments] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [selectedDoctor, setSelectedDoctor] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>(
    format(new Date(), "yyyy-MM-dd"),
  );
  const [selectedStatus, setSelectedStatus] = useState<string>("");

  // Fetch appointments
  useEffect(() => {
    const fetchAppointments = async () => {
      setIsLoading(true);
      try {
        let url = "/api/appointments?";
        const params = new URLSearchParams();

        if (selectedDoctor && selectedDoctor !== "all") {
          params.append("doctorId", selectedDoctor);
        }

        if (selectedDate) {
          params.append("date", selectedDate);
        }

        if (selectedStatus && selectedStatus !== "all") {
          params.append("status", selectedStatus);
        }

        if (currentBranch?.id) {
          params.append("branchId", currentBranch.id);
        }

        url += params.toString();

        const response = await fetch(url);
        const data = await response.json();

        if (response.ok) {
          setAppointments(data.appointments || []);
        } else {
          toast.error(data.error || "Failed to fetch appointments");
        }
      } catch (error) {
        console.error("Error fetching appointments:", error);
        toast.error("An unexpected error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppointments();
  }, [selectedDoctor, selectedDate, selectedStatus, currentBranch?.id]);

  // Fetch doctors
  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        let url = "/api/doctors";
        if (currentBranch?.id) {
          url += `?branchId=${currentBranch.id}`;
        }

        const response = await fetch(url);
        const data = await response.json();

        if (response.ok) {
          setDoctors(data.doctors || []);
        } else {
          console.error("Failed to fetch doctors:", data.error);
        }
      } catch (error) {
        console.error("Error fetching doctors:", error);
      }
    };

    fetchDoctors();
  }, [currentBranch?.id]);

  // Navigate to create new appointment page
  const handleCreateAppointment = () => {
    router.push("/appointments/new");
  };

  // Navigate to appointment details page
  const handleViewAppointment = (id: string) => {
    router.push(`/appointments/${id}`);
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline">Scheduled</Badge>;
      case "confirmed":
        return <Badge variant="secondary">Confirmed</Badge>;
      case "completed":
        return <Badge variant="success">Completed</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      case "no-show":
        return <Badge variant="warning">No Show</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get queue status badge color
  const getQueueStatusBadge = (status: string) => {
    switch (status) {
      case "waiting":
        return <Badge>Waiting</Badge>;
      case "in-consultation":
        return <Badge variant="secondary">In Consultation</Badge>;
      case "paused":
        return <Badge variant="warning">Paused</Badge>;
      case "completed":
        return <Badge variant="success">Completed</Badge>;
      case "cancelled":
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Appointments</h1>
          <p className="text-muted-foreground">
            Manage patient appointments and schedules
          </p>
        </div>
        <Button onClick={handleCreateAppointment}>
          <PlusIcon className="mr-2 h-4 w-4" />
          New Appointment
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Appointment Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="doctor">Doctor</Label>
              <Select value={selectedDoctor} onValueChange={setSelectedDoctor}>
                <SelectTrigger id="doctor">
                  <SelectValue placeholder="All Doctors" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Doctors</SelectItem>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      {doctor.user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <div className="flex items-center">
                <Input
                  id="date"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no-show">No Show</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Appointments</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading appointments...</p>
            </div>
          ) : appointments.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40">
              <p className="text-muted-foreground">No appointments found</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={handleCreateAppointment}
              >
                <PlusIcon className="mr-2 h-4 w-4" />
                Create New Appointment
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Patient</TableHead>
                    <TableHead>Doctor</TableHead>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Queue Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {appointments.map((appointment) => (
                    <TableRow key={appointment.id}>
                      <TableCell>
                        <div className="font-medium">
                          {appointment.patient.firstName}{" "}
                          {appointment.patient.lastName}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {appointment.doctor.user.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {appointment.doctor.specialization}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {format(
                            new Date(appointment.appointmentDate),
                            "dd MMM yyyy",
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {appointment.startTime} - {appointment.endTime}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(appointment.status)}
                      </TableCell>
                      <TableCell>
                        {appointment.queueStatus ? (
                          <>
                            {getQueueStatusBadge(
                              appointment.queueStatus.status,
                            )}
                            <div className="text-sm text-muted-foreground mt-1">
                              Queue #{appointment.queueStatus.queueNumber}
                            </div>
                          </>
                        ) : (
                          <Badge variant="outline">No Queue</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewAppointment(appointment.id)}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
