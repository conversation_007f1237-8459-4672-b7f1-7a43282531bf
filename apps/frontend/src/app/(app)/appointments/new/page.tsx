"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useOrganization } from "@/contexts/organization-context";
import { useBranch } from "@/contexts/branch-context";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";

import { Async<PERSON>ombobox, ComboboxOption } from "@/components/ui/combobox";
import {
  TimeSlotCombobox,
  TimeSlotOption,
} from "@/components/ui/time-slot-combobox";

// Form schema
const appointmentFormSchema = z.object({
  patientId: z.string({
    required_error: "Please select a patient",
  }),
  doctorId: z.string({
    required_error: "Please select a doctor",
  }),
  branchId: z.string({
    required_error: "Please select a branch",
  }),
  appointmentDate: z.string({
    required_error: "Please select a date",
  }),
  startTime: z.string({
    required_error: "Please select a start time",
  }),
  duration: z
    .number({
      required_error: "Please enter a duration",
    })
    .min(5, "Duration must be at least 5 minutes"),
  type: z.string({
    required_error: "Please select an appointment type",
  }),
  notes: z.string().optional(),
});

export default function NewAppointmentPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { currentOrganization } = useOrganization();
  const { currentBranch } = useBranch();
  // We don't need to store all patients and doctors anymore since we're using async search
  // We don't need branches state anymore since we're using currentBranch
  const [isLoading, setIsLoading] = useState(false);

  // Search functions for patients, doctors, and time slots
  const searchPatients = async (query: string): Promise<ComboboxOption[]> => {
    try {
      const params = new URLSearchParams();
      params.append("query", query);
      if (currentBranch?.id) {
        params.append("branchId", currentBranch.id);
      }

      const response = await fetch(`/api/patients/search?${params.toString()}`);
      const data = await response.json();

      if (response.ok) {
        return data.patients || [];
      } else {
        console.error("Error searching patients:", data.error);
        return [];
      }
    } catch (error) {
      console.error("Error searching patients:", error);
      return [];
    }
  };

  const searchDoctors = async (query: string): Promise<ComboboxOption[]> => {
    try {
      const params = new URLSearchParams();
      params.append("query", query);
      if (currentBranch?.id) {
        params.append("branchId", currentBranch.id);
      }

      const response = await fetch(`/api/doctors/search?${params.toString()}`);
      const data = await response.json();

      if (response.ok) {
        return data.doctors || [];
      } else {
        console.error("Error searching doctors:", data.error);
        return [];
      }
    } catch (error) {
      console.error("Error searching doctors:", error);
      return [];
    }
  };

  const searchTimeSlots = async (query: string): Promise<TimeSlotOption[]> => {
    try {
      const doctorId = form.getValues("doctorId");
      const date = form.getValues("appointmentDate");

      if (!doctorId || !date) {
        return [];
      }

      const params = new URLSearchParams();
      params.append("doctorId", doctorId);
      params.append("date", date);
      if (currentBranch?.id) {
        params.append("branchId", currentBranch.id);
      }
      if (query) {
        params.append("query", query);
      }

      const response = await fetch(`/api/time-slots?${params.toString()}`);
      const data = await response.json();

      if (response.ok) {
        return data.timeSlots || [];
      } else {
        console.error("Error searching time slots:", data.error);
        return [];
      }
    } catch (error) {
      console.error("Error searching time slots:", error);
      return [];
    }
  };

  // Initialize form
  const form = useForm<z.infer<typeof appointmentFormSchema>>({
    resolver: zodResolver(appointmentFormSchema),
    defaultValues: {
      branchId: currentBranch?.id || "",
      duration: 15,
      type: "regular",
      notes: "",
    },
  });

  // State for pre-selected doctor info
  const [preSelectedDoctor, setPreSelectedDoctor] =
    useState<ComboboxOption | null>(null);
  // State for pre-selected time slot info
  const [preSelectedTimeSlot, setPreSelectedTimeSlot] = useState<string | null>(
    null,
  );
  // State to track if form has been initialized with URL parameters
  const [isInitialized, setIsInitialized] = useState(false);

  // Populate form with URL parameters
  useEffect(() => {
    if (!searchParams) return;

    const doctorId = searchParams.get("doctorId");
    const date = searchParams.get("date");
    const startTime = searchParams.get("startTime");
    const duration = searchParams.get("duration");

    if (doctorId) {
      form.setValue("doctorId", doctorId);
      // Fetch doctor details to display the name
      fetchDoctorDetails(doctorId);
    }
    if (date) {
      form.setValue("appointmentDate", date);
    }
    if (startTime) {
      const decodedStartTime = decodeURIComponent(startTime);
      form.setValue("startTime", decodedStartTime);
      // Set pre-selected time slot label for display
      setPreSelectedTimeSlot(decodedStartTime);
      // Mark the field as touched to ensure validation works properly
      form.trigger("startTime");
    }
    if (duration) {
      form.setValue("duration", parseInt(duration));
    }

    // Mark as initialized after setting URL parameters
    setIsInitialized(true);
  }, [searchParams, form]);

  // Fetch doctor details for pre-selected doctor
  const fetchDoctorDetails = async (doctorId: string) => {
    try {
      const response = await fetch(`/api/doctors/${doctorId}`);
      if (response.ok) {
        const data = await response.json();
        const doctor = data.doctor;
        if (doctor) {
          setPreSelectedDoctor({
            value: doctor.id,
            label: doctor.user.name,
          });
        }
      }
    } catch (error) {
      console.error("Error fetching doctor details:", error);
    }
  };

  // We don't need to fetch all patients and doctors upfront anymore since we're using async search
  // We don't need to fetch branches anymore since we're using currentBranch

  // Watch doctor and date fields for the time slot component
  const watchedDoctorId = form.watch("doctorId");
  const watchedAppointmentDate = form.watch("appointmentDate");

  // Reset startTime when doctor or date changes (but only after initial form population)
  useEffect(() => {
    // Only reset if the form has been initialized and this is a user-initiated change
    if (isInitialized) {
      form.setValue("startTime", "");
    }
  }, [watchedDoctorId, watchedAppointmentDate, form, isInitialized]);

  // Calculate end time based on start time and duration
  const calculateEndTime = (startTime: string, durationMinutes: number) => {
    if (!startTime) return "";

    const [hours, minutes] = startTime.split(":").map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);

    const endDate = new Date(startDate.getTime() + durationMinutes * 60000);
    return format(endDate, "HH:mm");
  };

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof appointmentFormSchema>) => {
    setIsLoading(true);
    try {
      // Get startTime from URL params if available, otherwise use form value
      const urlStartTime = searchParams?.get("startTime");
      const startTime = urlStartTime
        ? decodeURIComponent(urlStartTime)
        : values.startTime;

      // Calculate end time
      const endTime = calculateEndTime(startTime, values.duration);

      // Prepare the data
      const appointmentData = {
        ...values,
        startTime, // Use the startTime we determined above
        organizationId: currentOrganization?.id,
        endTime,
      };

      console.log("Form values:", values);
      console.log("URL startTime:", urlStartTime);
      console.log("Using startTime:", startTime);
      console.log("Submitting appointment data:", appointmentData);

      // Create the appointment
      const response = await fetch("/api/appointments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(appointmentData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Appointment created successfully");
        router.push("/appointments");
      } else {
        console.error("Appointment creation failed:", data);
        toast.error(data.error || "Failed to create appointment");
      }
    } catch (error) {
      console.error("Error creating appointment:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">New Appointment</h1>
          <p className="text-muted-foreground">
            Schedule a new appointment for a patient
          </p>
        </div>
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Appointment Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="patientId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Patient</FormLabel>
                      <FormControl>
                        <AsyncCombobox
                          value={field.value}
                          onChange={field.onChange}
                          onSearch={searchPatients}
                          placeholder="Search for a patient"
                          emptyMessage="No patients found"
                          loadingMessage="Searching patients..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="doctorId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Doctor</FormLabel>
                      <FormControl>
                        <AsyncCombobox
                          value={field.value}
                          onChange={(value) => {
                            field.onChange(value);
                            // Clear pre-selected doctor when user manually selects a different doctor
                            if (value !== preSelectedDoctor?.value) {
                              setPreSelectedDoctor(null);
                            }
                          }}
                          onSearch={searchDoctors}
                          placeholder="Search for a doctor"
                          emptyMessage="No doctors found"
                          loadingMessage="Searching doctors..."
                          selectedLabel={preSelectedDoctor?.label}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="branchId"
                  render={({ field }) => {
                    // Set the branch ID value from currentBranch
                    React.useEffect(() => {
                      if (currentBranch?.id) {
                        field.onChange(currentBranch.id);
                      }
                    }, [currentBranch?.id]);

                    return (
                      <FormItem>
                        <FormLabel>Branch</FormLabel>
                        <FormControl>
                          <Input
                            value={currentBranch?.name || ""}
                            disabled
                            readOnly
                          />
                        </FormControl>
                        <FormDescription>
                          Using your current branch: {currentBranch?.name}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="appointmentDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          min={format(new Date(), "yyyy-MM-dd")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="startTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Time</FormLabel>
                      <FormControl>
                        <TimeSlotCombobox
                          value={field.value}
                          onChange={(value, duration) => {
                            field.onChange(value);
                            // Auto-fill duration from the selected slot's duration
                            if (duration) {
                              form.setValue("duration", duration);
                            }
                            // Clear pre-selected time slot when user manually selects a different time
                            if (value !== preSelectedTimeSlot) {
                              setPreSelectedTimeSlot(null);
                            }
                          }}
                          onSearch={searchTimeSlots}
                          placeholder="Select a time slot"
                          emptyMessage="No available time slots"
                          loadingMessage="Loading time slots..."
                          doctorId={watchedDoctorId}
                          date={watchedAppointmentDate}
                          triggerSearchOnFocus={true}
                          selectedLabel={
                            preSelectedTimeSlot
                              ? `${preSelectedTimeSlot} (Pre-selected)`
                              : undefined
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        {field.value && form.getValues("duration")
                          ? `End Time: ${calculateEndTime(
                              field.value,
                              form.getValues("duration"),
                            )}`
                          : "Select a start time and duration"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration (minutes)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={5}
                          step={5}
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Appointment Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="regular">Regular</SelectItem>
                          <SelectItem value="follow-up">Follow-up</SelectItem>
                          <SelectItem value="emergency">Emergency</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes about the appointment"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Creating..." : "Create Appointment"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
