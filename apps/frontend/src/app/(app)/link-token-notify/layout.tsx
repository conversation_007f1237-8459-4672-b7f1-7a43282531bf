import { Metadata } from "next";
import { PageHeader } from "@/components/page-header";

export const metadata: Metadata = {
  title: "Link Token Notifications",
  description: "View link token generation notifications",
};

interface LinkTokenNotifyLayoutProps {
  children: React.ReactNode;
}

export default function LinkTokenNotifyLayout({
  children,
}: LinkTokenNotifyLayoutProps) {
  return (
    <div className="flex flex-col space-y-6">
      <PageHeader
        heading="Link Token Notifications"
        subheading="View link token generation notifications from ABDM"
      />
      <div>{children}</div>
    </div>
  );
}
