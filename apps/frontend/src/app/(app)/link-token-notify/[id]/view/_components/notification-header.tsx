"use client";

import Link from "next/link";
import { format } from "date-fns";
import { ArrowLeft, CheckCircle, XCircle } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { cn } from "@/lib/utils";
import { LinkTokenNotification } from "@/app/(app)/link-token-notify/(list)/_lib/queries";

interface NotificationHeaderProps {
  notification: LinkTokenNotification;
}

export function NotificationHeader({ notification }: NotificationHeaderProps) {
  const hasError = !!notification.error;
  const hasResponse = !!notification.response?.linkToken;

  let status = "Unknown";
  let variant: "outline" | "destructive" | "default" = "outline";
  let icon = null;

  if (hasError) {
    status = "Error";
    variant = "destructive";
    icon = <XCircle className="h-4 w-4 mr-2" />;
  } else if (hasResponse) {
    status = "Success";
    variant = "default";
    icon = <CheckCircle className="h-4 w-4 mr-2" />;
  }

  return (
    <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
      <div className="flex flex-col space-y-1">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" asChild>
            <Link href="/link-token-notify">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Link>
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            Link Token Notification
          </h2>
          <Badge
            variant={variant}
            className={cn(
              "ml-2",
              variant === "default" &&
                "bg-green-100 text-green-800 hover:bg-green-100",
              variant === "destructive" &&
                "bg-red-100 text-red-800 hover:bg-red-100",
              variant === "outline" &&
                "bg-gray-100 text-gray-800 hover:bg-gray-100",
            )}
          >
            {icon}
            {status}
          </Badge>
        </div>
        <div className="text-sm text-muted-foreground">
          Request ID: {notification.requestId} • Created:{" "}
          {format(new Date(notification.createdAt), "PPP p")}
        </div>
      </div>
    </div>
  );
}
