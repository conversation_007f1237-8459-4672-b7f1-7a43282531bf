"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";
import { LinkTokenNotification } from "@/app/(app)/link-token-notify/(list)/_lib/queries";

export async function getLinkTokenNotificationById(
  id: string,
): Promise<LinkTokenNotification> {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get organization ID from cookies
  const userInfoCookie = cookies().get("user-info")?.value;
  let organizationId = "";

  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(decodeURIComponent(userInfoCookie));
      organizationId = userInfo.organizationId;
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Find the link token notification
  const notification = await prisma.generateTokenNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("Link token notification not found");
  }

  // Format the data to match the expected interface
  return {
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
    // Ensure error and response are properly formatted
    error: notification.error
      ? typeof notification.error === "string"
        ? JSON.parse(notification.error)
        : notification.error
      : null,
    response: notification.response
      ? typeof notification.response === "string"
        ? JSON.parse(notification.response)
        : notification.response
      : null,
  };
}
