import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { LinkTokenNotification } from "@/app/(app)/link-token-notify/(list)/_lib/queries";

interface NotificationDetailsProps {
  notification: LinkTokenNotification;
}

export function NotificationDetails({
  notification,
}: NotificationDetailsProps) {
  const hasError = !!notification.error;
  const hasResponse = !!notification.response?.linkToken;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Details</CardTitle>
        <CardDescription>
          Details of the link token generation notification
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Request ID
            </h3>
            <p className="mt-1 text-sm">{notification.requestId}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Status
            </h3>
            <p className="mt-1 text-sm">
              {hasError ? "Error" : hasResponse ? "Success" : "Unknown"}
            </p>
          </div>
        </div>

        {notification.response?.abhaAddress && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              ABHA Address
            </h3>
            <p className="mt-1 text-sm">{notification.response.abhaAddress}</p>
          </div>
        )}

        {notification.response?.linkToken && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Link Token
            </h3>
            <p className="mt-1 text-sm font-mono bg-gray-50 p-2 rounded border">
              {notification.response.linkToken}
            </p>
          </div>
        )}

        {notification.error && (
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Error Details
            </h3>
            <div className="mt-1 space-y-2">
              <div>
                <span className="text-xs font-medium text-muted-foreground">
                  Code:
                </span>{" "}
                <Badge variant="outline" className="bg-red-50 text-red-700">
                  {notification.error.code}
                </Badge>
              </div>
              <div>
                <span className="text-xs font-medium text-muted-foreground">
                  Message:
                </span>
                <p className="mt-1 text-sm text-red-600">
                  {notification.error.message}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Created At
            </h3>
            <p className="mt-1 text-sm">
              {format(new Date(notification.createdAt), "PPP p")}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">
              Updated At
            </h3>
            <p className="mt-1 text-sm">
              {format(new Date(notification.updatedAt), "PPP p")}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
