"use client";

import * as React from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { format } from "date-fns";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ExternalLink, CheckCircle, XCircle } from "lucide-react";

import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Badge } from "@workspace/ui/components/badge";
import { DataTablePagination } from "@workspace/data-table/component/data-table-pagination";
import { DataTableViewOptions } from "@workspace/data-table/component/data-table-view-options";

import { LinkTokenNotification } from "../_lib/queries";

interface LinkTokenNotificationsTableProps {
  initialData: {
    data: LinkTokenNotification[];
    pageCount: number;
  };
}

export function LinkTokenNotificationsTable({
  initialData,
}: LinkTokenNotificationsTableProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [data] = React.useState(initialData);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [requestIdFilter, setRequestIdFilter] = React.useState<string>(
    searchParams?.get("requestId") || "",
  );

  // Define columns
  const columns: ColumnDef<LinkTokenNotification>[] = [
    {
      accessorKey: "requestId",
      header: "Request ID",
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate font-medium">
          {row.getValue("requestId")}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const hasError = !!row.original.error;
        const hasResponse = !!row.original.response?.linkToken;

        if (hasError) {
          return (
            <Badge
              variant="destructive"
              className="bg-red-100 text-red-800 hover:bg-red-100"
            >
              <XCircle className="h-3.5 w-3.5 mr-1" />
              Error
            </Badge>
          );
        } else if (hasResponse) {
          return (
            <Badge
              variant="default"
              className="bg-green-100 text-green-800 hover:bg-green-100"
            >
              <CheckCircle className="h-3.5 w-3.5 mr-1" />
              Success
            </Badge>
          );
        } else {
          return (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 hover:bg-gray-100"
            >
              Unknown
            </Badge>
          );
        }
      },
    },
    {
      accessorKey: "abhaAddress",
      header: "ABHA Address",
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.original.response?.abhaAddress || "-"}
        </div>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }) => (
        <div className="font-medium">
          {format(new Date(row.original.createdAt), "dd/MM/yyyy HH:mm:ss")}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center justify-end gap-2">
          <Button variant="ghost" size="icon" asChild className="h-8 w-8">
            <Link href={`/link-token-notify/${row.original.id}/view`}>
              <ExternalLink className="h-4 w-4" />
              <span className="sr-only">View</span>
            </Link>
          </Button>
        </div>
      ),
    },
  ];

  // Create table instance
  const table = useReactTable({
    data: data.data,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    manualPagination: true,
    pageCount: data.pageCount,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  // Handle search and filter changes
  const handleSearch = React.useCallback(() => {
    const params = new URLSearchParams();

    if (searchParams) {
      searchParams.forEach((value, key) => {
        params.set(key, value);
      });
    }

    if (requestIdFilter) {
      params.set("requestId", requestIdFilter);
    } else {
      params.delete("requestId");
    }

    params.set("page", "1");

    router.push(`${pathname}?${params.toString()}`);
  }, [pathname, router, searchParams, requestIdFilter]);

  // Handle pagination changes
  const handlePageChange = React.useCallback(
    (page: number) => {
      const params = new URLSearchParams();

      if (searchParams) {
        searchParams.forEach((value, key) => {
          params.set(key, value);
        });
      }

      params.set("page", page.toString());
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams],
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder="Filter by Request ID"
            value={requestIdFilter}
            onChange={(e) => setRequestIdFilter(e.target.value)}
            className="h-8 w-[250px]"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={handleSearch}
            className="h-8"
          >
            Search
          </Button>
        </div>
        <DataTableViewOptions table={table} />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <DataTablePagination
        table={table}
        pageCount={data.pageCount}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
