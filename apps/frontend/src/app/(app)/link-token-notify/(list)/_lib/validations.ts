import { z } from "zod";

export const GetLinkTokenNotificationsSchema = z.object({
  page: z.number().default(1),
  perPage: z.number().default(10),
  sort: z
    .object({
      field: z.string(),
      direction: z.enum(["asc", "desc"]),
    })
    .optional(),
  requestId: z.string().optional(),
  status: z.string().optional(),
});

export type GetLinkTokenNotificationsInput = z.infer<
  typeof GetLinkTokenNotificationsSchema
>;
