import * as React from "react";
import { DataTableSkeleton } from "@workspace/data-table/component/data-table-skeleton";
import { LinkTokenNotificationsTable } from "./_component/table";
import { getLinkTokenNotifications } from "./_lib/queries";
import { GetLinkTokenNotificationsSchema } from "./_lib/validations";

interface LinkTokenNotificationsPageProps {
  searchParams: {
    [key: string]: string | string[] | undefined;
  };
}

export default async function LinkTokenNotificationsPage({
  searchParams,
}: LinkTokenNotificationsPageProps) {
  // Parse search params
  const parsedParams = GetLinkTokenNotificationsSchema.parse({
    page: searchParams.page ? Number(searchParams.page) : 1,
    perPage: searchParams.per_page ? Number(searchParams.per_page) : 10,
    sort: searchParams.sort
      ? JSON.parse(decodeURIComponent(searchParams.sort as string))
      : undefined,
    requestId: searchParams.requestId,
    status: searchParams.status,
  });

  // Fetch data
  const notifications = await getLinkTokenNotifications(parsedParams);

  // Ensure the data is properly formatted for the table component
  const formattedNotifications = {
    data: notifications.data.map((notification) => ({
      ...notification,
      error: notification.error
        ? typeof notification.error === "string"
          ? JSON.parse(notification.error as string)
          : notification.error
        : null,
      response: notification.response
        ? typeof notification.response === "string"
          ? JSON.parse(notification.response as string)
          : notification.response
        : null,
    })),
    pageCount: notifications.pageCount,
  };

  return (
    <React.Suspense fallback={<DataTableSkeleton columnCount={6} />}>
      <LinkTokenNotificationsTable initialData={formattedNotifications} />
    </React.Suspense>
  );
}
