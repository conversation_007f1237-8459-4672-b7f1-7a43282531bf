import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>eader } from "@/components/dashboard-header";
import { DashboardShell } from "@/components/shell";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Patient Vitals",
  description: "View and manage patient vitals records",
};

export default async function VitalsPage() {
  return (
    <DashboardShell>
      <DashboardHeader
        heading="Patient Vitals"
        text="View and manage patient vitals records."
      >
        <Link href="/consultations">
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Record Vitals in Consultation
          </Button>
        </Link>
      </DashboardHeader>
      <div className="grid gap-4">
        <div className="text-center py-8">
          <h2 className="text-xl font-medium mb-2">Vitals Management</h2>
          <p className="text-muted-foreground mb-4">
            Vitals are recorded as part of patient consultations. To record new
            vitals, please create or open a consultation.
          </p>
          <Link href="/consultations">
            <Button>Go to Consultations</Button>
          </Link>
        </div>
      </div>
    </DashboardShell>
  );
}
