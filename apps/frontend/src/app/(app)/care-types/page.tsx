"use client";

import { useState, useEffect } from "react";
import { useCareType } from "@/contexts/care-type-context";
import { useDepartment } from "@/contexts/department-context";
import { Button } from "@/components/ui/button";
import { PlusI<PERSON>, Pencil, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";

export default function CareTypesPage() {
  const { careTypes, isLoading, refreshCareTypes } = useCareType();
  const { departments } = useDepartment();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCareType, setSelectedCareType] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    departmentId: "",
  });
  const [filterDepartmentId, setFilterDepartmentId] = useState("all");

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      price: "",
      departmentId: "",
    });
  };

  // Add useEffect to refresh care types when the page loads
  useEffect(() => {
    refreshCareTypes(
      filterDepartmentId === "all" ? undefined : filterDepartmentId,
    );
  }, []);

  const handleAddCareType = async () => {
    if (!formData.name) {
      toast.error("Please enter a name for the care type");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch("/api/care-types", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Care type added successfully");
        setIsAddDialogOpen(false);
        resetForm();
        // Force a refresh with a small delay
        setTimeout(() => {
          refreshCareTypes(
            filterDepartmentId === "all" ? undefined : filterDepartmentId,
          );
        }, 300);
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to add care type");
      }
    } catch (error) {
      console.error("Error adding care type:", error);
      toast.error("An error occurred while adding the care type");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditClick = (careType: any) => {
    setSelectedCareType(careType);
    setFormData({
      name: careType.name,
      description: careType.description || "",
      price: careType.price.toString(),
      departmentId: careType.departmentId,
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateCareType = async () => {
    if (!formData.name) {
      toast.error("Please enter a name for the care type");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/care-types/${selectedCareType.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Care type updated successfully");
        setIsEditDialogOpen(false);
        resetForm();
        // Force a refresh with a small delay
        setTimeout(() => {
          refreshCareTypes(
            filterDepartmentId === "all" ? undefined : filterDepartmentId,
          );
        }, 300);
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to update care type");
      }
    } catch (error) {
      console.error("Error updating care type:", error);
      toast.error("An error occurred while updating the care type");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteClick = (careType: any) => {
    setSelectedCareType(careType);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteCareType = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/care-types/${selectedCareType.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Care type deleted successfully");
        setIsDeleteDialogOpen(false);
        // Force a refresh with a small delay
        setTimeout(() => {
          refreshCareTypes(
            filterDepartmentId === "all" ? undefined : filterDepartmentId,
          );
        }, 300);
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete care type");
      }
    } catch (error) {
      console.error("Error deleting care type:", error);
      toast.error("An error occurred while deleting the care type");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFilterChange = (departmentId: string) => {
    setFilterDepartmentId(departmentId);
    refreshCareTypes(departmentId === "all" ? undefined : departmentId);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Care Types</h1>
          <p className="text-muted-foreground">
            Manage care types and pricing in your organization
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Care Type
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Care Type</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name*
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="price" className="text-right">
                  Price (₹)
                </Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="departmentId" className="text-right">
                  Department*
                </Label>
                <Select
                  value={formData.departmentId}
                  onValueChange={(value) =>
                    handleSelectChange("departmentId", value)
                  }
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((department) => (
                      <SelectItem key={department.id} value={department.id}>
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                onClick={handleAddCareType}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Adding..." : "Add Care Type"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-4 mb-4">
        <Label htmlFor="filter-department">Filter by Department:</Label>
        <Select value={filterDepartmentId} onValueChange={handleFilterChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="All Departments" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            {departments.map((department) => (
              <SelectItem key={department.id} value={department.id}>
                {department.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  Loading care types...
                </TableCell>
              </TableRow>
            ) : careTypes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  No care types found. Add a care type to get started.
                </TableCell>
              </TableRow>
            ) : (
              careTypes.map((careType) => (
                <TableRow key={careType.id}>
                  <TableCell className="font-medium">{careType.name}</TableCell>
                  <TableCell>{careType.department?.name || "—"}</TableCell>
                  <TableCell>₹{careType.price.toFixed(2)}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    {careType.description || "—"}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditClick(careType)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(careType)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Care Type Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Care Type</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Name*
              </Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-price" className="text-right">
                Price (₹)
              </Label>
              <Input
                id="edit-price"
                name="price"
                type="number"
                value={formData.price}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-departmentId" className="text-right">
                Department*
              </Label>
              <Select
                value={formData.departmentId}
                onValueChange={(value) =>
                  handleSelectChange("departmentId", value)
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((department) => (
                    <SelectItem key={department.id} value={department.id}>
                      {department.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right">
                Description
              </Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="col-span-3"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={handleUpdateCareType}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Updating..." : "Update Care Type"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Care Type Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Care Type</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete the care type "
              {selectedCareType?.name}"? This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={handleDeleteCareType}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete Care Type"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
