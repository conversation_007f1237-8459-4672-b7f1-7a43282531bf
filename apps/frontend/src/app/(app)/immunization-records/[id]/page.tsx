"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Trash2, Shield, User } from "lucide-react";
import Link from "next/link";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import { format } from "date-fns";

interface Immunization {
  id: string;
  patientId: string;
  doctorId: string;
  consultationId?: string;
  status: string;
  statusReason?: string;
  vaccineCode: string;
  vaccineDisplay: string;
  occurrenceDateTime: string;
  recorded: string;
  primarySource: boolean;
  reportOrigin?: string;
  location?: string;
  manufacturer?: string;
  lotNumber?: string;
  expirationDate?: string;
  site?: string;
  route?: string;
  doseQuantity?: string;
  performer?: string;
  note?: string;
  reasonCode?: string;
  reasonDisplay?: string;
  isSubpotent: boolean;
  subpotentReason?: string;
  programEligibility?: string;
  fundingSource?: string;
  reaction?: any[];
  protocolApplied?: any[];
  createdAt: string;
  updatedAt: string;
  patient?: {
    firstName: string;
    lastName: string;
  };
  doctor?: {
    user: {
      name: string;
    };
  };
}

export default function ImmunizationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [immunization, setImmunization] = useState<Immunization | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params?.id) {
      fetchImmunization(params.id as string);
    }
  }, [params?.id]);

  const fetchImmunization = async (id: string) => {
    try {
      setLoading(true);
      const response = await Fetch.get(`/api/immunization/${id}`);
      if (response.error) {
        throw new Error(response.error);
      }
      setImmunization(response as unknown as Immunization);
    } catch (error) {
      console.error("Error fetching immunization:", error);
      toast.error("Failed to fetch immunization record");
      router.push("/immunization-records");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!immunization) return;

    if (
      confirm(
        "Are you sure you want to delete this immunization record? This action cannot be undone.",
      )
    ) {
      try {
        const response = await Fetch.delete(
          `/api/immunization/${immunization.id}`,
        );
        if (response.error) {
          throw new Error(response.error);
        }
        toast.success("Immunization record deleted successfully");
        router.push("/immunization-records");
      } catch (error) {
        console.error("Error deleting immunization:", error);
        toast.error("Failed to delete immunization record");
      }
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading immunization record...</div>
        </div>
      </div>
    );
  }

  if (!immunization) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">
            Immunization Record Not Found
          </h1>
          <Link href="/immunization-records">
            <Button>Back to Immunization Records</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/immunization-records">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Immunization Records
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Immunization Record</h1>
            <p className="text-muted-foreground">
              {immunization.patient
                ? `${immunization.patient.firstName} ${immunization.patient.lastName}`
                : "Unknown Patient"}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Link href={`/immunization-records/${immunization.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </Link>
          <Button variant="destructive" size="sm" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Patient Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Patient Name
              </p>
              <p className="text-lg">
                {immunization.patient
                  ? `${immunization.patient.firstName} ${immunization.patient.lastName}`
                  : "Unknown Patient"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Administering Doctor
              </p>
              <p className="text-lg">
                {immunization.doctor?.user.name || "Unknown Doctor"}
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Date Given
                </p>
                <p>
                  {format(
                    new Date(immunization.occurrenceDateTime),
                    "MMM dd, yyyy",
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Vaccine Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Vaccine Name
              </p>
              <p className="text-lg font-semibold">
                {immunization.vaccineDisplay}
              </p>
            </div>
            {immunization.manufacturer && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Manufacturer
                </p>
                <p>{immunization.manufacturer}</p>
              </div>
            )}
            {immunization.lotNumber && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Lot Number
                </p>
                <p>{immunization.lotNumber}</p>
              </div>
            )}
            {immunization.expirationDate && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Expiration Date
                </p>
                <p>
                  {format(
                    new Date(immunization.expirationDate),
                    "MMM dd, yyyy",
                  )}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Protocol Applied */}
      {immunization.protocolApplied &&
        immunization.protocolApplied.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Protocol Applied</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {immunization.protocolApplied.map(
                  (protocol: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {protocol.series && (
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">
                              Series
                            </p>
                            <p>{protocol.series}</p>
                          </div>
                        )}
                        {protocol.doseNumber && (
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">
                              Dose Number
                            </p>
                            <p>{protocol.doseNumber}</p>
                          </div>
                        )}
                        {protocol.authority && (
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">
                              Authority
                            </p>
                            <p>{protocol.authority}</p>
                          </div>
                        )}
                        {protocol.targetDisease && (
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">
                              Target Disease
                            </p>
                            <p>{protocol.targetDisease}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ),
                )}
              </div>
            </CardContent>
          </Card>
        )}

      {/* Footer Information */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <div>
              Created:{" "}
              {format(
                new Date(immunization.createdAt),
                "MMM dd, yyyy 'at' HH:mm",
              )}
            </div>
            <div>
              Last updated:{" "}
              {format(
                new Date(immunization.updatedAt),
                "MMM dd, yyyy 'at' HH:mm",
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
