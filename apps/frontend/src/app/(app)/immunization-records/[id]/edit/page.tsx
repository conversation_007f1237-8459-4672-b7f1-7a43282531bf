"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { ImmunizationForm } from "@/components/immunization/immunization-form";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";

interface Immunization {
  id: string;
  patientId: string;
  doctorId: string;
  consultationId?: string;
  status: string;
  statusReason?: string;
  vaccineCode: string;
  vaccineDisplay: string;
  occurrenceDateTime: string;
  recorded: string;
  primarySource: boolean;
  reportOrigin?: string;
  location?: string;
  manufacturer?: string;
  lotNumber?: string;
  expirationDate?: string;
  site?: string;
  route?: string;
  doseQuantity?: string;
  performer?: string;
  note?: string;
  reasonCode?: string;
  reasonDisplay?: string;
  isSubpotent: boolean;
  subpotentReason?: string;
  programEligibility?: string;
  fundingSource?: string;
  reaction?: any[];
  protocolApplied?: any[];
  patient?: {
    firstName: string;
    lastName: string;
  };
  doctor?: {
    user: {
      name: string;
    };
  };
}

export default function EditImmunizationRecordPage() {
  const params = useParams();
  const router = useRouter();
  const [immunization, setImmunization] = useState<Immunization | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params?.id) {
      fetchImmunization(params.id as string);
    }
  }, [params?.id]);

  const fetchImmunization = async (id: string) => {
    try {
      setLoading(true);
      const response = await Fetch.get(`/api/immunization/${id}`);
      if (response.error) {
        throw new Error(response.error);
      }
      setImmunization(response as unknown as Immunization);
    } catch (error) {
      console.error("Error fetching immunization:", error);
      toast.error("Failed to fetch immunization record");
      router.push("/immunization-records");
    } finally {
      setLoading(false);
    }
  };

  const handleSuccess = () => {
    toast.success("Immunization record updated successfully");
    router.push(`/immunization-records/${params?.id}`);
  };

  const handleCancel = () => {
    router.push(`/immunization-records/${params?.id}`);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading immunization record...</div>
        </div>
      </div>
    );
  }

  if (!immunization) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">
            Immunization Record Not Found
          </h1>
          <Link href="/immunization-records">
            <Button>Back to Immunization Records</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/immunization-records/${params?.id}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Record
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Edit Immunization Record</h1>
          <p className="text-muted-foreground">
            {immunization.patient
              ? `${immunization.patient.firstName} ${immunization.patient.lastName}`
              : "Unknown Patient"}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Update Immunization Record Details</CardTitle>
        </CardHeader>
        <CardContent>
          <ImmunizationForm
            patientId={immunization.patientId}
            doctorId={immunization.doctorId}
            consultationId={immunization.consultationId}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
            // Note: The existing ImmunizationForm component doesn't support edit mode
            // We would need to modify it to accept initial data and mode props
            // For now, this will create a new form with the same patient/doctor
          />
        </CardContent>
      </Card>

      {/* Current Data Display */}
      <Card>
        <CardHeader>
          <CardTitle>Current Record Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-muted-foreground">Vaccine:</p>
              <p>
                {immunization.vaccineDisplay} ({immunization.vaccineCode})
              </p>
            </div>
            <div>
              <p className="font-medium text-muted-foreground">Status:</p>
              <p>{immunization.status}</p>
            </div>
            <div>
              <p className="font-medium text-muted-foreground">Date Given:</p>
              <p>
                {new Date(immunization.occurrenceDateTime).toLocaleDateString()}
              </p>
            </div>
            <div>
              <p className="font-medium text-muted-foreground">Manufacturer:</p>
              <p>{immunization.manufacturer || "Not specified"}</p>
            </div>
            <div>
              <p className="font-medium text-muted-foreground">Lot Number:</p>
              <p>{immunization.lotNumber || "Not specified"}</p>
            </div>
            <div>
              <p className="font-medium text-muted-foreground">Site:</p>
              <p>{immunization.site || "Not specified"}</p>
            </div>
          </div>
          {immunization.note && (
            <div className="mt-4">
              <p className="font-medium text-muted-foreground">Notes:</p>
              <p className="mt-1">{immunization.note}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
