"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { ImmunizationForm } from "@/components/immunization/immunization-form";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
}

interface Doctor {
  id: string;
  user: {
    name: string;
  };
}

interface Consultation {
  id: string;
  consultationDate: string;
  status: string;
  patient: {
    firstName: string;
    lastName: string;
  };
  doctor: {
    user: {
      name: string;
    };
  };
}

export default function NewImmunizationRecordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [selectedPatientId, setSelectedPatientId] = useState("");
  const [selectedDoctorId, setSelectedDoctorId] = useState("");
  const [consultationId, setConsultationId] = useState("");
  const [isLoadingConsultations, setIsLoadingConsultations] = useState(false);

  useEffect(() => {
    // Get parameters from URL if available
    const patientId = searchParams?.get("patientId");
    const doctorId = searchParams?.get("doctorId");
    const consId = searchParams?.get("consultationId");

    if (patientId) setSelectedPatientId(patientId);
    if (doctorId) setSelectedDoctorId(doctorId);
    if (consId) setConsultationId(consId);

    fetchPatients();
    fetchDoctors();
  }, [searchParams]);

  // Fetch consultations when patient and doctor are selected
  useEffect(() => {
    if (selectedPatientId && selectedDoctorId) {
      fetchConsultations();
    } else {
      setConsultations([]);
      setConsultationId("");
    }
  }, [selectedPatientId, selectedDoctorId]);

  const fetchPatients = async () => {
    try {
      const response = await Fetch.get("/api/patients");
      if (response.error) {
        throw new Error(response.error);
      }
      // Extract patients array from nested response
      const patientsData = response.patients || response;
      setPatients(Array.isArray(patientsData) ? patientsData : []);
    } catch (error) {
      console.error("Error fetching patients:", error);
      toast.error("Failed to fetch patients");
      setPatients([]);
    }
  };

  const fetchDoctors = async () => {
    try {
      const response = await Fetch.get("/api/doctors");
      if (response.error) {
        throw new Error(response.error);
      }
      // Extract doctors array from nested response
      const doctorsData = response.doctors || response;
      setDoctors(Array.isArray(doctorsData) ? doctorsData : []);
    } catch (error) {
      console.error("Error fetching doctors:", error);
      toast.error("Failed to fetch doctors");
      setDoctors([]);
    }
  };

  const fetchConsultations = async () => {
    if (!selectedPatientId || !selectedDoctorId) {
      setConsultations([]);
      return;
    }

    setIsLoadingConsultations(true);
    try {
      const response = await Fetch.get(
        `/api/consultations?patientId=${selectedPatientId}&doctorId=${selectedDoctorId}`,
      );
      if (response.error) {
        throw new Error(response.error);
      }
      // Extract consultations array from nested response
      const consultationsData = response.consultations || response;
      const consultationsList = Array.isArray(consultationsData)
        ? consultationsData
        : [];
      setConsultations(consultationsList);

      // If no consultations found, show a message
      if (consultationsList.length === 0) {
        toast.info(
          "No consultations found for this patient-doctor combination",
        );
      }
    } catch (error) {
      console.error("Error fetching consultations:", error);
      toast.error("Failed to fetch consultations");
      setConsultations([]);
    } finally {
      setIsLoadingConsultations(false);
    }
  };

  const handleSuccess = () => {
    toast.success("Immunization record created successfully");
    // Navigate to the list page and force a refresh
    router.push("/immunization-records");
    // Use setTimeout to ensure navigation happens first, then refresh
    setTimeout(() => {
      router.refresh();
    }, 100);
  };

  const handleCancel = () => {
    router.push("/immunization-records");
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/immunization-records">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Immunization Records
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">New Immunization Record</h1>
          <p className="text-muted-foreground">
            Create a new immunization record for a patient
          </p>
        </div>
      </div>

      {/* Patient, Doctor, and Consultation Selection */}
      {(!selectedPatientId || !selectedDoctorId || !consultationId) && (
        <Card>
          <CardHeader>
            <CardTitle>Select Patient, Doctor, and Consultation</CardTitle>
            <p className="text-sm text-muted-foreground">
              All three selections are required to create an immunization record
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Patient <span className="text-red-500">*</span>
                </label>
                <select
                  value={selectedPatientId}
                  onChange={(e) => {
                    setSelectedPatientId(e.target.value);
                    setConsultationId(""); // Reset consultation when patient changes
                  }}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a patient</option>
                  {(patients || []).map((patient) => (
                    <option key={patient.id} value={patient.id}>
                      {patient.firstName} {patient.lastName}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Doctor <span className="text-red-500">*</span>
                </label>
                <select
                  value={selectedDoctorId}
                  onChange={(e) => {
                    setSelectedDoctorId(e.target.value);
                    setConsultationId(""); // Reset consultation when doctor changes
                  }}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a doctor</option>
                  {(doctors || []).map((doctor) => (
                    <option key={doctor.id} value={doctor.id}>
                      {doctor.user.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Consultation Selection */}
            {selectedPatientId && selectedDoctorId && (
              <div>
                <label className="block text-sm font-medium mb-2">
                  Consultation <span className="text-red-500">*</span>
                  <span className="text-xs text-muted-foreground ml-2">
                    Select the consultation this immunization record relates to
                  </span>
                </label>
                {isLoadingConsultations ? (
                  <div className="w-full p-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500">
                    Loading consultations...
                  </div>
                ) : consultations.length > 0 ? (
                  <select
                    value={consultationId}
                    onChange={(e) => setConsultationId(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a consultation</option>
                    {consultations.map((consultation) => (
                      <option key={consultation.id} value={consultation.id}>
                        {new Date(
                          consultation.consultationDate,
                        ).toLocaleDateString()}{" "}
                        - {consultation.status}
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="w-full p-2 border border-gray-300 rounded-md bg-red-50 text-red-600">
                    No consultations found for this patient-doctor combination.
                    Please create a consultation first.
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Form - Only show when all three are selected */}
      {selectedPatientId && selectedDoctorId && consultationId && (
        <Card>
          <CardHeader>
            <CardTitle>Immunization Record Details</CardTitle>
            <p className="text-sm text-muted-foreground">
              Creating immunization record for consultation on{" "}
              {consultations.find((c) => c.id === consultationId)
                ?.consultationDate
                ? new Date(
                    consultations.find(
                      (c) => c.id === consultationId,
                    )!.consultationDate,
                  ).toLocaleDateString()
                : ""}
            </p>
          </CardHeader>
          <CardContent>
            <ImmunizationForm
              patientId={selectedPatientId}
              doctorId={selectedDoctorId}
              consultationId={consultationId}
              onSuccess={handleSuccess}
              onCancel={handleCancel}
            />
          </CardContent>
        </Card>
      )}

      {(!selectedPatientId || !selectedDoctorId || !consultationId) && (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-muted-foreground">
              {!selectedPatientId || !selectedDoctorId
                ? "Please select both a patient and a doctor to see available consultations."
                : !consultationId
                  ? "Please select a consultation to continue creating the immunization record."
                  : "Please complete all required selections to continue."}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
