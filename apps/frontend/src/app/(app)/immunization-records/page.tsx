"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Fetch } from "@/services/fetch";
import { toast } from "sonner";
import {
  Plus,
  Search,
  Shield,
  Calendar,
  User,
  Stethoscope,
  RefreshCw,
  Upload,
  Loader2,
  Download,
} from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface Immunization {
  id: string;
  patientId: string;
  doctorId: string;
  consultationId?: string;
  status: string;
  statusReason?: string;
  vaccineCode: string;
  vaccineDisplay: string;
  occurrenceDateTime: string;
  recorded: string;
  primarySource: boolean;
  reportOrigin?: string;
  location?: string;
  manufacturer?: string;
  lotNumber?: string;
  expirationDate?: string;
  site?: string;
  route?: string;
  doseQuantity?: string;
  performer?: string;
  note?: string;
  reasonCode?: string;
  reasonDisplay?: string;
  isSubpotent: boolean;
  subpotentReason?: string;
  programEligibility?: string;
  fundingSource?: string;
  reaction?: any[];
  protocolApplied?: any[];
  createdAt: string;
  updatedAt: string;
  patient?: {
    firstName: string;
    lastName: string;
    gender?: string;
    dateOfBirth?: string;
    phone?: string;
  };
  doctor?: {
    user: {
      name: string;
    };
  };
}

export default function ImmunizationRecordsPage() {
  const [immunizations, setImmunizations] = useState<Immunization[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [uploadingIds, setUploadingIds] = useState<Set<string>>(new Set());

  // Upload dialog state
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedImmunization, setSelectedImmunization] =
    useState<Immunization | null>(null);
  const [fetchRequests] = useState<any[]>([]);
  const [selectedFetchRequest, setSelectedFetchRequest] = useState<string>("");
  const [loadingFetchRequests] = useState(false);

  useEffect(() => {
    fetchImmunizations();
  }, []);

  // Refresh data when the page becomes visible (e.g., when navigating back)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchImmunizations();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Also refresh when the component mounts
    const handleFocus = () => {
      fetchImmunizations();
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleFocus);
    };
  }, []);

  const fetchImmunizations = async () => {
    try {
      setLoading(true);
      const response = await Fetch.get("/api/immunization");
      console.log("Raw immunization response:", response);

      if (response.error) {
        throw new Error(response.error);
      }

      // Filter values that are actual immunization records (exclude success/status keys)
      const immunizationsData = Object.values(response).filter(
        (item) => typeof item === "object" && item?.id && item?.vaccineDisplay,
      );

      console.log("Parsed immunizations:", immunizationsData.length);
      setImmunizations(immunizationsData);
    } catch (error) {
      console.error("Error fetching immunizations:", error);
      toast.error("Failed to fetch immunization records");
      setImmunizations([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle upload button click - show dialog to select HI request
  // const handleUploadClick = async (immunization: Immunization) => {
  //   setSelectedImmunization(immunization);
  //   setLoadingFetchRequests(true);
  //   setUploadDialogOpen(true);

  //   try {
  //     // Load HI requests using the same logic as discharge summaries
  //     // Pass consultationId if available to match exact consultation
  //     const url = immunization.consultationId
  //       ? `/api/patients/${immunization.patientId}/hi-requests?consultationId=${immunization.consultationId}`
  //       : `/api/patients/${immunization.patientId}/hi-requests`;

  //     const response = await Fetch.get(url);
  //     if (response.error) {
  //       throw new Error(response.error);
  //     }
  //     setFetchRequests(response.hiRequests || []);
  //   } catch (error) {
  //     console.error("Error loading HI requests:", error);
  //     toast.error("Failed to load health record requests");
  //     setFetchRequests([]);
  //   } finally {
  //     setLoadingFetchRequests(false);
  //   }
  // };

  // Handle actual upload to selected HI request
  const handleUpload = async () => {
    if (!selectedImmunization || !selectedFetchRequest) {
      toast.error("Please select a health record request");
      return;
    }

    try {
      setUploadingIds((prev) => new Set(prev).add(selectedImmunization.id));

      // Generate FHIR bundle for the immunization record
      const bundleParams = new URLSearchParams();

      // Add required parameters for immunization bundle (matching strategy expectations)
      const patientName = selectedImmunization.patient
        ? `${selectedImmunization.patient.firstName} ${selectedImmunization.patient.lastName}`
        : "Unknown Patient";
      const doctorName =
        selectedImmunization.doctor?.user?.name || "Unknown Doctor";

      // Patient information
      bundleParams.append("patientName", patientName);
      bundleParams.append(
        "patientFirstName",
        selectedImmunization.patient?.firstName || "Unknown",
      );
      bundleParams.append(
        "patientLastName",
        selectedImmunization.patient?.lastName || "Patient",
      );
      bundleParams.append("patientId", selectedImmunization.patientId);
      bundleParams.append(
        "patientGender",
        selectedImmunization.patient?.gender || "",
      );
      bundleParams.append(
        "patientBirthDate",
        selectedImmunization.patient?.dateOfBirth || "",
      );
      bundleParams.append(
        "patientPhone",
        selectedImmunization.patient?.phone || "",
      );

      // Practitioner information
      bundleParams.append("practitionerName", doctorName);
      bundleParams.append("practitionerId", selectedImmunization.doctorId);

      // Organization information (use defaults for now)
      bundleParams.append("organizationName", "Healthcare Facility");
      bundleParams.append("organizationId", "ORG001");
      bundleParams.append("organizationPhone", "+91 11 2345 6789");
      bundleParams.append("organizationEmail", "<EMAIL>");

      // Composition details
      bundleParams.append(
        "compositionIdentifier",
        `immunization-${selectedImmunization.id}`,
      );
      bundleParams.append("compositionDate", new Date().toISOString());
      bundleParams.append("compositionTitle", "Immunization Record");

      // Immunization details (matching API endpoint expectations)
      bundleParams.append("vaccinationStatus", selectedImmunization.status);
      bundleParams.append("vaccineCode", selectedImmunization.vaccineCode);
      bundleParams.append("vaccineName", selectedImmunization.vaccineDisplay);
      bundleParams.append(
        "vaccinationDate",
        selectedImmunization.occurrenceDateTime,
      );

      // Add optional immunization fields if they exist (matching API expectations)
      if (selectedImmunization.site) {
        bundleParams.append("vaccinationSite", selectedImmunization.site);
      }
      if (selectedImmunization.route) {
        bundleParams.append("vaccinationRoute", selectedImmunization.route);
      }
      if (selectedImmunization.doseQuantity) {
        bundleParams.append(
          "vaccinationDose",
          selectedImmunization.doseQuantity,
        );
      }

      // Add recommendation details for next dose (optional)
      bundleParams.append(
        "recommendedVaccineName",
        `${selectedImmunization.vaccineDisplay} - Next Dose`,
      );
      bundleParams.append(
        "recommendedVaccineCode",
        selectedImmunization.vaccineCode,
      );

      // Set recommended date to 28 days from vaccination date (common for vaccines)
      const nextDoseDate = new Date(selectedImmunization.occurrenceDateTime);
      nextDoseDate.setDate(nextDoseDate.getDate() + 28);
      bundleParams.append(
        "recommendedDate",
        nextDoseDate.toISOString().split("T")[0],
      );

      // Step 1: Generate the FHIR bundle
      const bundleResponse = await Fetch.get(
        `/api/fhir/bundles/immunization-record?${bundleParams.toString()}`,
      );

      if (bundleResponse.error) {
        throw new Error(bundleResponse.error);
      }

      // Step 2: Send To Patient using the same approach as discharge summaries
      const uploadResponse = await Fetch.post(
        "/api/abdm/upload-immunization-record",
        {
          patientId: selectedImmunization.patientId,
          hiRequestId: selectedFetchRequest,
          fhirBundle: bundleResponse,
          consultationId: selectedImmunization.consultationId,
        },
      );

      if (uploadResponse.error) {
        throw new Error(uploadResponse.error);
      }

      toast.success("Immunization record uploaded to ABDM successfully!");
      setUploadDialogOpen(false);
      setSelectedImmunization(null);
      setSelectedFetchRequest("");
    } catch (error) {
      console.error("Error uploading immunization record:", error);
      toast.error(
        `Failed to upload immunization record: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    } finally {
      setUploadingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(selectedImmunization.id);
        return newSet;
      });
    }
  };

  // Handle PDF download
  const handleDownloadPdf = async (recordId: string) => {
    try {
      const response = await fetch(
        `/api/pdf/immunization-records?recordId=${recordId}`,
      );

      if (!response.ok) {
        throw new Error("Failed to generate PDF");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `immunization-record-${recordId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("PDF downloaded successfully");
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error("Failed to download PDF");
    }
  };

  const filteredImmunizations = (immunizations || []).filter((immunization) => {
    const searchLower = searchTerm.toLowerCase();
    const patientName = immunization.patient
      ? `${immunization.patient.firstName} ${immunization.patient.lastName}`
      : "";
    const doctorName = immunization.doctor?.user.name || "";
    const vaccineName = immunization.vaccineDisplay || "";

    return (
      patientName.toLowerCase().includes(searchLower) ||
      doctorName.toLowerCase().includes(searchLower) ||
      vaccineName.toLowerCase().includes(searchLower) ||
      immunization.vaccineCode.toLowerCase().includes(searchLower)
    );
  });

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading immunization records...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Immunization Records</h1>
          <p className="text-muted-foreground">
            Manage patient immunization and vaccination records
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => fetchImmunizations()}
            disabled={loading}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          <Link href="/immunization-records/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Immunization Record
            </Button>
          </Link>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search by patient name, doctor, vaccine name, or code..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Records
                </p>
                <p className="text-2xl font-bold">
                  {(immunizations || []).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  This Month
                </p>
                <p className="text-2xl font-bold">
                  {
                    (immunizations || []).filter(
                      (i) =>
                        new Date(i.createdAt).getMonth() ===
                        new Date().getMonth(),
                    ).length
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Filtered Results
                </p>
                <p className="text-2xl font-bold">
                  {filteredImmunizations.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Immunization Records List */}
      <div className="space-y-4">
        {filteredImmunizations.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Shield className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No immunization records found
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm
                  ? "No records match your search criteria."
                  : "Get started by creating your first immunization record."}
              </p>
              {!searchTerm && (
                <Link href="/immunization-records/new">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Immunization Record
                  </Button>
                </Link>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredImmunizations.map((immunization) => (
            <Card
              key={immunization.id}
              className="hover:shadow-md transition-shadow"
            >
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">
                      {immunization.patient
                        ? `${immunization.patient.firstName} ${immunization.patient.lastName}`
                        : "Unknown Patient"}
                    </CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Stethoscope className="h-4 w-4" />
                        <span>
                          {immunization.doctor?.user.name || "Unknown Doctor"}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Given:{" "}
                          {format(
                            new Date(immunization.occurrenceDateTime),
                            "MMM dd, yyyy",
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={
                        immunization.status === "completed"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {immunization.status}
                    </Badge>
                    {immunization.primarySource && (
                      <Badge variant="outline">Primary Source</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Vaccine
                    </p>
                    <p className="text-sm font-semibold">
                      {immunization.vaccineDisplay}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Code: {immunization.vaccineCode}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {immunization.manufacturer && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Manufacturer
                        </p>
                        <p className="text-sm">{immunization.manufacturer}</p>
                      </div>
                    )}
                    {immunization.lotNumber && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Lot Number
                        </p>
                        <p className="text-sm">{immunization.lotNumber}</p>
                      </div>
                    )}
                    {immunization.site && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Site
                        </p>
                        <p className="text-sm">{immunization.site}</p>
                      </div>
                    )}
                  </div>

                  {immunization.note && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Notes
                      </p>
                      <p className="text-sm line-clamp-2">
                        {immunization.note}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-between items-center pt-2">
                    <div className="text-xs text-muted-foreground">
                      Recorded:{" "}
                      {format(
                        new Date(immunization.recorded),
                        "MMM dd, yyyy 'at' HH:mm",
                      )}
                    </div>
                    <div className="space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadPdf(immunization.id)}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download PDF
                      </Button>

                      <Link href={`/immunization-records/${immunization.id}`}>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </Link>
                      <Link
                        href={`/immunization-records/${immunization.id}/edit`}
                      >
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Upload Immunization Record to ABDM</DialogTitle>
            <DialogDescription>
              Select a health record request to upload this immunization record
              to ABDM.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedImmunization && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Patient</Label>
                <p className="text-sm text-muted-foreground">
                  {selectedImmunization.patient
                    ? `${selectedImmunization.patient.firstName} ${selectedImmunization.patient.lastName}`
                    : "Unknown Patient"}
                </p>
                <Label className="text-sm font-medium">Vaccine</Label>
                <p className="text-sm text-muted-foreground">
                  {selectedImmunization.vaccineDisplay} (
                  {selectedImmunization.vaccineCode})
                </p>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="fetch-request">Health Record Request</Label>
              {loadingFetchRequests ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">
                    Loading requests...
                  </span>
                </div>
              ) : fetchRequests.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No active health record requests found for this patient.
                </p>
              ) : (
                <Select
                  value={selectedFetchRequest}
                  onValueChange={setSelectedFetchRequest}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a health record request" />
                  </SelectTrigger>
                  <SelectContent>
                    {fetchRequests.map((request) => (
                      <SelectItem key={request.id} value={request.id}>
                        <div className="flex flex-col">
                          <span>
                            Request{" "}
                            {request.transactionId?.slice(-8) ||
                              request.id.slice(-8)}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(
                              request.requestTimestamp,
                            ).toLocaleDateString()}{" "}
                            - {request.status}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setUploadDialogOpen(false);
                setSelectedImmunization(null);
                setSelectedFetchRequest("");
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpload}
              disabled={
                !selectedFetchRequest ||
                uploadingIds.has(selectedImmunization?.id || "")
              }
            >
              {uploadingIds.has(selectedImmunization?.id || "") ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Send To Patient
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
