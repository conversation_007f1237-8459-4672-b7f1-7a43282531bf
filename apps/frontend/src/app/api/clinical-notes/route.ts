export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";

// GET /api/clinical-notes - Get all clinical notes
export async function GET(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const consultationId = searchParams?.get("consultationId");
    const patientId = searchParams?.get("patientId");
    const doctorId = searchParams?.get("doctorId");

    // Get organization ID from user info cookie
    const userInfoCookie = cookies().get("user-info")?.value;
    let organizationId = "";

    if (userInfoCookie) {
      try {
        const userInfo = JSON.parse(userInfoCookie);
        organizationId = userInfo.organizationId || "";
      } catch (error) {
        console.error("Error parsing user info:", error);
      }
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "No organization found" },
        { status: 404 },
      );
    }

    // Build the where clause
    let where: any = {
      organizationId,
    };

    if (consultationId) {
      where.consultationId = consultationId;
    }

    if (patientId) {
      where.patientId = patientId;
    }

    if (doctorId) {
      where.doctorId = doctorId;
    }

    // Get clinical notes
    const clinicalNotes = await db.clinicalNote.findMany({
      where,
      include: {
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({ clinicalNotes });
  } catch (error) {
    console.error("Error fetching clinical notes:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

// POST /api/clinical-notes - Create a new clinical note
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get organization ID from user info cookie
    const userInfoCookie = cookies().get("user-info")?.value;
    let organizationId = "";

    if (userInfoCookie) {
      try {
        const userInfo = JSON.parse(userInfoCookie);
        organizationId = userInfo.organizationId || "";
      } catch (error) {
        console.error("Error parsing user info:", error);
      }
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "No organization found" },
        { status: 404 },
      );
    }

    // Get the clinical note data from the request
    const {
      consultationId,
      patientId,
      doctorId,
      content,
      chiefComplaints,
      chiefComplaintsDateFrom,
      chiefComplaintsDateTo,
      allergies,
      medicalHistory,
      medicalHistoryDateFrom,
      medicalHistoryDateTo,
      investigationAdvice,
      investigationStatus,
      investigationSpecimen,
      procedure,
      procedureStatus,
      procedureReason,
      procedureOutcome,
      followUp,
      noteType,
      snomedTags,
      snomedDiagnoses,
    } = await req.json();

    // Validate required fields
    if (!consultationId || !patientId || !doctorId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 },
      );
    }

    // Create the clinical note
    const clinicalNote = await db.clinicalNote.create({
      data: {
        consultationId,
        patientId,
        doctorId,
        organizationId,
        content,
        chiefComplaints: chiefComplaints || undefined,
        chiefComplaintsDateFrom: chiefComplaintsDateFrom || undefined,
        chiefComplaintsDateTo: chiefComplaintsDateTo || undefined,
        allergies: allergies || undefined,
        medicalHistory: medicalHistory || undefined,
        medicalHistoryDateFrom: medicalHistoryDateFrom || undefined,
        medicalHistoryDateTo: medicalHistoryDateTo || undefined,
        investigationAdvice: investigationAdvice || undefined,
        investigationStatus: investigationStatus || undefined,
        investigationSpecimen: investigationSpecimen || undefined,
        procedure: procedure || undefined,
        procedureStatus: procedureStatus || undefined,
        procedureReason: procedureReason || undefined,
        procedureOutcome: procedureOutcome || undefined,
        followUp: followUp || undefined,
        noteType: noteType || "general",
        snomedTags: snomedTags
          ? typeof snomedTags === "string"
            ? JSON.parse(snomedTags)
            : snomedTags
          : undefined,
        snomedDiagnoses: snomedDiagnoses
          ? JSON.stringify(snomedDiagnoses)
          : undefined,
      },
      include: {
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
          },
        },
      },
    });

    // Call the new bundle API and store the returned FHIR bundle
    try {
      const {
        generateClinicalNotesPdfBase64,
        getUploadedDocumentsBase64,
        buildOpConsultationBundlePayload,
        callExternalBundleApi,
      } = await import("@/lib/bundle-utils");

      // Fetch full consultation data for bundle generation
      const consultation = await db.consultation.findUnique({
        where: {
          id: consultationId,
          organizationId,
        },
        include: {
          patient: true,
          doctor: {
            include: {
              user: true,
            },
          },
          organization: true,
          branch: {
            include: {
              organization: true,
            },
          },
          clinicalNotes: {
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
          },
          vitals: {
            orderBy: {
              recordedAt: "desc",
            },
            take: 1,
          },
          prescriptions: {
            include: {
              items: true,
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
          },
          AllergyIntolerance: {
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });

      if (consultation) {
        // Generate clinical notes PDF specifically for this bundle
        const generatedPdfBase64 = await generateClinicalNotesPdfBase64(
          consultationId,
        );

        // Get uploaded documents (if any)
        const uploadedDocuments = await getUploadedDocumentsBase64(
          consultationId,
        );

        // Build the bundle payload with form data
        const bundlePayload = buildOpConsultationBundlePayload(
          consultation,
          generatedPdfBase64,
          uploadedDocuments,
          {
            investigationStatus,
            procedureStatus,
            procedureReason,
            procedureOutcome,
            medicalHistory,
          }
        );

        // Call the bundle API
        const bundleApiResponse = await callExternalBundleApi(bundlePayload, 'OPConsultRecord');

        // Store the returned FHIR bundle in the database
        // The external API should return the FHIR bundle directly or in a specific field
        if (bundleApiResponse) {
          const timestamp = Date.now();
          const bundleId = `opconsult-${consultationId}-${timestamp}`;

          // The external API response should contain the FHIR bundle
          // Adjust this based on the actual response structure from your external API
          const fhirBundle = bundleApiResponse.fhirBundle || bundleApiResponse;

          const storedBundle = await db.fhirBundle.create({
            data: {
              bundleId: bundleId,
              consultationId: consultationId,
              patientId: clinicalNote.patientId,
              bundleType: "OPConsultation",
              bundleJson: fhirBundle,
              status: "generated",
              organizationId: organizationId,
            },
          });

          console.log(
            "✅ Successfully called bundle API and stored FHIR bundle",
            {
              consultationId,
              bundleId: storedBundle.bundleId,
              externalApiResponse: typeof bundleApiResponse === 'object' ? 'object' : bundleApiResponse,
              bundleSize: JSON.stringify(fhirBundle).length,
            },
          );
        } else {
          console.warn(
            "⚠️ Bundle API call returned empty response",
            {
              consultationId,
              apiResponse: bundleApiResponse,
            },
          );
        }
      }
    } catch (bundleError) {
      console.error(
        "❌ Failed to call bundle API for OP consultation",
        {
          consultationId,
          error:
            bundleError instanceof Error
              ? bundleError.message
              : String(bundleError),
        },
      );
      // Don't fail the clinical note creation if bundle API call fails
    }

    return NextResponse.json(
      { clinicalNote, message: "Clinical note created successfully" },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error creating clinical note:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
