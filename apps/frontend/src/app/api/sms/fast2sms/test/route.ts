// /**
//  * API endpoint for testing Fast2SMS integration
//  * POST /api/sms/fast2sms/test
//  */

// import { NextRequest, NextResponse } from "next/server";
// import { getCurrentUser } from "@/lib/session";
// import { fast2smsService } from "@/lib/sms/fast2sms";
// import { logger } from "@/lib/logger";

// export const dynamic = "force-dynamic";
// export const maxDuration = 60; // 60 seconds timeout

// export async function POST(req: NextRequest) {
//   try {
//     // Get the current user from cookies
//     const user = await getCurrentUser();
//     if (!user) {
//       return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
//     }

//     // Get the request data
//     const { phone, otp, message, testType = "otp" } = await req.json();

//     // Validate required fields
//     if (!phone) {
//       return NextResponse.json(
//         { error: "Phone number is required" },
//         { status: 400 },
//       );
//     }

//     // Validate phone number format (10 digits)
//     const phoneDigits = phone.replace(/\D/g, "");
//     if (phoneDigits.length !== 10) {
//       return NextResponse.json(
//         { error: "Invalid phone number format. Please enter 10 digits." },
//         { status: 400 },
//       );
//     }

//     let result;

//     if (testType === "otp") {
//       // Test OTP sending
//       if (!otp) {
//         return NextResponse.json(
//           { error: "OTP is required for OTP test" },
//           { status: 400 },
//         );
//       }

//       result = await fast2smsService.sendOtp({
//         to: phone,
//         otp: otp,
//       });
//     } else if (testType === "custom") {
//       // Test custom message sending
//       if (!message) {
//         return NextResponse.json(
//           { error: "Message is required for custom message test" },
//           { status: 400 },
//         );
//       }

//       result = await fast2smsService.sendCustomMessage({
//         to: phone,
//         message: message,
//       });
//     } else {
//       return NextResponse.json(
//         { error: "Invalid test type. Use 'otp' or 'custom'" },
//         { status: 400 },
//       );
//     }

//     if (!result.success) {
//       logger.error("Fast2SMS test failed", {
//         error: result.error,
//         phone: phoneDigits,
//         testType,
//         userId: user.id,
//       });

//       return NextResponse.json(
//         { 
//           success: false,
//           error: result.error || "Failed to send SMS",
//           details: result.details 
//         },
//         { status: 500 },
//       );
//     }

//     logger.info("Fast2SMS test successful", {
//       phone: phoneDigits,
//       testType,
//       messageId: result.messageId,
//       userId: user.id,
//     });

//     return NextResponse.json({
//       success: true,
//       message: "SMS sent successfully",
//       messageId: result.messageId,
//       details: result.details,
//     });
//   } catch (error) {
//     logger.error("Error in Fast2SMS test endpoint", {
//       error: error instanceof Error ? error.message : String(error),
//       stack: error instanceof Error ? error.stack : undefined,
//     });

//     return NextResponse.json(
//       { error: "Internal server error" },
//       { status: 500 },
//     );
//   }
// }

// /**
//  * Get Fast2SMS service configuration and balance
//  * GET /api/sms/fast2sms/test
//  */
// export async function GET(_req: NextRequest) {
//   try {
//     // Get the current user from cookies
//     const user = await getCurrentUser();
//     if (!user) {
//       return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
//     }

//     // Check configuration
//     const config = fast2smsService.checkConfiguration();

//     // Check balance
//     const balanceResult = await fast2smsService.checkBalance();

//     return NextResponse.json({
//       configuration: config,
//       balance: balanceResult.success ? {
//         amount: balanceResult.balance,
//         currency: "INR"
//       } : {
//         error: balanceResult.error,
//         details: balanceResult.details
//       }
//     });
//   } catch (error) {
//     logger.error("Error checking Fast2SMS status", {
//       error: error instanceof Error ? error.message : String(error),
//       stack: error instanceof Error ? error.stack : undefined,
//     });

//     return NextResponse.json(
//       { error: "Internal server error" },
//       { status: 500 },
//     );
//   }
// }
