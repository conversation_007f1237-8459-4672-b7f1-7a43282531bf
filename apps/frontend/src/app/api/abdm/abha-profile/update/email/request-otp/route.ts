/**
 * API endpoint to request OTP for email verification
 * POST /api/abdm/abha-profile/update/email/request-otp
 */

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { profileUpdate } from "@/services/abdm";

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { patientId, email } = await req.json();

    if (!patientId || !email) {
      return NextResponse.json(
        { error: "Patient ID and email are required" },
        { status: 400 },
      );
    }

    // Import and use validation function
    const { validateEmail } = await import("@/lib/validation");

    // Validate email format
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      return NextResponse.json(
        { error: emailValidation.message || "Invalid email format" },
        { status: 400 },
      );
    }

    // Get patient and ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      return NextResponse.json({ error: "Patient not found" }, { status: 404 });
    }

    if (!patient.abhaProfile || !patient.abhaProfile.xToken) {
      return NextResponse.json(
        {
          error:
            "Patient does not have an active ABHA session. Please login to ABHA first.",
        },
        { status: 400 },
      );
    }

    // Check if token is expired
    if (
      patient.abhaProfile.xTokenExpiresAt &&
      new Date() > new Date(patient.abhaProfile.xTokenExpiresAt)
    ) {
      return NextResponse.json(
        { error: "ABHA session has expired. Please login to ABHA again." },
        { status: 401 },
      );
    }

    // Request OTP for email verification
    const result = await profileUpdate.requestEmailUpdateOtp(
      email,
      patient.abhaProfile.xToken,
    );

    // Return the result
    return NextResponse.json({
      txnId: result.txnId,
      message: result.message,
    });
  } catch (error: any) {
    console.error("Error requesting email verification OTP:", error);
    return NextResponse.json(
      { error: error.message || "Failed to request OTP" },
      { status: 500 },
    );
  }
}
