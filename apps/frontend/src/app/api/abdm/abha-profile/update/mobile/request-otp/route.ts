/**
 * API endpoint to request OTP for mobile verification
 * POST /api/abdm/abha-profile/update/mobile/request-otp
 */

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { profileUpdate } from "@/services/abdm";

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { patientId, mobile } = await req.json();

    if (!patientId || !mobile) {
      return NextResponse.json(
        { error: "Patient ID and mobile number are required" },
        { status: 400 },
      );
    }

    // Import and use validation function
    const { validateMobile } = await import("@/lib/validation");

    // Validate mobile format
    const mobileValidation = validateMobile(mobile);
    if (!mobileValidation.isValid) {
      return NextResponse.json(
        {
          error: mobileValidation.message || "Invalid mobile number format",
        },
        { status: 400 },
      );
    }

    // Get patient and ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      return NextResponse.json({ error: "Patient not found" }, { status: 404 });
    }

    if (!patient.abhaProfile || !patient.abhaProfile.xToken) {
      return NextResponse.json(
        {
          error:
            "Patient does not have an active ABHA session. Please login to ABHA first.",
        },
        { status: 400 },
      );
    }

    // Check if token is expired
    if (
      patient.abhaProfile.xTokenExpiresAt &&
      new Date() > new Date(patient.abhaProfile.xTokenExpiresAt)
    ) {
      return NextResponse.json(
        { error: "ABHA session has expired. Please login to ABHA again." },
        { status: 401 },
      );
    }

    // Request OTP for mobile verification
    const result = await profileUpdate.requestMobileUpdateOtp(
      mobile,
      patient.abhaProfile.xToken,
    );

    // Return the result
    return NextResponse.json({
      txnId: result.txnId,
      message: result.message,
    });
  } catch (error: any) {
    console.error("Error requesting mobile verification OTP:", error);
    return NextResponse.json(
      { error: error.message || "Failed to request OTP" },
      { status: 500 },
    );
  }
}
