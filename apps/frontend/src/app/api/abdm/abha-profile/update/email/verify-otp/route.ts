/**
 * API endpoint to verify <PERSON><PERSON> for email update
 * POST /api/abdm/abha-profile/update/email/verify-otp
 */

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { profileUpdate } from "@/services/abdm";

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { patientId, otp, txnId } = await req.json();

    if (!patientId || !otp || !txnId) {
      return NextResponse.json(
        { error: "Patient ID, OTP, and transaction ID are required" },
        { status: 400 },
      );
    }

    // Get patient and ABHA profile
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: { abhaProfile: true },
    });

    if (!patient) {
      return NextResponse.json({ error: "Patient not found" }, { status: 404 });
    }

    if (!patient.abhaProfile || !patient.abhaProfile.xToken) {
      return NextResponse.json(
        {
          error:
            "Patient does not have an active ABHA session. Please login to ABHA first.",
        },
        { status: 400 },
      );
    }

    // Check if token is expired
    if (
      patient.abhaProfile.xTokenExpiresAt &&
      new Date() > new Date(patient.abhaProfile.xTokenExpiresAt)
    ) {
      return NextResponse.json(
        { error: "ABHA session has expired. Please login to ABHA again." },
        { status: 401 },
      );
    }

    // Verify OTP for email update
    const result = await profileUpdate.verifyEmailUpdateOtp(
      otp,
      txnId,
      patient.abhaProfile.xToken,
    );

    // Return the result
    return NextResponse.json({
      verified: true,
      message: result.message || "Email updated successfully",
    });
  } catch (error: any) {
    console.error("Error verifying email update OTP:", error);
    return NextResponse.json(
      { error: error.message || "Failed to verify OTP" },
      { status: 500 },
    );
  }
}
