export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getAccessToken } from "@/services/abdm/utils/auth";
import { abdmFetch } from "@/lib/abdm-fetch";

/**
 * Auto-encrypt and upload DiagnosticReport bundles
 * POST /api/abdm/auto-upload/diagnostic-report
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      careContextReference,
      transactionId,
      dataPushUrl,
      keyMaterial,
      requesterNonce,
      requesterPublicKey,
    } = body;

    console.log("🚀 Starting DiagnosticReport bundle auto-upload", {
      careContextReference,
      transactionId,
      dataPushUrl: dataPushUrl?.substring(0, 50) + "...",
    });

    // Fetch the most recent DiagnosticReport bundle for this consultation
    const fhirBundle = await db.fhirBundle.findFirst({
      where: {
        consultationId: careContextReference,
        bundleType: "DiagnosticReport",
      },
      select: {
        bundleId: true,
        bundleType: true,
        bundleJson: true,
        consultationId: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!fhirBundle) {
      console.warn("⚠️ No DiagnosticReport bundle found", {
        careContextReference,
      });
      return NextResponse.json({
        success: false,
        message: "No DiagnosticReport bundle found",
        careContextReference,
        bundleType: "DiagnosticReport",
      });
    }

    console.log("📦 Found DiagnosticReport bundle", {
      bundleId: fhirBundle.bundleId,
      careContextReference,
    });

    // Get sender credentials from environment variables
    const senderNonce = process.env.NEXT_PUBLIC_SENDER_NONCE;
    const senderPrivateKey = process.env.NEXT_PUBLIC_SENDER_PRIVATE_KEY;
    const senderPublicKey = process.env.NEXT_PUBLIC_SENDER_PUBLIC_KEY;

    if (!senderNonce || !senderPrivateKey || !senderPublicKey) {
      console.error("❌ Missing sender credentials");
      return NextResponse.json(
        { error: "Missing sender credentials" },
        { status: 500 },
      );
    }

    // Step 1: Encrypt the FHIR bundle
    const bundleString = JSON.stringify(fhirBundle.bundleJson).replace(
      /\n/g,
      " ",
    );
    const fideliusUrl = `${process.env.NEXT_PUBLIC_FIDELIUS_API_URL || "https://api.healthcare.flinkk.io"}/fidelius-api/encrypt`;

    console.log("🔐 Encrypting DiagnosticReport bundle", {
      bundleId: fhirBundle.bundleId,
      bundleSize: bundleString.length,
    });

    const encryptionResponse = await fetch(fideliusUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-API-Key": process.env.NEXT_PUBLIC_FIDELIUS_API_KEY || "",
      },
      body: JSON.stringify({
        string_to_encrypt: bundleString,
        sender_nonce: senderNonce,
        requester_nonce: requesterNonce,
        sender_private_key: senderPrivateKey,
        requester_public_key: requesterPublicKey,
      }),
    });

    if (!encryptionResponse.ok) {
      throw new Error(`Encryption failed: ${encryptionResponse.status}`);
    }

    const encryptionResult = await encryptionResponse.json();
    const encryptedData = encryptionResult.data?.encryptedData;

    if (!encryptedData) {
      throw new Error("No encrypted data received");
    }

    console.log("✅ DiagnosticReport bundle encrypted successfully", {
      bundleId: fhirBundle.bundleId,
    });

    // Step 2: Upload to ABDM
    const accessToken = await getAccessToken();

    const requestBody = {
      pageNumber: 0,
      pageCount: 1,
      transactionId,
      entries: [
        {
          content: encryptedData,
          media: "application/fhir+json",
          checksum: "string",
          careContextReference,
        },
      ],
      keyMaterial: {
        cryptoAlg: keyMaterial.cryptoAlg,
        curve: keyMaterial.curve,
        dhPublicKey: {
          expiry: keyMaterial.dhPublicKey.expiry,
          parameters: keyMaterial.dhPublicKey.parameters,
          keyValue: senderPublicKey,
        },
        nonce: senderNonce,
      },
    };

    console.log("📤 Uploading DiagnosticReport bundle to ABDM", {
      bundleId: fhirBundle.bundleId,
      transactionId,
    });

    await abdmFetch(dataPushUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CM-ID": process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(requestBody),
    });

    console.log("✅ DiagnosticReport bundle uploaded successfully", {
      bundleId: fhirBundle.bundleId,
      transactionId,
    });

    // Update bundle status
    await db.fhirBundle.update({
      where: { bundleId: fhirBundle.bundleId },
      data: {
        status: "uploaded",
        transactionId,
      },
    });

    return NextResponse.json({
      success: true,
      bundleId: fhirBundle.bundleId,
      bundleType: "DiagnosticReport",
      careContextReference,
      transactionId,
    });
  } catch (error) {
    console.error("❌ DiagnosticReport auto-upload error", error);

    return NextResponse.json(
      {
        success: false,
        bundleType: "DiagnosticReport",
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
