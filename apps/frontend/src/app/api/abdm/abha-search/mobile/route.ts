/**
 * API endpoint for searching ABHA using mobile number
 */

import { NextRequest, NextResponse } from "next/server";
import { searchAbhaByMobile } from "@/services/abdm/abha-search/mobile/search";

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { mobile } = body;

    // Validate mobile number
    if (!mobile) {
      return NextResponse.json(
        { error: "Mobile number is required" },
        { status: 400 },
      );
    }

    // Call the service function
    const result = await searchAbhaByMobile(mobile);

    // Check if result is valid and contains ABHA data
    if (Array.isArray(result) && result.length > 0) {
      const firstResult = result[0];
      if (firstResult.ABHA && firstResult.ABHA.length > 0) {
        // ABHA found - return the result with proper structure
        return NextResponse.json({
          success: true,
          data: result,
          message: "ABHA information found successfully",
        });
      }
    }

    // If we get here, no ABHA was found
    return NextResponse.json(
      { error: "No ABHA information found for this mobile number" },
      { status: 404 },
    );
  } catch (error) {
    console.error("Error searching ABHA by mobile:", error);

    // Handle specific error types
    if (error instanceof Error) {
      // Check for specific ABDM error messages
      if (error.message.includes("User not found")) {
        return NextResponse.json(
          { error: "No ABHA information found for this mobile number" },
          { status: 404 },
        );
      }
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Generic error
    return NextResponse.json(
      { error: "Failed to search ABHA by mobile" },
      { status: 500 },
    );
  }
}
