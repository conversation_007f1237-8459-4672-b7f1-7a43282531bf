export const maxDuration = 299;

import { NextRequest } from "next/server";
import { abhaCard } from "@/services/abdm";
import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Stream ABHA card directly to the browser
 * GET /api/abdm/abha-card/download-stream?patientId=XXX
 *
 * The ABHA number and X-token are retrieved from the database based on the patientId
 */
export async function GET(req: NextRequest) {
  try {
    // Get the patient ID from the URL parameters
    const url = new URL(req.url);
    const patientId = url.searchParams?.get("patientId");

    // Validate required fields
    if (!patientId) {
      return new Response("Patient ID is required", { status: 400 });
    }

    // Initialize variables
    let abhaNumber: string | null = null;
    let xToken: string | null = null;

    // Get the ABHA number and xToken from the database
    try {
      const abhaProfile = await db.abhaProfile.findUnique({
        where: {
          patientId,
        },
      });

      if (!abhaProfile) {
        return new Response(
          "No ABHA profile found. Please create or link an ABHA account first.",
          { status: 404 },
        );
      }

      // Get ABHA number from the profile
      if (abhaProfile.abhaNumber) {
        abhaNumber = abhaProfile.abhaNumber;
      }

      // Get xToken from the profile
      if (abhaProfile.xToken) {
        // Check if the X-token is expired
        if (
          abhaProfile.xTokenExpiresAt &&
          new Date(abhaProfile.xTokenExpiresAt) < new Date()
        ) {
          // Update the database to mark the token as expired
          await db.abhaProfile.update({
            where: { patientId },
            data: { xTokenExpiresAt: new Date() },
          });

          return new Response(
            JSON.stringify({
              error: "Your ABHA session has expired. Please login again.",
              code: "TOKEN_EXPIRED",
            }),
            {
              status: 401,
              headers: {
                "Content-Type": "application/json",
              },
            },
          );
        }

        xToken = abhaProfile.xToken;
      }
    } catch (dbError) {
      return new Response("Error retrieving ABHA profile. Please try again.", {
        status: 500,
      });
    }

    // For ABHA card download, we need a valid X-token from ABHA login
    if (!xToken) {
      return new Response(
        "X-token is required for ABHA card download. Please login to ABHA first.",
        { status: 401 },
      );
    }

    // Call ABDM service to download ABHA card
    try {
      // Get the ABHA card HTML content
      const htmlContent = await abhaCard.downloadAbhaCard(xToken);

      // Calculate request duration for monitoring (removed)

      // Determine the content type and prepare the response
      let contentType = "text/html";
      let fileName = `ABHA_Card_${abhaNumber}.html`;
      let cardData = "";

      try {
        if (htmlContent.includes('<img src="data:image/')) {
          // Extract the image data URL
          const dataUrlMatch = htmlContent.match(/src="([^"]+)"/);
          if (dataUrlMatch && dataUrlMatch[1]) {
            cardData = dataUrlMatch[1];
            // Extract the content type
            const contentTypeMatch = cardData.match(/data:([^;]+);base64/);
            if (contentTypeMatch && contentTypeMatch[1]) {
              contentType = contentTypeMatch[1];
              fileName = `ABHA_Card_${abhaNumber}.${
                contentType.split("/")[1] || "png"
              }`;
            }
          }
        } else if (htmlContent.includes('<embed src="data:application/pdf')) {
          // Extract the PDF data URL
          const dataUrlMatch = htmlContent.match(/src="([^"]+)"/);
          if (dataUrlMatch && dataUrlMatch[1]) {
            cardData = dataUrlMatch[1];
            contentType = "application/pdf";
            fileName = `ABHA_Card_${abhaNumber}.pdf`;
          }
        } else {
          // For HTML content, create a data URL
          cardData = `data:text/html;base64,${Buffer.from(htmlContent).toString(
            "base64",
          )}`;
        }
      } catch (error) {
        console.error("Error processing ABHA card content:", error);
        // Fallback to raw HTML
        cardData = `data:text/html;base64,${Buffer.from(htmlContent).toString(
          "base64",
        )}`;
      }

      // If no card data was extracted, use the raw HTML
      if (!cardData) {
        cardData = `data:text/html;base64,${Buffer.from(htmlContent).toString(
          "base64",
        )}`;
      }

      // Return the data URL and content type for the frontend to handle
      return new Response(
        JSON.stringify({
          cardData,
          contentType,
          fileName,
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-cache, no-store, must-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        },
      );
    } catch (error) {
      // Provide a user-friendly error message
      let errorMessage = "Failed to download ABHA card";
      let statusCode = 500;

      if (error instanceof Error) {
        errorMessage = error.message;

        // Map common errors to appropriate status codes
        if (
          errorMessage.includes("Invalid") ||
          errorMessage.includes("format")
        ) {
          statusCode = 400;
        } else if (
          errorMessage.includes("Authentication") ||
          errorMessage.includes("token")
        ) {
          statusCode = 401;
        } else if (
          errorMessage.includes("timeout") ||
          errorMessage.includes("timed out")
        ) {
          statusCode = 504;
        }
      }

      return new Response(errorMessage, { status: statusCode });
    }
  } catch (error) {
    // Provide a generic error message
    return new Response(
      "An unexpected error occurred while downloading ABHA card",
      { status: 500 },
    );
  }
}
