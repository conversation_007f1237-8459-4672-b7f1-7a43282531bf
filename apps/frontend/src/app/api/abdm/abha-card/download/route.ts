export const maxDuration = 299;

import { NextRequest } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaCard } from "@/services/abdm";

import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Download ABHA card
 * GET /api/abdm/abha-card/download
 */
export async function GET(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Get the patientId from the URL parameters
    const url = new URL(req.url);
    const patientId = url.searchParams?.get("patientId");

    // Validate patientId
    if (!patientId) {
      return new Response("Patient ID is required", { status: 400 });
    }

    // Initialize variables
    let abhaNumber: string | null = null;
    let xToken: string | null = null;

    // Get the ABHA number and xToken from the database
    try {
      const abhaProfile = await db.abhaProfile.findUnique({
        where: {
          patientId,
        },
      });

      if (!abhaProfile) {
        return new Response(
          "No ABHA profile found. Please create or link an ABHA account first.",
          { status: 404 },
        );
      }

      // Get ABHA number from the profile
      if (abhaProfile.abhaNumber) {
        abhaNumber = abhaProfile.abhaNumber;
      }

      // Get xToken from the profile
      if (abhaProfile.xToken) {
        // Check if the X-token is expired
        if (
          abhaProfile.xTokenExpiresAt &&
          new Date(abhaProfile.xTokenExpiresAt) < new Date()
        ) {
          // Update the database to mark the token as expired
          await db.abhaProfile.update({
            where: { patientId },
            data: { xTokenExpiresAt: new Date() },
          });

          return new Response(
            JSON.stringify({
              error: "Your ABHA session has expired. Please login again.",
              code: "TOKEN_EXPIRED",
            }),
            {
              status: 401,
              headers: {
                "Content-Type": "application/json",
              },
            },
          );
        }

        xToken = abhaProfile.xToken;
      }
    } catch (dbError) {
      return new Response("Error retrieving ABHA profile. Please try again.", {
        status: 500,
      });
    }
    // Validate that we have the required data
    if (!abhaNumber) {
      return new Response(
        "ABHA number is required and could not be determined",
        { status: 400 },
      );
    }

    if (!xToken) {
      return new Response(
        "X-token is required for ABHA card viewing. Please login to ABHA first.",
        { status: 401 },
      );
    }

    // Call ABDM service to download ABHA card
    try {
      // Get the ABHA card HTML content
      const htmlContent = await abhaCard.downloadAbhaCard(xToken);

      // Always return as HTML to ensure proper rendering in iframe
      return new Response(htmlContent, {
        status: 200,
        headers: {
          "Content-Type": "text/html",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      });
    } catch (error) {
      // Provide a user-friendly error message
      let errorMessage = "Failed to load ABHA card";
      let statusCode = 500;

      if (error instanceof Error) {
        errorMessage = error.message;

        // Map common errors to appropriate status codes
        if (
          errorMessage.includes("Invalid") ||
          errorMessage.includes("format")
        ) {
          statusCode = 400;
        } else if (
          errorMessage.includes("Authentication") ||
          errorMessage.includes("token")
        ) {
          statusCode = 401;
        } else if (
          errorMessage.includes("timeout") ||
          errorMessage.includes("timed out")
        ) {
          statusCode = 504;
        }
      }

      return new Response(errorMessage, { status: statusCode });
    }
  } catch (error) {
    // Provide a generic error message
    return new Response(
      "An unexpected error occurred while loading ABHA card",
      { status: 500 },
    );
  }
}
