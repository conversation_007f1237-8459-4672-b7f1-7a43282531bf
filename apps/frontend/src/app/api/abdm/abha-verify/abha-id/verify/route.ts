export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaVerify, abhaProfile } from "@/services/abdm";

import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Verify OTP for ABHA ID verification
 * POST /api/abdm/abha-verify/abha-id/verify
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the OTP, transaction ID, patient ID, standalone flag, isMobileVerification flag, and otpMethod from the request
    const {
      otp,
      txnId,
      patientId,
      standalone = false,
      isMobileVerification = false,
      otpMethod = "aadhaar-linked",
    } = await req.json();

    // Validate required fields
    if (!otp || !txnId) {
      return NextResponse.json(
        { error: "OTP and transaction ID are required" },
        { status: 400 },
      );
    }

    // Validate OTP method
    if (!["aadhaar-linked", "abha-linked"].includes(otpMethod)) {
      return NextResponse.json(
        {
          error:
            "Invalid OTP method. Must be 'aadhaar-linked' or 'abha-linked'",
        },
        { status: 400 },
      );
    }

    // Call ABDM service to verify OTP
    const result = await abhaVerify.abhaId.verifyAbhaIdOtp(
      otp,
      txnId,
      otpMethod,
    );

    // Log the response for debugging
    console.log("ABDM verification response:", JSON.stringify(result, null, 2));

    // Extract basic ABHA details from the verification response
    const abhaNumber = result?.accounts?.[0]?.ABHANumber;
    const abhaAddress = result?.accounts?.[0]?.preferredAbhaAddress;
    const abhaName = result?.accounts?.[0]?.name;
    const abhaGender = result?.accounts?.[0]?.gender;
    const abhaYearOfBirth = result?.accounts?.[0]?.yearOfBirth;
    const abhaDob = result?.accounts?.[0]?.dob;
    const abhaPhone = result?.accounts?.[0]?.mobile;
    const abhaEmail = result?.accounts?.[0]?.email;

    // The token can be in either result.token or result.xToken depending on the ABDM API version
    const xToken = result.token || result.xToken;

    // Try to get more detailed profile information using the token
    let profileDetails = null;

    if (xToken) {
      try {
        // Call the ABHA profile service to get complete details
        console.log("Fetching ABHA profile details with token:", xToken);
        profileDetails = await abhaProfile.getAbhaProfileDetails(xToken);
        console.log(
          "ABHA profile details:",
          JSON.stringify(profileDetails, null, 2),
        );
      } catch (profileError) {
        console.error("Error fetching ABHA profile details:", profileError);
        // Continue with the flow even if profile details fetch fails
      }
    }

    // Extract patient details from profile API response if available, otherwise use verification response
    const fullName = profileDetails?.name || abhaName || "";
    const gender = profileDetails?.gender || abhaGender || "";
    const yearOfBirth = profileDetails?.yearOfBirth || abhaYearOfBirth || "";

    // Add one to dayOfBirth to fix the off-by-one issue
    let dayOfBirthValue = profileDetails?.dayOfBirth || "";
    console.log("Original dayOfBirth value:", dayOfBirthValue);

    if (dayOfBirthValue && !isNaN(Number(dayOfBirthValue))) {
      dayOfBirthValue = String(Number(dayOfBirthValue) + 1);
      console.log("Adjusted dayOfBirth value:", dayOfBirthValue);
    }
    const dayOfBirth = dayOfBirthValue;

    const monthOfBirth = profileDetails?.monthOfBirth || "";
    const dob = profileDetails?.dateOfBirth || abhaDob || "";
    const phone = profileDetails?.mobile || abhaPhone || "";
    const email = profileDetails?.email || abhaEmail || "";
    const address = profileDetails?.address || "";
    const city = profileDetails?.townName || profileDetails?.districtName || "";
    const state = profileDetails?.stateName || "";
    const pincode = profileDetails?.pinCode || "";

    // Check if we're in standalone mode or if patientId is not provided
    const isStandalone = standalone === true;
    if (isStandalone || !patientId) {
      // If no patientId is provided, we'll just return the ABHA details
      // This allows the frontend to create a patient with these details
      return NextResponse.json({
        verified: true,
        message: isMobileVerification
          ? "Mobile number linked to ABHA verified successfully"
          : result.message || "ABHA ID verified successfully",
        abhaNumber,
        abhaAddress,
        healthIdNumber: abhaNumber,
        name: fullName,
        gender,
        yearOfBirth,
        dayOfBirth,
        monthOfBirth,
        dob,
        phone,
        email,
        address,
        city,
        state,
        pincode,
        token: xToken,
        authResult: result?.authResult || "success",
      });
    }

    try {
      // Get the patient to get the organizationId
      const patient = await db.patient.findUnique({
        where: { id: patientId },
      });

      if (!patient) {
        // If patient is not found, we'll just return the ABHA details
        // This allows the frontend to create a patient with these details
        return NextResponse.json({
          verified: true,
          message: isMobileVerification
            ? "Mobile number linked to ABHA verified successfully"
            : result.message || "ABHA ID verified successfully",
          abhaNumber,
          abhaAddress,
          healthIdNumber: abhaNumber,
          name: fullName,
          gender,
          yearOfBirth,
          dayOfBirth,
          monthOfBirth,
          dob,
          phone,
          email,
          address,
          city,
          state,
          pincode,
          token: xToken,
          authResult: result?.authResult || "success",
        });
      }

      // Calculate token expiration time (2 hours from now)
      const xTokenExpiresAt = new Date();
      xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

      // Log the token for debugging
      console.log("Token to be saved:", xToken);

      await db.abhaProfile.upsert({
        where: { patientId },
        update: {
          abhaNumber,
          abhaAddress,
          healthIdNumber: abhaNumber,
          xToken,
          xTokenExpiresAt: xTokenExpiresAt,
          abhaStatus: result?.authResult || "success", // Default to success if not provided
          kycVerified: profileDetails?.kycVerified || false,
        },
        create: {
          patientId,
          organizationId: patient.organizationId,
          abhaNumber,
          abhaAddress,
          healthIdNumber: abhaNumber,
          xToken,
          xTokenExpiresAt: xTokenExpiresAt,
          abhaStatus: result?.authResult || "success", // Default to success if not provided
          kycVerified: profileDetails?.kycVerified || false,
        },
      });

      // Trigger ABHA profile synchronization asynchronously
      try {
        // Use fetch to make a non-blocking call to the sync API
        fetch(
          `${req.nextUrl.origin}/api/patients/${patientId}/abha-profile-patient-sync`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              // Pass the authentication cookie to maintain the session
              Cookie: req.headers.get("cookie") || "",
            },
          },
        ).catch((syncError) => {
          // Log any errors but don't block the main flow
          console.error("Error triggering ABHA profile sync:", syncError);
        });
      } catch (syncError) {
        // Log any errors but don't block the main flow
        console.error("Error initiating ABHA profile sync:", syncError);
      }

      // Automatically generate link token after ABHA ID verification
      try {
        console.log(
          "Automatically generating link token for patient:",
          patientId,
        );

        // Get the patient's primary branch
        const primaryBranchId = patient.primaryBranchId;

        if (primaryBranchId && abhaNumber && abhaAddress) {
          // Check if the branch has a HIP ID
          const branch = await db.branch.findUnique({
            where: { id: primaryBranchId },
            select: { hipId: true, name: true, organizationId: true },
          });

          if (branch?.hipId) {
            // Check if a link token already exists for this patient-branch combination
            const existingLinkToken = await db.abhaLinkToken.findFirst({
              where: {
                patientId,
                branchId: primaryBranchId,
                status: "active",
                linkTokenExpiry: {
                  gt: new Date(),
                },
              },
            });

            if (!existingLinkToken) {
              console.log(
                "No existing link token found for this branch. Generating new token...",
              );

              // Import the link token generation service
              const { generateLinkToken } = await import(
                "@/services/abdm/care-context/link-token"
              );

              // Generate link token asynchronously for this specific branch
              const linkTokenResult = await generateLinkToken(
                patientId,
                primaryBranchId,
              );
              console.log("Link token generation result:", linkTokenResult);
            } else {
              console.log(
                "Valid link token already exists for this branch:",
                existingLinkToken.id,
              );
            }
          } else {
            console.log(
              "Branch does not have HIP ID, skipping link token generation. Branch:",
              branch?.name || "Unknown",
            );
          }
        } else {
          console.log("Missing required data for link token generation:", {
            primaryBranchId,
            abhaNumber: !!abhaNumber,
            abhaAddress: !!abhaAddress,
          });
        }
      } catch (linkTokenError) {
        // Log error but don't fail the main ABHA verification flow
        console.error(
          "Error generating link token after ABHA ID verification:",
          linkTokenError,
        );
      }
    } catch (error) {
      console.error("Error updating ABHA profile:", error);
      // Continue with the flow even if there's an error updating the profile
      // This ensures the user gets a response even if the database update fails
    }

    // Return the verification result with appropriate message based on verification type
    return NextResponse.json({
      verified: true,
      message: isMobileVerification
        ? "Mobile number linked to ABHA verified successfully"
        : result.message || "ABHA ID verified successfully",
      abhaNumber,
      abhaAddress,
      healthIdNumber: abhaNumber,
      // Include additional profile details if available
      name: fullName,
      gender,
      yearOfBirth,
      dayOfBirth,
      monthOfBirth,
      dob,
      phone,
      email,
      address,
      city,
      state,
      pincode,
      token: xToken,
      authResult: result?.authResult || "success",
      profileDetails: profileDetails || null, // Include the full profile details for debugging
    });
  } catch (error) {
    console.error("Error in ABHA ID verification:", error);

    // Provide more detailed error information
    let errorMessage = "Failed to verify OTP";
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        name: error.name,
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      };
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
      },
      { status: 500 },
    );
  }
}
