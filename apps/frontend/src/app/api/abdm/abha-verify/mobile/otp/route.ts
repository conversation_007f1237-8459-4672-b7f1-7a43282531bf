export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaVerify } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Request OTP for Mobile verification
 * POST /api/abdm/abha-verify/mobile/otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the mobile number and captcha token from the request
    const { mobile, captchaToken } = await req.json();

    // Validate required fields
    if (!mobile) {
      return NextResponse.json(
        { error: "Mobile number is required" },
        { status: 400 },
      );
    }

    // Verify captcha token if provided
    if (captchaToken) {
      try {
        const captchaResponse = await fetch(
          `${req.nextUrl.origin}/api/captcha/verify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ token: captchaToken }),
          },
        );

        const captchaData = await captchaResponse.json();

        if (!captchaResponse.ok || !captchaData.success) {
          return NextResponse.json(
            {
              error: "Captcha verification failed",
              details: captchaData.error || "Invalid captcha",
            },
            { status: 400 },
          );
        }

        console.log("Captcha verification successful:", captchaData);
      } catch (captchaError) {
        console.error("Error verifying captcha:", captchaError);
        return NextResponse.json(
          { error: "Failed to verify captcha" },
          { status: 500 },
        );
      }
    } else {
      console.log("No captcha token provided, skipping verification");
    }

    // Call ABDM service to generate OTP
    const result = await abhaVerify.mobile.requestMobileOtp(mobile);

    console.dir({ result }, { depth: null });

    // Return the transaction ID and message
    return NextResponse.json({
      message: result.message || "OTP sent successfully",
      txnId: result.txnId || "", // Provide a default empty string if txnId is not present
    });
  } catch (error) {
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to generate OTP",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
