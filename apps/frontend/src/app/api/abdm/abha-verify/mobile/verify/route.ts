export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaVerify } from "@/services/abdm";

import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Verify OTP for Mobile verification
 * POST /api/abdm/abha-verify/mobile/verify
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the OTP, transaction ID, patient ID, mobile, and standalone flag from the request
    const requestData = await req.json();

    const { otp, txnId, patientId, mobile, standalone } = requestData;

    // Check if we're in standalone mode
    const isStandalone = standalone === true;

    // Validate required fields
    if (!otp || !txnId) {
      console.log("Validation failed - missing OTP or txnId:", { otp, txnId });
      return NextResponse.json(
        { error: "OTP and transaction ID are required" },
        { status: 400 },
      );
    }

    // Call ABDM service to verify OTP
    const result = await abhaVerify.mobile.verifyMobileOtp(otp, txnId);

    // Extract basic ABHA details from the verification response
    const abhaNumber = result?.accounts?.[0]?.ABHANumber;
    const abhaAddress = result?.accounts?.[0]?.preferredAbhaAddress;
    const healthIdNumber = result?.accounts?.[0]?.ABHANumber;
    const xToken = result.token;
    const abhaStatus = result?.authResult;

    console.log("ABDM verification response:", JSON.stringify(result, null, 2));
    console.log("Token from verification response:", xToken);

    // Try to get more detailed profile information using the token
    let profileDetails = null;

    // Skip profile API call if token is missing
    if (!xToken) {
      console.error(
        "Token is missing in the verification response, skipping profile API call",
      );
    } else {
      try {
        // Call the profile API to get complete details
        console.log("Calling profile API with token:", xToken);
        console.log(
          "API URL:",
          `${req.nextUrl.origin}/api/abdm/abha-profile/get-details`,
        );

        const profileResponse = await fetch(
          `${req.nextUrl.origin}/api/abdm/abha-profile/get-details`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ token: xToken }),
          },
        );

        console.log("Profile API response status:", profileResponse.status);

        if (profileResponse.ok) {
          profileDetails = await profileResponse.json();
          console.log(
            "Profile details fetched successfully:",
            JSON.stringify(profileDetails, null, 2),
          );
          console.log(
            "Profile details kycVerified field:",
            profileDetails?.kycVerified,
          );
        } else {
          const errorText = await profileResponse.text();
          console.error("Failed to fetch profile details:", errorText);

          // Try to parse the error response
          try {
            const errorJson = JSON.parse(errorText);
            console.error("Error details:", errorJson);
          } catch (parseError) {
            console.error("Could not parse error response as JSON");
          }
        }
      } catch (error) {
        console.error("Error fetching profile details:", error);
      }
    }

    // Extract patient details from profile API response if available, otherwise use verification response
    // If profile API call failed, we'll use the details from the verification response
    console.log("Using verification response data for patient details");

    // Extract PHR addresses from profile response
    let phrAddresses: string[] = [];
    if (profileDetails?.data?.ABHAProfile?.phrAddress) {
      phrAddresses = profileDetails.data.ABHAProfile.phrAddress;
      console.log("Extracted PHR addresses from profile:", phrAddresses);
    }

    // Extract name from verification response
    const abhaName =
      profileDetails?.data?.ABHAProfile?.name ||
      profileDetails?.name ||
      result?.accounts?.[0]?.name ||
      "";

    // Extract gender from verification response
    const abhaGender =
      profileDetails?.data?.ABHAProfile?.gender ||
      profileDetails?.gender ||
      result?.accounts?.[0]?.gender ||
      "";

    // Extract date of birth from verification response
    // The dob format in the verification response is "DD-MM-YYYY"
    const abhaDob =
      profileDetails?.data?.ABHAProfile?.dob ||
      result?.accounts?.[0]?.dob ||
      "";
    console.log("DOB from verification response:", abhaDob);

    // Extract year of birth for backward compatibility
    const abhaYearOfBirth =
      profileDetails?.data?.ABHAProfile?.yearOfBirth ||
      profileDetails?.yearOfBirth ||
      (abhaDob ? abhaDob.split("-")[2] : "") ||
      "";
    const abhaDayOfBirth =
      profileDetails?.data?.ABHAProfile?.dayOfBirth ||
      profileDetails?.dayOfBirth ||
      "";
    console.log("Original dayOfBirth value:", abhaDayOfBirth);
    console.log(
      "Adjusted dayOfBirth value:",
      abhaDayOfBirth ? Number(abhaDayOfBirth) + 1 : "",
    );
    const abhaMonthOfBirth =
      profileDetails?.data?.ABHAProfile?.monthOfBirth ||
      profileDetails?.monthOfBirth ||
      "";

    // Extract phone from profile response or use the mobile from the request
    // If mobile is not provided in the request, try to extract it from the token's mobile claim
    const mobileFromRequest = mobile || result?.mobile || "";
    const abhaPhone =
      profileDetails?.data?.ABHAProfile?.mobile ||
      profileDetails?.mobile ||
      mobileFromRequest ||
      "";

    // Extract email from profile response
    const abhaEmail =
      profileDetails?.data?.ABHAProfile?.email || profileDetails?.email || "";

    // Extract address from profile response
    const abhaAddress1 =
      profileDetails?.data?.ABHAProfile?.address ||
      profileDetails?.address ||
      "";

    // Extract city from profile response
    const abhaCity =
      profileDetails?.data?.ABHAProfile?.townName ||
      profileDetails?.townName ||
      profileDetails?.districtName ||
      "";

    // Extract state from profile response
    const abhaState =
      profileDetails?.data?.ABHAProfile?.stateName ||
      profileDetails?.stateName ||
      "";

    // Extract pincode from profile response
    const abhaPincode =
      profileDetails?.data?.ABHAProfile?.pinCode ||
      profileDetails?.pinCode ||
      "";

    // Use the first PHR address if available, otherwise use the default ABHA address
    let finalAbhaAddress = abhaAddress;
    if (phrAddresses && phrAddresses.length > 0) {
      finalAbhaAddress = phrAddresses[0]; // Use the first PHR address
      console.log("Using first PHR address as ABHA address:", finalAbhaAddress);
    } else {
      console.log(
        "No PHR addresses found, using default ABHA address:",
        finalAbhaAddress,
      );
    }

    // Format gender for our database
    let formattedGender = "other";
    if (abhaGender === "M") formattedGender = "male";
    if (abhaGender === "F") formattedGender = "female";

    // If in standalone mode, we don't need to update a patient record
    if (isStandalone) {
      // Return the verification result with ABHA details
      return NextResponse.json({
        verified: true,
        message: result.message || "Mobile verified successfully",
        abhaNumber,
        abhaAddress: finalAbhaAddress,
        healthIdNumber,
        name: abhaName,
        gender: formattedGender,
        yearOfBirth: abhaYearOfBirth,
        dayOfBirth: abhaDayOfBirth + 1,
        monthOfBirth: abhaMonthOfBirth,
        dob: abhaDob, // Include full date of birth
        phone: abhaPhone,
        email: abhaEmail,
        address: abhaAddress1,
        city: abhaCity,
        state: abhaState,
        pincode: abhaPincode,
        token: xToken,
        phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
      });
    }

    // If not in standalone mode, we need to update the patient record
    // Check that patientId is defined
    if (!patientId) {
      // If no patientId is provided, we'll just return the ABHA details
      // This allows the frontend to create a patient with these details
      return NextResponse.json({
        verified: true,
        message: result.message || "Mobile verified successfully",
        abhaNumber,
        abhaAddress: finalAbhaAddress,
        healthIdNumber,
        name: abhaName,
        gender: formattedGender,
        yearOfBirth: abhaYearOfBirth,
        dayOfBirth: abhaDayOfBirth + 1,
        monthOfBirth: abhaMonthOfBirth,
        dob: abhaDob, // Include full date of birth
        phone: abhaPhone,
        email: abhaEmail,
        address: abhaAddress1,
        city: abhaCity,
        state: abhaState,
        pincode: abhaPincode,
        token: xToken,
        phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
      });
    }

    try {
      // Get the patient to get the organizationId
      const patient = await db.patient.findUnique({
        where: { id: patientId },
      });

      if (!patient) {
        // If patient is not found, we'll just return the ABHA details
        // This allows the frontend to create a patient with these details
        return NextResponse.json({
          verified: true,
          message: result.message || "Mobile verified successfully",
          abhaNumber,
          abhaAddress: finalAbhaAddress,
          healthIdNumber,
          name: abhaName,
          gender: formattedGender,
          yearOfBirth: abhaYearOfBirth,
          dayOfBirth: abhaDayOfBirth + 1,
          monthOfBirth: abhaMonthOfBirth,
          dob: abhaDob, // Include full date of birth
          phone: abhaPhone,
          email: abhaEmail,
          address: abhaAddress1,
          city: abhaCity,
          state: abhaState,
          pincode: abhaPincode,
          token: xToken,
          phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
        });
      }

      // Calculate token expiration time (2 hours from now)
      const xTokenExpiresAt = new Date();
      xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

      // Log KYC verification status for debugging
      console.log(
        "Profile Details Full Object:",
        JSON.stringify(profileDetails, null, 2),
      );
      console.log("KYC Verification Status:", {
        profileDetailsExists: !!profileDetails,
        kycVerifiedValue: profileDetails?.kycVerified,
        kycVerifiedType: typeof profileDetails?.kycVerified,
        finalKycValue: profileDetails?.kycVerified || false,
        profileDetailsKeys: Object.keys(profileDetails || {}),
      });

      // Also check if kycVerified exists with different casing
      if (profileDetails) {
        console.log("Checking for kycVerified variations:", {
          kycVerified: profileDetails.kycVerified,
          kycverified: (profileDetails as any).kycverified,
          KycVerified: (profileDetails as any).KycVerified,
          isKycVerified: (profileDetails as any).isKycVerified,
        });
      }

      await db.abhaProfile.upsert({
        where: { patientId },
        update: {
          abhaNumber,
          abhaAddress: finalAbhaAddress,
          healthIdNumber,
          xToken,
          xTokenExpiresAt,
          abhaStatus,
          kycVerified: profileDetails?.kycVerified || false,
        },
        create: {
          patientId,
          organizationId: patient.organizationId,
          abhaNumber,
          abhaAddress: finalAbhaAddress,
          healthIdNumber,
          xToken,
          xTokenExpiresAt,
          abhaStatus,
          kycVerified: profileDetails?.kycVerified || false,
        },
      });
    } catch (error) {
      console.error("Error updating patient ABHA profile:", error);
      // If there's an error updating the patient, we'll just return the ABHA details
      return NextResponse.json({
        verified: true,
        message: result.message || "Mobile verified successfully",
        abhaNumber,
        abhaAddress: finalAbhaAddress,
        healthIdNumber,
        name: abhaName,
        gender: formattedGender,
        yearOfBirth: abhaYearOfBirth,
        dayOfBirth: abhaDayOfBirth + 1,
        monthOfBirth: abhaMonthOfBirth,
        dob: abhaDob, // Include full date of birth
        phone: abhaPhone,
        email: abhaEmail,
        address: abhaAddress1,
        city: abhaCity,
        state: abhaState,
        pincode: abhaPincode,
        token: xToken,
        phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
      });
    }

    // Trigger ABHA profile synchronization asynchronously
    try {
      // Use fetch to make a non-blocking call to the sync API
      fetch(
        `${req.nextUrl.origin}/api/patients/${patientId}/abha-profile-patient-sync`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // Pass the authentication cookie to maintain the session
            Cookie: req.headers.get("cookie") || "",
          },
        },
      ).catch((syncError) => {
        // Log any errors but don't block the main flow
        console.error("Error triggering ABHA profile sync:", syncError);
      });
    } catch (syncError) {
      // Log any errors but don't block the main flow
      console.error("Error initiating ABHA profile sync:", syncError);
    }

    // Automatically generate link token after mobile verification
    try {
      // Get the patient to access primary branch
      const patientForLinkToken = await db.patient.findUnique({
        where: { id: patientId },
        select: { primaryBranchId: true },
      });

      if (!patientForLinkToken) {
        return NextResponse.json({
          verified: true,
          message: "Mobile verified successfully",
          abhaNumber,
          abhaAddress: finalAbhaAddress,
          healthIdNumber,
          name: abhaName,
          gender: formattedGender,
          yearOfBirth: abhaYearOfBirth,
          dayOfBirth: abhaDayOfBirth + 1,
          monthOfBirth: abhaMonthOfBirth,
          dob: abhaDob,
          phone: abhaPhone,
          email: abhaEmail,
          address: abhaAddress1,
          city: abhaCity,
          state: abhaState,
          pincode: abhaPincode,
          token: xToken,
          phrAddresses: phrAddresses,
        });
      }

      // Get the patient's primary branch
      const primaryBranchId = patientForLinkToken.primaryBranchId;

      if (primaryBranchId && abhaNumber && finalAbhaAddress) {
        // Check if the branch has a HIP ID
        const branch = await db.branch.findUnique({
          where: { id: primaryBranchId },
          select: { hipId: true, name: true, organizationId: true },
        });

        if (branch?.hipId) {
          // Check if a link token already exists for this patient-branch combination
          const existingLinkToken = await db.abhaLinkToken.findFirst({
            where: {
              patientId,
              branchId: primaryBranchId,
              status: "active",
              linkTokenExpiry: {
                gt: new Date(),
              },
            },
          });

          if (!existingLinkToken) {
            const { generateLinkToken } = await import(
              "@/services/abdm/care-context/link-token"
            );

            await generateLinkToken(patientId, primaryBranchId);
          }
        }
      }
    } catch (linkTokenError) {
      console.error("Error generating link token:", linkTokenError);
    }

    // Return the verification result
    return NextResponse.json({
      verified: true,
      message: "Mobile verified successfully",
      abhaNumber,
      abhaAddress: finalAbhaAddress,
      healthIdNumber,
      name: abhaName,
      gender: formattedGender,
      yearOfBirth: abhaYearOfBirth,
      dayOfBirth: abhaDayOfBirth + 1,
      monthOfBirth: abhaMonthOfBirth,
      dob: abhaDob, // Include full date of birth
      phone: abhaPhone,
      email: abhaEmail,
      address: abhaAddress1,
      city: abhaCity,
      state: abhaState,
      pincode: abhaPincode,
      token: xToken,
      phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to verify OTP",
      },
      { status: 500 },
    );
  }
}
