export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaVerify } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Request OTP for Aadhaar verification
 * POST /api/abdm/abha-verify/aadhaar/otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the Aadhaar number from the request
    const { aadhaar } = await req.json();

    // Validate required fields
    if (!aadhaar) {
      return NextResponse.json(
        { error: "Aadhaar number is required" },
        { status: 400 },
      );
    }

    // Call ABDM service to generate OTP
    const result = await abhaVerify.aadhaar.requestAadhaarOtp(aadhaar);

    // Return the transaction ID and message
    return NextResponse.json({
      txnId: result.txnId,
      message: result.message || "OTP sent successfully",
    });
  } catch (error) {
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to generate OTP",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
