export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaVerify } from "@/services/abdm";

import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Verify OTP for Aadhaar verification
 * POST /api/abdm/abha-verify/aadhaar/verify
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the OTP, transaction ID, and patient ID from the request
    const { otp, txnId, patientId, standalone } = await req.json();

    // Validate required fields
    if (!otp || !txnId) {
      return NextResponse.json(
        { error: "OTP and transaction ID are required" },
        { status: 400 },
      );
    }

    // Check if we're in standalone mode
    const isStandalone = standalone === true;

    // Call ABDM service to verify OTP
    const result = await abhaVerify.aadhaar.verifyAadhaarOtp(otp, txnId);

    // Extract basic ABHA details from the verification response
    const abhaNumber = result?.accounts?.[0]?.ABHANumber;
    const abhaAddress = result?.accounts?.[0]?.preferredAbhaAddress;
    const healthIdNumber = result?.accounts?.[0]?.ABHANumber;
    const xToken = result.token || result.xToken; // Handle both token formats
    const abhaStatus = result?.authResult;

    console.log("ABDM verification response:", JSON.stringify(result, null, 2));
    console.log("Token from verification response:", xToken);

    // Try to get more detailed profile information using the token
    let profileDetails = null;

    // Skip profile API call if token is missing
    if (!xToken) {
      console.error(
        "Token is missing in the verification response, skipping profile API call",
      );
    } else {
      try {
        // Call the profile API to get complete details
        console.log("Calling profile API with token:", xToken);
        const profileResponse = await fetch(
          `${req.nextUrl.origin}/api/abdm/abha-profile/get-details`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ token: xToken }),
          },
        );

        if (profileResponse.ok) {
          profileDetails = await profileResponse.json();
          console.log("Profile details fetched successfully:", profileDetails);
        } else {
          console.error(
            "Failed to fetch profile details:",
            await profileResponse.text(),
          );
        }
      } catch (error) {
        console.error("Error fetching profile details:", error);
      }
    }

    // Extract patient details from profile API response if available, otherwise use verification response
    const abhaName = profileDetails?.name || result?.accounts?.[0]?.name || "";
    const abhaGender =
      profileDetails?.gender || result?.accounts?.[0]?.gender || "";
    const abhaYearOfBirth =
      profileDetails?.yearOfBirth ||
      (result?.accounts?.[0]?.dob
        ? result?.accounts?.[0]?.dob.split("-")[2]
        : "") ||
      "";
    const abhaDayOfBirth = profileDetails?.dayOfBirth || "";
    console.log("Original dayOfBirth value:", abhaDayOfBirth);
    const abhaMonthOfBirth = profileDetails?.monthOfBirth || "";
    const abhaPhone = profileDetails?.mobile || "";
    const abhaEmail = profileDetails?.email || "";
    const abhaAddress1 = profileDetails?.address || "";
    const abhaCity =
      profileDetails?.townName || profileDetails?.districtName || "";
    const abhaState = profileDetails?.stateName || "";
    const abhaPincode = profileDetails?.pinCode || "";

    // Split the full name into first and last name (similar to ABHA create API)
    const nameParts = abhaName.split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.length > 1 ? nameParts.slice(1).join(" ") : "";

    console.log("Name splitting:", {
      abhaName,
      firstName,
      lastName,
      nameParts,
    });

    // Format gender for our database
    let formattedGender = "other";
    if (abhaGender === "M") formattedGender = "male";
    if (abhaGender === "F") formattedGender = "female";

    // If in standalone mode, we don't need to update a patient record
    if (isStandalone) {
      // Return the verification result with ABHA details
      return NextResponse.json({
        verified: true,
        message: result.message || "Aadhaar verified successfully",
        abhaNumber,
        abhaAddress,
        healthIdNumber,
        name: abhaName,
        firstName,
        lastName,
        gender: formattedGender,
        yearOfBirth: abhaYearOfBirth,
        dayOfBirth: abhaDayOfBirth ? Number(abhaDayOfBirth) + 1 : "",
        monthOfBirth: abhaMonthOfBirth,
        phone: abhaPhone,
        email: abhaEmail,
        address: abhaAddress1,
        city: abhaCity,
        state: abhaState,
        pincode: abhaPincode,
        token: xToken,
      });
    }

    // If not in standalone mode, we need to update the patient record
    // Check that patientId is defined
    if (!patientId) {
      // If no patientId is provided, we'll just return the ABHA details
      // This allows the frontend to create a patient with these details
      return NextResponse.json({
        verified: true,
        message: result.message || "Aadhaar verified successfully",
        abhaNumber,
        abhaAddress,
        healthIdNumber,
        name: abhaName,
        firstName,
        lastName,
        gender: formattedGender,
        yearOfBirth: abhaYearOfBirth,
        dayOfBirth: abhaDayOfBirth ? Number(abhaDayOfBirth) + 1 : "",
        monthOfBirth: abhaMonthOfBirth,
        phone: abhaPhone,
        email: abhaEmail,
        address: abhaAddress1,
        city: abhaCity,
        state: abhaState,
        pincode: abhaPincode,
        token: xToken,
      });
    }

    try {
      // Get the patient to get the organizationId
      const patient = await db.patient.findUnique({
        where: { id: patientId },
      });

      if (!patient) {
        // If patient is not found, we'll just return the ABHA details
        // This allows the frontend to create a patient with these details
        return NextResponse.json({
          verified: true,
          message: result.message || "Aadhaar verified successfully",
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          name: abhaName,
          firstName,
          lastName,
          gender: formattedGender,
          yearOfBirth: abhaYearOfBirth,
          dayOfBirth: abhaDayOfBirth ? Number(abhaDayOfBirth) + 1 : "",
          monthOfBirth: abhaMonthOfBirth,
          phone: abhaPhone,
          email: abhaEmail,
          address: abhaAddress1,
          city: abhaCity,
          state: abhaState,
          pincode: abhaPincode,
          token: xToken,
        });
      }

      // Calculate token expiration time (2 hours from now)
      const xTokenExpiresAt = new Date();
      xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

      await db.abhaProfile.upsert({
        where: { patientId },
        update: {
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          xToken,
          xTokenExpiresAt,
          abhaStatus,
          kycVerified: profileDetails?.kycVerified || false,
        },
        create: {
          patientId,
          organizationId: patient.organizationId,
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          xToken,
          xTokenExpiresAt,
          abhaStatus,
          kycVerified: profileDetails?.kycVerified || false,
        },
      });
    } catch (error) {
      console.error("Error updating patient ABHA profile:", error);
      // If there's an error updating the patient, we'll just return the ABHA details
      return NextResponse.json({
        verified: true,
        message: result.message || "Aadhaar verified successfully",
        abhaNumber,
        abhaAddress,
        healthIdNumber,
        name: abhaName,
        firstName,
        lastName,
        gender: formattedGender,
        yearOfBirth: abhaYearOfBirth,
        dayOfBirth: abhaDayOfBirth ? Number(abhaDayOfBirth) + 1 : "",
        monthOfBirth: abhaMonthOfBirth,
        phone: abhaPhone,
        email: abhaEmail,
        address: abhaAddress1,
        city: abhaCity,
        state: abhaState,
        pincode: abhaPincode,
        token: xToken,
      });
    }

    // Trigger ABHA profile synchronization asynchronously
    try {
      // Use fetch to make a non-blocking call to the sync API
      fetch(
        `${req.nextUrl.origin}/api/patients/${patientId}/abha-profile-patient-sync`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // Pass the authentication cookie to maintain the session
            Cookie: req.headers.get("cookie") || "",
          },
        },
      ).catch((syncError) => {
        // Log any errors but don't block the main flow
        console.error("Error triggering ABHA profile sync:", syncError);
      });
    } catch (syncError) {
      // Log any errors but don't block the main flow
      console.error("Error initiating ABHA profile sync:", syncError);
    }

    // Automatically generate link token after Aadhaar verification
    try {
      console.log(
        "Automatically generating link token for patient:",
        patientId,
      );

      // Get the patient to access primary branch
      const patientForLinkToken = await db.patient.findUnique({
        where: { id: patientId },
        select: { primaryBranchId: true },
      });

      if (!patientForLinkToken) {
        console.log("Patient not found for link token generation");
        return NextResponse.json({
          verified: true,
          message: result.message || "Aadhaar verified successfully",
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          name: abhaName,
          firstName,
          lastName,
          gender: formattedGender,
          yearOfBirth: abhaYearOfBirth,
          dayOfBirth: abhaDayOfBirth ? Number(abhaDayOfBirth) + 1 : "",
          monthOfBirth: abhaMonthOfBirth,
          phone: abhaPhone,
          email: abhaEmail,
          address: abhaAddress1,
          city: abhaCity,
          state: abhaState,
          pincode: abhaPincode,
          token: xToken,
        });
      }

      // Get the patient's primary branch
      const primaryBranchId = patientForLinkToken.primaryBranchId;

      if (primaryBranchId && abhaNumber && abhaAddress) {
        // Check if the branch has a HIP ID
        const branch = await db.branch.findUnique({
          where: { id: primaryBranchId },
          select: { hipId: true, name: true, organizationId: true },
        });

        if (branch?.hipId) {
          // Check if a link token already exists for this patient-branch combination
          const existingLinkToken = await db.abhaLinkToken.findFirst({
            where: {
              patientId,
              branchId: primaryBranchId,
              status: "active",
              linkTokenExpiry: {
                gt: new Date(),
              },
            },
          });

          if (!existingLinkToken) {
            console.log(
              "No existing link token found for this branch. Generating new token...",
            );

            // Import the link token generation service
            const { generateLinkToken } = await import(
              "@/services/abdm/care-context/link-token"
            );

            // Generate link token asynchronously for this specific branch
            const linkTokenResult = await generateLinkToken(
              patientId,
              primaryBranchId,
            );
            console.log("Link token generation result:", linkTokenResult);
          } else {
            console.log(
              "Valid link token already exists for this branch:",
              existingLinkToken.id,
            );
          }
        } else {
          console.log(
            "Branch does not have HIP ID, skipping link token generation. Branch:",
            branch?.name || "Unknown",
          );
        }
      } else {
        console.log("Missing required data for link token generation:", {
          primaryBranchId,
          abhaNumber: !!abhaNumber,
          abhaAddress: !!abhaAddress,
        });
      }
    } catch (linkTokenError) {
      // Log error but don't fail the main ABHA verification flow
      console.error(
        "Error generating link token after Aadhaar verification:",
        linkTokenError,
      );
    }

    // Return the verification result
    return NextResponse.json({
      verified: true,
      message: result.message || "Aadhaar verified successfully",
      abhaNumber,
      abhaAddress,
      healthIdNumber,
      name: abhaName,
      firstName,
      lastName,
      gender: formattedGender,
      yearOfBirth: abhaYearOfBirth,
      dayOfBirth: abhaDayOfBirth ? Number(abhaDayOfBirth) + 1 : "",
      monthOfBirth: abhaMonthOfBirth,
      phone: abhaPhone,
      email: abhaEmail,
      address: abhaAddress1,
      city: abhaCity,
      state: abhaState,
      pincode: abhaPincode,
      token: xToken,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to verify OTP",
      },
      { status: 500 },
    );
  }
}
