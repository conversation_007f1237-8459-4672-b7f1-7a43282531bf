export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaVerify } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Request OTP for ABHA Address verification
 * POST /api/abdm/abha-verify/abha-address/otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the ABHA Address and OTP method from the request
    const { abhaAddress, otpMethod = "aadhaar-linked" } = await req.json();

    // Validate required fields
    if (!abhaAddress) {
      return NextResponse.json(
        { error: "ABHA Address is required" },
        { status: 400 },
      );
    }

    // Validate OTP method
    if (!["aadhaar-linked", "abha-linked"].includes(otpMethod)) {
      return NextResponse.json(
        {
          error:
            "Invalid OTP method. Must be 'aadhaar-linked' or 'abha-linked'",
        },
        { status: 400 },
      );
    }

    // Call ABDM service to generate OTP
    const result = await abhaVerify.abhaAddress.requestAbhaAddressOtp(
      abhaAddress,
      otpMethod,
    );

    // Return the transaction ID and message
    return NextResponse.json({
      txnId: result.txnId || "",
      message: result.message || "OTP sent successfully",
    });
  } catch (error) {
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to generate OTP",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
