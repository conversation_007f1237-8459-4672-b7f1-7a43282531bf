export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaVerify, abhaProfile } from "@/services/abdm";

import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Verify OTP for ABHA Address verification
 * POST /api/abdm/abha-verify/abha-address/verify
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the OTP, transaction ID, patient ID, standalone flag, and otpMethod from the request
    const {
      otp,
      txnId,
      patientId,
      standalone = false,
      otpMethod = "aadhaar-linked",
    } = await req.json();

    // Validate required fields
    if (!otp || !txnId) {
      return NextResponse.json(
        { error: "OTP and transaction ID are required" },
        { status: 400 },
      );
    }

    // Validate OTP method
    if (!["aadhaar-linked", "abha-linked"].includes(otpMethod)) {
      return NextResponse.json(
        {
          error:
            "Invalid OTP method. Must be 'aadhaar-linked' or 'abha-linked'",
        },
        { status: 400 },
      );
    }

    // Call ABDM service to verify OTP
    const result = await abhaVerify.abhaAddress.verifyAbhaAddressOtp(
      otp,
      txnId,
      otpMethod,
    );

    console.log("ABDM verification response:", JSON.stringify(result, null, 2));

    // Extract basic ABHA details from the verification response
    const abhaNumber = result?.users?.[0]?.abhaNumber;
    const abhaAddress = result?.users?.[0]?.abhaAddress;
    const abhaName = result?.users?.[0]?.name;
    const abhaGender = result?.users?.[0]?.gender;
    const abhaYearOfBirth = result?.users?.[0]?.yearOfBirth;
    const abhaDayOfBirth = result?.users?.[0]?.dayOfBirth;
    const abhaMonthOfBirth = result?.users?.[0]?.monthOfBirth;
    const abhaPhone = result?.users?.[0]?.mobile;
    const abhaEmail = result?.users?.[0]?.email;
    const abhaAddress1 = result?.users?.[0]?.address;
    const abhaCity = result?.users?.[0]?.city;
    const abhaState = result?.users?.[0]?.state;
    const abhaPincode = result?.users?.[0]?.pincode;

    // The token can be in either result.tokens.token or result.token depending on the ABDM API version
    const xToken = result.tokens?.token || result.token;

    // Try to get more detailed profile information using the token
    let profileDetails = null;

    if (xToken) {
      try {
        // Call the ABHA profile service to get complete details using the specific endpoint for ABHA address
        console.log(
          "Fetching ABHA address profile details with token:",
          xToken,
        );
        profileDetails = await abhaProfile.getAbhaAddressProfileDetails(xToken);
        console.log(
          "ABHA address profile details:",
          JSON.stringify(profileDetails, null, 2),
        );
      } catch (profileError) {
        console.error(
          "Error fetching ABHA address profile details:",
          profileError,
        );
        // Continue with the flow even if profile details fetch fails
      }
    }

    // Extract patient details from profile API response if available, otherwise use verification response
    const fullName =
      profileDetails?.fullName || profileDetails?.name || abhaName || "";
    const gender = profileDetails?.gender || abhaGender || "";
    const yearOfBirth = profileDetails?.yearOfBirth || abhaYearOfBirth || "";

    // Extract PHR addresses from profile response
    let phrAddresses: string[] = [];
    if (
      profileDetails?.phrAddress &&
      Array.isArray(profileDetails.phrAddress)
    ) {
      phrAddresses = profileDetails.phrAddress;
      console.log(
        "Extracted PHR addresses from ABHA address profile:",
        phrAddresses,
      );
    }

    // Use the first PHR address if available, otherwise use the default ABHA address
    let finalAbhaAddress = abhaAddress;
    if (phrAddresses && phrAddresses.length > 0) {
      finalAbhaAddress = phrAddresses[0]; // Use the first PHR address
      console.log("Using first PHR address as ABHA address:", finalAbhaAddress);
    } else {
      console.log(
        "No PHR addresses found, using default ABHA address:",
        finalAbhaAddress,
      );
    }

    // Use individual name fields from profile if available, otherwise split the full name
    let firstName = "";
    let lastName = "";

    if (profileDetails?.firstName && profileDetails?.lastName) {
      // Use the individual name fields directly from the profile API
      firstName = profileDetails.firstName;
      lastName = profileDetails.lastName;
      console.log("Using individual name fields from profile:", {
        firstName,
        lastName,
      });
    } else {
      // Fallback to splitting the full name
      const nameParts = fullName.split(" ");
      firstName = nameParts[0] || "";
      lastName = nameParts.length > 1 ? nameParts.slice(1).join(" ") : "";
      console.log("Split name from fullName:", {
        fullName,
        firstName,
        lastName,
        nameParts,
      });
    }

    // Add one to dayOfBirth to fix the off-by-one issue
    let dayOfBirthValue = profileDetails?.dayOfBirth || abhaDayOfBirth || "";
    console.log("Original dayOfBirth value:", dayOfBirthValue);

    if (dayOfBirthValue && !isNaN(Number(dayOfBirthValue))) {
      dayOfBirthValue = String(Number(dayOfBirthValue) + 1);
      console.log("Adjusted dayOfBirth value:", dayOfBirthValue);
    }
    const dayOfBirth = dayOfBirthValue;

    const monthOfBirth = profileDetails?.monthOfBirth || abhaMonthOfBirth || "";
    const dob = profileDetails?.dateOfBirth || "";
    const phone = profileDetails?.mobile || abhaPhone || "";
    const email = profileDetails?.email || abhaEmail || "";
    const address = profileDetails?.address || abhaAddress1 || "";
    const city =
      profileDetails?.townName ||
      profileDetails?.districtName ||
      abhaCity ||
      "";
    const state = profileDetails?.stateName || abhaState || "";
    //SRIDHAR: Fixed Pincode issue
    // const pincode = profileDetails?.pincode || abhaPincode || "";
    const pincode = profileDetails?.pinCode || abhaPincode || "";

    // Check if we're in standalone mode or if patientId is not provided
    const isStandalone = standalone === true;
    if (isStandalone || !patientId) {
      // If in standalone mode or no patientId is provided, we'll just return the ABHA details
      // This allows the frontend to create a patient with these details
      return NextResponse.json({
        verified: true,
        message: "ABHA Address verified successfully",
        abhaNumber,
        abhaAddress: finalAbhaAddress,
        healthIdNumber: abhaNumber,
        name: fullName,
        firstName,
        lastName,
        gender,
        yearOfBirth,
        dayOfBirth,
        monthOfBirth,
        dob,
        phone,
        email,
        address,
        city,
        state,
        pincode,
        token: xToken,
        authResult: result?.users?.[0]?.status || "success",
        profileDetails: profileDetails || null, // Include the full profile details for debugging
        phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
      });
    }

    try {
      // Get the patient to get the organizationId
      const patient = await db.patient.findUnique({
        where: { id: patientId },
      });

      if (!patient) {
        // If patient is not found, we'll just return the ABHA details
        // This allows the frontend to create a patient with these details
        return NextResponse.json({
          verified: true,
          message: "ABHA Address verified successfully",
          abhaNumber,
          abhaAddress: finalAbhaAddress,
          healthIdNumber: abhaNumber,
          name: fullName,
          firstName,
          lastName,
          gender,
          yearOfBirth,
          dayOfBirth,
          monthOfBirth,
          dob,
          phone,
          email,
          address,
          city,
          state,
          pincode,
          token: xToken,
          authResult: result?.users?.[0]?.status || "success",
          profileDetails: profileDetails || null, // Include the full profile details for debugging
          phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
        });
      }

      // Calculate token expiration time (2 hours from now)
      const xTokenExpiresAt = new Date();
      xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

      await db.abhaProfile.upsert({
        where: { patientId },
        update: {
          abhaNumber,
          abhaAddress: finalAbhaAddress,
          healthIdNumber: abhaNumber,
          xToken,
          xTokenExpiresAt: xTokenExpiresAt,
          abhaStatus: result?.users?.[0]?.status || "success", // Default to success if not provided
          kycVerified: profileDetails?.kycVerified || false,
        },
        create: {
          patientId,
          organizationId: patient.organizationId,
          abhaNumber,
          abhaAddress: finalAbhaAddress,
          healthIdNumber: abhaNumber,
          xToken,
          xTokenExpiresAt: xTokenExpiresAt,
          abhaStatus: result?.users?.[0]?.status || "success", // Default to success if not provided
          kycVerified: profileDetails?.kycVerified || false,
        },
      });

      // Trigger ABHA profile synchronization asynchronously
      try {
        // Use fetch to make a non-blocking call to the sync API
        fetch(
          `${req.nextUrl.origin}/api/patients/${patientId}/abha-profile-patient-sync`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              // Pass the authentication cookie to maintain the session
              Cookie: req.headers.get("cookie") || "",
            },
          },
        ).catch((syncError) => {
          // Log any errors but don't block the main flow
          console.error("Error triggering ABHA profile sync:", syncError);
        });
      } catch (syncError) {
        // Log any errors but don't block the main flow
        console.error("Error initiating ABHA profile sync:", syncError);
      }

      // Automatically generate link token after ABHA address verification
      try {
        console.log(
          "Automatically generating link token for patient:",
          patientId,
        );
        debugger;
        // Get the patient's primary branch
        const primaryBranchId = patient.primaryBranchId;

        if (primaryBranchId && abhaNumber && finalAbhaAddress) {
          // Check if the branch has a HIP ID
          const branch = await db.branch.findUnique({
            where: { id: primaryBranchId },
            select: { hipId: true },
          });

          if (branch?.hipId) {
            // Import the link token generation service
            const { generateLinkToken } = await import(
              "@/services/abdm/care-context/link-token"
            );

            // Generate link token asynchronously
            const linkTokenResult = await generateLinkToken(
              patientId,
              primaryBranchId,
            );
            console.log("Link token generation result:", linkTokenResult);
          } else {
            console.log(
              "Branch does not have HIP ID, skipping link token generation",
            );
          }
        } else {
          console.log("Missing required data for link token generation:", {
            primaryBranchId,
            abhaNumber: !!abhaNumber,
            abhaAddress: !!finalAbhaAddress,
          });
        }
      } catch (linkTokenError) {
        // Log error but don't fail the main ABHA verification flow
        console.error(
          "Error generating link token after ABHA address verification:",
          linkTokenError,
        );
      }
    } catch (error) {
      console.error("Error updating ABHA profile:", error);
      // Continue with the flow even if there's an error updating the profile
      // This ensures the user gets a response even if the database update fails
    }

    // Return the verification result with all ABHA details
    return NextResponse.json({
      verified: true,
      message: "ABHA Address verified successfully",
      abhaNumber,
      abhaAddress: finalAbhaAddress,
      healthIdNumber: abhaNumber,
      name: fullName,
      firstName,
      lastName,
      gender,
      yearOfBirth,
      dayOfBirth,
      monthOfBirth,
      dob,
      phone,
      email,
      address,
      city,
      state,
      pincode,
      token: xToken,
      authResult: result?.users?.[0]?.status || "success",
      profileDetails: profileDetails || null, // Include the full profile details for debugging
      phrAddresses: phrAddresses, // Include all PHR addresses for frontend use
    });
  } catch (error) {
    console.error("Error in ABHA Address verification:", error);

    // Provide more detailed error information
    let errorMessage = "Failed to verify OTP";
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        name: error.name,
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      };
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
      },
      { status: 500 },
    );
  }
}
