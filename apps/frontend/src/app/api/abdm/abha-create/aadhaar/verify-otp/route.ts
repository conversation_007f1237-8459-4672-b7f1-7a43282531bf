export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaCreate } from "@/services/abdm";

import { db } from "@/lib/db";
import { getCurrentBranchFromCookies } from "@/lib/branch-cookies";

export const dynamic = "force-dynamic";

/**
 * Verify OTP for ABHA creation using Aadhaar
 * POST /api/abdm/abha-create/aadhaar/verify-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the OTP, mobile, transaction ID, patient ID, ABHA address, userMobile, aadhaarLinkedMobile, and skipAddressCreation flag from the request
    const {
      otp,
      mobile, // This is the Aadhaar-linked mobile for ABDM API
      txnId,
      patientId,
      abhaAddress: selectedAbhaAddress,
      skipAddressCreation = false,
      userMobile, // Communication mobile number entered by user
    } = await req.json();

    console.log("Verify OTP request payload:", {
      otp: "REDACTED",
      mobile,
      txnId,
      patientId,
      selectedAbhaAddress,
      skipAddressCreation,
    });

    // Validate required fields
    if (!otp || !mobile || !txnId) {
      return NextResponse.json(
        {
          error: "OTP, mobile number, and transaction ID are required",
        },
        { status: 400 },
      );
    }

    // Log if a selected ABHA address was provided
    if (selectedAbhaAddress) {
      console.log("Selected ABHA address:", selectedAbhaAddress);
    }

    // Check if we're in standalone mode (no patientId or empty patientId)
    const isStandalone = !patientId || patientId === "";

    // Validate mobile number format
    if (!/^\d{10}$/.test(mobile)) {
      return NextResponse.json(
        { error: "Invalid mobile number format. Please enter 10 digits." },
        { status: 400 },
      );
    }

    // Call ABDM service to verify OTP and create ABHA
    const data = await abhaCreate.aadhaar.verifyEnrollmentOtp(
      otp,
      txnId,
      mobile,
    );

    console.dir({ data }, { depth: null });

    // Extract ABHA details from both ABHAProfile and accounts sections
    // Check for ABHA details in ABHAProfile first, then fall back to accounts
    const abhaNumber =
      data?.ABHAProfile?.ABHANumber ||
      data?.accounts?.[0]?.ABHANumber ||
      data?.users?.[0]?.abhaNumber ||
      data?.abhaNumber;

    // Use the selected ABHA address if provided, otherwise extract from the response
    let abhaAddress = selectedAbhaAddress;

    // If no selected ABHA address was provided, extract from the response
    if (!abhaAddress) {
      // Check all possible field names for ABHA address
      // Based on your logs, preferredAbhaAddress is directly in the response
      abhaAddress =
        data?.preferredAbhaAddress ||
        data?.ABHAProfile?.preferredAbhaAddress ||
        data?.ABHAProfile?.abhaAddress ||
        data?.accounts?.[0]?.preferredAbhaAddress ||
        data?.accounts?.[0]?.abhaAddress ||
        data?.users?.[0]?.abhaAddress ||
        data?.abhaAddress;

      console.log("ABHA address extraction attempt:", {
        "data.preferredAbhaAddress": data?.preferredAbhaAddress,
        "data.ABHAProfile.preferredAbhaAddress":
          data?.ABHAProfile?.preferredAbhaAddress,
        selectedAddress: abhaAddress,
      });

      // If we still don't have an ABHA address, try to extract it from phrAddress fields
      if (!abhaAddress && data?.ABHAProfile?.phrAddress) {
        // Handle both array and string formats for phrAddress
        if (Array.isArray(data.ABHAProfile.phrAddress)) {
          abhaAddress = data.ABHAProfile.phrAddress[0]; // Use the first PHR address
        } else if (typeof data.ABHAProfile.phrAddress === "string") {
          abhaAddress = data.ABHAProfile.phrAddress;
        }
      }

      // If we still don't have an ABHA address, try to extract it from accounts phrAddress
      if (!abhaAddress && data?.accounts?.[0]?.phrAddress) {
        if (Array.isArray(data.accounts[0].phrAddress)) {
          abhaAddress = data.accounts[0].phrAddress[0];
        } else if (typeof data.accounts[0].phrAddress === "string") {
          abhaAddress = data.accounts[0].phrAddress;
        }
      }

      // If we still don't have an ABHA address, try to extract it from the healthId field
      if (!abhaAddress && data?.accounts?.[0]?.healthId) {
        abhaAddress = data.accounts[0].healthId;
      }

      // If we still don't have an ABHA address, try to extract it from the healthIdNumber field
      if (!abhaAddress && data?.accounts?.[0]?.healthIdNumber) {
        abhaAddress = data.accounts[0].healthIdNumber;
      }

      // If we still don't have an ABHA address but have an ABHA number, create a default address
      // if (!abhaAddress && abhaNumber) {
      //   abhaAddress = `${abhaNumber}@abdm`;
      // }

      // If we still don't have an ABHA address, set it to an empty string
      if (!abhaAddress) {
        abhaAddress = "";
      }
    }

    const healthIdNumber =
      data?.ABHAProfile?.ABHANumber ||
      data?.accounts?.[0]?.ABHANumber ||
      data?.users?.[0]?.abhaNumber ||
      data?.abhaNumber;
    const xToken = data.token || data.tokens?.token;
    // Always set abhaStatus to "verified" when creating a new ABHA profile
    const abhaStatus = "verified";

    // Extract PHR addresses from the ABDM response
    let phrAddresses: string[] = [];

    // Check various possible locations for PHR addresses in the response
    if (
      data?.ABHAProfile?.phrAddress &&
      Array.isArray(data.ABHAProfile.phrAddress)
    ) {
      phrAddresses = data.ABHAProfile.phrAddress.filter(
        (addr: any) => typeof addr === "string",
      );
    } else if (
      data?.accounts?.[0]?.phrAddress &&
      Array.isArray(data.accounts[0].phrAddress)
    ) {
      phrAddresses = data.accounts[0].phrAddress.filter(
        (addr: any) => typeof addr === "string",
      );
    } else if (
      data?.accounts?.[0]?.phrAddress &&
      typeof data.accounts[0].phrAddress === "string"
    ) {
      phrAddresses = [data.accounts[0].phrAddress];
    }

    // Remove duplicates and filter out empty strings
    phrAddresses = [...new Set(phrAddresses)].filter(
      (addr) => addr && addr.trim() !== "",
    );

    console.log("Extracted ABHA details:", {
      abhaNumber,
      abhaAddress,
      healthIdNumber,
      xToken: xToken ? "Token exists" : "Token missing",
      abhaStatus,
      phrAddresses,
    });

    // Log all possible ABHA address fields from the response for debugging
    console.log("All possible ABHA address fields in response:", {
      "ABHAProfile.abhaAddress": data?.ABHAProfile?.abhaAddress,
      "ABHAProfile.preferredAbhaAddress":
        data?.ABHAProfile?.preferredAbhaAddress,
      "ABHAProfile.phrAddress": data?.ABHAProfile?.phrAddress,
      "accounts[0].preferredAbhaAddress":
        data?.accounts?.[0]?.preferredAbhaAddress,
      "accounts[0].abhaAddress": data?.accounts?.[0]?.abhaAddress,
      "accounts[0].phrAddress": data?.accounts?.[0]?.phrAddress,
      "accounts[0].healthId": data?.accounts?.[0]?.healthId,
      "accounts[0].healthIdNumber": data?.accounts?.[0]?.healthIdNumber,
      "users[0].abhaAddress": data?.users?.[0]?.abhaAddress,
      abhaAddress: data?.abhaAddress,
      finalAbhaAddress: abhaAddress,
    });

    // Log the full accounts array for debugging
    if (data?.accounts && data.accounts.length > 0) {
      console.log("Full accounts[0] object:", data.accounts[0]);
    }

    // Log the full users array for debugging
    if (data?.users && data.users.length > 0) {
      console.log("Full users[0] object:", data.users[0]);
    }

    // Extract patient details from ABHA response
    // Check for different name fields in the ABHA response
    let abhaName = "";

    // Check for name in ABHAProfile if available
    if (data?.ABHAProfile?.firstName) {
      const firstName = data.ABHAProfile.firstName || "";
      const middleName = data.ABHAProfile.middleName || "";
      const lastName = data.ABHAProfile.lastName || "";

      // Combine the name parts
      abhaName = [firstName, middleName, lastName].filter(Boolean).join(" ");
      console.log("Name extracted from ABHAProfile:", abhaName);
    }
    // Fallback to accounts[0].name if ABHAProfile is not available
    else if (data?.accounts?.[0]?.name) {
      abhaName = data.accounts[0].name;
      console.log("Name extracted from accounts[0].name:", abhaName);
    }

    // Extract gender and year of birth
    const abhaGender =
      data?.ABHAProfile?.gender || data?.accounts?.[0]?.gender || "";

    // Extract year of birth from DOB if available
    let abhaYearOfBirth = "";
    if (data?.ABHAProfile?.dob) {
      // DOB format in ABHAProfile is "DD-MM-YYYY"
      const dobParts = data.ABHAProfile.dob.split("-");
      if (dobParts.length === 3) {
        abhaYearOfBirth = dobParts[2];
        console.log(
          "Year of birth extracted from ABHAProfile.dob:",
          abhaYearOfBirth,
        );
      }
    } else if (data?.accounts?.[0]?.yearOfBirth) {
      abhaYearOfBirth = data.accounts[0].yearOfBirth;
      console.log(
        "Year of birth extracted from accounts[0].yearOfBirth:",
        abhaYearOfBirth,
      );
    }

    // Format gender for our database
    let formattedGender = "other";
    if (abhaGender === "M") formattedGender = "male";
    if (abhaGender === "F") formattedGender = "female";

    // Calculate token expiration time (2 hours from now)
    const xTokenExpiresAt = new Date();
    xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

    // Extract mobile number from ABHA response
    const abhaResponseMobile =
      data?.mobile || data?.ABHAProfile?.mobile || data?.accounts?.[0]?.mobile;

    // Dual Mobile System: Only update mobile if communication mobile is provided and different from Aadhaar-linked mobile
    let shouldUpdateMobile = false;

    const actualAadhaarMobile = abhaResponseMobile || mobile;

    if (
      userMobile &&
      userMobile.trim() !== "" &&
      userMobile !== actualAadhaarMobile
    ) {
      // Communication mobile is provided and different from Aadhaar-linked mobile
      console.log("Communication mobile differs from Aadhaar-linked mobile:", {
        communicationMobile: userMobile,
        aadhaarLinkedMobile: actualAadhaarMobile,
      });
      shouldUpdateMobile = true;
    } else {
      // No communication mobile provided or same as Aadhaar-linked mobile
      console.log("Using Aadhaar-linked mobile for communication:", {
        aadhaarLinkedMobile: actualAadhaarMobile,
        communicationMobile: userMobile || "not provided",
      });
      shouldUpdateMobile = false;
    }

    // If mobile update is required, we need to call ABDM mobile update APIs
    if (shouldUpdateMobile && xToken) {
      console.log("Initiating mobile number update in ABDM...");

      try {
        // Import the mobile update services
        const { profileUpdate } = await import("@/services/abdm");

        // Request OTP for mobile update
        const otpResult = await profileUpdate.requestMobileUpdateOtp(
          userMobile,
          xToken,
        );
        console.log("Mobile update OTP requested:", otpResult);

        // Return response indicating mobile update is required
        const updateMessage = !abhaResponseMobile
          ? "Your ABHA profile doesn't have a mobile number. Please verify the OTP sent to your mobile number to update it."
          : "Mobile number update required. Please verify the OTP sent to your new mobile number.";

        return NextResponse.json({
          mobileUpdateRequired: true,
          txnId: otpResult.txnId,
          message: updateMessage,
          userMobile, // Communication mobile
          abhaResponseMobile, // Aadhaar-linked mobile from ABHA response
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          patientId,
          xToken,
          phrAddresses,
        });
      } catch (error) {
        console.error("Error requesting mobile update OTP:", error);
        // Continue with the original mobile number if update fails
        console.log(
          "Continuing with ABHA response mobile number due to update error",
        );
        shouldUpdateMobile = false;
      }
    }

    // If in standalone mode, check if patient exists in current organization or create a new one
    if (isStandalone) {
      // Check if a patient with this phone number already exists IN THE CURRENT ORGANIZATION
      const existingPatient = await db.patient.findFirst({
        where: {
          phone: mobile,
          organizationId: user.organizationId, // Only check within current organization
        },
        include: {
          abhaProfile: true,
        },
      });

      if (existingPatient) {
        // Patient exists in current organization, return the patient info
        return NextResponse.json({
          message:
            "Patient found in current organization. Please select ABHA address to complete profile creation.",
          abhaNumber,
          abhaAddress: "", // Will be set after address selection
          healthIdNumber,
          xToken,
          xTokenExpiresAt: xTokenExpiresAt.toISOString(),
          patientId: existingPatient.id,
          patientExists: true,
          phrAddresses,
          ABHAProfile: data?.ABHAProfile || {}, // Include ABHA profile data
        });
      } else {
        // Patient doesn't exist in current organization, create a new one
        // (Even if patient exists in other organizations, we create a new record for this org)
        // Get the organization ID directly from the user object
        if (!user.organizationId) {
          throw new Error("User organization not found in session");
        }

        // Get the current branch from cookies or fallback to default
        let currentBranch = null;

        // Try to get current branch from cookies
        const currentBranchId = getCurrentBranchFromCookies();

        if (currentBranchId) {
          currentBranch = await db.branch.findUnique({
            where: {
              id: currentBranchId,
              organizationId: user.organizationId, // Ensure branch belongs to user's org
            },
          });
        }

        // Fallback to default branch if no current branch found
        if (!currentBranch) {
          currentBranch = await db.branch.findFirst({
            where: {
              organizationId: user.organizationId,
            },
            orderBy: {
              createdAt: "asc", // Get the oldest branch as default
            },
          });
        }

        if (!currentBranch) {
          throw new Error("No branch found for the organization");
        }

        // Create a new patient with ABHA details
        // Use individual name fields from ABHAProfile if available, otherwise split the combined name
        let firstName = "";
        let lastName = "";

        if (data?.ABHAProfile?.firstName) {
          firstName = data.ABHAProfile.firstName || "";
          lastName = data.ABHAProfile.lastName || "";
          console.log("Using individual name fields from ABHAProfile:", {
            firstName,
            lastName,
          });
        } else if (abhaName) {
          // Fallback to splitting the combined name
          const nameParts = abhaName.split(" ");
          firstName = nameParts[0] || "";
          lastName = nameParts.length > 1 ? nameParts.slice(1).join(" ") : "";
          console.log("Using split name from abhaName:", {
            firstName,
            lastName,
            abhaName,
          });
        }

        // Ensure we have at least a firstName
        if (!firstName && !lastName) {
          firstName = "Unknown";
          console.log("No name found, using default firstName: Unknown");
        }

        // Create date of birth from year of birth or full DOB if available
        let dateOfBirth;
        if (data?.ABHAProfile?.dob) {
          // DOB format in ABHAProfile is "DD-MM-YYYY", convert to YYYY-MM-DD
          const dobParts = data.ABHAProfile.dob.split("-");
          if (dobParts.length === 3) {
            const [day, month, year] = dobParts;
            dateOfBirth = new Date(`${year}-${month}-${day}`);
            console.log(
              "Using full DOB from ABHAProfile:",
              data.ABHAProfile.dob,
              "->",
              dateOfBirth,
            );
          } else {
            dateOfBirth = new Date(`${abhaYearOfBirth}-01-01`);
            console.log(
              "Invalid DOB format, using year only:",
              abhaYearOfBirth,
            );
          }
        } else if (abhaYearOfBirth) {
          dateOfBirth = new Date(`${abhaYearOfBirth}-01-01`);
          console.log("Using year of birth only:", abhaYearOfBirth);
        } else {
          // Default to a reasonable date if no birth info is available
          dateOfBirth = new Date("1990-01-01");
          console.log(
            "No birth info available, using default date: 1990-01-01",
          );
        }

        // Extract additional details from ABHA profile if available
        // Check for address details in ABHAProfile first, then fall back to accounts
        const address =
          data?.ABHAProfile?.address || data?.accounts?.[0]?.address || "";
        const stateName =
          data?.ABHAProfile?.stateName || data?.accounts?.[0]?.stateName || "";
        const districtName =
          data?.ABHAProfile?.districtName ||
          data?.accounts?.[0]?.districtName ||
          "";
        const pincode =
          data?.ABHAProfile?.pinCode ||
          data?.ABHAProfile?.pincode ||
          data?.accounts?.[0]?.pincode ||
          "";
        const email =
          data?.ABHAProfile?.email || data?.accounts?.[0]?.email || null;

        // Create the patient without ABHA profile (will be created after address selection)
        const newPatient = await db.patient.create({
          data: {
            firstName,
            lastName,
            dateOfBirth,
            gender: formattedGender,
            phone: abhaResponseMobile || mobile, // Aadhaar-linked mobile (prefer ABHA response)
            ...(userMobile &&
              userMobile !== (abhaResponseMobile || mobile) && {
                communicationMobile: userMobile,
              }), // Communication mobile (only if different)
            email: email, // Include email if available from ABHA
            address: address, // Include address from ABHA
            state: stateName, // Include state from ABHA
            city: districtName, // Use district as city
            pincode: pincode, // Include pincode from ABHA
            status: "active",
            organizationId: user.organizationId,
            primaryBranchId: currentBranch.id,
          },
          // Include all fields in the return value
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
            email: true,
            phone: true,
            address: true,
            state: true,
            city: true,
            pincode: true,
            status: true,
            organizationId: true,
            primaryBranchId: true,
          },
        });

        return NextResponse.json({
          message:
            "Patient created successfully. Please select ABHA address to complete profile creation.",
          abhaNumber,
          abhaAddress: "", // Will be set after address selection
          healthIdNumber,
          xToken,
          xTokenExpiresAt: xTokenExpiresAt.toISOString(),
          patientId: newPatient.id,
          patientExists: false,
          phrAddresses,
          ABHAProfile: data?.ABHAProfile || {}, // Include ABHA profile data
        });
      }
    } else {
      // We have a patientId, so update the existing patient's ABHA profile
      // Double-check that patientId is defined and not empty
      if (!patientId || patientId === "") {
        throw new Error("Patient ID is required but was not provided");
      }

      const patient = await db.patient.findUnique({
        where: { id: patientId },
      });

      if (!patient) {
        throw new Error("Patient not found");
      }

      return NextResponse.json({
        message:
          "Patient found. Please select ABHA address to complete profile creation.",
        abhaNumber,
        abhaAddress: "", // Will be set after address selection
        healthIdNumber,
        xToken,
        xTokenExpiresAt: xTokenExpiresAt.toISOString(),
        patientId,
        phrAddresses,
        ABHAProfile: data?.ABHAProfile || {}, // Include ABHA profile data
      });
    }
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to verify OTP",
      },
      { status: 500 },
    );
  }
}
