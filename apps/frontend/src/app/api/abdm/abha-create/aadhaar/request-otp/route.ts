export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaCreate } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Request OTP for ABHA creation using Aadhaar
 * POST /api/abdm/abha-create/aadhaar/request-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the Aadhaar number from the request
    const { aadhaar } = await req.json();

    // Validate required fields
    if (!aadhaar) {
      return NextResponse.json(
        { error: "Aadhaar number is required" },
        { status: 400 },
      );
    }

    // Call ABDM service to generate OTP
    const result = await abhaCreate.aadhaar.requestEnrollmentOtp(aadhaar);

    // Return the transaction ID and message
    return NextResponse.json({
      txnId: result.txnId,
      message:
        result.message ||
        "OTP sent successfully to your Aadhaar-linked mobile number",
    });
  } catch (error) {
    let errorMessage = "Failed to generate OTP";

    if (error instanceof Error) {
      errorMessage = error.message;

      // Handle specific invalid Aadhaar error
      if (
        errorMessage.toLowerCase().includes("invalid loginid") ||
        errorMessage.toLowerCase().includes("invalid login id")
      ) {
        errorMessage = "Aadhaar Number is not valid";
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
