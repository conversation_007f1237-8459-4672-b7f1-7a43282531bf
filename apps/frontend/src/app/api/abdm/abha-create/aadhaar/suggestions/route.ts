import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaCreate } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Get ABHA address suggestions for ABHA creation
 * GET /api/abdm/abha-create/aadhaar/suggestions?txnId=<txnId>
 */
export async function GET(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the transaction ID from the query parameters
    const txnId = req.nextUrl.searchParams.get("txnId");

    // Validate required fields
    if (!txnId) {
      return NextResponse.json(
        { error: "Transaction ID is required" },
        { status: 400 },
      );
    }

    // Call ABDM service to get ABHA address suggestions
    const result = await abhaCreate.aadhaar.getAbhaAddressSuggestions(txnId);

    // Return the suggestions
    return NextResponse.json({
      suggestions: result.suggestions,
      txnId: result.txnId,
      message:
        result.message || "ABHA address suggestions retrieved successfully",
    });
  } catch (error) {
    console.error("Error getting ABHA address suggestions:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to get ABHA address suggestions",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
