export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { abhaCreate } from "@/services/abdm";
import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Create ABHA address for ABHA creation
 * POST /api/abdm/abha-create/aadhaar/create-abha-address
 */
export async function POST(req: NextRequest) {
  try {
    // Get the transaction ID, ABHA address, and patient ID from the request
    const { txnId, abhaAddress, patientId, abhaNumber, healthIdNumber } =
      await req.json();

    // Validate required fields
    if (!txnId) {
      return NextResponse.json(
        { error: "Transaction ID is required" },
        { status: 400 },
      );
    }

    if (!abhaAddress) {
      return NextResponse.json(
        { error: "ABHA address is required" },
        { status: 400 },
      );
    }

    // Call ABDM service to create ABHA address
    const result = await abhaCreate.aadhaar.createAbhaAddress(
      txnId,
      abhaAddress,
    );

    // Log the result to see what KYC information is available
    console.log("ABHA creation result:", JSON.stringify(result, null, 2));
    console.log("KYC status from result.profile:", result.profile?.kycVerified);
    console.log("KYC status from result:", (result as any).kycVerified);
    console.log("Result keys:", Object.keys(result || {}));
    console.log("Profile keys:", Object.keys(result.profile || {}));

    // Format the ABHA address with @sbx suffix if not already present
    const formattedAbhaAddress = result.abhaAddress?.includes("@")
      ? result.abhaAddress
      : `${result.abhaAddress || abhaAddress}@sbx`;

    // If patientId is provided, update the ABHA profile
    if (patientId) {
      try {
        // Calculate token expiration time (2 hours from now)
        const xTokenExpiresAt = new Date();
        xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

        // Get the patient to check organization ID
        const patient = await db.patient.findUnique({
          where: { id: patientId },
          select: { organizationId: true },
        });

        if (patient) {
          // Update the ABHA profile with the new address
          const updatedProfile = await db.abhaProfile.upsert({
            where: { patientId },
            update: {
              abhaAddress: formattedAbhaAddress,
              abhaNumber: result.abhaNumber || abhaNumber,
              healthIdNumber: result.abhaNumber || healthIdNumber || abhaNumber,
              abhaStatus: "verified",
              kycVerified: result.profile?.kycVerified || false, // Use KYC status from ABDM response
            },
            create: {
              patientId,
              organizationId: patient.organizationId,
              abhaAddress: formattedAbhaAddress,
              abhaNumber: result.abhaNumber || abhaNumber,
              healthIdNumber: result.abhaNumber || healthIdNumber || abhaNumber,
              abhaStatus: "verified",
              kycVerified: result.profile?.kycVerified || false, // Use KYC status from ABDM response
            },
          });

          console.log(
            "ABHA profile updated with new address:",
            formattedAbhaAddress,
          );
          console.log("Updated ABHA profile:", updatedProfile);

          // Trigger ABHA profile synchronization asynchronously
          try {
            // Use fetch to make a non-blocking call to the sync API
            fetch(
              `${req.nextUrl.origin}/api/patients/${patientId}/abha-profile-patient-sync`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  // Pass the authentication cookie to maintain the session
                  Cookie: req.headers.get("cookie") || "",
                },
              },
            ).catch((syncError) => {
              // Log any errors but don't block the main flow
              console.error("Error triggering ABHA profile sync:", syncError);
            });
          } catch (syncError) {
            // Log any errors but don't block the main flow
            console.error("Error initiating ABHA profile sync:", syncError);
          }
        } else {
          console.error("Patient not found for ID:", patientId);
        }
      } catch (dbError) {
        console.error("Error updating ABHA profile:", dbError);
      }
    } else {
      console.log("No patientId provided, skipping ABHA profile update");
    }

    // Return the ABHA details
    return NextResponse.json({
      success: true,
      abhaAddress: formattedAbhaAddress,
      abhaNumber: result.abhaNumber || abhaNumber,
      healthIdNumber: result.abhaNumber || healthIdNumber || abhaNumber,
      profile: result.profile,
      message: result.message || "ABHA address created successfully",
    });
  } catch (error) {
    console.error("Error creating ABHA address:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to create ABHA address",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
