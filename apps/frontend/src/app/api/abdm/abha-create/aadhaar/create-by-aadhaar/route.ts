export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaCreate } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Create ABHA by Aadhaar verification
 * POST /api/abdm/abha-create/aadhaar/create-by-aadhaar
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the OTP, mobile, and transaction ID from the request
    const { otp, mobile, txnId } = await req.json();

    // Validate required fields
    if (!otp || !mobile || !txnId) {
      return NextResponse.json(
        {
          error: "OTP, mobile number, and transaction ID are required",
        },
        { status: 400 },
      );
    }

    // Validate mobile number format
    if (!/^\d{10}$/.test(mobile)) {
      return NextResponse.json(
        { error: "Invalid mobile number format. Please enter 10 digits." },
        { status: 400 },
      );
    }

    // Call ABDM service to create ABHA by Aadhaar
    const data = await abhaCreate.aadhaar.createByAadhaar(otp, txnId, mobile);

    // Extract ABHA details from both ABHAProfile and accounts sections
    // Check for ABHA details in ABHAProfile first, then fall back to accounts
    const abhaNumber =
      data?.ABHAProfile?.ABHANumber ||
      data?.accounts?.[0]?.ABHANumber ||
      data?.users?.[0]?.abhaNumber ||
      data?.abhaNumber;

    // Extract ABHA address from the response
    const abhaAddress =
      data?.ABHAProfile?.abhaAddress ||
      data?.ABHAProfile?.preferredAbhaAddress ||
      data?.accounts?.[0]?.preferredAbhaAddress ||
      data?.accounts?.[0]?.abhaAddress ||
      data?.users?.[0]?.abhaAddress ||
      data?.abhaAddress ||
      (data?.accounts?.[0]?.phrAddress ? data.accounts[0].phrAddress : "") ||
      (data?.accounts?.[0]?.healthId ? data.accounts[0].healthId : "") ||
      (data?.accounts?.[0]?.healthIdNumber
        ? data.accounts[0].healthIdNumber
        : "");

    const healthIdNumber =
      data?.ABHAProfile?.ABHANumber ||
      data?.accounts?.[0]?.ABHANumber ||
      data?.users?.[0]?.abhaNumber ||
      data?.abhaNumber;
    const xToken = data.token || data.tokens?.token;
    const abhaStatus = data?.ABHAProfile?.abhaStatus || data?.authResult;

    // Return the ABHA details
    return NextResponse.json({
      message: "ABHA created successfully",
      abhaNumber,
      abhaAddress,
      healthIdNumber,
      xToken: xToken ? "Token exists" : "Token missing",
      abhaStatus,
      txnId,
      ABHAProfile: data.ABHAProfile || {}, // Include the full ABHAProfile object
    });
  } catch (error) {
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to create ABHA by Aadhaar",
      },
      { status: 500 },
    );
  }
}
