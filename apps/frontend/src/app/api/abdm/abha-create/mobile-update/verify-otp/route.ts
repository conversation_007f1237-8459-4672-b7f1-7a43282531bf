export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { profileUpdate } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Verify OTP for mobile update during ABHA creation
 * POST /api/abdm/abha-create/mobile-update/verify-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the required fields from the request
    const {
      otp,
      txnId,
      userMobile,
      abhaNumber,
      healthIdNumber,
      patientId,
      xToken,
    } = await req.json();

    console.log("Mobile update OTP verification request:", {
      otp: "REDACTED",
      txnId,
      userMobile,
      abhaNumber,
      patientId,
    });

    // Validate required fields
    if (!otp || !txnId || !userMobile || !xToken) {
      return NextResponse.json(
        {
          error: "OTP, transaction ID, mobile number, and X-Token are required",
        },
        { status: 400 },
      );
    }

    // Verify the mobile update OTP
    const verificationResult = await profileUpdate.verifyMobileUpdateOtp(
      otp,
      txnId,
      xToken,
    );

    console.log("Mobile update OTP verification result:", verificationResult);

    // Validate the verification result from ABDM
    // The ABDM service should return a successful response with proper status
    if (!verificationResult || verificationResult.error) {
      const errorMessage =
        verificationResult?.error || "OTP verification failed";
      console.error("ABDM OTP verification failed:", errorMessage);
      return NextResponse.json(
        {
          error: errorMessage,
        },
        { status: 400 },
      );
    }

    // Critical validation - check the authResult field from ABDM
    // ABDM returns authResult: "success" for valid OTP, "failed" for invalid OTP
    if (verificationResult.authResult !== "success") {
      const errorMessage =
        verificationResult.message ||
        "Invalid OTP. Please check and try again.";
      console.error(
        "ABDM OTP verification failed - authResult:",
        verificationResult.authResult,
        "Message:",
        errorMessage,
      );
      return NextResponse.json(
        {
          error: errorMessage,
        },
        { status: 400 },
      );
    }

    // Additional validation - check if the response indicates success
    // ABDM typically returns status or success indicators
    if (
      verificationResult.status === "FAILED" ||
      verificationResult.verified === false
    ) {
      console.error(
        "ABDM OTP verification returned failure status:",
        verificationResult,
      );
      return NextResponse.json(
        {
          error: "Invalid OTP. Please check and try again.",
        },
        { status: 400 },
      );
    }

    // Fetch the full ABHA profile to get complete user information including name
    let profileDetails = null;
    try {
      console.log("Fetching ABHA profile details after mobile verification...");
      const { getAbhaProfileDetails } = await import(
        "@/services/abdm/abha-profile/get-profile"
      );
      profileDetails = await getAbhaProfileDetails(xToken);
      console.log("ABHA profile details fetched:", profileDetails);
    } catch (profileError) {
      console.error("Error fetching ABHA profile details:", profileError);
      // Continue without profile details - we'll use basic info
    }

    // Extract name and other details from profile if available
    let ABHAProfile: any = {
      mobile: userMobile,
      abhaStatus: "verified",
      ABHANumber: abhaNumber,
    };

    if (profileDetails) {
      // Extract name fields from profile
      const firstName = profileDetails.firstName || "";
      const lastName = profileDetails.lastName || "";
      const middleName = profileDetails.middleName || "";

      // Combine name parts or use full name if available
      const fullName =
        profileDetails.name ||
        profileDetails.fullName ||
        [firstName, middleName, lastName].filter(Boolean).join(" ");

      // Extract and format date of birth
      let dateOfBirth = null;
      let yearOfBirth = "";
      let monthOfBirth = "";
      let dayOfBirth = "";

      if (profileDetails.dob) {
        // DOB format is typically "DD-MM-YYYY" from ABDM
        const dobParts = profileDetails.dob.split("-");
        if (dobParts.length === 3) {
          const [day, month, year] = dobParts;
          dayOfBirth = day;
          monthOfBirth = month;
          yearOfBirth = year;
          // Convert to ISO format for dateOfBirth
          dateOfBirth = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
          console.log("Extracted DOB from profile:", {
            originalDob: profileDetails.dob,
            dateOfBirth,
            yearOfBirth,
            monthOfBirth,
            dayOfBirth,
          });
        }
      } else if (profileDetails.dateOfBirth) {
        dateOfBirth = profileDetails.dateOfBirth;
        // Try to extract year from dateOfBirth if it's in ISO format
        if (dateOfBirth.includes("-")) {
          const parts = dateOfBirth.split("-");
          if (parts.length >= 3) {
            yearOfBirth = parts[0];
            monthOfBirth = parts[1];
            dayOfBirth = parts[2];
          }
        }
      } else if (profileDetails.yearOfBirth) {
        yearOfBirth = profileDetails.yearOfBirth.toString();
        monthOfBirth = profileDetails.monthOfBirth?.toString() || "01";
        dayOfBirth = profileDetails.dayOfBirth?.toString() || "01";
        dateOfBirth = `${yearOfBirth}-${monthOfBirth.padStart(2, "0")}-${dayOfBirth.padStart(2, "0")}`;
      }

      ABHAProfile = {
        ...ABHAProfile,
        firstName,
        lastName,
        middleName,
        name: fullName,
        fullName,
        dob: profileDetails.dob,
        dateOfBirth,
        yearOfBirth,
        monthOfBirth,
        dayOfBirth,
        gender: profileDetails.gender,
        email: profileDetails.email,
        address: profileDetails.address,
        stateName: profileDetails.stateName,
        districtName: profileDetails.districtName,
        pincode: profileDetails.pincode || profileDetails.pinCode,
      };

      console.log("Extracted ABHAProfile with complete details:", ABHAProfile);
    }

    // Return the verification result with transaction data and profile information
    return NextResponse.json({
      message: "Mobile number updated successfully",
      verified: true,
      abhaNumber,
      healthIdNumber,
      xToken,
      txnId,
      userMobile,
      patientId: patientId || null,
      // Include the verification result data for frontend processing
      verificationResult,
      // Include the ABHA profile information
      ABHAProfile,
    });
  } catch (error) {
    console.error("Error verifying mobile update OTP:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to verify mobile update OTP",
      },
      { status: 500 },
    );
  }
}
