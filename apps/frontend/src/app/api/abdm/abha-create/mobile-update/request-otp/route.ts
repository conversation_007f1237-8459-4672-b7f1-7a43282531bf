export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { generateUUID } from "@/lib/utils";
import { abdmFetch } from "@/lib/abdm-fetch";
import { encryptData } from "@/services/abdm/utils/encryption";
import { getAccessToken } from "@/services/abdm/utils/auth";

export const dynamic = "force-dynamic";

// Environment variables
const ABDM_SANDBOX_BASE_URL =
  process.env.ABDM_SANDBOX_BASE_URL || "https://abhasbx.abdm.gov.in/abha/api";
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Request OTP for mobile update during ABHA creation
 * POST /api/abdm/abha-create/mobile-update/request-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the required fields from the request
    const { userMobile, txnId, xToken } = await req.json();

    console.log("Mobile update OTP request:", {
      userMobile,
      txnId,
      hasXToken: !!xToken,
    });

    // Validate required fields
    if (!userMobile || !txnId || !xToken) {
      return NextResponse.json(
        { error: "User mobile, transaction ID, and X-Token are required" },
        { status: 400 },
      );
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Encrypt the mobile number
    const encryptedMobile = await encryptData(userMobile);

    // Prepare the payload for mobile update OTP during ABHA creation
    const payload = {
      txnId: txnId,
      scope: ["abha-enrol", "mobile-verify"],
      loginHint: "mobile",
      loginId: encryptedMobile,
      otpSystem: "abdm",
    };

    console.log("Sending mobile update OTP request to ABDM:", {
      endpoint: `${ABDM_SANDBOX_BASE_URL}/v3/enrollment/request/otp`,
      payload: {
        ...payload,
        loginId: "ENCRYPTED",
      },
    });

    // Make the API request with the enhanced abdmFetch
    const data = await abdmFetch(
      `${ABDM_SANDBOX_BASE_URL}/v3/enrollment/request/otp`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    console.log("Mobile update OTP request successful:", {
      txnId: data.txnId,
      hasMessage: !!data.message,
    });

    // Return the data with a default message if not provided
    return NextResponse.json({
      txnId: data.txnId,
      message:
        data.message || "OTP sent successfully to the provided mobile number",
    });
  } catch (error: any) {
    console.error("Error requesting mobile update OTP:", error);
    return NextResponse.json(
      { error: error.message || "Failed to request OTP" },
      { status: 500 },
    );
  }
}
