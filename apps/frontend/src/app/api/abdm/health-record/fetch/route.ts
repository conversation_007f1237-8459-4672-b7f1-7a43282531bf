/**
 * API endpoint for fetching health records from ABDM
 */

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";
import { healthRecord } from "@/services/abdm";
import { getUserFromRequestCookies } from "@/lib/auth-cookies";
import { getCurrentBranchObjectFromCookies } from "@/lib/branch-cookies";

/**
 * Get the facility ID (HIP ID) from the current branch
 * @returns The facility ID or throws an error if not found
 */
async function getFacilityIdFromCurrentBranch(): Promise<string> {
  // Get current branch from cookies
  const currentBranch = getCurrentBranchObjectFromCookies();

  if (!currentBranch?.id) {
    throw new Error("Current branch not found in cookies");
  }

  // Fetch the branch details from database to get the facility ID
  const branch = await db.branch.findUnique({
    where: { id: currentBranch.id },
    select: {
      id: true,
      name: true,
      hipId: true,
      hipStatus: true,
    },
  });

  if (!branch) {
    throw new Error("Branch not found in database");
  }

  if (!branch.hipId) {
    throw new Error(
      `Branch "${branch.name}" is not registered as a HIP. Please register the facility first.`,
    );
  }

  if (branch.hipStatus !== "registered") {
    throw new Error(
      `Branch "${branch.name}" HIP registration is not active. Status: ${branch.hipStatus}`,
    );
  }

  return branch.hipId;
}

export async function POST(req: NextRequest) {
  try {
    // Authenticate the user
    // const session = await getServerSession(authOptions);
    // if (!session?.user) {
    //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    // }

    const user = await getUserFromRequestCookies(req);

    // Get request body first to check if this is a system call
    const body = await req.json();
    const { consentId } = body;

    let organizationId: string;

    // If user is null (webhook/system call), get organization from consent
    if (!user) {
      console.log("No user session found - this appears to be a system/webhook call");

      if (!consentId) {
        return NextResponse.json(
          { error: "Consent ID is required" },
          { status: 400 },
        );
      }

      // Get organization ID from the consent record
      const consent = await db.consent.findUnique({
        where: { id: consentId },
        select: { organizationId: true },
      });

      if (!consent) {
        return NextResponse.json(
          { error: "Consent not found" },
          { status: 404 },
        );
      }

      organizationId = consent.organizationId;
      console.log(`Using organization ID from consent: ${organizationId}`);
    } else {
      // Normal user session
      organizationId = user.organizationId;

      if (!organizationId) {
        return NextResponse.json(
          { error: "Organization ID not found in user session" },
          { status: 400 },
        );
      }
    }

    // Validate required fields
    if (!consentId) {
      return NextResponse.json(
        { error: "Consent ID is required" },
        { status: 400 },
      );
    }

    // Check if the consent exists and belongs to the organization
    // First try with internal ID, then with ABDM consent ID
    let consent = await db.consent.findFirst({
      where: {
        id: consentId, // Try internal database ID first
        organizationId,
      },
      include: {
        patient: {
          include: {
            abhaProfile: true,
          },
        },
      },
    });

    // If not found by internal ID, try by ABDM consent ID
    if (!consent) {
      consent = await db.consent.findFirst({
        where: {
          consentId: consentId, // Try ABDM consent ID
          organizationId,
        },
        include: {
          patient: {
            include: {
              abhaProfile: true,
            },
          },
        },
      });
    }

    if (!consent) {
      console.error("Consent lookup failed:", {
        consentId,
        organizationId,
        userInfo: user ? { id: user.id, role: user.role } : "No user",
      });

      return NextResponse.json(
        { error: "Consent not found or does not belong to your organization" },
        { status: 404 },
      );
    }

    // Check if the user is authorized to fetch health records
    // This should be based on the user's role and relationship to the patient

    // Remove appointment restriction for doctors - allow all doctors to fetch health records
    // if (user && user.role === "doctor") {
    //   const doctor = await db.doctor.findFirst({
    //     where: {
    //       userId: user.id,
    //     },
    //   });

    //   if (!doctor) {
    //     return NextResponse.json(
    //       { error: "Doctor profile not found" },
    //       { status: 404 },
    //     );
    //   }

    //   // Set the doctor ID for the fetch record (no appointment check required)
    //   body.doctorId = doctor.id;
    // }

    // Get facility ID from current branch
    const facilityId = await getFacilityIdFromCurrentBranch();

    // Fetch health records from ABDM using the actual ABDM consent ID
    const result = await healthRecord.fetch.fetchHealthRecords(
      consent.consentId || consent.consentRequestId, // Use the actual ABDM consent ID
      facilityId, // Pass the facility ID
    );

    // Create an audit log
    await db.consentAuditLog.create({
      data: {
        consentId: consent.id,
        action: "FETCH_RECORDS",
        actorId: user?.id || "system",
        actorRole: user?.role || "system",
        details: {
          transactionId: result[0]?.transactionId || "unknown",
          timestamp: new Date().toISOString(),
          systemCall: !user, // Indicate if this was a system call
        },
        ipAddress: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
      },
    });

    // Get organization details for logging
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { id: true, name: true },
    });

    // Get patient details for logging
    const patient = consent.patient;

    // Enhanced logging for pull health records button click
    console.log("🔄 PULL HEALTH RECORDS INITIATED");
    console.log("📋 Request Details:", {
      timestamp: new Date().toISOString(),
      transactionId: result[0]?.transactionId,
      consentId: consent.consentId || consent.consentRequestId,
      internalConsentId: consent.id,
    });
    console.log("🏥 Organization:", {
      id: organization?.id || organizationId,
      name: organization?.name || "Unknown Organization",
    });
    console.log("👤 Patient:", {
      id: patient.id,
      name: `${patient.firstName} ${patient.lastName}`,
      abhaNumber: patient.abhaProfile?.abhaNumber || "Not available",
    });
    console.log("📊 Health Information Types (HI Types):", consent.hiTypes);
    console.log("🎯 Purpose:", consent.purpose);
    console.log("📅 Permission Details:", consent.permission);
    console.log("👨‍⚕️ Requested By:", {
      userId: user?.id || "system",
      userRole: user?.role || "system",
      userName: user?.name || "System/Webhook",
      isSystemCall: !user,
    });

    // Log the request with enhanced details
    abdmLogger.info(
      LogCategory.HEALTH_RECORD,
      `🔄 PULL HEALTH RECORDS: Button clicked - Health records fetch initiated`,
      {
        action: "PULL_HEALTH_RECORDS_INITIATED",
        transactionId: result[0]?.transactionId,
        consentId: consent.consentId || consent.consentRequestId,
        internalConsentId: consent.id,
        organization: {
          id: organization?.id || organizationId,
          name: organization?.name || "Unknown Organization",
        },
        patient: {
          id: patient.id,
          name: `${patient.firstName} ${patient.lastName}`,
          abhaNumber: patient.abhaProfile?.abhaNumber || "Not available",
        },
        hiTypes: consent.hiTypes,
        purpose: consent.purpose,
        permission: consent.permission,
        requestedBy: {
          userId: user?.id || "system",
          userRole: user?.role || "system",
          userName: user?.name || "System/Webhook",
          isSystemCall: !user,
        },
        facilityId,
        timestamp: new Date().toISOString(),
      },
      result[0]?.transactionId,
      consent.patientId,
    );

    return NextResponse.json({
      success: true,
      transactionId: result[0]?.transactionId,
      status: result[0]?.status,
    });
  } catch (error) {
    // Log error
    abdmLogger.error(
      LogCategory.HEALTH_RECORD,
      `Failed to fetch health records`,
      error,
    );

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Something went wrong",
      },
      { status: 500 },
    );
  }
}
