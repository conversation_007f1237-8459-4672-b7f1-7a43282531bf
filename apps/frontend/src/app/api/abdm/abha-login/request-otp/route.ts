export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaLogin } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Request ABHA login OTP
 * POST /api/abdm/abha-login/request-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Start timing the request for performance monitoring
    const startTime = Date.now();

    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the ABHA number from the request
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }

    const { abhaNumber } = requestBody;

    // Validate required fields
    if (!abhaNumber) {
      return NextResponse.json(
        { error: "ABHA number is required" },
        { status: 400 },
      );
    }

    // Validate ABHA number format
    if (!/^[\d-]{10,17}$/.test(abhaNumber)) {
      return NextResponse.json(
        { error: "Invalid ABHA number format" },
        { status: 400 },
      );
    }

    // Call ABDM service to request OTP
    const result = await abhaLogin.requestAbhaLoginOtp(abhaNumber);

    // Calculate request duration for monitoring
    const requestDuration = Date.now() - startTime;

    // Return the transaction ID and any other relevant information
    return NextResponse.json({
      txnId: result.txnId,
      message: "OTP sent successfully",
      // Include additional metadata for debugging
      metadata: {
        requestId: req.headers.get("x-request-id") || "unknown",
        timestamp: new Date().toISOString(),
        processingTimeMs: requestDuration,
      },
    });
  } catch (error) {
    // Determine appropriate status code and message
    let statusCode = 500;
    let errorMessage =
      "An unexpected error occurred while requesting ABHA login OTP";

    if (error instanceof Error) {
      errorMessage = error.message;

      // Map common errors to appropriate status codes
      if (
        errorMessage.includes("Invalid") ||
        errorMessage.includes("required")
      ) {
        statusCode = 400; // Bad request
      } else if (
        errorMessage.includes("Unauthorized") ||
        errorMessage.includes("Authentication")
      ) {
        statusCode = 401; // Unauthorized
      } else if (errorMessage.includes("timeout")) {
        statusCode = 504; // Gateway timeout
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
      },
      { status: statusCode },
    );
  }
}
