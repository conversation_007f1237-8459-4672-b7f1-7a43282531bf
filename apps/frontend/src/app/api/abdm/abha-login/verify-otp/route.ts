export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaLogin } from "@/services/abdm";

import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Verify ABHA login OTP
 * POST /api/abdm/abha-login/verify-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the OTP and transaction ID from the request
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 },
      );
    }

    const { otp, txnId, patientId } = requestBody;

    // Validate required fields
    if (!otp) {
      return NextResponse.json({ error: "OTP is required" }, { status: 400 });
    }

    if (!txnId) {
      return NextResponse.json(
        { error: "Transaction ID is required" },
        { status: 400 },
      );
    }

    // Validate OTP format
    if (!/^\d{6}$/.test(otp)) {
      return NextResponse.json(
        { error: "Invalid OTP format" },
        { status: 400 },
      );
    }

    const data = await abhaLogin.verifyAbhaLoginOtp(txnId, otp);

    // Get the patient to get the organizationId
    const patient = await db.patient.findUnique({
      where: { id: patientId },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    // Calculate token expiration time (2 hours from now)
    const xTokenExpiresAt = new Date();
    xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

    await db.abhaProfile.upsert({
      where: { patientId },
      update: {
        abhaNumber: data?.accounts?.[0]?.ABHANumber,
        abhaAddress: data?.accounts?.[0]?.preferredAbhaAddress,
        healthIdNumber: data?.accounts?.[0]?.ABHANumber,
        xToken: data.token,
        xTokenExpiresAt: xTokenExpiresAt,
        abhaStatus: data?.authResult,
        kycVerified: data?.accounts?.[0]?.kycVerified || false,
      },
      create: {
        patientId,
        organizationId: patient.organizationId,
        abhaNumber: data?.accounts?.[0]?.ABHANumber,
        abhaAddress: data?.accounts?.[0]?.preferredAbhaAddress,
        healthIdNumber: data?.accounts?.[0]?.ABHANumber,
        xToken: data.token,
        xTokenExpiresAt: xTokenExpiresAt,
        abhaStatus: data?.authResult,
        kycVerified: data?.accounts?.[0]?.kycVerified || false,
      },
    });

    // Trigger ABHA profile synchronization asynchronously
    try {
      // Use fetch to make a non-blocking call to the sync API
      fetch(
        `${req.nextUrl.origin}/api/patients/${patientId}/abha-profile-patient-sync`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // Pass the authentication cookie to maintain the session
            Cookie: req.headers.get("cookie") || "",
          },
        },
      ).catch((syncError) => {
        // Log any errors but don't block the main flow
        console.error("Error triggering ABHA profile sync:", syncError);
      });
    } catch (syncError) {
      // Log any errors but don't block the main flow
      console.error("Error initiating ABHA profile sync:", syncError);
    }

    // Return the X-token and any other relevant information
    return NextResponse.json({
      message: "ABHA login successful",
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to verify OTP",
      },
      { status: 500 },
    );
  }
}
