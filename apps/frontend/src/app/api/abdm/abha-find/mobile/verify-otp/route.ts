export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaFind } from "@/services/abdm";
import { db } from "@/lib/db";

export const dynamic = "force-dynamic";

/**
 * Verify OTP for ABHA verification
 * POST /api/abdm/abha-find/mobile/verify-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const requestData = await req.json();
    const {
      otp,
      txnId,
      patientId,
      standalone,
      selectedIndex = 0,
    } = requestData;

    const isStandalone = standalone === true;

    if (!otp || !txnId) {
      console.log("Validation failed - missing OTP or txnId:", { otp, txnId });
      return NextResponse.json(
        { error: "OTP and transaction ID are required" },
        { status: 400 },
      );
    }

    // Call ABDM to verify OTP
    const result = await abhaFind.verifyOtp(otp, txnId);

    if (
      !result ||
      result.authResult?.toLowerCase() !== "success" ||
      !result.token
    ) {
      console.error("OTP verification failed:", result);
      return NextResponse.json(
        {
          verified: false,
          error: "OTP verification failed",
          message: result?.message || "Invalid or expired OTP",
        },
        { status: 400 },
      );
    }

    const selectedAccount =
      result?.accounts?.[selectedIndex] || result?.accounts?.[0];

    const abhaNumber = selectedAccount?.ABHANumber;
    const abhaAddress = selectedAccount?.preferredAbhaAddress;
    const healthIdNumber = selectedAccount?.ABHANumber;
    const abhaDob = selectedAccount?.dob || "";
    const abhaGender = selectedAccount?.gender || "";
    const abhaName = selectedAccount?.name || "";
    const abhaStatus = result?.authResult;
    const xToken = result.token;

    console.log("ABDM verification response:", JSON.stringify(result, null, 2));
    console.log("Token from verification response:", xToken);

    let profileDetails = null;
    if (!xToken) {
      console.error("Token missing; skipping profile API call");
    } else {
      try {
        profileDetails = await abhaFind.getProfileDetails(xToken);
        console.log("Profile details fetched:", profileDetails);
      } catch (error) {
        console.error("Error fetching profile details:", error);
      }
    }

    // Use profile details if available
    const finalName = profileDetails?.name || abhaName;
    const finalGender = profileDetails?.gender || abhaGender;
    const finalYearOfBirth =
      profileDetails?.yearOfBirth ||
      (abhaDob ? abhaDob.split("-")[2] : "") ||
      "";
    const finalDayOfBirth = profileDetails?.dayOfBirth || "";
    const finalMonthOfBirth = profileDetails?.monthOfBirth || "";
    const abhaPhone = profileDetails?.mobile || "";
    const abhaEmail = profileDetails?.email || "";
    const abhaAddress1 = profileDetails?.address || "";
    const abhaCity =
      profileDetails?.townName || profileDetails?.districtName || "";
    const abhaState = profileDetails?.stateName || "";
    const abhaPincode = profileDetails?.pinCode || "";

    // Name parsing
    const nameParts = finalName.split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.length > 1 ? nameParts.slice(1).join(" ") : "";

    // Format gender
    let formattedGender = "other";
    if (finalGender === "M") formattedGender = "male";
    if (finalGender === "F") formattedGender = "female";

    // Standalone mode: return directly
    if (isStandalone || !patientId) {
      return NextResponse.json({
        verified: true,
        message: result.message || "Mobile verified successfully",
        abhaNumber,
        abhaAddress,
        healthIdNumber,
        name: finalName,
        firstName,
        lastName,
        gender: formattedGender,
        yearOfBirth: finalYearOfBirth,
        dayOfBirth: finalDayOfBirth ? Number(finalDayOfBirth) + 1 : "",
        monthOfBirth: finalMonthOfBirth,
        dob: abhaDob,
        phone: abhaPhone,
        email: abhaEmail,
        address: abhaAddress1,
        city: abhaCity,
        state: abhaState,
        pincode: abhaPincode,
        token: xToken,
      });
    }

    // Update DB with ABHA profile
    try {
      const patient = await db.patient.findUnique({ where: { id: patientId } });

      if (!patient) {
        return NextResponse.json({
          verified: true,
          message: result.message || "Mobile verified successfully",
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          name: finalName,
          firstName,
          lastName,
          gender: formattedGender,
          yearOfBirth: finalYearOfBirth,
          dayOfBirth: finalDayOfBirth ? Number(finalDayOfBirth) + 1 : "",
          monthOfBirth: finalMonthOfBirth,
          dob: abhaDob,
          phone: abhaPhone,
          email: abhaEmail,
          address: abhaAddress1,
          city: abhaCity,
          state: abhaState,
          pincode: abhaPincode,
          token: xToken,
        });
      }

      const xTokenExpiresAt = new Date();
      xTokenExpiresAt.setHours(xTokenExpiresAt.getHours() + 2);

      await db.abhaProfile.upsert({
        where: { patientId },
        update: {
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          xToken,
          xTokenExpiresAt,
          abhaStatus,
          kycVerified: result?.kycVerified || false,
        },
        create: {
          patientId,
          organizationId: patient.organizationId,
          abhaNumber,
          abhaAddress,
          healthIdNumber,
          xToken,
          xTokenExpiresAt,
          abhaStatus,
          kycVerified: result?.kycVerified || false,
        },
      });
    } catch (error) {
      console.error("Error updating patient ABHA profile:", error);
    }

    // Trigger background sync
    try {
      fetch(
        `${req.nextUrl.origin}/api/patients/${patientId}/abha-profile-patient-sync`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Cookie: req.headers.get("cookie") || "",
          },
        },
      ).catch((syncError) => {
        console.error("Error triggering ABHA profile sync:", syncError);
      });
    } catch (syncError) {
      console.error("Error initiating ABHA profile sync:", syncError);
    }

    return NextResponse.json({
      verified: true,
      message: result.message || "Mobile verified successfully",
      abhaNumber,
      abhaAddress,
      healthIdNumber,
      name: finalName,
      firstName,
      lastName,
      gender: formattedGender,
      yearOfBirth: finalYearOfBirth,
      dayOfBirth: finalDayOfBirth ? Number(finalDayOfBirth) + 1 : "",
      monthOfBirth: finalMonthOfBirth,
      dob: abhaDob,
      phone: abhaPhone,
      email: abhaEmail,
      address: abhaAddress1,
      city: abhaCity,
      state: abhaState,
      pincode: abhaPincode,
      token: xToken,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to verify OTP",
      },
      { status: 500 },
    );
  }
}
