export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaFind } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Request OTP for ABHA verification using index
 * POST /api/abdm/abha-find/mobile/request-otp
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the index and searchTxnId from the request
    const requestData = await req.json();
    console.log("Request data:", requestData);

    const { index, searchTxnId } = requestData;
    console.log("Extracted index:", index, "type:", typeof index);
    console.log("Extracted searchTxnId:", searchTxnId);

    // Validate required fields
    if (index === undefined || index === null) {
      return NextResponse.json({ error: "Index is required" }, { status: 400 });
    }

    if (!searchTxnId) {
      return NextResponse.json(
        { error: "Search transaction ID is required" },
        { status: 400 },
      );
    }

    // Call ABDM service to request OTP
    const result = await abhaFind.requestOtpWithIndex(index, searchTxnId);

    // Return the transaction ID and message
    return NextResponse.json({
      message: result.message || "OTP sent successfully",
      txnId: result.txnId || "", // Provide a default empty string if txnId is not present
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to request OTP",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
