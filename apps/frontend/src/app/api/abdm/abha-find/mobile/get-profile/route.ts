export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaFind } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Get ABHA profile details using transaction ID
 * POST /api/abdm/abha-find/mobile/get-profile
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the transaction ID from the request
    const { txnId } = await req.json();

    // Validate required fields
    if (!txnId) {
      return NextResponse.json(
        { error: "Transaction ID is required" },
        { status: 400 },
      );
    }

    // Call ABDM service to get profile details
    console.log("API route: Getting ABHA profile details with txnId:", txnId);
    const result = await abhaFind.getProfileDetails(txnId);
    console.log("API route: Profile details result:", result);

    // Return the profile details
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error getting ABHA profile details:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to get ABHA profile details",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
