export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/session";
import { abhaFind } from "@/services/abdm";

export const dynamic = "force-dynamic";

/**
 * Search ABHA by mobile number
 * POST /api/abdm/abha-find/mobile/search
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the mobile number from the request
    const { mobile } = await req.json();

    // Validate required fields
    if (!mobile) {
      return NextResponse.json(
        { error: "Mobile number is required" },
        { status: 400 },
      );
    }

    // Call ABDM service to search ABHA by mobile
    console.log("API route: Searching ABHA by mobile:", mobile);
    const result = await abhaFind.searchAbhaByMobile(mobile);
    console.log("API route: Search result:", result);

    // Return the search results
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to search ABHA",
      },
      {
        status:
          error instanceof Error && error.message.includes("Invalid")
            ? 400
            : 500,
      },
    );
  }
}
