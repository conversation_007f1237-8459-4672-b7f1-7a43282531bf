export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";

// GET /api/consultations/[id] - Get a specific consultation
export async function GET(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Get the consultation
    const consultation = await db.consultation.findUnique({
      where: {
        id,
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
            phone: true,
            email: true,
            address: true,
            city: true,
            state: true,
            pincode: true,
            bloodGroup: true,
            abhaProfile: {
              select: {
                id: true,
                abhaNumber: true,
                abhaAddress: true,
                healthIdNumber: true,
                abhaStatus: true,
              },
            },
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        appointment: true,
        vitals: {
          orderBy: {
            recordedAt: "desc",
          },
        },
        clinicalNotes: {
          orderBy: {
            createdAt: "desc",
          },
        },
        prescriptions: {
          include: {
            items: true,
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        labTestRequests: {
          include: {
            diagnosticReports: {
              select: {
                id: true,
                reportType: true,
                status: true,
                reportDate: true,
                conclusion: true,
              },
            },
          },
          orderBy: {
            requestDate: "desc",
          },
        },
      },
    });

    if (!consultation) {
      return NextResponse.json(
        { error: "Consultation not found" },
        { status: 404 },
      );
    }

    // For development purposes, we're skipping doctor check
    // In a production environment, you would uncomment the following code:
    /*
    // Get the current user from cookies (if not already fetched)
    const user = await getCurrentUser();

    // If the user is a doctor, check if they are the doctor for this consultation
    if (user && user.role === "doctor") {
      const doctor = await db.doctor.findFirst({
        where: {
          userId: user.id,
        },
      });

      if (!doctor || doctor.id !== consultation.doctorId) {
        return NextResponse.json(
          { error: "Forbidden: You are not authorized to view this consultation" },
          { status: 403 }
        );
      }
    }
    */

    return NextResponse.json({ consultation });
  } catch (error) {
    console.error("Error fetching consultation:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

// PUT /api/consultations/[id] - Update a consultation
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the consultation data from the request
    const {
      consultationDate,
      startTime,
      endTime,
      status,
      followUpDate,
      followUpNotes,
    } = await req.json();

    // Check if the consultation exists
    const existingConsultation = await db.consultation.findUnique({
      where: {
        id,
      },
    });

    if (!existingConsultation) {
      return NextResponse.json(
        { error: "Consultation not found" },
        { status: 404 },
      );
    }

    // If the user is a doctor, check if they are the doctor for this consultation
    // if (user.role === "doctor") {
    //   const doctor = await db.doctor.findFirst({
    //     where: {
    //       userId: user.id,
    //     },
    //   });

    //   // if (!doctor || doctor.id !== existingConsultation.doctorId) {
    //   //   return NextResponse.json(
    //   //     {
    //   //       error:
    //   //         "Forbidden: You are not authorized to update this consultation",
    //   //     },
    //   //     { status: 403 },
    //   //   );
    //   // }
    // }

    // Update the consultation
    const updatedConsultation = await db.consultation.update({
      where: {
        id,
      },
      data: {
        consultationDate: consultationDate
          ? new Date(consultationDate)
          : undefined,
        startTime,
        endTime,
        status,
        followUpDate: followUpDate ? new Date(followUpDate) : null,
        followUpNotes,
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
            phone: true,
            email: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        appointment: true,
      },
    });

    // If consultation is marked as completed, handle completion logic
    if (status === "completed") {
      // Update appointment status if associated
      if (updatedConsultation.appointmentId) {
        await db.appointment.update({
          where: {
            id: updatedConsultation.appointmentId,
          },
          data: {
            status: "completed",
          },
        });

        // Also update the queue status if it exists
        const queueStatus = await db.queueStatus.findFirst({
          where: {
            appointmentId: updatedConsultation.appointmentId,
          },
        });

        if (queueStatus) {
          await db.queueStatus.update({
            where: {
              id: queueStatus.id,
            },
            data: {
              status: "completed",
              completionTime: new Date(),
            },
          });
        }
      }

      // Automatically create care context when consultation is completed
      try {
        console.log(
          `🎯 Consultation ${id} marked as completed, attempting to create care context`,
        );

        // Check if patient has ABHA profile
        const patient = await db.patient.findUnique({
          where: { id: updatedConsultation.patientId },
          include: { abhaProfile: true },
        });

        if (patient?.abhaProfile?.abhaNumber) {
          // Check if care context already exists
          const existingCareContext = await db.careContext.findFirst({
            where: { consultationId: id },
          });

          if (!existingCareContext) {
            console.log(`📋 Creating care context for consultation ${id}`);

            // Import the ABDM service to create care context
            const { abdmService } = await import(
              "@/services/abdm-service-compat"
            );

            // Fetch consultation data to determine which HI types to include
            const consultationWithData = await db.consultation.findUnique({
              where: { id },
              include: {
                vitals: true,
                clinicalNotes: true,
                prescriptions: true,
                invoices: true,
                labTestRequests: true,
                Immunization: true, // Correct relationship name
                DocumentReference: true, // Correct relationship name for discharge summaries
              },
            });

            // Dynamic HI type selection based on actual data present
            const dynamicHiTypes = [];

            if (
              consultationWithData?.vitals &&
              consultationWithData.vitals.length > 0
            ) {
              dynamicHiTypes.push("WellnessRecord");
              console.log(
                `✅ Including WellnessRecord - found ${consultationWithData.vitals.length} vitals records`,
              );
            }

            if (
              consultationWithData?.clinicalNotes &&
              consultationWithData.clinicalNotes.length > 0
            ) {
              dynamicHiTypes.push("OPConsultation");
              console.log(
                `✅ Including OPConsultation - found ${consultationWithData.clinicalNotes.length} clinical notes`,
              );
            }

            if (
              consultationWithData?.prescriptions &&
              consultationWithData.prescriptions.length > 0
            ) {
              dynamicHiTypes.push("Prescription");
              console.log(
                `✅ Including Prescription - found ${consultationWithData.prescriptions.length} prescriptions`,
              );
            }

            if (consultationWithData?.invoices) {
              dynamicHiTypes.push("Invoice");
              console.log(
                `✅ Including Invoice - found invoice with ID: ${consultationWithData.invoices.id}`,
              );
            }

            if (
              consultationWithData?.labTestRequests &&
              consultationWithData.labTestRequests.length > 0
            ) {
              dynamicHiTypes.push("DiagnosticReport");
              console.log(
                `✅ Including DiagnosticReport - found ${consultationWithData.labTestRequests.length} lab test requests`,
              );
            }

            // Check for immunization records
            if (
              consultationWithData?.Immunization &&
              consultationWithData.Immunization.length > 0
            ) {
              dynamicHiTypes.push("ImmunizationRecord");
              console.log(
                `✅ Including ImmunizationRecord - found ${consultationWithData.Immunization.length} immunization records`,
              );
            }

            // Check for document references
            const allDocumentReferences =
              consultationWithData?.DocumentReference || [];

            // Check for discharge summary data (stored as DocumentReference with specific type)
            const dischargeSummaryDocs = allDocumentReferences.filter(
              (doc) =>
                doc.type === "discharge-summary" ||
                doc.typeDisplay?.toLowerCase().includes("discharge"),
            );

            if (dischargeSummaryDocs.length > 0) {
              dynamicHiTypes.push("DischargeSummary");
              console.log(
                `✅ Including DischargeSummary - found ${dischargeSummaryDocs.length} discharge summary documents`,
              );
            }

            // Check for other health documents (non-discharge summary DocumentReferences)
            const healthDocuments = allDocumentReferences.filter(
              (doc) =>
                doc.type !== "discharge-summary" &&
                !doc.typeDisplay?.toLowerCase().includes("discharge"),
            );

            if (healthDocuments.length > 0) {
              dynamicHiTypes.push("HealthDocumentRecord");
              console.log(
                `✅ Including HealthDocumentRecord - found ${healthDocuments.length} health document references`,
              );
            }

            // Fallback logic: if no data exists, use default fallback set
            const finalHiTypes =
              dynamicHiTypes.length > 0
                ? dynamicHiTypes
                : [
                    "DischargeSummary",
                    "ImmunizationRecord",
                    "DiagnosticReport",
                  ];

            if (dynamicHiTypes.length === 0) {
              console.log(
                `⚠️ No consultation data found, using fallback HI types: ${finalHiTypes.join(", ")}`,
              );
            } else {
              console.log(
                `🎯 Dynamic HI types selected: ${finalHiTypes.join(", ")}`,
              );
            }

            // Step 1: Create care context using dynamically determined HI types
            const result = await abdmService.linkCareContext(
              updatedConsultation.patientId,
              "", // No appointment ID needed
              finalHiTypes,
              updatedConsultation.branchId,
              id, // consultation ID
            );

            console.log(
              `✅ Care context linked successfully for consultation ${id}:`,
              result.careContext?.id,
            );

            // Step 2: Notify about the care context (same as manual flow)
            try {
              console.log(
                `📤 Sending notification for care context ${result.careContext?.id}`,
              );

              await abdmService.notifyCareContext(result.careContext?.id);

              console.log(
                `✅ Care context notification sent successfully for consultation ${id}`,
              );
            } catch (notifyError) {
              console.error(
                `❌ Failed to notify care context for consultation ${id}:`,
                notifyError,
              );
              // Continue anyway since the care context was created
            }
          } else {
            console.log(
              `ℹ️ Care context already exists for consultation ${id}`,
            );
          }
        } else {
          console.log(
            `⚠️ Patient ${updatedConsultation.patientId} does not have ABHA profile, skipping care context creation`,
          );
        }
      } catch (careContextError) {
        console.error(
          `❌ Failed to create care context for consultation ${id}:`,
          careContextError,
        );
        // Don't fail the consultation update if care context creation fails
      }
    }

    return NextResponse.json({
      consultation: updatedConsultation,
      message: "Consultation updated successfully",
    });
  } catch (error) {
    console.error("Error updating consultation:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

// DELETE /api/consultations/[id] - Delete a consultation
export async function DELETE(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the consultation exists
    const existingConsultation = await db.consultation.findUnique({
      where: {
        id,
      },
    });

    if (!existingConsultation) {
      return NextResponse.json(
        { error: "Consultation not found" },
        { status: 404 },
      );
    }

    // Delete the consultation
    await db.consultation.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({
      message: "Consultation deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting consultation:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
