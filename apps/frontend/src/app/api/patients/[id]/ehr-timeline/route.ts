export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";

// GET /api/patients/[id]/ehr-timeline - Get EHR timeline for a patient
export async function GET(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const patientId = params.id;

    // Get the patient
    const patient = await db.patient.findUnique({
      where: {
        id: patientId,
      },
    });

    if (!patient) {
      return NextResponse.json({ error: "Patient not found" }, { status: 404 });
    }

    // Get consultations
    const consultations = await db.consultation.findMany({
      where: {
        patientId,
      },
      include: {
        doctor: {
          include: {
            user: true,
          },
        },
        branch: true,
      },
      orderBy: {
        consultationDate: "desc",
      },
    });

    // Get vitals
    const vitals = await db.vitals.findMany({
      where: {
        consultation: {
          patientId,
        },
      },
      include: {
        consultation: {
          include: {
            doctor: {
              include: {
                user: true,
              },
            },
            branch: true,
          },
        },
      },
      orderBy: {
        recordedAt: "desc",
      },
    });

    // Get clinical notes
    const clinicalNotes = await db.clinicalNote.findMany({
      where: {
        consultation: {
          patientId,
        },
      },
      include: {
        consultation: {
          include: {
            doctor: {
              include: {
                user: true,
              },
            },
            branch: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Get prescriptions
    const prescriptions = await db.prescription.findMany({
      where: {
        consultation: {
          patientId,
        },
      },
      include: {
        consultation: {
          include: {
            doctor: {
              include: {
                user: true,
              },
            },
            branch: true,
          },
        },
        items: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Mock ABHA records for the timeline
    const abhaRecords = [
      {
        id: "abha-1",
        type: "abha",
        source: "abha",
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        title: "Prescription from Apollo Hospitals",
        description: "Antibiotics for respiratory infection",
        provider: "Apollo Hospitals",
        details: {
          medications: [
            {
              name: "Azithromycin",
              dosage: "500mg",
              frequency: "Once daily",
              duration: "5 days",
            },
            {
              name: "Paracetamol",
              dosage: "650mg",
              frequency: "As needed",
              duration: "3 days",
            },
          ],
        },
      },
      {
        id: "abha-2",
        type: "abha",
        source: "abha",
        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
        title: "Complete Blood Count (CBC)",
        description: "Diagnostic report from SRL Diagnostics",
        provider: "SRL Diagnostics",
        details: {
          results: [
            {
              name: "Hemoglobin",
              value: "14.2 g/dL",
              normalRange: "13.5-17.5 g/dL",
            },
            {
              name: "WBC Count",
              value: "7.5 x 10^9/L",
              normalRange: "4.5-11.0 x 10^9/L",
            },
          ],
        },
      },
      {
        id: "abha-3",
        type: "abha",
        source: "abha",
        date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days ago
        title: "Discharge Summary",
        description: "Discharge summary for appendectomy from AIIMS Delhi",
        provider: "AIIMS Delhi",
        details: {
          admissionDate: new Date(
            Date.now() - 63 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          dischargeDate: new Date(
            Date.now() - 60 * 24 * 60 * 60 * 1000,
          ).toISOString(),
          diagnosis: "Acute appendicitis",
          procedure: "Laparoscopic appendectomy",
          followUp: "2 weeks post-discharge",
        },
      },
    ];

    // Transform consultations to timeline events
    const consultationEvents = consultations.map((consultation) => ({
      id: consultation.id,
      type: "consultation",
      source: "internal",
      date: consultation.consultationDate,
      title: `Consultation with ${consultation.doctor.user.name}`,
      description: `${consultation.status} consultation at ${consultation.branch.name}`,
      provider: consultation.branch.name,
      details: {
        status: consultation.status,
        doctor: consultation.doctor.user.name,
        branch: consultation.branch.name,
      },
    }));

    // Transform vitals to timeline events
    const vitalEvents = vitals.map((vital) => ({
      id: vital.id,
      type: "vitals",
      source: "internal",
      date: vital.recordedAt,
      title: "Vitals Recorded",
      description: `BP: ${vital.bloodPressureSystolic}/${vital.bloodPressureDiastolic}, Pulse: ${vital.pulse}, Temp: ${vital.temperature}°C`,
      provider: vital.consultation.branch.name,
      details: {
        bloodPressure: `${vital.bloodPressureSystolic}/${vital.bloodPressureDiastolic}`,
        pulse: vital.pulse,
        temperature: vital.temperature,
        oxygenSaturation: vital.oxygenSaturation,
        respiratoryRate: vital.respiratoryRate,
        height: vital.height,
        weight: vital.weight,
        bmi: vital.bmi,
      },
    }));

    // Transform clinical notes to timeline events
    const noteEvents = clinicalNotes.map((note) => ({
      id: note.id,
      type: "note",
      source: "internal",
      date: note.createdAt,
      title: `${note.noteType} Note`,
description: (note?.content ?? "").substring(0, 100) + 
             ((note?.content?.length ?? 0) > 100 ? "..." : ""),

      provider: note.consultation.branch.name,
      details: {
        type: note.noteType,
        content: note?.content,
        doctor: note.consultation.doctor.user.name,
      },
    }));

    // Transform prescriptions to timeline events
    const prescriptionEvents = prescriptions.map((prescription) => ({
      id: prescription.id,
      type: "prescription",
      source: "internal",
      date: prescription.createdAt,
      title: "Prescription",
      description: `${prescription.items.length} medication(s) prescribed`,
      provider: prescription.consultation.branch.name,
      details: {
        medications: prescription.items.map((item) => ({
          name: item.medicationName,
          dosage: item.dosage,
          frequency: item.frequency,
          duration: item.duration,
          instructions: item.instructions,
        })),
        doctor: prescription.consultation.doctor.user.name,
      },
    }));

    // Combine all events and sort by date (newest first)
    const timeline = [
      ...consultationEvents,
      ...vitalEvents,
      ...noteEvents,
      ...prescriptionEvents,
      ...abhaRecords,
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    return NextResponse.json({
      timeline,
    });
  } catch (error) {
    console.error("Error fetching EHR timeline:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
