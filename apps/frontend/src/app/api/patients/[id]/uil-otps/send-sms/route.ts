/**
 * API endpoint to send <PERSON><PERSON> OTP via SMS to a patient
 * POST /api/patients/[id]/uil-otps/send-sms
 */

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";

import { logger } from "@/lib/logger";
import { sendHealthRecordOtpDLT } from "@/lib/sms/fast2sms";

export const dynamic = "force-dynamic";
export const maxDuration = 60; // 60 seconds timeout

export async function POST(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const patientId = params.id;

    if (!patientId) {
      return NextResponse.json(
        { error: "Patient ID is required" },
        { status: 400 },
      );
    }

    // Get the patient to verify it exists and get phone number
    const patient = await db.patient.findUnique({
      where: { id: patientId },
      include: {
        primaryBranch: {
          select: {
            id: true,
            name: true,
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!patient) {
      return NextResponse.json({ error: "Patient not found" }, { status: 404 });
    }

    // Check if patient has a phone number
    if (!patient.phone) {
      return NextResponse.json(
        { error: "Patient does not have a phone number" },
        { status: 400 },
      );
    }

    // Find active UIL OTP for this patient
    // Active OTP is one that is not verified and has not expired
    const now = new Date();

    const activeOtp = await db.uILOtpNotify.findFirst({
      where: {
        patientId,
        verified: false,
        expiresAt: {
          gt: now,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!activeOtp) {
      return NextResponse.json(
        { error: "No active OTP found" },
        { status: 404 },
      );
    }

    try {
      // Send OTP via Fast2SMS using the OTP route
      const smsResult = await sendHealthRecordOtpDLT({
        to: patient.phone,
        facilityName: patient.primaryBranch?.name || "Aranco Healthcare",
        otp: activeOtp.otp,
      });

      if (smsResult.success) {
        // Update SMS delivery status in database
        await db.uILOtpNotify.update({
          where: { id: activeOtp.id },
          data: {
            smsDeliveryStatus: "sent",
            smsMessageId: smsResult.messageId,
            smsDeliveredAt: new Date(),
            smsError: null, // Clear any previous error
          },
        });

        logger.info("UIL OTP SMS resent successfully via Fast2SMS", {
          patientId: patient.id,
          phone: patient.phone,
          messageId: smsResult.messageId,
          userId: user.id,
        });

        return NextResponse.json({
          success: true,
          message: "OTP sent successfully via SMS",
          messageId: smsResult.messageId,
          expiresAt: activeOtp.expiresAt,
        });
      } else {
        // Update SMS delivery status as failed in database
        await db.uILOtpNotify.update({
          where: { id: activeOtp.id },
          data: {
            smsDeliveryStatus: "failed",
            smsError: smsResult.error || "Unknown SMS delivery error",
          },
        });

        logger.error("Failed to resend UIL OTP SMS via Fast2SMS", {
          patientId: patient.id,
          phone: patient.phone,
          error: smsResult.error,
          details: smsResult.details,
          userId: user.id,
        });

        return NextResponse.json(
          {
            success: false,
            error: smsResult.error || "Failed to send SMS",
            details: smsResult.details,
          },
          { status: 500 },
        );
      }
    } catch (smsError) {
      // Update SMS delivery status as failed in database
      try {
        await db.uILOtpNotify.update({
          where: { id: activeOtp.id },
          data: {
            smsDeliveryStatus: "failed",
            smsError: smsError instanceof Error ? smsError.message : String(smsError),
          },
        });
      } catch (dbError) {
        logger.error("Failed to update SMS delivery status in database", {
          patientId: patient.id,
          linkRefNumber: activeOtp.linkRefNumber,
          error: dbError instanceof Error ? dbError.message : String(dbError),
        });
      }

      logger.error("Error resending UIL OTP SMS via Fast2SMS", {
        patientId: patient.id,
        phone: patient.phone,
        error: smsError instanceof Error ? smsError.message : String(smsError),
        userId: user.id,
      });

      return NextResponse.json(
        {
          success: false,
          error: "Failed to send SMS due to technical error",
        },
        { status: 500 },
      );
    }
  } catch (error) {
    logger.error("Error in UIL OTP SMS resend endpoint", {
      error: error instanceof Error ? error.message : String(error),
      patientId: params.id,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to send UIL OTP via SMS",
      },
      { status: 500 },
    );
  }
}
