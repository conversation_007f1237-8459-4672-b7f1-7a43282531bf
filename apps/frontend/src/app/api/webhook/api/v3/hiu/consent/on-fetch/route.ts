import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { abdmLogger, LogCategory } from "@/lib/abdm-logger";
import { createConsentAuditLog } from "@/app/api/abdm/consent/audit/actions";


/**
 * ABDM Consent Fetch Callback Webhook
 * 
 * This webhook receives callbacks from ABDM when consent fetch requests are processed.
 * It updates the consent record with detailed consent information received from ABDM.
 * 
 * Endpoint: POST /api/webhook/api/v3/hiu/consent/on-fetch
 */
export async function POST(req: NextRequest) {
  try {
    // Get the request ID from headers for logging
    const requestId = req.headers.get("request-id") || "unknown";

    // Parse the webhook payload
    const payload = await req.json();

    // Enhanced logging for callback reception
    console.log("📞 CONSENT FETCH CALLBACK RECEIVED");
    console.log("📋 Callback Details:", {
      timestamp: new Date().toISOString(),
      requestId,
      endpoint: "/api/webhook/api/v3/hiu/consent/on-fetch",
      trigger: "This callback was triggered by ABDM after consent fetch API call",
    });
    console.log("📦 Payload Summary:", {
      hasConsent: !!payload.consent,
      consentStatus: payload.consent?.status,
      hasConsentDetail: !!payload.consent?.consentDetail,
      hasError: !!payload.error,
      hasResponse: !!payload.response,
    });

    // Log the received webhook payload
    abdmLogger.info(
      LogCategory.CONSENT,
      `📞 CONSENT FETCH CALLBACK: Received callback from ABDM`,
      {
        action: "CONSENT_FETCH_CALLBACK_RECEIVED",
        requestId,
        payload: payload,
        timestamp: new Date().toISOString(),
      },
      requestId,
    );

    // Extract consent information from payload
    const { consent, error, response } = payload;
    const responseRequestId = response?.requestId;

    if (error) {
      console.log("❌ CONSENT FETCH ERROR:", error);
      
      abdmLogger.error(
        LogCategory.CONSENT,
        `❌ CONSENT FETCH ERROR: Error in consent fetch callback`,
        {
          action: "CONSENT_FETCH_ERROR",
          requestId,
          responseRequestId,
          error: error,
          timestamp: new Date().toISOString(),
        },
        requestId,
      );

      return NextResponse.json(
        { error: "Consent fetch error received" },
        { status: 400 },
      );
    }

    if (!consent || !consent.consentDetail) {
      console.log("❌ MISSING CONSENT DETAILS");
      
      abdmLogger.error(
        LogCategory.CONSENT,
        `❌ MISSING CONSENT DETAILS: Consent or consentDetail missing in callback`,
        {
          action: "MISSING_CONSENT_DETAILS",
          requestId,
          responseRequestId,
          hasConsent: !!consent,
          hasConsentDetail: !!consent?.consentDetail,
          timestamp: new Date().toISOString(),
        },
        requestId,
      );

      return NextResponse.json(
        { error: "Missing consent details" },
        { status: 400 },
      );
    }

    const { status, consentDetail, signature } = consent;
    const { consentId: abdmConsentId } = consentDetail;

    console.log(`🔍 PROCESSING CONSENT FETCH: ABDM Consent ID: ${abdmConsentId}, Status: ${status}`);

    // Find the consent record by ABDM consent ID
    let consentRecord = await db.consent.findFirst({
      where: { 
        OR: [
          { consentId: abdmConsentId },
          { consentRequestId: abdmConsentId }
        ]
      },
      include: {
        patient: {
          include: {
            abhaProfile: true,
          },
        },
        organization: true,
      },
    });

    if (!consentRecord) {
      console.log(`⚠️ CONSENT NOT FOUND: No consent found for ABDM ID: ${abdmConsentId}`);
      
      abdmLogger.warn(
        LogCategory.CONSENT,
        `⚠️ CONSENT NOT FOUND: Could not find consent record for ABDM consent ID`,
        {
          action: "CONSENT_NOT_FOUND",
          requestId,
          responseRequestId,
          abdmConsentId,
          status,
          timestamp: new Date().toISOString(),
        },
        requestId,
      );

      return NextResponse.json(
        { error: "Consent not found", abdmConsentId },
        { status: 404 },
      );
    }

    console.log(`✅ CONSENT FOUND: Internal ID: ${consentRecord.id}, Current Status: ${consentRecord.status}`);

    // Update the consent record with detailed information
    const updatedConsent = await db.consent.update({
      where: { id: consentRecord.id },
      data: {
        status: status,
        // consentId: abdmConsentId, // Ensure we have the ABDM consent ID
        // consentArtifact: JSON.stringify({
        //   consentDetail: consentDetail,
        //   signature: signature,
        //   fetchedAt: new Date().toISOString(),
        //   callbackPayload: payload,
        // }),
        // Update care contexts if provided
        careContexts: consentDetail.careContexts ? JSON.stringify(consentDetail.careContexts) : (consentRecord.careContexts as any),
        // Update HI types if provided
        hiTypes: consentDetail.hiTypes || consentRecord.hiTypes,
        // Update permission if provided
        permission: consentDetail.permission ? JSON.stringify(consentDetail.permission) : (consentRecord.permission as any),
        // Update expiry date if provided
        expiryDate: consentDetail.permission?.dataEraseAt ? new Date(consentDetail.permission.dataEraseAt) : consentRecord.expiryDate,
        updatedAt: new Date(),
      },
    });

    console.log(`💾 CONSENT UPDATED: Successfully updated consent with detailed information`);
    console.log("📋 Update Details:", {
      consentId: updatedConsent.id,
      abdmConsentId: abdmConsentId,
      status: status,
      hiTypes: consentDetail.hiTypes?.length || 0,
      careContexts: consentDetail.careContexts?.length || 0,
      hasSignature: !!signature,
    });

    // Create audit log for the consent fetch callback
    await createConsentAuditLog({
      consentId: consentRecord.id,
      action: "CONSENT_FETCH_CALLBACK",
      actorId: "system",
      actorRole: "ABDM",
      details: {
        requestId,
        responseRequestId,
        abdmConsentId,
        status,
        consentDetail: consentDetail,
        signature: signature ? "present" : "missing",
        timestamp: new Date().toISOString(),
        headers: {
          requestId,
        },
      },
      ipAddress: req.headers.get("x-forwarded-for") || req.ip || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
    });

    abdmLogger.info(
      LogCategory.CONSENT,
      `✅ CONSENT FETCH COMPLETED: Successfully updated consent with detailed information`,
      {
        action: "CONSENT_FETCH_COMPLETED",
        requestId,
        responseRequestId,
        consentId: consentRecord.id,
        abdmConsentId,
        status,
        patientId: consentRecord.patientId,
        organizationId: consentRecord.organizationId,
        updateDetails: {
          hiTypesCount: consentDetail.hiTypes?.length || 0,
          careContextsCount: consentDetail.careContexts?.length || 0,
          hasSignature: !!signature,
          hasPermission: !!consentDetail.permission,
        },
        timestamp: new Date().toISOString(),
      },
      requestId,
      consentRecord.patientId,
    );

    // Return success response
    return NextResponse.json({
      success: true,
      message: "Consent fetch callback processed successfully",
      data: {
        consentId: consentRecord.id,
        abdmConsentId: abdmConsentId,
        status: status,
        updatedAt: updatedConsent.updatedAt,
      },
    });

  } catch (error) {
    console.error("❌ Error processing consent fetch callback:", error);

    abdmLogger.error(
      LogCategory.CONSENT,
      `❌ CONSENT FETCH CALLBACK ERROR: Failed to process consent fetch callback`,
      {
        action: "CONSENT_FETCH_CALLBACK_ERROR",
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      },
    );

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unexpected error occurred",
      },
      { status: 500 },
    );
  }
}
