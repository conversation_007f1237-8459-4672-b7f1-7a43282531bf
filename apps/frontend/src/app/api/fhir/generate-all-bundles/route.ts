import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";
import { generateCustomOpConsultBundle } from "../custom-op-consult-strategy";
import { generateCustomWellnessBundle } from "../custom-wellness-strategy";
import { generateCustomPrescriptionBundle } from "../custom-prescription-strategy";
import { generateCustomInvoiceBundle } from "../custom-invoice-strategy";
import {
  generateResource,
  ResourceType,
} from "@workspace/fhir-observation/src";
// PDF generation removed to avoid React errors in production

/**
 * Map database gender values to FHIR-compliant gender values
 * @param gender - Gender value from database (f, m, o, female, male, other, etc.)
 * @returns FHIR-compliant gender value (female, male, other, unknown)
 */
function mapGenderToFhir(gender?: string | null): string {
  if (!gender) return "unknown";

  const normalizedGender = gender.toLowerCase().trim();

  switch (normalizedGender) {
    case "f":
    case "female":
      return "female";
    case "m":
    case "male":
      return "male";
    case "o":
    case "other":
      return "other";
    default:
      return "unknown";
  }
}

export const maxDuration = 299;

/**
 * This API endpoint generates all three FHIR bundles (wellness record, prescription, and OP consult note)
 * for a given consultation when it's marked as completed.
 *
 * POST /api/fhir/generate-all-bundles
 *
 * Required body parameters:
 * - consultationId: The ID of the completed consultation
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    // if (!user || !user.organizationId) {
    //   return NextResponse.json(
    //     { error: "Unauthorized" },
    //     { status: 401 }
    //   );
    // }

    // Get request body
    const body = await req.json();
    const { consultationId } = body;

    // Validate required fields
    if (!consultationId) {
      return NextResponse.json(
        { error: "Missing required field: consultationId" },
        { status: 400 },
      );
    }

    // Fetch consultation data with related information
    const consultation = await db.consultation.findUnique({
      where: {
        id: consultationId,
        organizationId: user?.organizationId,
      },
      include: {
        patient: {
          include: {
            abhaProfile: true,
          },
        },
        doctor: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
        vitals: true,
        clinicalNotes: true,
        prescriptions: {
          include: {
            items: true,
          },
        },
        AllergyIntolerance: true,
        appointment: true,
        organization: true,
        branch: true,
        invoices: {
          include: {
            items: {
              orderBy: {
                sequence: "asc",
              },
            },
          },
        },
      },
    });

    if (!consultation) {
      return NextResponse.json(
        { error: "Consultation not found or access denied" },
        { status: 404 },
      );
    }

    // Check if consultation is completed (case-insensitive check)
    console.log("Consultation status:", consultation.status);

    if (consultation.status.toLowerCase() !== "completed") {
      return NextResponse.json(
        {
          error:
            "Consultation must be marked as completed before generating FHIR bundles",
          currentStatus: consultation.status,
          consultationId: consultationId,
        },
        { status: 400 },
      );
    }

    const { patient, doctor, organization, prescriptions } = consultation;

    // Get the current date and time
    const now = new Date();

    // Extract patient details
    const patientID = patient.abhaProfile?.abhaNumber || patient.id;
    const patientFirstName = patient.firstName || "Unknown";
    const patientLastName = patient.lastName || "Patient";
    const patientGender = mapGenderToFhir(patient.gender);
    const patientBirthDate = patient.dateOfBirth.toISOString().split("T")[0];
    const patientPhone = patient.phone || "";
    const patientEmail = patient.email || "";
    const patientAddress = patient.address || "";
    const patientCity = patient.city || "";
    const patientState = patient.state || "";
    const patientPostalCode = patient.pincode || "";
    const patientCountry = patient.country || "India";

    // Extract practitioner details
    const practitionerID = doctor.id;
    const practitionerName = doctor.user?.name || "Unknown Doctor";
    const practitionerQualification = doctor.qualification || "MBBS";

    // Extract organization details
    const organizationID = organization.id;
    const organizationName = organization.name;

    // Extract vitals data from consultation
    // Try to get actual vitals data from the consultation
    console.log("Checking vitals data...");
    console.log("Consultation vitals count:", consultation.vitals?.length || 0);

    const vitalData =
      consultation.vitals &&
      Array.isArray(consultation.vitals) &&
      consultation.vitals.length > 0
        ? consultation.vitals[0]
        : null;

    console.log("Vital data found:", !!vitalData);
    if (vitalData) {
      console.log("Vital data sample:", {
        pulse: vitalData.pulse,
        bloodPressureSystolic: vitalData.bloodPressureSystolic,
        temperature: vitalData.temperature,
        height: vitalData.height,
        weight: vitalData.weight,
      });
    }

    // Extract actual values without defaults - Basic Vital Signs
    const heartRate = vitalData?.pulse?.toString();
    const systolicBP = vitalData?.bloodPressureSystolic?.toString();
    const diastolicBP = vitalData?.bloodPressureDiastolic?.toString();
    const temperature = vitalData?.temperature?.toString();
    const respiratoryRate = vitalData?.respiratoryRate?.toString();
    const height = vitalData?.height?.toString();
    const weight = vitalData?.weight?.toString();
    const oxygenSaturation = vitalData?.oxygenSaturation?.toString();

    // Extract comprehensive wellness data - Body Measurements
    const bmi = vitalData?.bmi?.toString();
    const waistCircumference = (
      vitalData as any
    )?.waistCircumference?.toString();
    const hipCircumference = (vitalData as any)?.hipCircumference?.toString();
    const bodyFatPercentage = (vitalData as any)?.bodyFatPercentage?.toString();

    // Extract Physical Activity data
    const exerciseFrequency = (vitalData as any)?.exerciseFrequency;
    const exerciseDuration = (vitalData as any)?.exerciseDuration;
    const exerciseType = (vitalData as any)?.exerciseType;
    const physicalActivityLevel = (vitalData as any)?.physicalActivityLevel;

    // Extract General Assessment data
    const generalAppearance = (vitalData as any)?.generalAppearance;
    const mentalStatus = (vitalData as any)?.mentalStatus;
    const painLevel = (vitalData as any)?.painLevel;
    const mobilityStatus = (vitalData as any)?.mobilityStatus;

    // Extract Women Health data
    const menstrualCycle = (vitalData as any)?.menstrualCycle;
    const lastMenstrualPeriod = (vitalData as any)?.lastMenstrualPeriod;
    const pregnancyStatus = (vitalData as any)?.pregnancyStatus;
    const breastfeedingStatus = (vitalData as any)?.breastfeedingStatus;

    // Extract Lifestyle data
    const smokingStatus = (vitalData as any)?.smokingStatus;
    const alcoholConsumption = (vitalData as any)?.alcoholConsumption;
    const dietaryHabits = (vitalData as any)?.dietaryHabits;
    const sleepPattern = (vitalData as any)?.sleepPattern;
    const stressLevel = (vitalData as any)?.stressLevel;

    // Create a map of available vital signs for easier checking
    const availableVitals = {
      heartRate:
        heartRate !== undefined && heartRate !== null && heartRate !== "",
      bloodPressure:
        systolicBP !== undefined &&
        systolicBP !== null &&
        systolicBP !== "" &&
        diastolicBP !== undefined &&
        diastolicBP !== null &&
        diastolicBP !== "",
      temperature:
        temperature !== undefined && temperature !== null && temperature !== "",
      respiratoryRate:
        respiratoryRate !== undefined &&
        respiratoryRate !== null &&
        respiratoryRate !== "",
      oxygenSaturation:
        oxygenSaturation !== undefined &&
        oxygenSaturation !== null &&
        oxygenSaturation !== "",
      height: height !== undefined && height !== null && height !== "",
      weight: weight !== undefined && weight !== null && weight !== "",
    };

    console.log("Available vitals:", availableVitals);
    console.log("Raw vital values:", {
      heartRate,
      systolicBP,
      diastolicBP,
      temperature,
      respiratoryRate,
      oxygenSaturation,
      height,
      weight,
    });

    // Extract medication details from prescriptions
    // Use defaults if no prescriptions are available to ensure FHIR bundle generation works
    let medicationCode = "386864001"; // Default: Oral tablet
    let medicationDisplay = "Medication";
    let medicationText = "Medication";
    let dosageText = "As directed";

    if (
      prescriptions &&
      prescriptions.length > 0 &&
      prescriptions[0].items &&
      prescriptions[0].items.length > 0
    ) {
      const medicationItem = prescriptions[0].items[0];
      medicationCode = medicationItem.snomedCode || medicationCode;
      medicationDisplay = medicationItem.medicationName || medicationDisplay;
      medicationText = medicationItem.medicationName || medicationText;
      dosageText = `${medicationItem.dosage || "1"} ${medicationItem.frequency || "daily"}`;
    }

    // Extract condition details from clinical notes
    // Use defaults to ensure FHIR bundle generation works
    let conditionCode = "Z00.00"; // Default: General medical examination
    let conditionDisplay = "General consultation";
    let conditionText = "General consultation";

    if (consultation.clinicalNotes && consultation.clinicalNotes.length > 0) {
      const note = consultation.clinicalNotes[0];

      // Prioritize structured chiefComplaints field
      if (note.chiefComplaints && note.chiefComplaints.trim()) {
        conditionDisplay = note.chiefComplaints.substring(0, 100); // Limit length for display
        conditionText = note.chiefComplaints;
        conditionCode = "386661006"; // Clinical finding code
      }
      // Fallback to content field for backward compatibility
      else if (note.content && note?.content?.trim()) {
        conditionDisplay = note?.content?.substring(0, 100); // Limit length for display
        conditionText = note?.content;
        conditionCode = "386661006"; // Clinical finding code
      }

      // Override with SNOMED diagnoses if available (highest priority)
      if (note.snomedDiagnoses) {
        try {
          const diagnoses = JSON.parse(note.snomedDiagnoses);
          if (diagnoses && diagnoses.length > 0) {
            conditionCode = diagnoses[0].code || conditionCode;
            conditionDisplay = diagnoses[0].term || conditionDisplay;
            conditionText = diagnoses[0].term || conditionText;
          }
        } catch (e) {
          console.error("Error parsing SNOMED diagnoses:", e);
        }
      }
    }

    // Generate all FHIR bundles
    const bundles: {
      wellness: any;
      prescription: any;
      opConsultNote: any;
      invoice: any;
      dischargeSummary: any;
      diagnosticReport: any;
    } = {
      wellness: null,
      prescription: null,
      opConsultNote: null,
      invoice: null,
      dischargeSummary: null,
      diagnosticReport: null,
    };

    // 1. Generate a single Wellness Record bundle with all vital signs
    try {
      // Create a single wellness record with all available vital signs
      // First, check if any vital signs are available
      const hasAnyVitals = Object.values(availableVitals).some(
        (value) => value === true,
      );

      console.log("Has any vitals:", hasAnyVitals);
      console.log("Available vitals check:", Object.entries(availableVitals));

      if (hasAnyVitals) {
        console.log("✅ Generating wellness bundle with vitals");
        // Choose a primary vital sign for the main observation
        // Heart rate is commonly used, but we'll fall back to any available vital
        let primaryObservationCode = "8867-4"; // Default to heart rate
        let primaryObservationDisplay = "Heart rate";
        let primaryObservationText = "Heart rate";
        let primaryObservationValue = heartRate;
        let primaryObservationUnit = "beats/minute";
        let primaryObservationUnitCode = "/min";

        if (availableVitals.heartRate) {
          // Already set to heart rate by default
        } else if (availableVitals.bloodPressure) {
          primaryObservationCode = "85354-9";
          primaryObservationDisplay = "Blood pressure";
          primaryObservationText = "Blood pressure";
          primaryObservationValue = systolicBP;
          primaryObservationUnit = "mmHg";
          primaryObservationUnitCode = "mm[Hg]";
        } else if (availableVitals.temperature) {
          primaryObservationCode = "8310-5";
          primaryObservationDisplay = "Body temperature";
          primaryObservationText = "Body temperature";
          primaryObservationValue = temperature;
          primaryObservationUnit = "Cel";
          primaryObservationUnitCode = "Cel";
        } else if (availableVitals.respiratoryRate) {
          primaryObservationCode = "9279-1";
          primaryObservationDisplay = "Respiratory rate";
          primaryObservationText = "Respiratory rate";
          primaryObservationValue = respiratoryRate;
          primaryObservationUnit = "breaths/minute";
          primaryObservationUnitCode = "/min";
        } else if (availableVitals.oxygenSaturation) {
          primaryObservationCode = "2708-6";
          primaryObservationDisplay = "Oxygen saturation";
          primaryObservationText = "Oxygen saturation";
          primaryObservationValue = oxygenSaturation;
          primaryObservationUnit = "%";
          primaryObservationUnitCode = "%";
        } else if (availableVitals.height) {
          primaryObservationCode = "8302-2";
          primaryObservationDisplay = "Body height";
          primaryObservationText = "Body height";
          primaryObservationValue = height;
          primaryObservationUnit = "cm";
          primaryObservationUnitCode = "cm";
        } else if (availableVitals.weight) {
          primaryObservationCode = "29463-7";
          primaryObservationDisplay = "Body weight";
          primaryObservationText = "Body weight";
          primaryObservationValue = weight;
          primaryObservationUnit = "kg";
          primaryObservationUnitCode = "kg";
        }

        // Create a vital signs summary for the observation note
        const vitalSignsSummaryText = [
          availableVitals.heartRate ? `HR: ${heartRate} bpm` : null,
          availableVitals.bloodPressure
            ? `BP: ${systolicBP}/${diastolicBP} mmHg`
            : null,
          availableVitals.temperature ? `Temp: ${temperature}°C` : null,
          availableVitals.respiratoryRate
            ? `RR: ${respiratoryRate} breaths/min`
            : null,
          availableVitals.oxygenSaturation
            ? `SpO2: ${oxygenSaturation}%`
            : null,
          availableVitals.height ? `Height: ${height} cm` : null,
          availableVitals.weight ? `Weight: ${weight} kg` : null,
        ]
          .filter(Boolean)
          .join(", ");

        // Generate a single wellness record with all vital signs using our custom strategy
        const wellnessParams = {
          // Composition details
          compositionIdentifier: `wellness-${consultationId}`,
          compositionDate: now.toISOString(),
          compositionTitle: "Wellness Record",

          // Patient details
          patientID,
          patientFirstName,
          patientLastName,
          patientGender,
          patientBirthDate,
          patientPhone,
          patientEmail,
          patientAddress,
          patientCity,
          patientState,
          patientPostalCode,
          patientCountry,

          // Practitioner details
          practitionerID,
          practitionerName,
          practitionerQualification,

          // Organization details
          organizationID,
          organizationName,

          // Encounter details
          encounterID: consultationId,
          encounterDate: consultation.consultationDate.toISOString(),
          encounterType: "Wellness check",

          // Primary Observation (required fields)
          observationCode: primaryObservationCode,
          observationDisplay: primaryObservationDisplay,
          observationText: primaryObservationText,
          observationEffectiveDateTime:
            consultation.consultationDate.toISOString(),
          observationValueQuantity: Number(primaryObservationValue),
          observationValueUnit: primaryObservationUnit,
          observationValueSystem: "http://unitsofmeasure.org",
          observationValueCode: primaryObservationUnitCode,

          // Add a note with all vital signs
          observationNote: vitalSignsSummaryText,
        };

        // Create an object with all comprehensive wellness data
        const vitalsData = {
          // Basic Vital Signs
          heartRate: availableVitals.heartRate ? heartRate : undefined,
          systolicBP: availableVitals.bloodPressure ? systolicBP : undefined,
          diastolicBP: availableVitals.bloodPressure ? diastolicBP : undefined,
          temperature: availableVitals.temperature ? temperature : undefined,
          respiratoryRate: availableVitals.respiratoryRate
            ? respiratoryRate
            : undefined,
          oxygenSaturation: availableVitals.oxygenSaturation
            ? oxygenSaturation
            : undefined,
          height: availableVitals.height ? height : undefined,
          weight: availableVitals.weight ? weight : undefined,

          // Body Measurements
          bmi: bmi,
          waistCircumference: waistCircumference,
          hipCircumference: hipCircumference,
          bodyFatPercentage: bodyFatPercentage,

          // Physical Activity
          exerciseFrequency: exerciseFrequency,
          exerciseDuration: exerciseDuration,
          exerciseType: exerciseType,
          physicalActivityLevel: physicalActivityLevel,

          // General Assessment
          generalAppearance: generalAppearance,
          mentalStatus: mentalStatus,
          painLevel: painLevel,
          mobilityStatus: mobilityStatus,

          // Women Health
          menstrualCycle: menstrualCycle,
          lastMenstrualPeriod: lastMenstrualPeriod,
          pregnancyStatus: pregnancyStatus,
          breastfeedingStatus: breastfeedingStatus,

          // Lifestyle
          smokingStatus: smokingStatus,
          alcoholConsumption: alcoholConsumption,
          dietaryHabits: dietaryHabits,
          sleepPattern: sleepPattern,
          stressLevel: stressLevel,
        };

        // Generate the wellness record with all vital signs
        bundles.wellness = await generateCustomWellnessBundle(
          wellnessParams,
          vitalsData,
        );

        // Add a summary of available vital signs as metadata
        const vitalSignsSummary: any = {};

        if (availableVitals.heartRate) {
          vitalSignsSummary.heartRate = Number(heartRate);
        }

        if (availableVitals.bloodPressure) {
          vitalSignsSummary.systolicBP = Number(systolicBP);
          vitalSignsSummary.diastolicBP = Number(diastolicBP);
        }

        if (availableVitals.temperature) {
          vitalSignsSummary.temperature = Number(temperature);
        }

        if (availableVitals.respiratoryRate) {
          vitalSignsSummary.respiratoryRate = Number(respiratoryRate);
        }

        if (availableVitals.oxygenSaturation) {
          vitalSignsSummary.oxygenSaturation = Number(oxygenSaturation);
        }

        if (availableVitals.height) {
          vitalSignsSummary.height = Number(height);
        }

        if (availableVitals.weight) {
          vitalSignsSummary.weight = Number(weight);
        }

        bundles.wellness.vitalSigns = vitalSignsSummary;
      } else {
        console.log(
          "❌ No vital signs available, skipping wellness record generation",
        );
        console.log("Vitals availability check failed:", {
          hasVitalData: !!vitalData,
          availableVitals,
          rawValues: {
            heartRate,
            systolicBP,
            diastolicBP,
            temperature,
            respiratoryRate,
            oxygenSaturation,
            height,
            weight,
          },
        });
      }
    } catch (error) {
      console.error("Error generating wellness record bundle:", error);
    }

    // 2. Generate Prescription bundle with all medications from the consultation
    try {
      // Create base parameters for the prescription
      const prescriptionParams = {
        // Composition details
        compositionIdentifier: `prescription-${consultationId}`,
        compositionDate: now.toISOString(),
        compositionTitle: "Prescription",

        // Patient details
        patientID,
        patientFirstName,
        patientLastName,
        patientGender,
        patientBirthDate,
        patientPhone,
        patientEmail,
        patientAddress,
        patientCity,
        patientState,
        patientPostalCode,
        patientCountry,

        // Patient ABHA Profile
        patientAbhaProfile: patient.abhaProfile ? {
          id: patient.abhaProfile.id, // Include the required id field
          abhaNumber: patient.abhaProfile.abhaNumber,
          abhaAddress: patient.abhaProfile.abhaAddress,
          healthIdNumber: patient.abhaProfile.healthIdNumber,
        } : undefined,

        // Practitioner details
        practitionerID,
        practitionerName,
        practitionerQualification,

        // Organization details
        organizationID,
        organizationName,
        organizationAddress: consultation.branch?.address || "",
        organizationPhone: consultation.branch?.phone || "",
        organizationEmail: consultation.branch?.email || "",

        // Branch details
        branchID: consultation.branch?.id,
        branchName: consultation.branch?.name || "Default Branch",

        // Encounter details
        encounterID: consultationId,
        encounterDate: consultation.consultationDate.toISOString(),
        encounterType: "Outpatient consultation",

        // First medication (required fields)
        medicationCode,
        medicationDisplay,
        medicationText,
        medicationAuthoredOn: now.toISOString(),
        medicationStatus: "active",
        medicationIntent: "order",
        dosageText,
        dosageRouteCode: "26643006",
        dosageRoute: "Oral route",
        dosageMethodCode: "421521009",
        dosageMethod: "Swallow",
        dosageFrequency: 2,
        dosagePeriodValue: 1,
        dosagePeriodUnit: "d",
        dosageAsNeeded: false,
        dosageAsNeededCode: "266599000",
        dosageAsNeededReason: conditionText || "As needed for symptoms",
      };

      // Extract all medications from prescriptions
      const allMedications = [];

      if (prescriptions && prescriptions.length > 0) {
        // Iterate through all prescriptions
        for (const prescription of prescriptions) {
          if (prescription.items && Array.isArray(prescription.items)) {
            // Add each medication item to the array
            for (const item of prescription.items) {
              allMedications.push({
                snomedCode: item.snomedCode,
                medicationName: item.medicationName,
                medicationText: item.medicationName,
                dosage: item.dosage,
                frequency: item.frequency,
                duration: item.duration,
                notes: item.instructions,
              });
            }
          }
        }
      }

      // If no medications were found, add a default one
      if (allMedications.length === 0) {
        allMedications.push({
          snomedCode: medicationCode,
          medicationName: medicationDisplay,
          medicationText: medicationText,
          dosage: "As directed",
          frequency: "",
          duration: "",
          notes: "",
        });
      }

      // Generate the prescription bundle with all medications
      bundles.prescription = await generateCustomPrescriptionBundle(
        prescriptionParams,
        allMedications,
      );

      // No database storage - just keep the JSON object in memory
    } catch (error) {
      console.error("Error generating prescription bundle:", error);
    }

    // 3. Generate OP Consult Note bundle
    try {
      // Create the base parameters for the OP consult note
      const opConsultParams: any = {
        // Composition details
        compositionIdentifier: `op-consult-note-${consultationId}`,
        compositionDate: now.toISOString(),
        compositionTitle: "OP Consultation",

        // Patient details
        patientID,
        patientFirstName,
        patientLastName,
        patientGender,
        patientBirthDate,
        patientPhone,
        patientEmail,
        patientAddress,
        patientCity,
        patientState,
        patientPostalCode,
        patientCountry,

        // Practitioner details
        practitionerID,
        practitionerName,
        practitionerQualification,

        // Organization details
        organizationID,
        organizationName,

        // Encounter details
        encounterID: consultationId,
        encounterDate: consultation.consultationDate.toISOString(),
        encounterType: "Outpatient consultation",

        // Observation (Vital Signs) - required fields
        observationCode: "8867-4",
        observationDisplay: "Heart rate",
        observationText: "Heart rate",
        observationEffectiveDateTime:
          consultation.consultationDate.toISOString(),
        observationValueQuantity: heartRate ? Number(heartRate) : undefined,
        observationValueUnit: "beats/minute",
        observationValueSystem: "http://unitsofmeasure.org",
        observationValueCode: "/min",

        // Clinical Notes - comprehensive observation note from structured fields
        observationNote:
          consultation.clinicalNotes && consultation.clinicalNotes.length > 0
            ? [
                consultation.clinicalNotes[0].chiefComplaints &&
                  `Chief Complaints: ${consultation.clinicalNotes[0].chiefComplaints}`,
                consultation.clinicalNotes[0].allergies &&
                  `Allergies: ${consultation.clinicalNotes[0].allergies}`,
                consultation.clinicalNotes[0].medicalHistory &&
                  `Medical History: ${consultation.clinicalNotes[0].medicalHistory}`,
                consultation.clinicalNotes[0].investigationAdvice &&
                  `Investigation Advice: ${consultation.clinicalNotes[0].investigationAdvice}`,
                consultation.clinicalNotes[0].procedure &&
                  `Procedure: ${consultation.clinicalNotes[0].procedure}`,
                consultation.clinicalNotes[0].followUp &&
                  `Follow Up: ${consultation.clinicalNotes[0].followUp}`,
                consultation.clinicalNotes[0].content &&
                  `Additional Notes: ${consultation.clinicalNotes[0].content}`,
              ]
                .filter(Boolean)
                .join(". ") || "No clinical notes available"
            : "No clinical notes available",

        // Medication (always include required fields)
        medicationCode,
        medicationDisplay,
        medicationText,
        medicationAuthoredOn: now.toISOString(),
        medicationStatus: "active",
        medicationIntent: "order",
        dosageText,
        dosageRouteCode: "26643006",
        dosageRoute: "Oral route",
        dosageMethodCode: "421521009",
        dosageMethod: "Swallow",
        dosageFrequency: 2,
        dosagePeriodValue: 1,
        dosagePeriodUnit: "d",
        dosageAsNeeded: false,
        dosageAsNeededCode: "266599000",
        dosageAsNeededReason: conditionText || "As needed for symptoms",

        // Diagnostic Report (required fields)
        reportCode: conditionCode || "24331-1",
        reportDisplay: conditionDisplay || "Consultation Report",
        reportText: conditionText || "Consultation Report",
        reportEffectiveDateTime: consultation.consultationDate.toISOString(),
        reportIssuedDateTime: now.toISOString(),
        reportStatus: "final",
        reportCategory: "LAB",
        reportCategoryDisplay: "Laboratory",
        reportConclusion:
          consultation.clinicalNotes && consultation.clinicalNotes.length > 0
            ? [
                consultation.clinicalNotes[0].investigationAdvice &&
                  `Investigation Advice: ${consultation.clinicalNotes[0].investigationAdvice}`,
                consultation.clinicalNotes[0].procedure &&
                  `Procedure: ${consultation.clinicalNotes[0].procedure}`,
                consultation.clinicalNotes[0].medicalHistory &&
                  `Medical History: ${consultation.clinicalNotes[0].medicalHistory}`,
                consultation.clinicalNotes[0].content &&
                  `Additional Notes: ${consultation.clinicalNotes[0].content}`,
              ]
                .filter(Boolean)
                .join(". ") || "Based on clinical findings"
            : "Based on clinical findings",
      };

      // Always add condition details (required for FHIR bundle generation)
      Object.assign(opConsultParams, {
        // Chief complaint (Condition) - required fields
        conditionCode,
        conditionDisplay,
        conditionText,
        conditionRecordedDate: consultation.consultationDate.toISOString(),
        conditionOnsetDate: consultation.consultationDate.toISOString(),
        clinicalStatus: "active",
        clinicalStatusDisplay: "Active",
        verificationStatus: "confirmed",
        verificationStatusDisplay: "Confirmed",
        conditionClinicalStatus: "active",
        conditionClinicalStatusDisplay: "Active",
        conditionVerificationStatus: "confirmed",
        conditionVerificationStatusDisplay: "Confirmed",
        conditionCategory: "problem-list-item",
        conditionCategoryDisplay: "Problem List Item",
        conditionSeverityCode: "6736007",
        conditionSeverity: "Moderate",
      });

      // Add vital signs summary with all available vital signs
      const vitalSignsSummaryText =
        [
          availableVitals.heartRate ? `HR: ${heartRate} bpm` : null,
          availableVitals.bloodPressure
            ? `BP: ${systolicBP}/${diastolicBP} mmHg`
            : null,
          availableVitals.temperature ? `Temp: ${temperature}°C` : null,
          availableVitals.respiratoryRate
            ? `RR: ${respiratoryRate} breaths/min`
            : null,
          availableVitals.oxygenSaturation
            ? `SpO2: ${oxygenSaturation}%`
            : null,
          availableVitals.height ? `Height: ${height} cm` : null,
          availableVitals.weight ? `Weight: ${weight} kg` : null,
        ]
          .filter(Boolean)
          .join(", ") || "No vital signs recorded";

      Object.assign(opConsultParams, {
        vitalSignsSummary: vitalSignsSummaryText,

        // Document Reference
        documentTitle: "OP Consultation Report",
        documentDescription: "Outpatient consultation report",
        documentCreated: now.toISOString(),
        documentContentType: "application/pdf",
        documentUrl: `https://example.com/documents/consultation-${consultationId}.pdf`,

        // Set empty appointment values to remove follow-up appointment
        appointmentDescription: "",
        appointmentStart: "",
        appointmentEnd: "",
        appointmentCreated: "",

        // Structured clinical notes fields for proper FHIR mapping
        chiefComplaints: consultation.clinicalNotes?.[0]?.chiefComplaints,
        allergies: consultation.clinicalNotes?.[0]?.allergies,
        medicalHistory: consultation.clinicalNotes?.[0]?.medicalHistory,
        investigationAdvice:
          consultation.clinicalNotes?.[0]?.investigationAdvice,
        procedure: consultation.clinicalNotes?.[0]?.procedure,
        followUp: consultation.clinicalNotes?.[0]?.followUp,
        additionalNotes: consultation.clinicalNotes?.[0]?.content,
      });

      // Generate the OP consult note using our custom strategy that removes follow-up appointment
      bundles.opConsultNote =
        await generateCustomOpConsultBundle(opConsultParams);

      // No database storage - just keep the JSON object in memory
    } catch (error) {
      console.error("Error generating OP consult note bundle:", error);
    }

    // 4. Generate Invoice bundle (if invoice exists)
    try {
      console.log("Checking for invoice in consultation...");
      console.log("Consultation invoice exists:", !!consultation.invoices);

      // Check if invoice exists for this consultation (singular relationship)
      if (consultation.invoices) {
        const invoice = consultation.invoices; // Direct access since it's singular
        console.log(
          "Found invoice for bundle generation:",
          invoice.invoiceNumber,
        );

        // Create the base parameters for the invoice bundle
        const invoiceParams = {
          // Composition details
          compositionIdentifier: `invoice-${consultationId}`,
          compositionDate: now.toISOString(),
          compositionTitle: "Healthcare Invoice Record",

          // Patient details
          patientID,
          patientFirstName,
          patientLastName,
          patientGender,
          patientBirthDate,
          patientPhone,
          patientEmail,
          patientAddress,
          patientCity,
          patientState,
          patientPostalCode,
          patientCountry,

          // Practitioner details
          practitionerID,
          practitionerName,
          practitionerQualification,

          // Organization details
          organizationID,
          organizationName,
        };

        // Prepare invoice data
        const invoiceData = {
          invoiceNumber: invoice.invoiceNumber,
          invoiceDate: invoice.invoiceDate.toISOString(),
          status: invoice.status,
          type: invoice.type,
          subtotal: invoice.subtotal.toString(),
          taxAmount: invoice.taxAmount.toString(),
          discountAmount: invoice.discountAmount.toString(),
          totalAmount: invoice.totalAmount.toString(),
          currency: invoice.currency,
          items:
            invoice.items?.map((item: any) => ({
              sequence: item.sequence,
              serviceCode: item.serviceCode,
              serviceDisplay: item.serviceDisplay,
              category: item.category,
              quantity: item.quantity.toString(),
              unitPrice: item.unitPrice.toString(),
              mrp: item.mrp?.toString(),
              discountAmount: item.discountAmount.toString(),
              taxRate: item.taxRate.toString(),
              cgstAmount: item.cgstAmount.toString(),
              sgstAmount: item.sgstAmount.toString(),
              igstAmount: item.igstAmount.toString(),
              totalAmount: item.totalAmount.toString(),
              notes: item.notes,
            })) || [],
        };

        // Generate the invoice bundle
        bundles.invoice = await generateCustomInvoiceBundle(
          invoiceParams,
          invoiceData,
        );

        console.log("Invoice bundle generated successfully");
      } else {
        console.log(
          "No invoice found for this consultation, skipping invoice bundle generation",
        );
      }
    } catch (error) {
      console.error("Error generating invoice bundle:", error);
    }

    // 5. Generate Discharge Summary Bundle (if discharge summaries exist)
    try {
      const dischargeSummaries = await db.documentReference.findMany({
        where: {
          consultationId: consultationId,
          type: "discharge-summary",
        },
        include: {
          consultation: {
            include: {
              patient: true,
              doctor: {
                include: {
                  user: true,
                },
              },
            },
          },
        },
      });

      if (dischargeSummaries.length > 0) {
        const dischargeSummary = dischargeSummaries[0];
        const content = dischargeSummary.content as any;

        const dischargeSummaryParams = {
          // Composition details
          compositionIdentifier: `discharge-summary-${dischargeSummary.id}`,
          compositionDate: now.toISOString(),
          compositionTitle: "Discharge Summary",

          // Patient details
          patientID,
          patientFirstName,
          patientLastName,
          patientGender,
          patientBirthDate,
          patientPhone,
          patientEmail,
          patientAddress,
          patientCity,
          patientState,
          patientPostalCode,
          patientCountry,

          // Practitioner details
          practitionerID,
          practitionerName,
          practitionerQualification,

          // Organization details
          organizationID,
          organizationName,

          // Discharge summary specific data
          admissionDate:
            content?.admissionDate ||
            consultation.consultationDate.toISOString().split("T")[0],
          dischargeDate:
            content?.dischargeDate || now.toISOString().split("T")[0],
          chiefComplaint: content?.chiefComplaint,
          medicalHistory: content?.medicalHistory,
          investigations: content?.investigations,
          procedures: content?.proceduresPerformed,
          medications: content?.medicationsList,
          carePlan: content?.carePlan,
          dischargeInstructions: content?.dischargeInstructions,
          followUpInstructions: content?.recommendations,

          // Required condition fields for FHIR bundle generation
          conditionCode: "Z51.11",
          conditionDisplay: "Encounter for antineoplastic chemotherapy",
          conditionText: content?.condition || "General medical condition",
          clinicalStatus: "active",
          clinicalStatusDisplay: "Active",
          verificationStatus: "confirmed",
          verificationStatusDisplay: "Confirmed",
        };

        bundles.dischargeSummary = await generateResource(
          ResourceType.REPORT_DISCHARGE_SUMMARY,
          dischargeSummaryParams,
        );

        console.log("Discharge summary bundle generated successfully");
      } else {
        console.log(
          "No discharge summaries found for this consultation, skipping discharge summary bundle generation",
        );
      }
    } catch (error) {
      console.error("Error generating discharge summary bundle:", error);
    }

    // 6. Generate Diagnostic Report Bundle (if diagnostic reports exist)
    try {
      const diagnosticReports = await db.diagnosticReport.findMany({
        where: {
          consultationId: consultationId,
        },
        include: {
          consultation: {
            include: {
              patient: true,
              doctor: {
                include: {
                  user: true,
                },
              },
            },
          },
        },
      });

      if (diagnosticReports.length > 0) {
        const diagnosticReport = diagnosticReports[0];

        const diagnosticReportParams = {
          // Composition details
          compositionIdentifier: `diagnostic-report-${diagnosticReport.id}`,
          compositionDate: now.toISOString(),
          compositionTitle: "Diagnostic Report",

          // Patient details
          patientID,
          patientFirstName,
          patientLastName,
          patientGender,
          patientBirthDate,
          patientPhone,
          patientEmail,
          patientAddress,
          patientCity,
          patientState,
          patientPostalCode,
          patientCountry,

          // Practitioner details
          practitionerID,
          practitionerName,
          practitionerQualification,

          // Organization details
          organizationID,
          organizationName,

          // Diagnostic report specific data
          reportCode: diagnosticReport.code || "33747-0",
          reportDisplay:
            diagnosticReport.codeDisplay || "General laboratory test",
          reportText: diagnosticReport.codeDisplay || "Laboratory test result",
          reportEffectiveDateTime: diagnosticReport.effectiveDate.toISOString(),
          reportIssuedDateTime: diagnosticReport.issuedDate.toISOString(),
          reportStatus: diagnosticReport.status || "final",
          reportCategory: diagnosticReport.category || "LAB",
          reportCategoryDisplay: "Laboratory",
          reportConclusion: diagnosticReport.conclusion,

          // Required diagnostic report fields for FHIR bundle generation
          status: diagnosticReport.status || "final",
          category: diagnosticReport.category || "LAB",
          categoryDisplay: "Laboratory",

          // Required observation fields
          observationCode: diagnosticReport.code || "33747-0",
          observationDisplay:
            diagnosticReport.codeDisplay || "General laboratory test",
          observationText:
            diagnosticReport.codeDisplay || "Laboratory test result",
          observationEffectiveDateTime:
            diagnosticReport.effectiveDate.toISOString(),
          observationValueQuantity: parseFloat(
            String(diagnosticReport.result) || "0",
          ),
          observationValueUnit: "unit",
          observationValueSystem: "http://unitsofmeasure.org",
          observationValueCode: "unit",
          observationStatus: "final",
          observationNote: diagnosticReport.conclusion,
        };

        bundles.diagnosticReport = await generateResource(
          ResourceType.REPORT_DIAGNOSTIC,
          diagnosticReportParams,
        );
      } else {
        console.log(
          "No diagnostic reports found for this consultation, skipping diagnostic report bundle generation",
        );
      }
    } catch (error) {
      console.error("❌ Failed to auto-generate FHIR bundles for lab report", {
        consultationId,
        error: error instanceof Error ? error.message : String(error),
      });
      console.error("Full error details:", error);
    }

    // Generate PDF binaries and add them to bundles
    try {
      // Prepare consultation data for PDF generation
      // const consultationData = {
      //   ...consultation,
      //   organization: consultation.organization,
      //   branch: consultation.organization || null, // Add branch property
      // };

      // PDF generation removed to avoid React errors in production
      console.log(
        "Skipping PDF generation to avoid React errors in production",
      );

      // Invoice PDF generation removed to avoid React errors in production
      console.log(
        "Skipping invoice PDF generation to avoid React errors in production",
      );

      // Combined PDF generation removed to avoid React errors in production
      console.log(
        "Skipping combined PDF generation to avoid React errors in production",
      );

      // Return all generated bundles as separate JSON objects in the response
      console.log("Final bundle generation results:", {
        hasWellness: !!bundles.wellness,
        hasPrescription: !!bundles.prescription,
        hasOpConsult: !!bundles.opConsultNote,
        hasInvoice: !!bundles.invoice,
        hasDischargeSummary: !!bundles.dischargeSummary,
        hasDiagnosticReport: !!bundles.diagnosticReport,
      });

      return NextResponse.json({
        success: true,
        message: "FHIR bundles generated successfully",
        wellnessRecord: bundles.wellness || null,
        prescription: bundles.prescription || null,
        opConsultNote: bundles.opConsultNote || null,
        invoice: bundles.invoice || null,
        dischargeSummary: bundles.dischargeSummary || null,
        diagnosticReport: bundles.diagnosticReport || null,
      });
    } catch (error) {
      console.error("Error generating FHIR bundles:", error);
      return NextResponse.json(
        {
          error: "Failed to generate FHIR bundles",
          details: error instanceof Error ? error.message : String(error),
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Error generating FHIR bundles:", error);
    return NextResponse.json(
      {
        error: "Failed to generate FHIR bundles",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
