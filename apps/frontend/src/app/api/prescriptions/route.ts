export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";

// GET /api/prescriptions - Get all prescriptions
export async function GET(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // For demo purposes, get the organization from cookies
    const userInfoCookie = cookies().get("user-info")?.value;
    let organizationId = "";

    if (userInfoCookie) {
      try {
        const userInfo = JSON.parse(userInfoCookie);
        organizationId = userInfo.organizationId || "";
      } catch (error) {
        console.error("Error parsing user info:", error);
      }
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "No organization found" },
        { status: 404 },
      );
    }

    // Get query parameters
    const url = new URL(req.url);
    const patientId = url.searchParams?.get("patientId");
    const doctorId = url.searchParams?.get("doctorId");
    const consultationId = url.searchParams?.get("consultationId");
    const status = url.searchParams?.get("status");
    const fromDate = url.searchParams?.get("fromDate");
    const toDate = url.searchParams?.get("toDate");

    // Build the query
    const where: any = {
      organizationId,
    };

    // Add filters if provided
    if (patientId) {
      where.patientId = patientId;
    }

    if (doctorId) {
      where.doctorId = doctorId;
    }

    if (consultationId) {
      where.consultationId = consultationId;
    }

    if (status) {
      where.status = status;
    }

    // Add date range filter if provided
    if (fromDate || toDate) {
      where.prescriptionDate = {};

      if (fromDate) {
        where.prescriptionDate.gte = new Date(fromDate);
      }

      if (toDate) {
        where.prescriptionDate.lte = new Date(toDate);
      }
    }

    // If the user is a doctor, only show their prescriptions
    if (user.role === "doctor") {
      const doctor = await db.doctor.findFirst({
        where: {
          userId: user.id,
        },
      });

      if (!doctor) {
        return NextResponse.json(
          { error: "Doctor profile not found" },
          { status: 404 },
        );
      }

      where.doctorId = doctor.id;
    }

    // Get the prescriptions
    const prescriptions = await db.prescription.findMany({
      where,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
        consultation: {
          select: {
            id: true,
            consultationDate: true,
            status: true,
          },
        },
        items: true,
      },
      orderBy: {
        prescriptionDate: "desc",
      },
    });

    return NextResponse.json({ prescriptions });
  } catch (error) {
    console.error("Error fetching prescriptions:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

// POST /api/prescriptions - Create a new prescription
export async function POST(req: NextRequest) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // For demo purposes, get the organization from cookies
    const userInfoCookie = cookies().get("user-info")?.value;
    let organizationId = "";

    if (userInfoCookie) {
      try {
        const userInfo = JSON.parse(userInfoCookie);
        organizationId = userInfo.organizationId || "";
      } catch (error) {
        console.error("Error parsing user info:", error);
      }
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "No organization found" },
        { status: 404 },
      );
    }

    // Get the prescription data from the request
    const {
      consultationId,
      patientId,
      doctorId,
      prescriptionDate,
      validUntil,
      instructions,
      status,
      items,
    } = await req.json();

    // Debug: Log the received items to see dosage values
    console.log("🔍 PRESCRIPTION API - Received items:");
    items?.forEach((item: any, index: number) => {
      console.log(`📋 Item ${index + 1}:`, {
        medicationName: item.medicationName,
        dosage: item.dosage,
        timing: item.timing,
        frequency: item.frequency
      });
    });

    // Validate required fields
    if (!consultationId || !patientId || !doctorId || !items || !items.length) {
      return NextResponse.json(
        {
          error:
            "Consultation, patient, doctor, and at least one medication item are required",
        },
        { status: 400 },
      );
    }

    // Check if the consultation exists
    const consultation = await db.consultation.findUnique({
      where: {
        id: consultationId,
      },
    });

    if (!consultation) {
      return NextResponse.json(
        { error: "Consultation not found" },
        { status: 404 },
      );
    }

    // // If the user is a doctor, check if they are the doctor for this consultation
    // if (user.role === "doctor") {
    //   const doctor = await db.doctor.findFirst({
    //     where: {
    //       userId: user.id,
    //     },
    //   });

    //   if (!doctor || doctor.id !== doctorId) {
    //     return NextResponse.json(
    //       {
    //         error:
    //           "Forbidden: You are not authorized to create prescriptions for this patient",
    //       },
    //       { status: 403 },
    //     );
    //   }
    // }

    // Create the prescription with items
    const prescription = await db.prescription.create({
      data: {
        consultationId,
        patientId,
        doctorId,
        organizationId,
        prescriptionDate: prescriptionDate
          ? new Date(prescriptionDate)
          : new Date(),
        validUntil: validUntil ? new Date(validUntil) : undefined,
        instructions,
        status: status || "active",
        items: {
          create: items.map((item: any) => ({
            medicationName: item.medicationName,
            dosage: item.dosage,
            frequency: item.timing || item.frequency || "1-1-D", // Map timing to frequency for database
            duration: item.duration,
            route: item.route && item.route.trim() ? item.route : null,
            method: item.method && item.method.trim() ? item.method : null, // Add method field
            instructions:
              item.instructions && item.instructions.trim()
                ? item.instructions
                : null,
            reason: item.reason && item.reason.trim() ? item.reason : null, // Add reason field
            snomedCode: item.snomedCode,
            rxcui: item.rxcui || item.rxNormData?.rxcui,
            rxNormData: item.rxNormData
              ? JSON.stringify(item.rxNormData)
              : null,
          })),
        },
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
        consultation: {
          select: {
            id: true,
            consultationDate: true,
            status: true,
          },
        },
        items: true,
      },
    });

    // Following OPConsult pattern: Call external bundle API and store FHIR bundle
    try {
      const {
        generatePrescriptionPdfBase64,
        getUploadedDocumentsBase64,
        buildPrescriptionBundlePayload,
        callExternalBundleApi,
        storeFhirBundleInDatabase,
      } = await import("@/lib/bundle-utils");

      // Check if Prescription bundle already exists for this consultation
      const existingBundle = await db.fhirBundle.findFirst({
        where: {
          consultationId,
          bundleType: "Prescription",
        },
      });

      if (existingBundle) {
        console.log("⚠️ Prescription bundle already exists, skipping external API call", {
          consultationId,
          existingBundleId: existingBundle.bundleId,
        });
      } else {
        // Fetch full consultation data for bundle generation
        const consultation = await db.consultation.findUnique({
          where: {
            id: consultationId,
            organizationId,
          },
          include: {
            patient: true,
            doctor: {
              include: {
                user: true,
              },
            },
            organization: true,
            branch: {
              include: {
                organization: true,
              },
            },
            clinicalNotes: {
              orderBy: {
                createdAt: "desc",
              },
              take: 1,
            },
            vitals: {
              orderBy: {
                recordedAt: "desc",
              },
              take: 1,
            },
            prescriptions: {
              include: {
                items: true,
              },
              orderBy: {
                createdAt: "desc",
              },
              take: 1,
            },
            Procedure: true,
            DiagnosticReport: true,
          },
        });

        if (!consultation) {
          throw new Error("Consultation not found");
        }

        // Generate prescription PDF specifically for this bundle
        const generatedPdfBase64 = await generatePrescriptionPdfBase64(consultationId);

        // Get uploaded documents for Prescription
        const uploadedDocuments = await getUploadedDocumentsBase64(consultationId, 'Prescription');

        // Build the prescription bundle payload
        const bundlePayload = buildPrescriptionBundlePayload(
          consultation,
          generatedPdfBase64,
          uploadedDocuments
        );

        console.log("🚀 CALLING EXTERNAL API for Prescription...");
        const bundleApiResponse = await callExternalBundleApi(bundlePayload, 'Prescription');
        console.log("📡 EXTERNAL API RESPONSE RECEIVED:", {
          hasResponse: !!bundleApiResponse,
          responseType: typeof bundleApiResponse,
          responseKeys: bundleApiResponse ? Object.keys(bundleApiResponse) : [],
        });

        // Store the returned FHIR bundle in the database
        if (bundleApiResponse) {
          console.log("🔍 PROCESSING BUNDLE RESPONSE...");
          const fhirBundle = bundleApiResponse.fhirBundle || bundleApiResponse;
          console.log("📦 FHIR BUNDLE TO STORE:", {
            bundleSize: JSON.stringify(fhirBundle).length,
            bundleKeys: Object.keys(fhirBundle || {}),
          });

          console.log("💾 CALLING storeFhirBundleInDatabase...");
          const storedBundle = await storeFhirBundleInDatabase(
            fhirBundle,
            consultationId,
            consultation.patientId,
            organizationId,
            "Prescription"
          );

          console.log("✅ Successfully stored Prescription FHIR bundle:", {
            bundleId: storedBundle.bundleId,
            bundleType: storedBundle.bundleType,
          });
        }
      }
    } catch (error) {
      console.error("❌ Failed to call external prescription bundle API", {
        consultationId,
        prescriptionId: prescription.id,
        error: error instanceof Error ? error.message : String(error),
      });
    }

    console.log("✅ Prescription saved successfully", {
      consultationId,
      prescriptionId: prescription.id,
    });

    return NextResponse.json({
      prescription,
      message: "Prescription created successfully",
    });
  } catch (error) {
    console.error("Error creating prescription:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
