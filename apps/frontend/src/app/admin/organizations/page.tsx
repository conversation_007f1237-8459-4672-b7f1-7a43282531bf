"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@workspace/ui/components/card";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Badge } from "@workspace/ui/components/badge";

import { SearchIcon, BuildingIcon, CalendarIcon } from "lucide-react";
import { toast } from "sonner";

interface Organization {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  createdAt: string;
  updatedAt: string;
  onboardingCompleted: boolean;
  status: "active" | "inactive";
  _count?: {
    users: number;
    branches: number;
  };
}

export default function OrganizationsPage() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "active" | "inactive"
  >("all");
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);

  // Fetch organizations
  const fetchOrganizations = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/organizations");
      if (response.ok) {
        const data = await response.json();
        setOrganizations(data.organizations || []);
      } else {
        toast.error("Failed to fetch organizations");
      }
    } catch (error) {
      console.error("Error fetching organizations:", error);
      toast.error("Error fetching organizations");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrganizations();
  }, []);

  // Toggle organization status
  const toggleOrganizationStatus = async (
    organizationId: string,
    currentStatus: string,
  ) => {
    try {
      setUpdatingStatus(organizationId);

      const newStatus = currentStatus === "active" ? "inactive" : "active";

      const response = await fetch(
        `/api/admin/organizations/${organizationId}/status`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status: newStatus }),
        },
      );

      if (response.ok) {
        // Update local state
        setOrganizations((prev) =>
          prev.map((org) =>
            org.id === organizationId
              ? { ...org, status: newStatus as "active" | "inactive" }
              : org,
          ),
        );
        toast.success(
          `Organization ${newStatus === "active" ? "activated" : "deactivated"} successfully`,
        );
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to update organization status");
      }
    } catch (error) {
      console.error("Error updating organization status:", error);
      toast.error("Error updating organization status");
    } finally {
      setUpdatingStatus(null);
    }
  };

  // Filter organizations
  const filteredOrganizations = organizations.filter((org) => {
    const matchesSearch =
      org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      org.slug.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || org.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Organizations Management</h1>
          <p className="text-muted-foreground">Loading organizations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Organizations Management</h1>
        <p className="text-muted-foreground">
          Manage all healthcare organizations on the platform.
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search organizations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant={statusFilter === "all" ? "default" : "outline"}
            onClick={() => setStatusFilter("all")}
            size="sm"
          >
            All
          </Button>
          <Button
            variant={statusFilter === "active" ? "default" : "outline"}
            onClick={() => setStatusFilter("active")}
            size="sm"
          >
            Active
          </Button>
          <Button
            variant={statusFilter === "inactive" ? "default" : "outline"}
            onClick={() => setStatusFilter("inactive")}
            size="sm"
          >
            Inactive
          </Button>
        </div>
      </div>

      {/* Organizations Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredOrganizations.map((org) => (
          <OrganizationCard
            key={org.id}
            organization={org}
            onToggleStatus={toggleOrganizationStatus}
            isUpdating={updatingStatus === org.id}
          />
        ))}
      </div>

      {filteredOrganizations.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <BuildingIcon className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              No organizations found
            </h3>
            <p className="text-muted-foreground text-center">
              {searchTerm || statusFilter !== "all"
                ? "Try adjusting your search or filter criteria."
                : "No organizations have been registered yet."}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Organization Card Component
function OrganizationCard({
  organization,
  onToggleStatus,
  isUpdating = false,
}: {
  organization: Organization;
  onToggleStatus: (id: string, status: string) => void;
  isUpdating?: boolean;
}) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          {organization.logo ? (
            <img
              src={organization.logo}
              alt={organization.name}
              className="h-10 w-10 rounded-lg object-cover"
            />
          ) : (
            <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <BuildingIcon className="h-5 w-5 text-primary" />
            </div>
          )}
          <div>
            <CardTitle className="text-lg">{organization.name}</CardTitle>
            <p className="text-sm text-muted-foreground">
              @{organization.slug}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Users</p>
            <p className="font-medium">{organization._count?.users || 0}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Branches</p>
            <p className="font-medium">{organization._count?.branches || 0}</p>
          </div>
        </div>

        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <CalendarIcon className="h-4 w-4" />
          <span>
            Created {new Date(organization.createdAt).toLocaleDateString()}
          </span>
        </div>

        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
            <Badge
              variant={
                organization.status === "active" ? "default" : "secondary"
              }
              className={
                organization.status === "active"
                  ? "bg-green-100 text-green-800 border-green-200"
                  : "bg-red-100 text-red-800 border-red-200"
              }
            >
              {organization.status}
            </Badge>
          </div>
          <Button
            variant={
              organization.status === "active" ? "destructive" : "default"
            }
            size="sm"
            onClick={() => onToggleStatus(organization.id, organization.status)}
            disabled={isUpdating}
            className={
              organization.status === "active"
                ? "bg-red-600 hover:bg-red-700 disabled:bg-red-400"
                : "bg-green-600 hover:bg-green-700 disabled:bg-green-400"
            }
          >
            {isUpdating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {organization.status === "active"
                  ? "Deactivating..."
                  : "Activating..."}
              </>
            ) : organization.status === "active" ? (
              "Deactivate"
            ) : (
              "Activate"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
