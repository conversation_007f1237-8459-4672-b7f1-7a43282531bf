# Super Admin Portal

The Super Admin portal provides system-wide management capabilities for the Aran Care platform. This portal is accessible only to users with the `superAdmin` role.

## Features

### 1. Organizations Management

- View all organizations on the platform
- Monitor organization status (active/inactive)
- View organization statistics (users, branches)
- Toggle organization status
- Search and filter organizations

### 2. HIU Notifications

Centralized management of all ABDM HIU (Health Information User) notifications:

- **Consent Notifications**: View and manage ABDM consent notifications
- **Health Info Requests**: Monitor health information requests
- **Care Context Notifications**: Track care context notifications
- **Link Token Notifications**: Manage link token notifications
- **UIL OTP Notifications**: View UIL OTP notifications
- **Webhook Notifications**: Monitor webhook notifications

### 3. System Logs

- Monitor system activities and errors
- View performance metrics
- Track authentication events
- Monitor API requests and database operations

### 4. Settings

- Configure system-wide settings
- Manage database settings
- Configure security settings
- Manage notification preferences
- Configure ABDM integration settings

## Access Control

### Authentication

- Only users with `superAdmin` role can access the admin portal
- Middleware protection at `/admin` routes
- Automatic redirect to main dashboard for non-super-admin users

### Role Hierarchy

The `superAdmin` role has the highest permission level (1000) in the system hierarchy:

- `superAdmin`: 1000
- `hospitalAdmin`: 100
- `branchAdmin`: 80
- `doctor`: 60
- etc.

## Directory Structure

```
apps/frontend/src/app/admin/
├── layout.tsx                          # Admin layout with authentication
├── page.tsx                           # Admin dashboard
├── @sidebar/                          # Admin sidebar (parallel route)
│   ├── default.tsx                    # Sidebar layout
│   └── _components/
│       └── admin-sidebar.tsx          # Admin navigation
├── organizations/
│   └── page.tsx                       # Organizations management
├── hiu-notifications/
│   ├── page.tsx                       # HIU notifications overview
│   ├── consent/
│   │   ├── page.tsx                   # Consent notifications
│   │   ├── _components/               # Table components
│   │   └── _lib/                      # Queries and validations
│   ├── health-info-requests/
│   ├── care-context/
│   ├── link-token/
│   ├── uil-otp/
│   └── webhook/
├── system-logs/
│   └── page.tsx                       # System logs
└── settings/
    └── page.tsx                       # Admin settings
```

## API Endpoints

### Organizations Management

- `GET /api/admin/organizations` - List all organizations
- `PATCH /api/admin/organizations/[id]/status` - Toggle organization status

### Development Utilities

- `POST /api/admin/create-super-admin` - Create super admin user (development only)

## Migration from Main App

The following features have been moved from the main application to the admin portal:

### HIU Notifications

Previously available in the main app sidebar under "ABDM Integration", these have been moved to the admin portal:

- Consent Notifications (`/abdm-consent-notify` → `/admin/hiu-notifications/consent`)
- Health Info Requests (`/hi-request` → `/admin/hiu-notifications/health-info-requests`)
- Care Context Notifications (`/care-context-notify` → `/admin/hiu-notifications/care-context`)
- Link Token Notifications (`/link-token-notify` → `/admin/hiu-notifications/link-token`)
- UIL OTP Notifications (`/uil-otp-notify` → `/admin/hiu-notifications/uil-otp`)
- Webhook Notifications (`/webhook-notify` → `/admin/hiu-notifications/webhook`)

## Development Setup

### Creating a Super Admin User

For development and testing purposes, you can create a super admin user using the API endpoint:

```bash
curl -X POST http://localhost:3000/api/admin/create-super-admin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123",
    "name": "Super Admin"
  }'
```

**Note**: This endpoint is only available in development mode and will be disabled in production.

### Accessing the Admin Portal

1. Create a super admin user (see above)
2. Sign in with the super admin credentials
3. Navigate to `/admin` or you'll be automatically redirected if you have super admin role

## Security Considerations

- Admin routes are protected by middleware
- Role-based access control enforced at multiple levels
- Super admin role required for all admin functionality
- Automatic redirects for unauthorized access attempts
- Development-only endpoints disabled in production

## UI/UX Design

The admin portal follows the same design principles as the main application:

- White background for form fields
- Blue headers for consistency
- Professional and minimal design
- Card-based layouts for better organization
- Responsive design for all screen sizes
- Consistent with existing UI components and patterns
