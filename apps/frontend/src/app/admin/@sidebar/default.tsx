"use client";

import Link from "next/link";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarRail,
} from "@workspace/ui/components/sidebar";

import { AdminSidebar } from "./_components/admin-sidebar";
import Small<PERSON>ogo from "@/app/(app)/@sidebar/_components/logo";

export default function AdminSidebarLayout() {
  return (
    <Sidebar collapsible="icon" variant="floating">
      <SidebarHeader className="p-3 group-data-[collapsible=icon]:p-2">
        <SidebarMenu>
          <Link href="/admin">
            <SidebarMenuItem className="flex gap-2 hover:bg-transparent">
              <SmallLogo />

              <div className="grid flex-1 text-left leading-tight group-data-[collapsible=icon]:hidden">
                <span className="truncate font-semibold text-sm">
                  Aran Care Admin
                </span>
                <span className="text-xs text-muted-foreground">
                  Super Admin Portal
                </span>
              </div>
            </SidebarMenuItem>
          </Link>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <AdminSidebar />
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
