"use client";

import React, { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "@workspace/ui/components/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@workspace/ui/components/collapsible";
import {
  HomeIcon,
  BuildingIcon,
  FileTextIcon,
  BellIcon,
  SettingsIcon,
  ChevronDownIcon,
} from "lucide-react";

// Admin menu configuration
const adminMenuItems = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: HomeIcon,
  },
  {
    title: "Organizations",
    href: "/admin/organizations",
    icon: BuildingIcon,
  },
  {
    title: "System Logs",
    href: "/admin/system-logs",
    icon: FileTextIcon,
  },
  {
    title: "ABDM Logs",
    href: "/admin/abdm-logs",
    icon: BellIcon,
    items: [
      {
        title: "Consent Notifications",
        href: "/admin/abdm-logs/consent-notifications",
      },
      {
        title: "Health Info Requests",
        href: "/admin/abdm-logs/health-info-requests",
      },
      {
        title: "Care Context Notifications",
        href: "/admin/abdm-logs/care-context-notifications",
      },
      {
        title: "Link Token Notifications",
        href: "/admin/abdm-logs/link-token-notifications",
      },
      {
        title: "UIL OTP Notifications",
        href: "/admin/abdm-logs/uil-otp-notifications",
      },
      {
        title: "Webhook Notifications",
        href: "/admin/abdm-logs/webhook-notifications",
      },
    ],
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: SettingsIcon,
  },
];

// Helper function to check if route is active
function isRouteActive(pathname: string, href: string): boolean {
  if (href === "/admin") {
    return pathname === "/admin";
  }
  return pathname.startsWith(href);
}

export function AdminSidebar() {
  const pathname = usePathname();
  const [openItems, setOpenItems] = useState<string[]>([]);

  const toggleItem = (href: string) => {
    setOpenItems((prev) =>
      prev.includes(href)
        ? prev.filter((item) => item !== href)
        : [...prev, href],
    );
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Admin Portal</SidebarGroupLabel>
      <SidebarMenu>
        {adminMenuItems.map((item) => {
          const hasSubItems = item.items && item.items.length > 0;
          const isOpen = openItems.includes(item.href);
          const isActive = isRouteActive(pathname ?? "", item.href);

          if (hasSubItems) {
            return (
              <Collapsible
                key={item.href}
                open={isOpen}
                onOpenChange={() => toggleItem(item.href)}
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton isActive={isActive}>
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                      <ChevronDownIcon
                        className={`ml-auto h-4 w-4 transition-transform ${isOpen ? "rotate-180" : ""}`}
                      />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {item.items.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.href}>
                          <SidebarMenuSubButton
                            asChild
                            isActive={isRouteActive(
                              pathname ?? "",
                              subItem.href,
                            )}
                          >
                            <Link href={subItem.href}>
                              <span>{subItem.title}</span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            );
          }

          return (
            <SidebarMenuItem key={item.href}>
              <SidebarMenuButton asChild isActive={isActive}>
                <Link href={item.href}>
                  <item.icon className="h-4 w-4" />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
