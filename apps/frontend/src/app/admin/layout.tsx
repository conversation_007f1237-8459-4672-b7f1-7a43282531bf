"use client";

import { useUserRole } from "@/hooks/use-user-role";
import { AppLayout } from "@/components/app-layout";
import { SidebarProvider } from "@workspace/ui/components/sidebar";
import { SidebarInset } from "@workspace/ui/components/sidebar";
import { Loader2 } from "lucide-react";

export default function AdminLayout({
  children,
  sidebar,
}: {
  children: React.ReactNode;
  sidebar: React.ReactNode;
}) {
  const { isLoading } = useUserRole();

  // useEffect(() => {
  //   if (!isLoading && !isSuperAdmin) {
  //     // Redirect non-super-admin users to the main dashboard
  //     router.push("/dashboard");
  //   }
  // }, [isSuperAdmin, isLoading, router]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Don't render anything if user is not a super admin
  // if (!isSuperAdmin) {
  //   return null;
  // }

  return (
    <SidebarProvider>
      {sidebar}
      <SidebarInset>
        <AppLayout>{children}</AppLayout>
      </SidebarInset>
    </SidebarProvider>
  );
}
