"use client";

import type { ConsentNotification } from "../_lib/queries";
import type { ColumnDef } from "@tanstack/react-table";

import { DataTableColumnHeader } from "@workspace/data-table/component/data-table-column-header";
import { Badge } from "@workspace/ui/components/badge";

// Helper function to format date
function formatDate(dateString: string): string {
  if (!dateString) return "—";

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) return "Invalid date";

  // Format the date as "MMM DD, YYYY"
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
}

// Helper function to format status
function formatStatus(status: string): string {
  return status
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

// Helper function to get status color
function getStatusColor(status: string): string {
  const statusMap: Record<string, string> = {
    GRANTED: "bg-green-50 text-green-700 hover:bg-green-100",
    REQUESTED: "bg-blue-50 text-blue-700 hover:bg-blue-100",
    DENIED: "bg-red-50 text-red-700 hover:bg-red-100",
    REVOKED: "bg-yellow-50 text-yellow-700 hover:bg-yellow-100",
    EXPIRED: "bg-gray-50 text-gray-700 hover:bg-gray-100",
  };

  return statusMap[status] || "bg-gray-50 text-gray-700 hover:bg-gray-100";
}

export function getConsentNotificationsTableColumns(): ColumnDef<ConsentNotification>[] {
  return [
    {
      id: "consentId",
      accessorKey: "consentId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Consent ID" />
      ),
      cell: ({ row }) => (
        <div className="font-medium w-[300px]">{row.getValue("consentId")}</div>
      ),
      enableSorting: true,
      enableHiding: false,
      meta: {
        label: "Consent ID",
        variant: "text",
      },
      enableColumnFilter: true,
    },
    {
      id: "status",
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <Badge variant="outline" className={getStatusColor(status)}>
            {formatStatus(status)}
          </Badge>
        );
      },
      enableSorting: true,
      meta: {
        label: "Status",
        variant: "select",
        options: [
          { label: "Granted", value: "GRANTED" },
          { label: "Requested", value: "REQUESTED" },
          { label: "Denied", value: "DENIED" },
          { label: "Revoked", value: "REVOKED" },
          { label: "Expired", value: "EXPIRED" },
        ],
      },
      enableColumnFilter: true,
    },
    {
      id: "patientId",
      accessorKey: "patientId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="ABHA Address" />
      ),
      cell: ({ row }) => {
        const abhaAddress = row.getValue("patientId") as string;
        return (
          <div className="max-w-[200px] truncate" title={abhaAddress}>
            {abhaAddress || "Unknown"}
          </div>
        );
      },
      enableSorting: false,
      enableColumnFilter: false,
    },
    {
      id: "purpose",
      accessorKey: "purpose",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Purpose" />
      ),
      cell: ({ row }) => {
        const purpose = row.original.purpose;
        return <div className="w-[120px]">{purpose?.text || "—"}</div>;
      },
      enableSorting: false,
      enableColumnFilter: false,
    },
    {
      id: "hiTypes",
      accessorKey: "hiTypes",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Health Info Types" />
      ),
      cell: ({ row }) => {
        const hiTypes = row.original.hiTypes;
        return (
          <div className="flex flex-wrap gap-1 w-[420px]">
            {hiTypes && hiTypes.length > 0
              ? hiTypes.map((type, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="bg-blue-50 text-blue-700"
                  >
                    {type}
                  </Badge>
                ))
              : "—"}
          </div>
        );
      },
      enableSorting: false,
      enableColumnFilter: false,
    },
    {
      id: "createdAt",
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Date" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as string;
        return <div className="w-[120px]">{formatDate(date)}</div>;
      },
      enableSorting: true,
      enableColumnFilter: false,
    },
  ];
}
