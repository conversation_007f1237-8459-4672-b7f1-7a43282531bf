"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import type { GetConsentNotificationsSchemaType } from "./validations";

export interface ConsentNotification {
  id: string;
  requestId: string;
  status: string;
  consentId: string;
  schemaVersion: string;
  patientId: string;
  organizationId?: string;
  careContexts: any;
  purpose: any;
  hipId: string;
  consentManagerId: string;
  hiTypes: string[];
  permission: any;
  signature: string;
  grantAcknowledgement: boolean;
  createdAt: string;
  updatedAt: string;
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export async function getConsentNotifications(
  input: GetConsentNotificationsSchemaType,
) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // For admin portal, we don't filter by organization - show all notifications
  const { page = 1, perPage = 10, patientId, status, search } = input;

  // Calculate pagination
  const skip = (page - 1) * perPage;

  // Build where clause
  const where: any = {};

  // Add filters if provided
  if (patientId) {
    where.patientId = patientId;
  }

  if (status) {
    where.status = status;
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      {
        consentId: {
          contains: search,
          mode: "insensitive",
        },
      },
      {
        requestId: {
          contains: search,
          mode: "insensitive",
        },
      },
      {
        status: {
          contains: search,
          mode: "insensitive",
        },
      },
      {
        patientId: {
          contains: search,
          mode: "insensitive",
        },
      },
    ];
  }

  // Get total count
  const total = await prisma.consentNotify.count({
    where,
  });

  // Get consent notifications with pagination
  const notifications = await prisma.consentNotify.findMany({
    where,
    orderBy: {
      createdAt: "desc",
    },
    skip,
    take: perPage,
  });

  // Calculate total pages
  const totalPages = Math.ceil(total / perPage);

  // Format the data to match the expected interface
  const formattedNotifications = notifications.map((notification) => ({
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  }));

  return {
    data: formattedNotifications,
    pageCount: totalPages,
  };
}

export async function getConsentNotificationById(id: string) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Find the consent notification
  const notification = await prisma.consentNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("Consent notification not found");
  }

  // Format the data to match the expected interface
  return {
    ...notification,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
  };
}
