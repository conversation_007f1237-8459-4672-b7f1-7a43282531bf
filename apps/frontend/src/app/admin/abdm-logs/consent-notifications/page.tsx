import * as React from "react";
import { DataTableSkeleton } from "@workspace/data-table/component/data-table-skeleton";
import { ConsentNotificationsTable } from "./_components/table";
import { getConsentNotifications } from "./_lib/queries";
import { GetConsentNotificationsSchema } from "./_lib/validations";

export default async function ConsentNotificationsPage(props: any) {
  // Parse search params manually
  const page = Number(props.searchParams?.page || 1);
  const perPage = Number(props.searchParams?.perPage || 10);
  const patientId = props.searchParams?.patientId || "";
  const status = props.searchParams?.status || "";
  const search = props.searchParams?.search || "";

  // Validate and transform the input using the schema
  const input = GetConsentNotificationsSchema.parse({
    page,
    perPage,
    patientId: patientId || undefined,
    status: status || undefined,
    search: search || undefined,
  });

  // Fetch data directly instead of using promises with React.use
  const data = await getConsentNotifications(input);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Consent Notifications</h1>
        <p className="text-muted-foreground">
          View and manage ABDM consent notifications across all organizations.
        </p>
      </div>

      <React.Suspense
        fallback={
          <DataTableSkeleton
            columnCount={7}
            filterCount={3}
            cellWidths={[
              "4rem", // select
              "20rem", // consent id
              "12rem", // status
              "15rem", // patient
              "20rem", // purpose
              "25rem", // health info types
              "10rem", // date
              "4rem", // actions
            ]}
            shrinkZero
          />
        }
      >
        <ConsentNotificationsTable initialData={data} />
      </React.Suspense>
    </div>
  );
}
