import * as React from "react";
import { DataTableSkeleton } from "@workspace/data-table/component/data-table-skeleton";
import { LinkTokenNotificationsTable } from "./_components/table";
import { LinkTokenNotificationsStats } from "./_components/stats";
import {
  getLinkTokenNotifications,
  getLinkTokenNotificationStats,
} from "./_lib/queries";
import { GetLinkTokenNotificationsSchema } from "./_lib/validations";

export default async function LinkTokenNotificationsPage(props: any) {
  // Parse search params manually
  const page = Number(props.searchParams?.page || 1);
  const perPage = Number(props.searchParams?.perPage || 10);
  const requestId = props.searchParams?.requestId || "";
  const status = props.searchParams?.status || "";
  const search = props.searchParams?.search || "";

  // Validate and transform the input using the schema
  const input = GetLinkTokenNotificationsSchema.parse({
    page,
    perPage,
    requestId: requestId || undefined,
    status: status || undefined,
    search: search || undefined,
  });

  // Fetch data directly instead of using promises with React.use
  const data = await getLinkTokenNotifications(input);
  const stats = await getLinkTokenNotificationStats();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Link Token Notifications</h1>
        <p className="text-muted-foreground">
          View and manage ABDM link token generation notifications across all
          organizations.
        </p>
      </div>

      <LinkTokenNotificationsStats stats={stats} />

      <React.Suspense
        fallback={
          <DataTableSkeleton
            columnCount={6}
            filterCount={3}
            cellWidths={[
              "4rem", // select
              "20rem", // request id
              "12rem", // status
              "25rem", // response/error
              "15rem", // created at
              "4rem", // actions
            ]}
            shrinkZero
          />
        }
      >
        <LinkTokenNotificationsTable initialData={data} />
      </React.Suspense>
    </div>
  );
}
