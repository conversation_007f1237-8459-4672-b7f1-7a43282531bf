"use server";

import { prisma } from "@/lib/prisma";
import { getCurrentUser } from "@/lib/session";
import type { GetLinkTokenNotificationsSchemaType } from "./validations";
import { Prisma } from "@prisma/client";

export interface LinkTokenNotification {
  id: string;
  requestId?: string;
  linkToken?: string;
  patientId?: string;
  branchId?: string;
  error: any;
  response: any;
  createdAt: string;
  updatedAt: string;
  status: "success" | "error";
  type: "notification" | "token";
}

export async function getLinkTokenNotifications(
  input: GetLinkTokenNotificationsSchemaType,
) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // For admin portal, we don't filter by organization - show all notifications
  const { page = 1, perPage = 10, requestId, status, search } = input;

  // Calculate pagination
  const skip = (page - 1) * perPage;

  // Build where clause
  const where: any = {};

  // Add filters if provided
  if (requestId) {
    where.requestId = requestId;
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      {
        requestId: {
          contains: search,
          mode: "insensitive",
        },
      },
    ];
  }

  // Get notifications and tokens separately
  const [notifications, linkTokens] = await Promise.all([
    prisma.generateTokenNotify.findMany({
      where,
      orderBy: { createdAt: "desc" },
      skip,
      take: Math.ceil(perPage / 2), // Split between notifications and tokens
    }),
    prisma.abhaLinkToken.findMany({
      where: {
        // Add search filter for link tokens if provided
        ...(search && {
          OR: [
            { linkToken: { contains: search, mode: "insensitive" } },
            { patientId: { contains: search, mode: "insensitive" } },
          ],
        }),
      },
      include: {
        patient: {
          select: { firstName: true, lastName: true },
        },
        branch: {
          select: { name: true },
        },
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: Math.ceil(perPage / 2),
    }),
  ]);

  // Get total counts
  const [notificationCount, tokenCount] = await Promise.all([
    prisma.generateTokenNotify.count({ where }),
    prisma.abhaLinkToken.count({
      where: {
        ...(search && {
          OR: [
            { linkToken: { contains: search, mode: "insensitive" } },
            { patientId: { contains: search, mode: "insensitive" } },
          ],
        }),
      },
    }),
  ]);

  const total = notificationCount + tokenCount;
  const totalPages = Math.ceil(total / perPage);

  // Format notifications
  const formattedNotifications: LinkTokenNotification[] = notifications.map(
    (notification) => ({
      id: notification.id,
      requestId: notification.requestId,
      error: notification.error,
      response: notification.response,
      createdAt: notification.createdAt.toISOString(),
      updatedAt: notification.updatedAt.toISOString(),
      status: notification.error ? "error" : "success",
      type: "notification" as const,
    }),
  );

  // Format link tokens
  const formattedTokens: LinkTokenNotification[] = linkTokens.map((token) => ({
    id: token.id,
    linkToken: token.linkToken,
    patientId: token.patientId,
    branchId: token.branchId,
    error: null,
    response: {
      patient: token.patient
        ? `${token.patient.firstName} ${token.patient.lastName}`
        : "Unknown",
      branch: token.branch?.name || "Unknown",
      status: token.status,
      expiry: token.linkTokenExpiry,
    },
    createdAt: token.createdAt.toISOString(),
    updatedAt: token.updatedAt.toISOString(),
    status: "success" as const,
    type: "token" as const,
  }));

  // Combine and sort by creation date
  const allData = [...formattedNotifications, ...formattedTokens].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  );

  // Apply status filter after formatting (since status is derived)
  const filteredNotifications = status
    ? allData.filter((n) => n.status === status)
    : allData;

  return {
    data: filteredNotifications,
    pageCount: totalPages,
  };
}

export async function getLinkTokenNotificationById(id: string) {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Find the link token notification
  const notification = await prisma.generateTokenNotify.findUnique({
    where: {
      id,
    },
  });

  if (!notification) {
    throw new Error("Link token notification not found");
  }

  // Format the data to match the expected interface
  return {
    id: notification.id,
    requestId: notification.requestId,
    error: notification.error,
    response: notification.response,
    createdAt: notification.createdAt.toISOString(),
    updatedAt: notification.updatedAt.toISOString(),
    status: notification.error ? "error" : "success",
  };
}

export async function getLinkTokenNotificationStats() {
  // Get the current user from cookies
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Get counts for both notifications and tokens
  const [
    notificationTotal,
    notificationSuccess,
    notificationError,
    notificationRecent,
    tokenTotal,
    tokenRecent,
  ] = await Promise.all([
    prisma.generateTokenNotify.count(),
    prisma.generateTokenNotify.count({
      where: {
        error: { equals: Prisma.JsonNull },
      },
    }),
    prisma.generateTokenNotify.count({
      where: {
        error: { not: Prisma.JsonNull },
      },
    }),
    prisma.generateTokenNotify.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
    }),
    prisma.abhaLinkToken.count(),
    prisma.abhaLinkToken.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
        },
      },
    }),
  ]);

  return {
    total: notificationTotal + tokenTotal,
    successCount: notificationSuccess + tokenTotal, // All tokens are considered successful
    errorCount: notificationError,
    recentCount: notificationRecent + tokenRecent,
  };
}
