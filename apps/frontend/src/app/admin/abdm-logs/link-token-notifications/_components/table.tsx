"use client";

import * as React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { DataTable } from "@workspace/data-table/component/data-table";
import { DataTableColumnHeader } from "@workspace/data-table/component/data-table-column-header";
import { DataTableToolbar } from "@workspace/data-table/component/data-table-toolbar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { useDataTable } from "@workspace/data-table/hooks/use-data-table";
import { EyeIcon, CopyIcon, MoreHorizontalIcon } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import type { LinkTokenNotification } from "../_lib/queries";

interface LinkTokenNotificationsTableProps {
  initialData: {
    data: LinkTokenNotification[];
    pageCount: number;
  };
}

export function LinkTokenNotificationsTable({
  initialData,
}: LinkTokenNotificationsTableProps) {
  // Memoize the columns so they don't re-render on every render
  const columns = React.useMemo<ColumnDef<LinkTokenNotification, unknown>[]>(
    () => [
      {
        accessorKey: "requestId",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="ID / Token" />
        ),
        cell: ({ row }) => {
          const requestId = row.getValue("requestId") as string;
          const linkToken = row.original.linkToken;
          const type = row.original.type;
          const displayValue = requestId || linkToken || "N/A";

          return (
            <div className="flex items-center gap-2">
              <Badge
                variant={type === "token" ? "default" : "secondary"}
                className="mr-2"
              >
                {type === "token" ? "Token" : "Notify"}
              </Badge>
              <code className="text-xs bg-muted px-2 py-1 rounded">
                {displayValue.slice(0, 12)}...
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(displayValue);
                  toast.success("ID copied to clipboard");
                }}
              >
                <CopyIcon className="h-3 w-3" />
              </Button>
            </div>
          );
        },
        meta: {
          variant: "text",
          label: "Request ID",
          placeholder: "Filter by request ID...",
        },
      },
      {
        accessorKey: "status",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Status" />
        ),
        cell: ({ row }) => {
          const status = row.getValue("status") as string;
          return (
            <Badge variant={status === "success" ? "default" : "destructive"}>
              {status}
            </Badge>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
        meta: {
          variant: "select",
          label: "Status",
          options: [
            { label: "Success", value: "success" },
            { label: "Error", value: "error" },
          ],
        },
      },
      {
        accessorKey: "response",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Response/Error" />
        ),
        cell: ({ row }) => {
          const response = row.getValue("response") as any;
          const error = row.original.error as any;
          const status = row.getValue("status") as string;

          if (status === "error" && error) {
            return (
              <div className="max-w-[300px]">
                <code className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded block truncate">
                  {typeof error === "string" ? error : JSON.stringify(error)}
                </code>
              </div>
            );
          }

          if (status === "success" && response) {
            const type = row.original.type;
            if (type === "token" && typeof response === "object") {
              return (
                <div className="max-w-[300px]">
                  <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                    <div>Patient: {response.patient}</div>
                    <div>Branch: {response.branch}</div>
                    <div>Status: {response.status}</div>
                  </div>
                </div>
              );
            }
            return (
              <div className="max-w-[300px]">
                <code className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded block truncate">
                  {typeof response === "string" ? response : "Success"}
                </code>
              </div>
            );
          }

          return <span className="text-muted-foreground">-</span>;
        },
      },
      {
        accessorKey: "createdAt",
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title="Created At" />
        ),
        cell: ({ row }) => {
          const date = new Date(row.getValue("createdAt") as string);
          return (
            <div className="text-sm">
              <div>{format(date, "MMM dd, yyyy")}</div>
              <div className="text-muted-foreground text-xs">
                {format(date, "HH:mm:ss")}
              </div>
            </div>
          );
        },
      },
      {
        id: "actions",
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  // TODO: Implement view details modal
                  toast.info("View details functionality coming soon");
                }}
              >
                <EyeIcon className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  const copyValue =
                    row.original.requestId || row.original.linkToken || "N/A";
                  navigator.clipboard.writeText(copyValue);
                  toast.success("ID copied to clipboard");
                }}
              >
                <CopyIcon className="mr-2 h-4 w-4" />
                Copy ID
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
      },
    ],
    [],
  );

  const { table } = useDataTable({
    data: initialData.data,
    columns,
    pageCount: initialData.pageCount,
    enableAdvancedFilter: false,
  });

  return (
    <div className="space-y-4">
      <DataTable table={table} doctype="Link Token Notification">
        <DataTableToolbar table={table} />
      </DataTable>
    </div>
  );
}
