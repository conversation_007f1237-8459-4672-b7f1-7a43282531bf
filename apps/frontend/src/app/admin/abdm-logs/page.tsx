import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@workspace/ui/components/card";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { BellIcon, FileTextIcon, LinkIcon, ShieldIcon } from "lucide-react";
import Link from "next/link";
import { prisma } from "@/lib/prisma";

async function getABDMLogCounts() {
  try {
    const [consentNotificationsCount, linkTokenNotificationsCount] =
      await Promise.all([
        prisma.consentNotify.count(),
        prisma.generateTokenNotify.count(),
      ]);

    return {
      consentNotifications: consentNotificationsCount,
      linkTokenNotifications: linkTokenNotificationsCount,
    };
  } catch (error) {
    console.error("Error fetching ABDM log counts:", error);
    return {
      consentNotifications: 0,
      linkTokenNotifications: 0,
    };
  }
}

export default async function ABDMLogsPage() {
  const counts = await getABDMLogCounts();

  const abdmLogTypes = [
    {
      title: "Consent Notifications",
      description: "View and manage ABDM consent notifications",
      href: "/admin/abdm-logs/consent-notifications",
      icon: ShieldIcon,
      count: counts.consentNotifications.toString(),
    },
    {
      title: "Health Info Requests",
      description: "Monitor health information requests",
      href: "/admin/abdm-logs/health-info-requests",
      icon: FileTextIcon,
      count: "--",
    },
    {
      title: "Care Context Notifications",
      description: "Track care context notifications",
      href: "/admin/abdm-logs/care-context-notifications",
      icon: LinkIcon,
      count: "--",
    },
    {
      title: "Link Token Notifications",
      description: "Manage link token notifications",
      href: "/admin/abdm-logs/link-token-notifications",
      icon: BellIcon,
      count: counts.linkTokenNotifications.toString(),
    },
    {
      title: "UIL OTP Notifications",
      description: "View UIL OTP notifications",
      href: "/admin/abdm-logs/uil-otp-notifications",
      icon: BellIcon,
      count: "--",
    },
    {
      title: "Webhook Notifications",
      description: "Monitor webhook notifications",
      href: "/admin/abdm-logs/webhook-notifications",
      icon: BellIcon,
      count: "--",
    },
  ];
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">ABDM Logs</h1>
        <p className="text-muted-foreground">
          Monitor and manage all ABDM (Ayushman Bharat Digital Mission) logs and
          notifications across the platform.
        </p>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {abdmLogTypes.map((type) => (
          <Card key={type.href} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    <type.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{type.title}</CardTitle>
                  </div>
                </div>
                <div className="text-2xl font-bold text-primary">
                  {type.count}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {type.description}
              </p>
              <Button asChild className="w-full">
                <Link href={type.href}>View Details</Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>ABDM System Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                Operational
              </div>
              <p className="text-sm text-muted-foreground">ABDM Integration</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">Active</div>
              <p className="text-sm text-muted-foreground">Webhook Endpoints</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">--</div>
              <p className="text-sm text-muted-foreground">
                Pending Notifications
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
