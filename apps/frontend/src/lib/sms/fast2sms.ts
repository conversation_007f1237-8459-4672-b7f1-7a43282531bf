import { logger } from "@/lib/logger";

export interface Fast2SmsResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  details?: any;
}

export interface DltOtpPayload {
  to: any;
  facilityName: any;
  otp: any;
  templateId?: any; // Optional override
  senderId?: any;   // Optional override
}

/**
 * Send OTP via Fast2SMS DLT route (using approved DLT template and sender ID)
 */
export async function sendHealthRecordOtpDLT({
  to,
  facilityName,
  otp,
  templateId,
  senderId
}: DltOtpPayload): Promise<Fast2SmsResponse> {
  const FAST2SMS_API_KEY = process.env.NEXT_PUBLIC_FAST2SMS_DLT_API_KEY;
  const DEFAULT_TEMPLATE_ID = process.env.NEXT_PUBLIC_FAST2SMS_TEMPLATE_ID;
  const DEFAULT_SENDER_ID = process.env.NEXT_PUBLIC_FAST2SMS_SENDER_ID || "ARANCO";

  if (!FAST2SMS_API_KEY || !(templateId || DEFAULT_TEMPLATE_ID)) {
    const error = "Fast2SMS DLT config (API key, template ID, or sender ID) is not set";
    logger.error("Fast2SMS config error", { error });
    return {
      success: false,
      error
    };
  }

  // Format phone number to last 10 digits only
  const formattedPhone = to.replace(/\D/g, "").slice(-10);

  const payload = {
    route: "dlt",
    sender_id: senderId || DEFAULT_SENDER_ID,
    message: templateId || DEFAULT_TEMPLATE_ID,
    variables_values: `${facilityName}|${otp}`,
    numbers: formattedPhone,
    flash: 0
  };

  logger.info("Sending Fast2SMS DLT OTP", { payload });

  try {
    const response = await fetch("https://www.fast2sms.com/dev/bulkV2", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: FAST2SMS_API_KEY,
      },
      body: JSON.stringify(payload),
    });

    const responseData = await response.json();

    if (!response.ok || responseData.return !== true) {
      logger.error("Fast2SMS DLT OTP failed", {
        status: response.status,
        statusText: response.statusText,
        responseData
      });

      return {
        success: false,
        error: responseData.message || "Fast2SMS DLT error",
        details: responseData,
      };
    }

    logger.info("Fast2SMS DLT OTP sent successfully", {
      requestId: responseData.request_id,
      to: formattedPhone,
      details: responseData
    });

    return {
      success: true,
      messageId: responseData.request_id || responseData.message_id,
      details: responseData
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Exception while sending Fast2SMS DLT OTP", {
      error: errorMessage,
      to: formattedPhone,
      stack: error instanceof Error ? error.stack : undefined
    });

    return {
      success: false,
      error: `Exception while sending SMS: ${errorMessage}`,
    };
  }
}

/**
 * Generate a numeric OTP (default 6 digits)
 */
export function generateRandomOTP(length: number = 6): string {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(Math.random() * (max - min + 1) + min).toString();
}
