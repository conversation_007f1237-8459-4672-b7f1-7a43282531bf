import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";
import { BasePDF, styles as baseStyles } from "./BasePDF";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  vitalsContainer: {
    marginBottom: 20,
    backgroundColor: "#ffffff",
    borderRadius: 8,
    borderWidth: 2,
    borderColor: "#2563eb",
    borderStyle: "solid",
    overflow: "hidden",
  },
  vitalsHeader: {
    backgroundColor: "#2563eb",
    padding: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  vitalsHeaderTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#ffffff",
    letterSpacing: 0.5,
  },
  vitalsDate: {
    fontSize: 11,
    fontWeight: "bold",
    color: "#dbeafe",
    backgroundColor: "#1d4ed8",
    padding: "4 8",
    borderRadius: 4,
  },
  vitalsBody: {
    padding: 16,
  },
  vitalsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  vitalItem: {
    width: "48%",
    marginBottom: 12,
    padding: 12,
    backgroundColor: "#f8fafc",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#e2e8f0",
    borderStyle: "solid",
    borderLeftWidth: 4,
    borderLeftColor: "#3b82f6",
  },
  vitalLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#475569",
    marginBottom: 4,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  vitalValue: {
    fontSize: 13,
    color: "#0f172a",
    fontWeight: "bold",
    lineHeight: 1.2,
  },
  vitalUnit: {
    fontSize: 9,
    color: "#64748b",
    marginLeft: 4,
    fontWeight: "normal",
  },
  notesSection: {
    marginTop: 16,
    padding: 12,
    backgroundColor: "#fef3c7",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#f59e0b",
    borderStyle: "solid",
    borderLeftWidth: 4,
    borderLeftColor: "#d97706",
  },
  notesLabel: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#92400e",
    marginBottom: 6,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  notesText: {
    fontSize: 10,
    color: "#1f2937",
    lineHeight: 1.5,
    fontStyle: "italic",
  },
  noDataMessage: {
    textAlign: "center",
    fontSize: 14,
    color: "#6b7280",
    fontStyle: "italic",
    padding: 40,
    backgroundColor: "#f9fafb",
    borderRadius: 8,
    borderWidth: 2,
    borderColor: "#e5e7eb",
    borderStyle: "dashed",
    margin: "20 0",
  },
  summarySection: {
    marginTop: 24,
    backgroundColor: "#ffffff",
    borderRadius: 8,
    borderWidth: 2,
    borderColor: "#059669",
    borderStyle: "solid",
    overflow: "hidden",
  },
  summaryHeader: {
    backgroundColor: "#059669",
    padding: 12,
    flexDirection: "row",
    alignItems: "center",
  },
  summaryTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#ffffff",
    letterSpacing: 0.5,
  },
  summaryIcon: {
    width: 16,
    height: 16,
    backgroundColor: "#10b981",
    borderRadius: 8,
    marginRight: 8,
  },
  summaryBody: {
    padding: 16,
  },
  summaryGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  summaryItem: {
    width: "48%",
    marginBottom: 8,
    padding: 10,
    backgroundColor: "#f0fdf4",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#bbf7d0",
    borderStyle: "solid",
    borderLeftWidth: 3,
    borderLeftColor: "#22c55e",
  },
  summaryLabel: {
    fontSize: 9,
    fontWeight: "bold",
    color: "#166534",
    marginBottom: 3,
    textTransform: "uppercase",
    letterSpacing: 0.3,
  },
  summaryValue: {
    fontSize: 12,
    color: "#0f172a",
    fontWeight: "bold",
    lineHeight: 1.2,
  },
  divider: {
    height: 1,
    backgroundColor: "#e2e8f0",
    marginVertical: 16,
  },
  recordNumber: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#ffffff",
    color: "#2563eb",
    fontSize: 10,
    fontWeight: "bold",
    padding: "4 8",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#2563eb",
    borderStyle: "solid",
  },
});

interface VitalsPDFProps {
  consultationData: ConsultationData;
  options?: PdfGenerationOptions;
}

export const VitalsPDF: React.FC<VitalsPDFProps> = ({
  consultationData,
  options,
}) => {
  const { vitals } = consultationData;

  const formatVitalValue = (value: any, unit?: string) => {
    if (value === null || value === undefined) return "N/A";
    return `${value}${unit ? ` ${unit}` : ""}`;
  };

  const getLatestVitals = () => {
    if (!vitals || vitals.length === 0) return null;
    return vitals[vitals.length - 1]; // Get the most recent vitals
  };

  const renderVitalItem = (label: string, value: any, unit?: string) => (
    <View style={styles.vitalItem}>
      <Text style={styles.vitalLabel}>{label}</Text>
      <Text style={styles.vitalValue}>{formatVitalValue(value, unit)}</Text>
    </View>
  );

  const renderVitalsRecord = (vital: any, index: number) => (
    <View key={index} style={styles.vitalsContainer}>
      <View style={styles.vitalsHeader}>
        <Text style={styles.vitalsHeaderTitle}>Vital Signs Record</Text>
        <Text style={styles.vitalsDate}>
          {format(new Date(vital.createdAt), "PPP p")}
        </Text>
      </View>
      <Text style={styles.recordNumber}>#{index + 1}</Text>

      <View style={styles.vitalsBody}>
        <View style={styles.vitalsGrid}>
          {renderVitalItem("Temperature", vital.temperature, "°F")}
          {renderVitalItem("Respiratory Rate", vital.respiratoryRate, "/min")}
          {renderVitalItem("Oxygen Saturation", vital.oxygenSaturation, "%")}
          {renderVitalItem("Weight", vital.weight, "kg")}
          {renderVitalItem(
            "Waist Circumference",
            vital.waistCircumference,
            "cm",
          )}
          {renderVitalItem("Hip Circumference", vital.hipCircumference, "cm")}
          {renderVitalItem("Body Fat Percentage", vital.bodyFatPercentage, "%")}
          {renderVitalItem("Exercise Frequency", vital.exerciseFrequency)}
          {renderVitalItem("Exercise Duration", vital.exerciseDuration)}
          {renderVitalItem("Exercise Type", vital.exerciseType)}
          {renderVitalItem(
            "Physical Activity Level",
            vital.physicalActivityLevel,
          )}
          {renderVitalItem("General Appearance", vital.generalAppearance)}
          {renderVitalItem("Mental Status", vital.mentalStatus)}
          {renderVitalItem("Pain Level", vital.painLevel)}
          {renderVitalItem("Mobility Status", vital.mobilityStatus)}
          {renderVitalItem("Menstrual Cycle", vital.menstrualCycle)}
          {renderVitalItem("Pregnancy Status", vital.pregnancyStatus)}
          {renderVitalItem("Breastfeeding Status", vital.breastfeedingStatus)}
          {renderVitalItem("Smoking Status", vital.smokingStatus)}
          {renderVitalItem("Alcohol Consumption", vital.alcoholConsumption)}
          {renderVitalItem("Dietary Habits", vital.dietaryHabits)}
          {renderVitalItem("Sleep Pattern", vital.sleepPattern)}
          {renderVitalItem("Stress Level", vital.stressLevel)}
        </View>

        {vital.notes && (
          <View style={styles.notesSection}>
            <Text style={styles.notesLabel}>Clinical Notes</Text>
            <Text style={styles.notesText}>{vital.notes}</Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderSummary = () => {
    const latestVitals = getLatestVitals();
    if (!latestVitals) return null;

    return (
      <View style={styles.summarySection}>
        <View style={styles.summaryHeader}>
          <View style={styles.summaryIcon} />
          <Text style={styles.summaryTitle}>Current Vitals Summary</Text>
        </View>
        <View style={styles.summaryBody}>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Temperature</Text>
              <Text style={styles.summaryValue}>
                {formatVitalValue(latestVitals.temperature, "°F")}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Oxygen Saturation</Text>
              <Text style={styles.summaryValue}>
                {formatVitalValue(latestVitals.oxygenSaturation, "%")}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Weight</Text>
              <Text style={styles.summaryValue}>
                {formatVitalValue(latestVitals.weight, "kg")}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Respiratory Rate</Text>
              <Text style={styles.summaryValue}>
                {formatVitalValue(latestVitals.respiratoryRate, "/min")}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <BasePDF
      consultationData={consultationData}
      title="Patient Vitals Report"
      options={options}
    >
      <Text style={baseStyles.sectionTitle}>Vital Signs Records</Text>
      <View style={styles.divider} />

      {!vitals || vitals.length === 0 ? (
        <Text style={styles.noDataMessage}>
          No vitals data recorded for this consultation.
        </Text>
      ) : (
        <>
          {vitals.map((vital, index) => renderVitalsRecord(vital, index))}
          {renderSummary()}
        </>
      )}
    </BasePDF>
  );
};
