/**
 * User Initiated Linking (UIL) service
 */

import { abdmFetch } from "@/lib/abdm-fetch";
import { getAccessToken } from "../utils/auth";
import { generateUUID } from "../utils/request";
import { db } from "@/lib/db";
import { sendUILOtpEmail } from "@/lib/email/send-uil-otp-email";
import { logger } from "@/lib/logger";
import {
  DiscoverRequest,
  OnDiscoverResponse,
  LinkInitRequest,
  OnInitResponse,
  LinkConfirmRequest,
  OnConfirmResponse,
} from "@/types/abdm/user-initiated-linking";
import {
  generateOTP,
  generateLinkRefNumber,
  storeOTP,
  validateOTP,
  markOTPAsVerified,
  getOTPData,
  updateOTPWithRequestId,
} from "./utils";
import { sendHealthRecordOtpDLT } from "@/lib/sms/fast2sms";

// ABDM API endpoints
const ABDM_BASE_URL = process.env.NEXT_PUBLIC_ABDM_BASE_URL;
const ABDM_CM_ID = process.env.NEXT_PUBLIC_ABDM_CM_ID || "sbx";

/**
 * Handle discovery request
 * @param request - Discovery request
 * @param hipId - HIP ID from request headers
 * @returns Patient records matching the discovery request
 */
export async function handleDiscovery(request: DiscoverRequest, hipId: string) {
  try {
    console.log(
      "Handling discovery request:",
      JSON.stringify(request, null, 2),
    );
    console.log("HIP ID from headers:", hipId);

    // Extract patient data from the request
    const abhaAddress = request.patient.id;
    const patientName = request.patient.name;
    const patientGender = request.patient.gender;
    const patientYearOfBirth = request.patient.yearOfBirth;

    // Extract mobile number from verifiedIdentifiers
    const mobileIdentifier = request.patient.verifiedIdentifiers?.find(
      identifier => identifier.type === "MOBILE"
    );
    const mobileNumber = mobileIdentifier?.value;

    console.log("Patient search criteria:", {
      abhaAddress,
      patientName,
      patientGender,
      patientYearOfBirth,
      mobileNumber,
    });

    // Find all branches that have this HIP ID to get all organizations
    const branches = await db.branch.findMany({
      where: {
        hipId: hipId,
      },
      select: {
        id: true,
        name: true,
        organizationId: true,
        hipId: true,
      },
    });

    if (!branches || branches.length === 0) {
      console.log(`No branches found with HIP ID: ${hipId}`);
      return null;
    }

    const organizationIds = branches.map(b => b.organizationId);
    console.log(
      `Found ${branches.length} branches with HIP ID ${hipId} across organizations: ${organizationIds.join(', ')}`,
    );

    // Try to find patient by ABHA address first (primary method)
    let patient = await db.patient.findFirst({
      where: {
        abhaProfile: {
          abhaAddress,
        },
        organizationId: {
          in: organizationIds, // Search across all organizations that have this HIP ID
        },
      },
      include: {
        abhaProfile: true,
        consultations: {
          where: {
            organizationId: {
              in: organizationIds, // Include consultations from all organizations with this HIP ID
            },
          },
          include: {
            doctor: {
              include: {
                user: true,
              },
            },
            branch: true,
            CareContext: true, // Include existing care contexts if any
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    // If patient not found by ABHA address, try fallback search by mobile + name
    if (!patient && mobileNumber && patientName) {
      console.log("Patient not found by ABHA address, trying fallback search by mobile + name");

      // Parse patient name into first and last name
      const nameParts = patientName.trim().split(/\s+/);
      const firstName = nameParts[0] || "";
      const lastName = nameParts.slice(1).join(" ") || "";

      console.log("Fallback search criteria:", {
        mobileNumber,
        firstName,
        lastName,
        organizationIds,
      });

      // Search by mobile number and name match
      patient = await db.patient.findFirst({
        where: {
          organizationId: {
            in: organizationIds,
          },
          OR: [
            { phone: mobileNumber }, // Aadhaar-linked mobile
            { communicationMobile: mobileNumber }, // Communication mobile
            // Also try without country code if mobile starts with +91
            ...(mobileNumber.startsWith("+91") ? [
              { phone: mobileNumber.substring(3) },
              { communicationMobile: mobileNumber.substring(3) },
            ] : []),
            // Also try with +91 if mobile doesn't have country code
            ...(!mobileNumber.startsWith("+") && mobileNumber.length === 10 ? [
              { phone: `+91${mobileNumber}` },
              { communicationMobile: `+91${mobileNumber}` },
            ] : []),
          ],
          // Match first name (case insensitive)
          firstName: { contains: firstName, mode: "insensitive" },
          // Match last name if provided (case insensitive)
          ...(lastName ? { lastName: { contains: lastName, mode: "insensitive" } } : {}),
        },
        include: {
          abhaProfile: true,
          consultations: {
            where: {
              organizationId: {
                in: organizationIds,
              },
            },
            include: {
              doctor: {
                include: {
                  user: true,
                },
              },
              branch: true,
              CareContext: true,
            },
            orderBy: {
              createdAt: "desc",
            },
          },
        },
      });

      if (patient) {
        console.log("Found patient via fallback search:", {
          patientId: patient.id,
          name: `${patient.firstName} ${patient.lastName}`,
          phone: patient.phone,
          communicationMobile: patient.communicationMobile,
          hasAbhaProfile: !!patient.abhaProfile,
        });
      } else {
        console.log("No patient found via fallback search either");
      }
    }

    if (!patient) {
      console.log("No patient found with ABHA address or fallback criteria");
      return null;
    }

    console.log("Found patient:", patient.id);
    console.log(
      "Found consultations (filtered by organization):",
      patient.consultations.length,
    );
    console.log("Organization IDs used for filtering:", organizationIds.join(', '));

    // Log consultation details for debugging
    const consultationsWithCareContext = patient.consultations.filter(
      (c) => c.CareContext.length > 0,
    );
    const consultationsWithoutCareContext = patient.consultations.filter(
      (c) => c.CareContext.length === 0,
    );

    console.log(
      "Consultations with existing care contexts:",
      consultationsWithCareContext.length,
    );
    console.log(
      "Consultations without care contexts:",
      consultationsWithoutCareContext.length,
    );

    // Prepare care contexts for ALL consultations (not just those with existing care contexts)
    const careContexts = patient.consultations.map((consultation) => {
      // Check if this consultation already has a care context
      const existingCareContext = consultation.CareContext[0]; // Get first care context if exists

      if (existingCareContext) {
        // Use existing care context
        return {
          referenceNumber: existingCareContext.id,
          display: existingCareContext.display,
        };
      } else {
        // Create a display name for consultation without care context
        const doctorName = consultation.doctor?.user?.name
          ? `${consultation.doctor.user.name} ${consultation.doctor.user.name || ""}`.trim()
          : "Unknown Doctor";
        const consultationDate = consultation.consultationDate
          ? new Date(consultation.consultationDate).toLocaleDateString()
          : "Unknown Date";
        const branchName = consultation.branch?.name || "Unknown Branch";

        return {
          referenceNumber: consultation.id, // Use consultation ID as reference
          display: `Consultation with ${doctorName} on ${consultationDate} at ${branchName}`,
        };
      }
    });

    console.log("Total care contexts being returned:", careContexts.length);
    console.log(
      "Care contexts:",
      careContexts.map((cc) => ({
        referenceNumber: cc.referenceNumber,
        display:
          cc.display.substring(0, 50) + (cc.display.length > 50 ? "..." : ""),
      })),
    );

    // Return patient data
    return {
      patient,
      careContexts,
      transactionId: request.transactionId,
      requestId: request.requestId,
    };
  } catch (error) {
    console.error("Error handling discovery request:", error);
    throw error;
  }
}

/**
 * Send on-discover response
 * @param patientData - Patient data
 * @returns Response from ABDM
 */
export async function sendOnDiscoverResponse(patientData: {
  patient: any;
  careContexts: { referenceNumber: string; display: string }[];
  transactionId: string;
  requestId: string;
}) {
  try {
    // Validate that we have care contexts
    if (!patientData.careContexts || patientData.careContexts.length === 0) {
      throw new Error("Cannot send on-discover response with 0 care contexts. ABDM requires count to be between 1-20.");
    }

    // Validate count is within ABDM limits (1-20)
    const count = patientData.careContexts.length;
    if (count < 1 || count > 20) {
      throw new Error(`Invalid care context count: ${count}. ABDM requires count to be between 1-20.`);
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the response payload
    const payload: OnDiscoverResponse = {
      transactionId: patientData.transactionId,
      patient: [
        {
          referenceNumber: patientData.patient.id,
          display: `${patientData.patient.firstName} ${patientData.patient.lastName}`,
          careContexts: patientData.careContexts,
          hiType: "Prescription", // Default hiType
          count: count,
        },
      ],
      matchedBy: ["MR"], // Medical Record
      response: {
        requestId: patientData.requestId,
      },
    };

    console.log(
      "Sending on-discover response:",
      JSON.stringify(payload, null, 2),
    );

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/user-initiated-linking/v3/patient/care-context/on-discover`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return response;
  } catch (error) {
    console.error("Error sending on-discover response:", error);
    throw error;
  }
}

/**
 * Handle link init request
 * @param request - Link init request
 * @returns Link reference number and OTP
 */
export async function handleLinkInit(request: LinkInitRequest) {
  try {
    console.log(
      "Handling link init request:",
      JSON.stringify(request, null, 2),
    );

    // Extract data from the request
    // const abhaAddress = request.abhaAddress; // Uncomment if needed

    // Check if we have at least one patient entry
    if (!request.patient || request.patient.length === 0) {
      console.error("No patient data in request");
      return null;
    }

    // Get the first patient entry
    const patientEntry = request.patient[0];
    const patientReferenceNumber = patientEntry.referenceNumber;
    const careContexts = patientEntry.careContexts.map((cc) => ({
      referenceNumber: cc.referenceNumber,
      display: cc.referenceNumber, // Use reference number as display for now
    }));

    // Find patient with matching reference number
    const patient = await db.patient.findFirst({
      where: {
        id: patientReferenceNumber,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        organizationId: true,
        abhaProfile: true,
      },
    });

    if (!patient) {
      console.log(
        "No patient found with reference number:",
        patientReferenceNumber,
      );
      return null;
    }

    console.log("Found patient:", patient.id);

    // Generate OTP and link reference number
    const otp = generateOTP();
    const linkRefNumber = generateLinkRefNumber();

    // Set expiry time (5 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 5);

    // Store OTP data
    await storeOTP({
      linkRefNumber,
      otp,
      transactionId: request.transactionId,
      patientId: patient.id,
      careContexts: careContexts,
      expiresAt,
      abhaAddress: request.abhaAddress, // Store original ABHA address for profile creation
    });

    // Log OTP for debugging
    console.log(`This is the OTP for linking ${patient.firstName} ${patient.lastName}: ${otp}`);

    // Get organization name for notifications
    const organization = await db.organization.findUnique({
      where: { id: patient.organizationId },
      select: { name: true },
    });

    const hospitalName = organization?.name || "Aran Care Hospital";

    // Try to send OTP via email if patient has an email
    if (patient.email) {
      try {
        // Send OTP via email
        const emailResult = await sendUILOtpEmail({
          to: patient.email,
          patientName: `${patient.firstName} ${patient.lastName}`,
          hospitalName,
          otp,
          expiresAt,
        });

        if (emailResult.success) {
          logger.info("UIL OTP email sent successfully", {
            patientId: patient.id,
            email: patient.email,
          });
        } else {
          logger.error("Failed to send UIL OTP email", {
            patientId: patient.id,
            email: patient.email,
            error: emailResult.error,
          });
        }
      } catch (emailError) {
        // Log error but continue with the flow
        logger.error("Error sending UIL OTP email", {
          patientId: patient.id,
          email: patient.email,
          error:
            emailError instanceof Error
              ? emailError.message
              : String(emailError),
        });
      }
    } else {
      logger.info("Patient does not have an email address for OTP delivery", {
        patientId: patient.id,
      });
    }

    // Try to send OTP via SMS if patient has a phone number
    if (patient.phone) {
      try {
        // Import the Fast2SMS service


        // Send OTP via Fast2SMS using the OTP route
        const smsResult = await sendHealthRecordOtpDLT({
          to: patient.phone,
          facilityName: hospitalName || "Aranco Healthcare",
          otp: otp,
        });

        if (smsResult.success) {
          // Update SMS delivery status in database
          await db.uILOtpNotify.update({
            where: { linkRefNumber },
            data: {
              smsDeliveryStatus: "sent",
              smsMessageId: smsResult.messageId,
              smsDeliveredAt: new Date(),
            },
          });

          logger.info("UIL OTP SMS sent successfully via Fast2SMS", {
            patientId: patient.id,
            phone: patient.phone,
            messageId: smsResult.messageId,
          });
        } else {
          // Update SMS delivery status as failed in database
          await db.uILOtpNotify.update({
            where: { linkRefNumber },
            data: {
              smsDeliveryStatus: "failed",
              smsError: smsResult.error || "Unknown SMS delivery error",
            },
          });

          logger.error("Failed to send UIL OTP SMS via Fast2SMS", {
            patientId: patient.id,
            phone: patient.phone,
            error: smsResult.error,
            details: smsResult.details,
          });
        }
      } catch (smsError) {
        // Update SMS delivery status as failed in database
        try {
          await db.uILOtpNotify.update({
            where: { linkRefNumber },
            data: {
              smsDeliveryStatus: "failed",
              smsError: smsError instanceof Error ? smsError.message : String(smsError),
            },
          });
        } catch (dbError) {
          logger.error("Failed to update SMS delivery status in database", {
            patientId: patient.id,
            linkRefNumber,
            error: dbError instanceof Error ? dbError.message : String(dbError),
          });
        }

        // Log error but continue with the flow
        logger.error("Error sending UIL OTP SMS via Fast2SMS", {
          patientId: patient.id,
          phone: patient.phone,
          error:
            smsError instanceof Error ? smsError.message : String(smsError),
        });
      }
    } else {
      logger.info("Patient does not have a phone number for OTP delivery", {
        patientId: patient.id,
      });
    }

    // Return link reference number and OTP
    return {
      linkRefNumber,
      otp,
      expiresAt,
      requestId: request.requestId,
      transactionId: request.transactionId,
    };
  } catch (error) {
    console.error("Error handling link init request:", error);
    throw error;
  }
}

/**
 * Send on-init response
 * @param linkData - Link data
 * @returns Response from ABDM
 */
export async function sendOnInitResponse(linkData: {
  linkRefNumber: string;
  otp: string;
  expiresAt: Date;
  requestId: string;
  transactionId: string;
}) {
  try {
    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Prepare the response payload
    const payload: OnInitResponse = {
      transactionId: linkData.transactionId,
      link: {
        referenceNumber: linkData.linkRefNumber,
        authenticationType: "MEDIATE",
        meta: {
          communicationMedium: "MOBILE",
          communicationHint: "OTP",
          communicationExpiry: linkData.expiresAt.toISOString(),
        },
      },
      response: {
        requestId: linkData.requestId,
      },
    };

    console.log("Sending on-init response:", JSON.stringify(payload, null, 2));

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/user-initiated-linking/v3/link/care-context/on-init`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    return response;
  } catch (error) {
    console.error("Error sending on-init response:", error);
    throw error;
  }
}

/**
 * Handle link confirm request
 * @param request - Link confirm request
 * @returns Boolean indicating if OTP is valid
 */
export async function handleLinkConfirm(request: LinkConfirmRequest) {
  try {
    console.log(
      "Handling link confirm request:",
      JSON.stringify(request, null, 2),
    );

    // Extract link reference number and OTP from the request
    const { linkRefNumber, token } = request.confirmation;

    // Validate OTP
    const isValid = await validateOTP(linkRefNumber, token);

    if (!isValid) {
      console.log("Invalid OTP for link reference number:", linkRefNumber);
      return { isValid: false };
    }

    // Mark OTP as verified
    await markOTPAsVerified(linkRefNumber);

    // Store request ID for later use
    await updateOTPWithRequestId(linkRefNumber, request.requestId);

    console.log("OTP verified for link reference number:", linkRefNumber);

    // Return success
    return { isValid: true, requestId: request.requestId, linkRefNumber };
  } catch (error) {
    console.error("Error handling link confirm request:", error);
    throw error;
  }
}

/**
 * Send on-confirm response
 * @param confirmData - Confirm data
 * @returns Response from ABDM
 */
export async function sendOnConfirmResponse(confirmData: {
  isValid: boolean;
  requestId?: string;
  linkRefNumber?: string;
}) {
  try {
    // If OTP is not valid or we don't have the required properties, we can't send a response
    if (
      !confirmData.isValid ||
      !confirmData.requestId ||
      !confirmData.linkRefNumber
    ) {
      throw new Error("Invalid confirm data");
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Create a unique request ID for this request
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();

    // Get OTP data
    const otpData = await getOTPData(confirmData.linkRefNumber);

    if (!otpData) {
      throw new Error("OTP data not found");
    }

    // Get patient data
    const patient = await db.patient.findUnique({
      where: {
        id: otpData.patientId,
      },
    });

    if (!patient) {
      throw new Error("Patient not found");
    }

    // Prepare the response payload
    const payload: OnConfirmResponse = {
      patient: [
        {
          referenceNumber: patient.id,
          display: `${patient.firstName} ${patient.lastName}`,
          careContexts: otpData.careContexts as any,
          hiType: "Prescription", // Default hiType
          count: (otpData.careContexts as any).length,
        },
      ],
      response: {
        requestId: confirmData.requestId,
      },
    };

    console.log(
      "Sending on-confirm response:",
      JSON.stringify(payload, null, 2),
    );

    // Make the API request
    const response = await abdmFetch(
      `${ABDM_BASE_URL}/hiecm/user-initiated-linking/v3/link/care-context/on-confirm`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "REQUEST-ID": requestId,
          TIMESTAMP: timestamp,
          "X-CM-ID": ABDM_CM_ID,
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
        signal: AbortSignal.timeout(120000), // 120 seconds timeout
      },
    );

    // After successful linking, create/update ABHA profile if patient doesn't have one
    await createOrUpdateAbhaProfileAfterLinking(patient, otpData);

    return response;
  } catch (error) {
    console.error("Error sending on-confirm response:", error);
    throw error;
  }
}

/**
 * Create or update ABHA profile after successful linking
 * This ensures that patients found via fallback search get an ABHA profile
 * so they can be found via ABHA address in future requests
 */
async function createOrUpdateAbhaProfileAfterLinking(
  patient: any,
  otpData: any,
) {
  try {
    // Check if patient already has an ABHA profile
    const existingProfile = await db.abhaProfile.findUnique({
      where: { patientId: patient.id },
    });

    if (existingProfile && existingProfile.abhaAddress) {
      console.log(
        `Patient ${patient.id} already has ABHA profile with address: ${existingProfile.abhaAddress}`,
      );
      return;
    }

    // Get the original ABHA address from the OTP data
    const abhaAddress = otpData.abhaAddress;

    if (!abhaAddress) {
      console.log(
        `Patient ${patient.id} linked successfully but no ABHA address available for profile creation.`,
      );
      return;
    }

    // Create ABHA profile for the patient
    console.log(
      `Creating ABHA profile for patient ${patient.id} with address: ${abhaAddress}`,
    );

    await db.abhaProfile.upsert({
      where: { patientId: patient.id },
      update: {
        abhaAddress: abhaAddress,
      },
      create: {
        patientId: patient.id,
        organizationId: patient.organizationId,
        abhaAddress: abhaAddress,
        abhaNumber: null, // Will be populated later if available
        healthIdNumber: null,
        aadhaarNumber: null,
        abhaCardUrl: null,
        abhaStatus: "ACTIVE",
        linkToken: null,
        linkTokenExpiry: null,
        xToken: null,
        xTokenExpiresAt: null,
        kycVerified: false,
      },
    });

    console.log(
      `Successfully created/updated ABHA profile for patient ${patient.id} with address: ${abhaAddress}`,
    );

  } catch (error) {
    console.error("Error creating ABHA profile after linking:", error);
    // Don't throw error - linking was successful, profile creation is optional
  }
}
