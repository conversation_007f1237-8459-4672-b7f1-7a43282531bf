{"info": {"_postman_id": "a5e7b8c9-d0e1-4f23-a6b7-c8d9e0f1a2b3", "name": "FHIR Bundles API Collection", "description": "A collection of API requests to generate various FHIR bundles", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Wellness Record", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/bundles/wellness-record?patientName=<PERSON>&patientId=22-7225-4829-5255&patientGender=male&patientBirthDate=1981-01-12&patientPhone=+919818512600&practitionerName=Dr. Smith&practitionerId=21-1521-3828-3227&organizationName=Health Hospital&organizationId=4567878&organizationPhone=+91 243 2634 1234&organizationEmail=<EMAIL>", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "bundles", "wellness-record"], "query": [{"key": "patientName", "value": "<PERSON>"}, {"key": "patientId", "value": "22-7225-4829-5255"}, {"key": "patientGender", "value": "male"}, {"key": "patientBirthDate", "value": "1981-01-12"}, {"key": "patientPhone", "value": "+919818512600"}, {"key": "practitioner<PERSON>ame", "value": "Dr. <PERSON>"}, {"key": "practitionerId", "value": "21-1521-3828-3227"}, {"key": "organizationName", "value": "Health Hospital"}, {"key": "organizationId", "value": "4567878"}, {"key": "organizationPhone", "value": "+91 243 2634 1234"}, {"key": "organizationEmail", "value": "<EMAIL>"}]}, "description": "Generates a FHIR Wellness Record bundle with the specified parameters"}, "response": []}, {"name": "Wellness Record (Default Values)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/bundles/wellness-record", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "bundles", "wellness-record"]}, "description": "Generates a FHIR Wellness Record bundle with default values"}, "response": []}, {"name": "Prescription", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/bundles/prescription?patientName=<PERSON>&patientId=22-7225-4829-5255&patientGender=male&patientBirthDate=1981-01-12&patientPhone=+919818512600&practitionerName=Dr. Smith&practitionerId=21-1521-3828-3227&medication1Name=Azithromycin 250 mg oral tablet&medication1Code=**********&medication1Instructions=One tablet at once&medication1AdditionalInstructions=With or after food&medication2Name=Paracetemol 500mg Oral Tab&medication2Instructions=Take two tablets orally with or after meal once a day&conditionName=Abdominal pain&conditionCode=21522001", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "bundles", "prescription"], "query": [{"key": "patientName", "value": "<PERSON>"}, {"key": "patientId", "value": "22-7225-4829-5255"}, {"key": "patientGender", "value": "male"}, {"key": "patientBirthDate", "value": "1981-01-12"}, {"key": "patientPhone", "value": "+919818512600"}, {"key": "practitioner<PERSON>ame", "value": "Dr. <PERSON>"}, {"key": "practitionerId", "value": "21-1521-3828-3227"}, {"key": "medication1Name", "value": "Azithromycin 250 mg oral tablet"}, {"key": "medication1Code", "value": "**********"}, {"key": "medication1Instructions", "value": "One tablet at once"}, {"key": "medication1AdditionalInstructions", "value": "With or after food"}, {"key": "medication2Name", "value": "Paracetemol 500mg Oral Tab"}, {"key": "medication2Instructions", "value": "Take two tablets orally with or after meal once a day"}, {"key": "conditionName", "value": "Abdominal pain"}, {"key": "conditionCode", "value": "21522001"}]}, "description": "Generates a FHIR Prescription bundle with medications and condition"}, "response": []}, {"name": "Prescription (Default Values)", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/bundles/prescription", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "bundles", "prescription"]}, "description": "Generates a FHIR Prescription bundle with default values"}, "response": []}, {"name": "Dynamic Bundle Generator", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/bundles?type=wellness-record&patientName=<PERSON>&patientId=22-7225-4829-5255", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "bundles"], "query": [{"key": "type", "value": "wellness-record"}, {"key": "patientName", "value": "<PERSON>"}, {"key": "patientId", "value": "22-7225-4829-5255"}]}, "description": "Generates a FHIR bundle based on the specified type and parameters"}, "response": []}, {"name": "Dynamic Bundle Generator (POST)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"wellness-record\",\n  \"patientName\": \"<PERSON>\",\n  \"patientId\": \"22-7225-4829-5255\",\n  \"patientGender\": \"male\",\n  \"patientBirthDate\": \"1981-01-12\",\n  \"patientPhone\": \"+919818512600\",\n  \"practitionerName\": \"<PERSON><PERSON> <PERSON>\",\n  \"practitionerId\": \"21-1521-3828-3227\"\n}"}, "url": {"raw": "http://localhost:3000/api/fhir/bundles", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "bundles"]}, "description": "Generates a FHIR bundle based on the specified type and parameters in the request body"}, "response": []}, {"name": "Observation Vital Signs", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/entry-observation-vital-signs", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "entry-observation-vital-signs"]}, "description": "Generates a FHIR Observation Vital Signs resource with default values"}, "response": []}, {"name": "Observation Body Measurement", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/entry-observation-body-measurement", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "entry-observation-body-measurement"]}, "description": "Generates a FHIR Observation Body Measurement resource with default values"}, "response": []}, {"name": "Observation Blood Pressure", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/entry-observation-blood-pressure", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "entry-observation-blood-pressure"]}, "description": "Generates a FHIR Observation Blood Pressure resource with default values"}, "response": []}, {"name": "OP Consult Note", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"patientId\": \"patient-id\",\n  \"doctorId\": \"doctor-id\",\n  \"consultationId\": \"consultation-id\"\n}"}, "url": {"raw": "http://localhost:3000/api/fhir/op-consult-note", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "op-consult-note"]}, "description": "Generates a FHIR OP Consult Note bundle based on patient, doctor, and consultation IDs"}, "response": []}, {"name": "New App - Wellness Record", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/fhir/bundles/wellness-record?patientName=<PERSON>&patientId=22-7225-4829-5255&patientGender=male&patientBirthDate=1981-01-12&patientPhone=+919818512600&practitionerName=Dr. Smith&practitionerId=21-1521-3828-3227&organizationName=Health Hospital&organizationId=4567878&organizationPhone=+91 243 2634 1234&organizationEmail=<EMAIL>", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "fhir", "bundles", "wellness-record"], "query": [{"key": "patientName", "value": "<PERSON>"}, {"key": "patientId", "value": "22-7225-4829-5255"}, {"key": "patientGender", "value": "male"}, {"key": "patientBirthDate", "value": "1981-01-12"}, {"key": "patientPhone", "value": "+919818512600"}, {"key": "practitioner<PERSON>ame", "value": "Dr. <PERSON>"}, {"key": "practitionerId", "value": "21-1521-3828-3227"}, {"key": "organizationName", "value": "Health Hospital"}, {"key": "organizationId", "value": "4567878"}, {"key": "organizationPhone", "value": "+91 243 2634 1234"}, {"key": "organizationEmail", "value": "<EMAIL>"}]}, "description": "Generates a FHIR Wellness Record bundle from the new-app API"}, "response": []}]}