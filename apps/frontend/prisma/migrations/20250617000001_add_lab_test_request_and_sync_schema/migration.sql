-- Migration: Add LabTestRequest table and sync Diagnostic<PERSON>eport
-- This migration ensures the database is in sync with the Prisma schema

-- Step 1: Create LabTestRequest table
CREATE TABLE IF NOT EXISTS "LabTestRequest" (
    "id" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "testType" TEXT NOT NULL,
    "testName" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'routine',
    "status" TEXT NOT NULL DEFAULT 'pending',
    "requestDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "requestedBy" TEXT NOT NULL,
    "notes" TEXT,
    "expectedDate" TIMESTAMP(3),
    "completedDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LabTestRequest_pkey" PRIMARY KEY ("id")
);

-- Step 2: Add foreign key constraints for LabTestRequest
DO $$
BEGIN
    -- Add foreign key constraints if they don't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'LabTestRequest_consultationId_fkey'
    ) THEN
        ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_consultationId_fkey" 
        FOREIGN KEY ("consultationId") REFERENCES "Consultation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'LabTestRequest_patientId_fkey'
    ) THEN
        ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_patientId_fkey" 
        FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'LabTestRequest_doctorId_fkey'
    ) THEN
        ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_doctorId_fkey" 
        FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'LabTestRequest_organizationId_fkey'
    ) THEN
        ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_organizationId_fkey" 
        FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'LabTestRequest_branchId_fkey'
    ) THEN
        ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_branchId_fkey" 
        FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;
END $$;

-- Step 3: Add labTestRequestId column to DiagnosticReport if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'DiagnosticReport' AND column_name = 'labTestRequestId'
    ) THEN
        ALTER TABLE "DiagnosticReport" ADD COLUMN "labTestRequestId" TEXT;
    END IF;
END $$;

-- Step 4: Add foreign key constraint for DiagnosticReport.labTestRequestId
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'DiagnosticReport_labTestRequestId_fkey'
    ) THEN
        ALTER TABLE "DiagnosticReport" ADD CONSTRAINT "DiagnosticReport_labTestRequestId_fkey" 
        FOREIGN KEY ("labTestRequestId") REFERENCES "LabTestRequest"("id") ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END $$;

-- Step 5: Ensure Organization status field exists (from previous migration)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'Organization' AND column_name = 'status'
    ) THEN
        ALTER TABLE "Organization" ADD COLUMN "status" TEXT NOT NULL DEFAULT 'active';
        
        -- Create index for better query performance
        CREATE INDEX IF NOT EXISTS "Organization_status_idx" ON "Organization"("status");
        
        -- Add check constraint to ensure only valid status values
        ALTER TABLE "Organization" ADD CONSTRAINT "Organization_status_check"
        CHECK ("status" IN ('active', 'inactive'));
    END IF;
END $$;

-- Step 6: Create Invoice table if it doesn't exist
CREATE TABLE IF NOT EXISTS "Invoice" (
    "id" TEXT NOT NULL,
    "invoiceNumber" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "invoiceDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dueDate" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'issued',
    "type" TEXT NOT NULL DEFAULT 'consultation',
    "subtotal" DECIMAL(10,2) NOT NULL,
    "taxAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "discountAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "totalAmount" DECIMAL(10,2) NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'INR',
    "paymentTerms" TEXT,
    "paymentStatus" TEXT NOT NULL DEFAULT 'pending',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "Invoice_pkey" PRIMARY KEY ("id")
);

-- Step 7: Create InvoiceItem table if it doesn't exist
CREATE TABLE IF NOT EXISTS "InvoiceItem" (
    "id" TEXT NOT NULL,
    "invoiceId" TEXT NOT NULL,
    "sequence" INTEGER NOT NULL,
    "serviceCode" TEXT,
    "serviceDisplay" TEXT NOT NULL,
    "category" TEXT NOT NULL DEFAULT 'service',
    "quantity" DECIMAL(10,2) NOT NULL DEFAULT 1,
    "unitPrice" DECIMAL(10,2) NOT NULL,
    "mrp" DECIMAL(10,2),
    "discountAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "taxRate" DECIMAL(5,2) NOT NULL DEFAULT 0,
    "cgstAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "sgstAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "igstAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "totalAmount" DECIMAL(10,2) NOT NULL,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "InvoiceItem_pkey" PRIMARY KEY ("id")
);

-- Step 8: Add unique constraints and foreign keys for Invoice
DO $$
BEGIN
    -- Add unique constraints if they don't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'Invoice_invoiceNumber_key'
    ) THEN
        CREATE UNIQUE INDEX "Invoice_invoiceNumber_key" ON "Invoice"("invoiceNumber");
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'Invoice_consultationId_key'
    ) THEN
        CREATE UNIQUE INDEX "Invoice_consultationId_key" ON "Invoice"("consultationId");
    END IF;

    -- Add foreign key constraints if they don't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'Invoice_consultationId_fkey'
    ) THEN
        ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_consultationId_fkey"
        FOREIGN KEY ("consultationId") REFERENCES "Consultation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'Invoice_patientId_fkey'
    ) THEN
        ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_patientId_fkey"
        FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'Invoice_doctorId_fkey'
    ) THEN
        ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_doctorId_fkey"
        FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'Invoice_organizationId_fkey'
    ) THEN
        ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_organizationId_fkey"
        FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'Invoice_branchId_fkey'
    ) THEN
        ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_branchId_fkey"
        FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'InvoiceItem_invoiceId_fkey'
    ) THEN
        ALTER TABLE "InvoiceItem" ADD CONSTRAINT "InvoiceItem_invoiceId_fkey"
        FOREIGN KEY ("invoiceId") REFERENCES "Invoice"("id") ON DELETE CASCADE ON UPDATE CASCADE;
    END IF;
END $$;

-- Verification queries (optional - for manual verification)
-- SELECT COUNT(*) as lab_test_requests FROM "LabTestRequest";
-- SELECT COUNT(*) as diagnostic_reports_with_lab_test FROM "DiagnosticReport" WHERE "labTestRequestId" IS NOT NULL;
-- SELECT COUNT(*) as organizations_with_status FROM "Organization" WHERE "status" IS NOT NULL;
-- SELECT COUNT(*) as invoices FROM "Invoice";
-- SELECT COUNT(*) as invoice_items FROM "InvoiceItem";
