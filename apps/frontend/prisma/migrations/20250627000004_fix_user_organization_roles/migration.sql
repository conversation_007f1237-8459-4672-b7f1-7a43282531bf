-- Fix UserOrganization roles field default value and existing data

-- First, update any existing records that have string roles to proper JSON arrays
UPDATE "UserOrganization" 
SET "roles" = CASE 
  WHEN "roles"::text = '"member"' THEN '["member"]'::jsonb
  WHEN "roles"::text = '"doctor"' THEN '["doctor"]'::jsonb
  WHEN "roles"::text = '"staff"' THEN '["staff"]'::jsonb
  WHEN "roles"::text = '"admin"' THEN '["admin"]'::jsonb
  WHEN "roles"::text = '"hospitalAdmin"' THEN '["hospitalAdmin"]'::jsonb
  WHEN "roles"::text = '"branchAdmin"' THEN '["branchAdmin"]'::jsonb
  WHEN "roles"::text = '"[]"' THEN '[]'::jsonb
  ELSE "roles"
END
WHERE "roles"::text LIKE '"%"' AND "roles"::text NOT LIKE '[%]';

-- Ensure all roles are arrays, not strings
UPDATE "UserOrganization" 
SET "roles" = '[]'::jsonb 
WHERE "roles"::text = '""' OR "roles"::text = 'null';
