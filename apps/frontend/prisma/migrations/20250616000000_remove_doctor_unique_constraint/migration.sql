-- Migration to remove unique constraint on <PERSON>.userId and add composite unique constraint
-- This allows users to have doctor profiles in multiple organizations

-- Step 1: Drop the existing unique constraint on userId
ALTER TABLE "Doctor" DROP CONSTRAINT IF EXISTS "Doctor_userId_key";

-- Step 2: Add composite unique constraint on userId and organizationId
-- This ensures a user can only have one doctor profile per organization
ALTER TABLE "Doctor" ADD CONSTRAINT "Doctor_userId_organizationId_key" UNIQUE ("userId", "organizationId");

-- Step 3: Create index for better query performance
CREATE INDEX IF NOT EXISTS "Doctor_userId_idx" ON "Doctor"("userId");
CREATE INDEX IF NOT EXISTS "Doctor_organizationId_idx" ON "Doctor"("organizationId");
