-- Remove unique constraint from HealthRecordFetch transactionId if it exists
-- This migration handles the case where the constraint might already be removed

DO $$
BEGIN
    -- Check if the unique constraint exists and drop it
    IF EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'HealthRecordFetch_transactionId_key' 
        AND table_name = 'HealthRecordFetch'
    ) THEN
        ALTER TABLE "HealthRecordFetch" DROP CONSTRAINT "HealthRecordFetch_transactionId_key";
    END IF;
END $$;
