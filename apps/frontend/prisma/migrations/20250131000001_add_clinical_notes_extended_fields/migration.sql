-- Add extended fields to ClinicalNote table for removing hardcoded values from OPConsult bundle
-- Migration: 20250131000001_add_clinical_notes_extended_fields

-- Chief Complaints date range fields
ALTER TABLE "ClinicalNote" ADD COLUMN "chiefComplaintsDateFrom" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "chiefComplaintsDateTo" TEXT;

-- Medical History date range fields  
ALTER TABLE "ClinicalNote" ADD COLUMN "medicalHistoryDateFrom" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "medicalHistoryDateTo" TEXT;

-- Investigation/Service Request fields
ALTER TABLE "ClinicalNote" ADD COLUMN "investigationStatus" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "investigationSpecimen" TEXT;

-- Procedure extended fields
ALTER TABLE "ClinicalNote" ADD COLUMN "procedureStatus" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "procedureReason" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "procedureOutcome" TEXT;

-- Follow-up extended fields
ALTER TABLE "ClinicalNote" ADD COLUMN "followUpServiceType" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "followUpDate" TEXT;

-- Referral fields
ALTER TABLE "ClinicalNote" ADD COLUMN "referralReason" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "referralSpecialty" TEXT;
ALTER TABLE "ClinicalNote" ADD COLUMN "referralProvider" TEXT;

-- Add comments for documentation
COMMENT ON COLUMN "ClinicalNote"."chiefComplaintsDateFrom" IS 'Start date for chief complaints symptoms';
COMMENT ON COLUMN "ClinicalNote"."chiefComplaintsDateTo" IS 'End date for chief complaints symptoms (null if ongoing)';
COMMENT ON COLUMN "ClinicalNote"."medicalHistoryDateFrom" IS 'Start date for medical history condition';
COMMENT ON COLUMN "ClinicalNote"."medicalHistoryDateTo" IS 'End date for medical history condition (null if ongoing)';
COMMENT ON COLUMN "ClinicalNote"."investigationStatus" IS 'Status of investigation request (ACTIVE, COMPLETED, CANCELLED, SUSPENDED)';
COMMENT ON COLUMN "ClinicalNote"."investigationSpecimen" IS 'Type of specimen required for investigation';
COMMENT ON COLUMN "ClinicalNote"."procedureStatus" IS 'Status of procedure (COMPLETED, IN_PROGRESS, CANCELLED, SUSPENDED)';
COMMENT ON COLUMN "ClinicalNote"."procedureReason" IS 'Reason for performing the procedure';
COMMENT ON COLUMN "ClinicalNote"."procedureOutcome" IS 'Outcome or result of the procedure';
COMMENT ON COLUMN "ClinicalNote"."followUpServiceType" IS 'Type of follow-up service (OPConsultation, IPConsultation, Emergency, etc.)';
COMMENT ON COLUMN "ClinicalNote"."followUpDate" IS 'Recommended date for follow-up appointment';
COMMENT ON COLUMN "ClinicalNote"."referralReason" IS 'Reason for referral to another provider';
COMMENT ON COLUMN "ClinicalNote"."referralSpecialty" IS 'Medical specialty for referral';
COMMENT ON COLUMN "ClinicalNote"."referralProvider" IS 'Specific provider or facility for referral';
