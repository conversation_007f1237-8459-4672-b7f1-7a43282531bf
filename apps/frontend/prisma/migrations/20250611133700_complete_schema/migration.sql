-- Complete Healthcare Application Schema Migration
-- This migration creates the entire database schema for the healthcare application
-- Generated on: 2025-06-11

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- AUTHENTICATION & USER MANAGEMENT TABLES
-- =============================================

-- NextAuth Account table
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- NextAuth Session table
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- NextAuth Verification Token table
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- Core User table
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "password" TEXT,
    "image" TEXT,
    "role" TEXT NOT NULL DEFAULT 'user',
    "currentBranchId" TEXT,
    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- ORGANIZATION & BRANCH MANAGEMENT
-- =============================================

-- Organization table
CREATE TABLE "Organization" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "logo" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "onboardingCompleted" BOOLEAN NOT NULL DEFAULT false,
    "slug" TEXT NOT NULL,
    CONSTRAINT "Organization_pkey" PRIMARY KEY ("id")
);

-- Branch table
CREATE TABLE "Branch" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "facilityType" TEXT NOT NULL DEFAULT 'clinic',
    "phone" TEXT,
    "email" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "pincode" TEXT,
    "latitude" TEXT,
    "longitude" TEXT,
    "isHeadOffice" BOOLEAN NOT NULL DEFAULT false,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "acceptsInsurance" BOOLEAN NOT NULL DEFAULT false,
    "hasAdminStaff" BOOLEAN NOT NULL DEFAULT false,
    "hasDoctors" BOOLEAN NOT NULL DEFAULT false,
    "hasNurses" BOOLEAN NOT NULL DEFAULT false,
    "insuranceProviders" TEXT,
    "services" TEXT,
    "specialties" TEXT,
    "teamSize" INTEGER NOT NULL DEFAULT 0,
    "hipId" TEXT,
    "hipStatus" TEXT,
    "hipRegistrationDate" TIMESTAMP(3),
    "hipRegistrationDetails" JSONB,
    "hipBridgeId" TEXT,
    "hipBridgeRegistrationDate" TIMESTAMP(3),
    "hipBridgeRegistrationDetails" JSONB,
    CONSTRAINT "Branch_pkey" PRIMARY KEY ("id")
);

-- User-Organization relationship table
CREATE TABLE "UserOrganization" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "roles" JSONB NOT NULL DEFAULT '[]',
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "UserOrganization_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- DEPARTMENT & CARE TYPE MANAGEMENT
-- =============================================

-- Department table
CREATE TABLE "Department" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "departmentCode" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    CONSTRAINT "Department_pkey" PRIMARY KEY ("id")
);

-- Branch-Department relationship table
CREATE TABLE "BranchDepartment" (
    "id" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "departmentId" TEXT NOT NULL,
    "locationDetails" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "departmentHead" TEXT,
    "emailAddress" TEXT,
    "phoneNumber" TEXT,
    "workingHours" TEXT,
    CONSTRAINT "BranchDepartment_pkey" PRIMARY KEY ("id")
);

-- Care Type table
CREATE TABLE "CareType" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "price" DOUBLE PRECISION NOT NULL,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "departmentId" TEXT NOT NULL,
    CONSTRAINT "CareType_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- DOCTOR MANAGEMENT
-- =============================================

-- Doctor table
CREATE TABLE "Doctor" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "profileDescription" TEXT,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "consultationFee" DOUBLE PRECISION,
    "experience" INTEGER,
    "qualification" TEXT,
    "specialization" TEXT,
    "departmentId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "contactEmail" TEXT,
    "contactPhone" TEXT,
    "joiningDate" TIMESTAMP(3),
    "yearsOfExperience" INTEGER NOT NULL DEFAULT 0,
    CONSTRAINT "Doctor_pkey" PRIMARY KEY ("id")
);

-- Doctor-Branch relationship table
CREATE TABLE "DoctorBranch" (
    "id" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "DoctorBranch_pkey" PRIMARY KEY ("id")
);

-- Doctor Document table
CREATE TABLE "DoctorDocument" (
    "id" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "DoctorDocument_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- STAFF MANAGEMENT
-- =============================================

-- Staff table
CREATE TABLE "Staff" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "organizationId" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "contactEmail" TEXT,
    "contactNumber" TEXT,
    "departmentId" TEXT,
    "email" TEXT NOT NULL,
    "joiningDate" TIMESTAMP(3),
    "name" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    CONSTRAINT "Staff_pkey" PRIMARY KEY ("id")
);

-- Staff-Branch relationship table
CREATE TABLE "StaffBranch" (
    "id" TEXT NOT NULL,
    "staffId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "StaffBranch_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- INVITATION SYSTEM
-- =============================================

-- Universal User Invitation table
CREATE TABLE "UserInvitation" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "roles" JSONB NOT NULL DEFAULT '[]',
    "branchIds" JSONB,
    "departmentId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "invitedBy" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    CONSTRAINT "UserInvitation_pkey" PRIMARY KEY ("id")
);

-- Doctor Invitation table (backward compatibility)
CREATE TABLE "DoctorInvitation" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "branchIds" JSONB,
    "departmentId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "invitedBy" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    CONSTRAINT "DoctorInvitation_pkey" PRIMARY KEY ("id")
);

-- Branch Admin Invitation table
CREATE TABLE "BranchAdminInvitation" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "invitedBy" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    CONSTRAINT "BranchAdminInvitation_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- DOCTOR SCHEDULING SYSTEM
-- =============================================

-- Doctor Schedule table
CREATE TABLE "DoctorSchedule" (
    "id" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "defaultDuration" INTEGER NOT NULL,
    "description" TEXT,
    "name" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    CONSTRAINT "DoctorSchedule_pkey" PRIMARY KEY ("id")
);

-- Schedule Slot table
CREATE TABLE "ScheduleSlot" (
    "id" TEXT NOT NULL,
    "doctorScheduleId" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "duration" INTEGER,
    "isRecurring" BOOLEAN NOT NULL DEFAULT true,
    CONSTRAINT "ScheduleSlot_pkey" PRIMARY KEY ("id")
);

-- Schedule Override table
CREATE TABLE "ScheduleOverride" (
    "id" TEXT NOT NULL,
    "doctorScheduleId" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "reason" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "duration" INTEGER,
    CONSTRAINT "ScheduleOverride_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- PATIENT MANAGEMENT
-- =============================================

-- Patient table
CREATE TABLE "Patient" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "dateOfBirth" TIMESTAMP(3) NOT NULL,
    "gender" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "email" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "pincode" TEXT,
    "emergencyContactName" TEXT,
    "emergencyContactPhone" TEXT,
    "emergencyContactRelation" TEXT,
    "bloodGroup" TEXT,
    "allergies" TEXT,
    "currentMedications" TEXT,
    "familyMedicalHistory" TEXT,
    "organizationId" TEXT NOT NULL,
    "primaryBranchId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "abhaDataSynced" BOOLEAN DEFAULT false,
    "abhaDataSyncedAt" TIMESTAMP(6),
    "alternatePhone" TEXT,
    "chronicDiseases" TEXT,
    "country" TEXT NOT NULL DEFAULT 'India',
    "district" TEXT,
    "insuranceExpiryDate" TIMESTAMP(3),
    "insurancePolicyNumber" TEXT,
    "insuranceProvider" TEXT,
    "maritalStatus" TEXT,
    "middleName" TEXT,
    "occupation" TEXT,
    "registrationDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'active',
    CONSTRAINT "Patient_pkey" PRIMARY KEY ("id")
);

-- Patient Document table
CREATE TABLE "PatientDocument" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "PatientDocument_pkey" PRIMARY KEY ("id")
);

-- Patient-Branch relationship table
CREATE TABLE "PatientBranch" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "visitDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "PatientBranch_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- APPOINTMENT & QUEUE MANAGEMENT
-- =============================================

-- Appointment table
CREATE TABLE "Appointment" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'scheduled',
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'regular',
    "appointmentDate" TIMESTAMP(3) NOT NULL,
    "cancellationReason" TEXT,
    "duration" INTEGER NOT NULL,
    CONSTRAINT "Appointment_pkey" PRIMARY KEY ("id")
);

-- Queue Status table
CREATE TABLE "QueueStatus" (
    "id" TEXT NOT NULL,
    "appointmentId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'waiting',
    "queueNumber" INTEGER NOT NULL,
    "estimatedStartTime" TIMESTAMP(3),
    "actualStartTime" TIMESTAMP(3),
    "completionTime" TIMESTAMP(3),
    "pauseReason" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "QueueStatus_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- CONSULTATION MANAGEMENT
-- =============================================

-- Consultation table
CREATE TABLE "Consultation" (
    "id" TEXT NOT NULL,
    "appointmentId" TEXT,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "consultationDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "startTime" TEXT,
    "endTime" TEXT,
    "status" TEXT NOT NULL DEFAULT 'in-progress',
    "followUpDate" TIMESTAMP(3),
    "followUpNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "Consultation_pkey" PRIMARY KEY ("id")
);

-- Vitals table
CREATE TABLE "Vitals" (
    "id" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "bloodPressureSystolic" INTEGER,
    "bloodPressureDiastolic" INTEGER,
    "pulse" INTEGER,
    "temperature" DECIMAL(5,2),
    "respiratoryRate" INTEGER,
    "oxygenSaturation" INTEGER,
    "height" DECIMAL(5,2),
    "weight" DECIMAL(5,2),
    "bmi" DECIMAL(5,2),
    "notes" TEXT,
    "recordedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "Vitals_pkey" PRIMARY KEY ("id")
);

-- Clinical Note table
CREATE TABLE "ClinicalNote" (
    "id" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "snomedTags" JSONB,
    "snomedDiagnoses" TEXT,
    "noteType" TEXT NOT NULL DEFAULT 'general',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "ClinicalNote_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- PRESCRIPTION MANAGEMENT
-- =============================================

-- Prescription table
CREATE TABLE "Prescription" (
    "id" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "prescriptionDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "validUntil" TIMESTAMP(3),
    "instructions" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "Prescription_pkey" PRIMARY KEY ("id")
);

-- Prescription Item table
CREATE TABLE "PrescriptionItem" (
    "id" TEXT NOT NULL,
    "prescriptionId" TEXT NOT NULL,
    "medicationName" TEXT NOT NULL,
    "dosage" TEXT NOT NULL,
    "frequency" TEXT NOT NULL,
    "duration" TEXT NOT NULL,
    "route" TEXT,
    "instructions" TEXT,
    "snomedCode" TEXT,
    "rxcui" TEXT,
    "rxNormData" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "PrescriptionItem_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- DIAGNOSTIC & PROCEDURE MANAGEMENT
-- =============================================

-- Diagnostic Report table
CREATE TABLE "DiagnosticReport" (
    "id" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "reportType" TEXT NOT NULL,
    "reportDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'final',
    "category" TEXT,
    "code" TEXT NOT NULL,
    "codeDisplay" TEXT NOT NULL,
    "conclusion" TEXT,
    "presentedForm" JSONB,
    "result" JSONB,
    "performer" TEXT,
    "specimen" TEXT,
    "effectiveDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "issuedDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "DiagnosticReport_pkey" PRIMARY KEY ("id")
);

-- Procedure table
CREATE TABLE "Procedure" (
    "id" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "procedureDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" TEXT NOT NULL DEFAULT 'completed',
    "category" TEXT,
    "code" TEXT NOT NULL,
    "codeDisplay" TEXT NOT NULL,
    "bodySite" TEXT,
    "outcome" TEXT,
    "complication" TEXT,
    "followUp" TEXT,
    "notes" TEXT,
    "performer" TEXT,
    "location" TEXT,
    "reasonCode" TEXT,
    "reasonDisplay" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "Procedure_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- ALLERGY & IMMUNIZATION MANAGEMENT
-- =============================================

-- Allergy Intolerance table
CREATE TABLE "AllergyIntolerance" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "consultationId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "asserter" TEXT,
    "category" TEXT NOT NULL,
    "clinicalStatus" TEXT NOT NULL DEFAULT 'active',
    "code" TEXT NOT NULL,
    "codeDisplay" TEXT NOT NULL,
    "criticality" TEXT NOT NULL DEFAULT 'low',
    "lastOccurrence" TIMESTAMP(3),
    "note" TEXT,
    "onsetDateTime" TIMESTAMP(3),
    "recordedDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "recorder" TEXT,
    "type" TEXT NOT NULL DEFAULT 'allergy',
    "verificationStatus" TEXT NOT NULL DEFAULT 'confirmed',
    "reaction" JSONB,
    CONSTRAINT "AllergyIntolerance_pkey" PRIMARY KEY ("id")
);

-- Immunization table
CREATE TABLE "Immunization" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "consultationId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'completed',
    "statusReason" TEXT,
    "vaccineCode" TEXT NOT NULL,
    "vaccineDisplay" TEXT NOT NULL,
    "occurrenceDateTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "recorded" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "primarySource" BOOLEAN NOT NULL DEFAULT true,
    "reportOrigin" TEXT,
    "location" TEXT,
    "manufacturer" TEXT,
    "lotNumber" TEXT,
    "expirationDate" TIMESTAMP(3),
    "site" TEXT,
    "route" TEXT,
    "doseQuantity" TEXT,
    "performer" TEXT,
    "note" TEXT,
    "reasonCode" TEXT,
    "reasonDisplay" TEXT,
    "isSubpotent" BOOLEAN NOT NULL DEFAULT false,
    "subpotentReason" TEXT,
    "programEligibility" TEXT,
    "fundingSource" TEXT,
    "reaction" JSONB,
    "protocolApplied" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "Immunization_pkey" PRIMARY KEY ("id")
);

-- Document Reference table
CREATE TABLE "DocumentReference" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "consultationId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'current',
    "docStatus" TEXT NOT NULL DEFAULT 'final',
    "type" TEXT NOT NULL,
    "typeDisplay" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "categoryDisplay" TEXT NOT NULL,
    "subject" TEXT,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "author" TEXT,
    "authenticator" TEXT,
    "custodian" TEXT,
    "description" TEXT,
    "securityLabel" TEXT,
    "content" JSONB NOT NULL,
    "context" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "DocumentReference_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- ABDM/ABHA INTEGRATION TABLES
-- =============================================

-- ABHA Profile table
CREATE TABLE "AbhaProfile" (
    "id" TEXT NOT NULL,
    "abhaNumber" TEXT,
    "abhaAddress" TEXT,
    "healthIdNumber" TEXT,
    "organizationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "aadhaarNumber" TEXT,
    "abhaCardUrl" TEXT,
    "abhaStatus" TEXT,
    "linkToken" TEXT,
    "linkTokenExpiry" TIMESTAMP(3),
    "patientId" TEXT NOT NULL,
    "xToken" TEXT,
    "xTokenExpiresAt" TIMESTAMP(3),
    CONSTRAINT "AbhaProfile_pkey" PRIMARY KEY ("id")
);

-- ABHA Link Token table
CREATE TABLE "AbhaLinkToken" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "linkToken" TEXT NOT NULL,
    "linkTokenExpiry" TIMESTAMP(3) NOT NULL,
    "hipId" TEXT NOT NULL,
    "requestId" TEXT,
    CONSTRAINT "AbhaLinkToken_pkey" PRIMARY KEY ("id")
);

-- Care Context table
CREATE TABLE "CareContext" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "display" TEXT NOT NULL,
    "hiTypes" TEXT[],
    "additionalInfo" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'Requested',
    "requestId" TEXT,
    "consultationId" TEXT NOT NULL,
    CONSTRAINT "CareContext_pkey" PRIMARY KEY ("id")
);

-- Consent table
CREATE TABLE "Consent" (
    "id" TEXT NOT NULL,
    "consentRequestId" TEXT NOT NULL,
    "consentId" TEXT,
    "patientId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'REQUESTED',
    "purpose" TEXT NOT NULL,
    "careContexts" JSONB,
    "hiTypes" TEXT[],
    "permission" JSONB NOT NULL,
    "consentArtifact" TEXT,
    "expiryDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "revokedAt" TIMESTAMP(3),
    "careTypeId" TEXT,
    CONSTRAINT "Consent_pkey" PRIMARY KEY ("id")
);

-- Consent Care Context relationship table
CREATE TABLE "ConsentCareContext" (
    "id" TEXT NOT NULL,
    "consentId" TEXT NOT NULL,
    "careContextId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "ConsentCareContext_pkey" PRIMARY KEY ("id")
);

-- Consent Audit Log table
CREATE TABLE "ConsentAuditLog" (
    "id" TEXT NOT NULL,
    "consentId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "actorId" TEXT NOT NULL,
    "actorRole" TEXT NOT NULL,
    "details" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ConsentAuditLog_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- HEALTH INFORMATION EXCHANGE TABLES
-- =============================================

-- Health Record Operation table
CREATE TABLE "HealthRecordOperation" (
    "id" TEXT NOT NULL,
    "fetchId" TEXT NOT NULL,
    "operationType" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "requestId" TEXT,
    "responseData" JSONB,
    "errorData" JSONB,
    "timestamp" TIMESTAMP(3),
    "patientId" TEXT,
    "recordType" TEXT,
    "recordId" TEXT,
    "bundleId" TEXT,
    "consentId" TEXT,
    "transactionId" TEXT,
    "errorMessage" TEXT,
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,
    CONSTRAINT "HealthRecordOperation_pkey" PRIMARY KEY ("id")
);

-- Health Record Fetch table
CREATE TABLE "HealthRecordFetch" (
    "id" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "consentId" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "requestTimestamp" TIMESTAMP(3),
    "responseTimestamp" TIMESTAMP(3),
    "responseData" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "HealthRecordFetch_pkey" PRIMARY KEY ("id")
);

-- Consent Notify table
CREATE TABLE "ConsentNotify" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "consentId" TEXT NOT NULL,
    "schemaVersion" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "careContexts" JSONB NOT NULL,
    "purpose" JSONB NOT NULL,
    "hipId" TEXT NOT NULL,
    "consentManagerId" TEXT NOT NULL,
    "hiTypes" TEXT[],
    "permission" JSONB NOT NULL,
    "signature" TEXT NOT NULL,
    "grantAcknowledgement" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "ConsentNotify_pkey" PRIMARY KEY ("id")
);

-- HI Request table
CREATE TABLE "HiRequest" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "consentId" TEXT NOT NULL,
    "dateRange" JSONB NOT NULL,
    "dataPushUrl" TEXT NOT NULL,
    "keyMaterial" JSONB NOT NULL,
    "acknowledgementSent" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "HiRequest_pkey" PRIMARY KEY ("id")
);

-- Care Context Notify table
CREATE TABLE "CareContextNotify" (
    "id" TEXT NOT NULL,
    "abhaAddress" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "response" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "CareContextNotify_pkey" PRIMARY KEY ("id")
);

-- Generate Token Notify table
CREATE TABLE "GenerateTokenNotify" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "error" JSONB,
    "response" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "GenerateTokenNotify_pkey" PRIMARY KEY ("id")
);

-- UIL OTP Notify table
CREATE TABLE "UILOtpNotify" (
    "id" TEXT NOT NULL,
    "linkRefNumber" TEXT NOT NULL,
    "otp" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "careContexts" JSONB NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "requestId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "UILOtpNotify_pkey" PRIMARY KEY ("id")
);

-- UIL OTP table
CREATE TABLE "UILOtp" (
    "id" TEXT NOT NULL,
    "linkRefNumber" TEXT NOT NULL,
    "otp" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "careContexts" JSONB NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "requestId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "UILOtp_pkey" PRIMARY KEY ("id")
);

-- HIU Consent Notify table
CREATE TABLE "HiuConsentNotify" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "webhookType" TEXT NOT NULL,
    "payload" JSONB NOT NULL,
    "headers" JSONB,
    "processed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "HiuConsentNotify_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- FHIR BUNDLE & RESOURCE MANAGEMENT
-- =============================================

-- FHIR Bundle table
CREATE TABLE "FhirBundle" (
    "id" TEXT NOT NULL,
    "bundleType" TEXT NOT NULL,
    "bundleJson" JSONB NOT NULL,
    "consultationId" TEXT,
    "patientId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "bundleId" TEXT NOT NULL,
    "consentId" TEXT,
    "transactionId" TEXT,
    "packageChecksum" TEXT,
    "statusDetails" JSONB,
    "organizationId" TEXT NOT NULL,
    CONSTRAINT "FhirBundle_pkey" PRIMARY KEY ("id")
);

-- FHIR Resource table
CREATE TABLE "FhirResource" (
    "id" TEXT NOT NULL,
    "resourceType" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "fhirJson" JSONB NOT NULL,
    "patientId" TEXT,
    "organizationId" TEXT NOT NULL,
    "sourceType" TEXT NOT NULL,
    "sourceId" TEXT NOT NULL,
    "bundleId" TEXT NOT NULL,
    "version" TEXT NOT NULL DEFAULT '4.0.1',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "FhirResource_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- OTP & MOBILE VERIFICATION
-- =============================================

-- Mobile OTP table
CREATE TABLE "MobileOtp" (
    "id" TEXT NOT NULL,
    "mobile" TEXT NOT NULL,
    "otp" TEXT NOT NULL,
    "purpose" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "maxAttempts" INTEGER NOT NULL DEFAULT 3,
    "patientId" TEXT,
    "userId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "MobileOtp_pkey" PRIMARY KEY ("id")
);

-- =============================================
-- INDEXES
-- =============================================

-- Authentication indexes
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- User and Organization indexes
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");
CREATE UNIQUE INDEX "Organization_slug_key" ON "Organization"("slug");
CREATE UNIQUE INDEX "UserOrganization_userId_organizationId_key" ON "UserOrganization"("userId", "organizationId");

-- Department and Branch indexes
CREATE UNIQUE INDEX "BranchDepartment_branchId_departmentId_key" ON "BranchDepartment"("branchId", "departmentId");

-- Doctor and Staff indexes
CREATE UNIQUE INDEX "Doctor_userId_key" ON "Doctor"("userId");
CREATE UNIQUE INDEX "DoctorBranch_doctorId_branchId_key" ON "DoctorBranch"("doctorId", "branchId");
CREATE UNIQUE INDEX "Staff_userId_key" ON "Staff"("userId");
CREATE UNIQUE INDEX "StaffBranch_staffId_branchId_key" ON "StaffBranch"("staffId", "branchId");

-- Invitation indexes
CREATE UNIQUE INDEX "UserInvitation_token_key" ON "UserInvitation"("token");
CREATE UNIQUE INDEX "DoctorInvitation_token_key" ON "DoctorInvitation"("token");
CREATE UNIQUE INDEX "BranchAdminInvitation_token_key" ON "BranchAdminInvitation"("token");

-- Patient indexes
CREATE UNIQUE INDEX "PatientBranch_patientId_branchId_key" ON "PatientBranch"("patientId", "branchId");

-- Appointment and Queue indexes
CREATE UNIQUE INDEX "QueueStatus_appointmentId_key" ON "QueueStatus"("appointmentId");
CREATE UNIQUE INDEX "Consultation_appointmentId_key" ON "Consultation"("appointmentId");

-- ABHA and Care Context indexes
CREATE UNIQUE INDEX "AbhaProfile_patientId_key" ON "AbhaProfile"("patientId");
CREATE UNIQUE INDEX "CareContext_organizationId_consultationId_key" ON "CareContext"("organizationId", "consultationId");
CREATE UNIQUE INDEX "ConsentCareContext_consentId_careContextId_key" ON "ConsentCareContext"("consentId", "careContextId");

-- Health Record indexes
CREATE UNIQUE INDEX "HealthRecordFetch_transactionId_key" ON "HealthRecordFetch"("transactionId");
CREATE UNIQUE INDEX "Consent_consentRequestId_key" ON "Consent"("consentRequestId");
CREATE UNIQUE INDEX "Consent_consentId_key" ON "Consent"("consentId");
CREATE UNIQUE INDEX "ConsentNotify_requestId_key" ON "ConsentNotify"("requestId");
CREATE UNIQUE INDEX "HiRequest_requestId_key" ON "HiRequest"("requestId");
CREATE UNIQUE INDEX "HiRequest_transactionId_key" ON "HiRequest"("transactionId");
CREATE UNIQUE INDEX "GenerateTokenNotify_requestId_key" ON "GenerateTokenNotify"("requestId");
CREATE UNIQUE INDEX "UILOtpNotify_linkRefNumber_key" ON "UILOtpNotify"("linkRefNumber");
CREATE UNIQUE INDEX "UILOtp_linkRefNumber_key" ON "UILOtp"("linkRefNumber");
CREATE UNIQUE INDEX "HiuConsentNotify_requestId_key" ON "HiuConsentNotify"("requestId");

-- FHIR indexes
CREATE UNIQUE INDEX "FhirBundle_bundleId_key" ON "FhirBundle"("bundleId");
CREATE UNIQUE INDEX "FhirResource_resourceType_resourceId_bundleId_key" ON "FhirResource"("resourceType", "resourceId", "bundleId");

-- Mobile OTP indexes
CREATE INDEX "MobileOtp_mobile_purpose_idx" ON "MobileOtp"("mobile", "purpose");

-- =============================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================

-- Authentication foreign keys
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Organization and Branch foreign keys
ALTER TABLE "Branch" ADD CONSTRAINT "Branch_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "UserOrganization" ADD CONSTRAINT "UserOrganization_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "UserOrganization" ADD CONSTRAINT "UserOrganization_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "User" ADD CONSTRAINT "User_currentBranchId_fkey" FOREIGN KEY ("currentBranchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Department foreign keys
ALTER TABLE "Department" ADD CONSTRAINT "Department_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "BranchDepartment" ADD CONSTRAINT "BranchDepartment_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "BranchDepartment" ADD CONSTRAINT "BranchDepartment_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Care Type foreign keys
ALTER TABLE "CareType" ADD CONSTRAINT "CareType_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "CareType" ADD CONSTRAINT "CareType_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Doctor foreign keys
ALTER TABLE "Doctor" ADD CONSTRAINT "Doctor_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Doctor" ADD CONSTRAINT "Doctor_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Doctor" ADD CONSTRAINT "Doctor_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DoctorBranch" ADD CONSTRAINT "DoctorBranch_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DoctorBranch" ADD CONSTRAINT "DoctorBranch_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DoctorDocument" ADD CONSTRAINT "DoctorDocument_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Staff foreign keys
ALTER TABLE "Staff" ADD CONSTRAINT "Staff_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Staff" ADD CONSTRAINT "Staff_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Staff" ADD CONSTRAINT "Staff_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "StaffBranch" ADD CONSTRAINT "StaffBranch_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "StaffBranch" ADD CONSTRAINT "StaffBranch_staffId_fkey" FOREIGN KEY ("staffId") REFERENCES "Staff"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Invitation foreign keys
ALTER TABLE "UserInvitation" ADD CONSTRAINT "UserInvitation_invitedBy_fkey" FOREIGN KEY ("invitedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "UserInvitation" ADD CONSTRAINT "UserInvitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "UserInvitation" ADD CONSTRAINT "UserInvitation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "DoctorInvitation" ADD CONSTRAINT "DoctorInvitation_invitedBy_fkey" FOREIGN KEY ("invitedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "DoctorInvitation" ADD CONSTRAINT "DoctorInvitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DoctorInvitation" ADD CONSTRAINT "DoctorInvitation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "BranchAdminInvitation" ADD CONSTRAINT "BranchAdminInvitation_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "BranchAdminInvitation" ADD CONSTRAINT "BranchAdminInvitation_invitedBy_fkey" FOREIGN KEY ("invitedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "BranchAdminInvitation" ADD CONSTRAINT "BranchAdminInvitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "BranchAdminInvitation" ADD CONSTRAINT "BranchAdminInvitation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Doctor Schedule foreign keys
ALTER TABLE "DoctorSchedule" ADD CONSTRAINT "DoctorSchedule_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DoctorSchedule" ADD CONSTRAINT "DoctorSchedule_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "DoctorSchedule" ADD CONSTRAINT "DoctorSchedule_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ScheduleSlot" ADD CONSTRAINT "ScheduleSlot_doctorScheduleId_fkey" FOREIGN KEY ("doctorScheduleId") REFERENCES "DoctorSchedule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ScheduleOverride" ADD CONSTRAINT "ScheduleOverride_doctorScheduleId_fkey" FOREIGN KEY ("doctorScheduleId") REFERENCES "DoctorSchedule"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Patient foreign keys
ALTER TABLE "Patient" ADD CONSTRAINT "Patient_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Patient" ADD CONSTRAINT "Patient_primaryBranchId_fkey" FOREIGN KEY ("primaryBranchId") REFERENCES "Branch"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Patient" ADD CONSTRAINT "Patient_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "PatientDocument" ADD CONSTRAINT "PatientDocument_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "PatientBranch" ADD CONSTRAINT "PatientBranch_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "PatientBranch" ADD CONSTRAINT "PatientBranch_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Appointment foreign keys
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Queue foreign keys
ALTER TABLE "QueueStatus" ADD CONSTRAINT "QueueStatus_appointmentId_fkey" FOREIGN KEY ("appointmentId") REFERENCES "Appointment"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "QueueStatus" ADD CONSTRAINT "QueueStatus_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "QueueStatus" ADD CONSTRAINT "QueueStatus_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "QueueStatus" ADD CONSTRAINT "QueueStatus_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Consultation foreign keys
ALTER TABLE "Consultation" ADD CONSTRAINT "Consultation_appointmentId_fkey" FOREIGN KEY ("appointmentId") REFERENCES "Appointment"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Consultation" ADD CONSTRAINT "Consultation_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Consultation" ADD CONSTRAINT "Consultation_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Consultation" ADD CONSTRAINT "Consultation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Consultation" ADD CONSTRAINT "Consultation_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Vitals foreign keys
ALTER TABLE "Vitals" ADD CONSTRAINT "Vitals_consultationId_fkey" FOREIGN KEY ("consultationId") REFERENCES "Consultation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Vitals" ADD CONSTRAINT "Vitals_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Vitals" ADD CONSTRAINT "Vitals_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "Vitals" ADD CONSTRAINT "Vitals_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Clinical Note foreign keys
ALTER TABLE "ClinicalNote" ADD CONSTRAINT "ClinicalNote_consultationId_fkey" FOREIGN KEY ("consultationId") REFERENCES "Consultation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ClinicalNote" ADD CONSTRAINT "ClinicalNote_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ClinicalNote" ADD CONSTRAINT "ClinicalNote_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ClinicalNote" ADD CONSTRAINT "ClinicalNote_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;
