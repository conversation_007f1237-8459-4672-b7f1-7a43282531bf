-- Lab Reports Feature Migration
-- This migration adds the LabTestRequest table and related schema changes for the Lab Reports feature
-- Generated on: 2025-06-11

-- =============================================
-- LAB REPORTS FEATURE TABLES
-- =============================================

-- Lab Test Request table
CREATE TABLE "LabTestRequest" (
    "id" TEXT NOT NULL,
    "consultationId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "doctorId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "branchId" TEXT NOT NULL,
    "testType" TEXT NOT NULL,
    "testName" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'routine',
    "status" TEXT NOT NULL DEFAULT 'pending',
    "requestDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "requestedBy" TEXT NOT NULL,
    "notes" TEXT,
    "expectedDate" TIMESTAMP(3),
    "completedDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "LabTestRequest_pkey" PRIMARY KEY ("id")
);

-- Add foreign key constraints for LabTestRequest
ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_consultationId_fkey" FOREIGN KEY ("consultationId") REFERENCES "Consultation"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_doctorId_fkey" FOREIGN KEY ("doctorId") REFERENCES "Doctor"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "LabTestRequest" ADD CONSTRAINT "LabTestRequest_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add labTestRequestId column to existing DiagnosticReport table
-- Note: DiagnosticReport table already exists from previous migration
ALTER TABLE "DiagnosticReport" ADD COLUMN "labTestRequestId" TEXT;

-- Add foreign key constraint for the new labTestRequestId column
ALTER TABLE "DiagnosticReport" ADD CONSTRAINT "DiagnosticReport_labTestRequestId_fkey" FOREIGN KEY ("labTestRequestId") REFERENCES "LabTestRequest"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Create indexes for better query performance
-- LabTestRequest indexes
CREATE INDEX "LabTestRequest_consultationId_idx" ON "LabTestRequest"("consultationId");
CREATE INDEX "LabTestRequest_patientId_idx" ON "LabTestRequest"("patientId");
CREATE INDEX "LabTestRequest_doctorId_idx" ON "LabTestRequest"("doctorId");
CREATE INDEX "LabTestRequest_organizationId_idx" ON "LabTestRequest"("organizationId");
CREATE INDEX "LabTestRequest_branchId_idx" ON "LabTestRequest"("branchId");
CREATE INDEX "LabTestRequest_status_idx" ON "LabTestRequest"("status");
CREATE INDEX "LabTestRequest_requestDate_idx" ON "LabTestRequest"("requestDate");
CREATE INDEX "LabTestRequest_completedDate_idx" ON "LabTestRequest"("completedDate");

-- DiagnosticReport indexes (only new ones for labTestRequestId)
CREATE INDEX "DiagnosticReport_labTestRequestId_idx" ON "DiagnosticReport"("labTestRequestId");

-- Add comments for documentation
-- LabTestRequest table comments
COMMENT ON TABLE "LabTestRequest" IS 'Lab test requests created during consultations';
COMMENT ON COLUMN "LabTestRequest"."testType" IS 'Type of lab test (e.g., blood, urine, imaging)';
COMMENT ON COLUMN "LabTestRequest"."testName" IS 'Specific name/description of the test';
COMMENT ON COLUMN "LabTestRequest"."priority" IS 'Priority level: routine, urgent, stat';
COMMENT ON COLUMN "LabTestRequest"."status" IS 'Request status: pending, in-progress, completed, cancelled';
COMMENT ON COLUMN "LabTestRequest"."requestedBy" IS 'Name of the doctor who requested the test';

-- DiagnosticReport table comments (only for new column)
COMMENT ON COLUMN "DiagnosticReport"."labTestRequestId" IS 'Link to the original lab test request';
