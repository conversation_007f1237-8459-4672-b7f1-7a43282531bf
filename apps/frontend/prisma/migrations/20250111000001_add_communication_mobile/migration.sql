-- Add communicationMobile field to Patient table
-- This field will store the user's preferred communication mobile number
-- separate from the Aadhaar-linked mobile number stored in the phone field

-- Add the new column
ALTER TABLE "Patient" ADD COLUMN "communicationMobile" TEXT;

-- Populate existing records with current phone values as communication mobile
-- This ensures backward compatibility for existing patients
UPDATE "Patient" SET "communicationMobile" = "phone" WHERE "communicationMobile" IS NULL;

-- Add comment to clarify field purposes
COMMENT ON COLUMN "Patient"."phone" IS 'Aadhaar-linked mobile number from ABDM';
COMMENT ON COLUMN "Patient"."communicationMobile" IS 'User preferred communication mobile number';
