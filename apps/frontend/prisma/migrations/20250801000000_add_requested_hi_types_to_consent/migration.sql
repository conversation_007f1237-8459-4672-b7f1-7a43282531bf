-- Migration: Add requestedHiTypes field to Consent table
-- This field will track the originally requested HI types for M3 consent functionality
-- It should be set only during initial consent creation and never updated afterwards

-- Add the new requestedHiTypes column to the Consent table
ALTER TABLE "Consent" ADD COLUMN "requestedHiTypes" TEXT[];

-- Update existing records to populate requestedHiTypes with current hiTypes values
-- This ensures backward compatibility for existing consent records
UPDATE "Consent" SET "requestedHiTypes" = "hiTypes" WHERE "requestedHiTypes" IS NULL;

-- Add a comment to document the purpose of this field
COMMENT ON COLUMN "Consent"."requestedHiTypes" IS 'Originally requested HI types at consent creation time. This field should not be updated after initial creation.';
