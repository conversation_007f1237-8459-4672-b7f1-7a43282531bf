# Migration: Restore Link Token Expiry as Required Field

## Date: 2025-06-26

## Description

This migration restores the `linkTokenExpiry` field in the `AbhaLinkToken` table to be a required field (NOT NULL) instead of optional.

## Changes Made

1. **Update NULL values**: Any existing records with NULL `linkTokenExpiry` are updated to have a default expiry of 24 hours from the current time
2. **Make field required**: The `linkTokenExpiry` column is changed from `DateTime?` to `DateTime` (NOT NULL)

## Reason for Change

- Restores the original 24-hour link token expiry logic
- Ensures all link tokens have a proper expiration time
- Maintains consistency with the ABDM link token validation flow

## Impact

- All existing link tokens will have valid expiry times
- New link tokens will be required to have expiry times
- Link token validation logic will work consistently across all operations

## Rollback

If needed, this can be rolled back by making the column optional again:

```sql
ALTER TABLE "AbhaLinkToken"
ALTER COLUMN "linkTokenExpiry" DROP NOT NULL;
```

## Related Files

- `prisma/schema.prisma` - Schema definition updated
- All link token related API routes and services - Expiry validation restored
