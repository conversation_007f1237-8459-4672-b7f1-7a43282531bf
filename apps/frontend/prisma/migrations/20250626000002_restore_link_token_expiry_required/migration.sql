-- Migration: Restore linkTokenExpiry as required field
-- This migration changes the linkTokenExpiry field from optional to required
-- in the AbhaLinkToken table to restore the original 24-hour expiry logic

-- First, update any NULL values to a default expiry (6 months from now)
UPDATE "AbhaLinkToken" 
SET "linkTokenExpiry" = NOW() + INTERVAL '6 months' 
WHERE "linkTokenExpiry" IS NULL;

-- Then make the column NOT NULL
ALTER TABLE "AbhaLinkToken" 
ALTER COLUMN "linkTokenExpiry" SET NOT NULL;
