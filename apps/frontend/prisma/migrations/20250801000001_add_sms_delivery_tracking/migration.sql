-- Migration: Add SMS delivery tracking fields to UILOtpNotify table
-- This migration adds fields to track SMS delivery status for Fast2SMS integration

-- Add SMS delivery tracking fields to UILOtpNotify table
ALTER TABLE "UILOtpNotify" ADD COLUMN "smsDeliveryStatus" TEXT DEFAULT 'pending';
ALTER TABLE "UILOtpNotify" ADD COLUMN "smsMessageId" TEXT;
ALTER TABLE "UILOtpNotify" ADD COLUMN "smsDeliveredAt" TIMESTAMP(3);
ALTER TABLE "UILOtpNotify" ADD COLUMN "smsError" TEXT;

-- Add comments to document the purpose of these fields
COMMENT ON COLUMN "UILOtpNotify"."smsDeliveryStatus" IS 'Status of SMS delivery: pending, sent, failed, delivered';
COMMENT ON COLUMN "UILOtpNotify"."smsMessageId" IS 'Message ID returned by Fast2SMS API';
COMMENT ON COLUMN "UILOtpNotify"."smsDeliveredAt" IS 'Timestamp when SMS was successfully sent';
COMMENT ON COLUMN "UILOtpNotify"."smsError" IS 'Error message if SMS delivery failed';
