-- CreateEnum
-- Add status field to Organization table

-- Step 1: Add the status column with default value
ALTER TABLE "Organization" ADD COLUMN "status" TEXT NOT NULL DEFAULT 'active';

-- Step 2: Create index for better query performance
CREATE INDEX "Organization_status_idx" ON "Organization"("status");

-- Step 3: Update existing organizations to have active status (safety measure)
UPDATE "Organization" SET "status" = 'active' WHERE "status" IS NULL OR "status" = '';

-- Step 4: Add check constraint to ensure only valid status values
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_status_check"
CHECK ("status" IN ('active', 'inactive'));

-- Verification query (optional - you can run this to verify)
-- SELECT COUNT(*) as total_orgs,
--        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_orgs,
--        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_orgs
-- FROM "Organization";
