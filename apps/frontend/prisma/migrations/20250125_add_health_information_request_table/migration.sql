-- CreateTable
CREATE TABLE "health_information_requests" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "transactionId" TEXT,
    "consentId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'INITIATED',
    "requestPayload" JSONB,
    "callbackPayload" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "health_information_requests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "health_information_requests_requestId_key" ON "health_information_requests"("requestId");

-- CreateIndex
CREATE UNIQUE INDEX "health_information_requests_transactionId_key" ON "health_information_requests"("transactionId");

-- CreateIndex
CREATE INDEX "health_information_requests_consentId_idx" ON "health_information_requests"("consentId");

-- CreateIndex
CREATE INDEX "health_information_requests_patientId_idx" ON "health_information_requests"("patientId");

-- CreateIndex
CREATE INDEX "health_information_requests_organizationId_idx" ON "health_information_requests"("organizationId");

-- CreateIndex
CREATE INDEX "health_information_requests_status_idx" ON "health_information_requests"("status");

-- AddForeignKey
ALTER TABLE "health_information_requests" ADD CONSTRAINT "health_information_requests_consentId_fkey" FOREIGN KEY ("consentId") REFERENCES "Consent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health_information_requests" ADD CONSTRAINT "health_information_requests_patientId_fkey" FOREIGN KEY ("patientId") REFERENCES "Patient"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "health_information_requests" ADD CONSTRAINT "health_information_requests_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
