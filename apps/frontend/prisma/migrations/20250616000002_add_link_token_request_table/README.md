# Migration: Add LinkTokenRequest Table

## Purpose

This migration adds the `LinkTokenRequest` table to support ABDM link token generation request context tracking.

## What it does

- Creates `LinkTokenRequest` table with the following fields:

  - `id`: Primary key (CUID)
  - `requestId`: Unique identifier for ABDM API requests
  - `patientId`: Reference to Patient
  - `organizationId`: Reference to Organization
  - `branchId`: Reference to Branch
  - `hipId`: HIP ID for the request
  - `abhaAddress`: ABHA address for the request
  - `status`: Request status (pending, completed, failed)
  - `createdAt`, `updatedAt`: Timestamps

- Adds foreign key constraints to Patient, Organization, and Branch tables
- Creates unique index on `requestId`

## Why this is needed

This table stores context for ABDM link token generation requests before making API calls to ABDM. When webhook callbacks are received, the system can match the request ID to determine which patient, organization, and branch the token belongs to.

## Related Changes

- Updated Prisma schema with LinkTokenRequest model
- Added relations to Patient, Organization, and Branch models
- Implemented link token generation service that uses this table

## How to apply

```bash
cd apps/frontend
npx prisma db push
```

## How to rollback

```bash
cd apps/frontend
psql -d your_database -f prisma/migrations/rollback_link_token_request_table.sql
```
