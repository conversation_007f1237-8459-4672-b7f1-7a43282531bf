-- Migration: Complete schema updates
-- Date: 2025-06-26
-- Adds wellness fields to Vitals table and updates Consultation-Invoice relationship

-- 1. Add all wellness fields to Vitals table (20 new fields)
ALTER TABLE "Vitals" ADD COLUMN "alcoholConsumption" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "bodyFatPercentage" DECIMAL(5,2);
ALTER TABLE "Vitals" ADD COLUMN "breastfeedingStatus" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "dietaryHabits" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "exerciseDuration" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "exerciseFrequency" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "exerciseType" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "generalAppearance" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "hipCircumference" DECIMAL(5,2);
ALTER TABLE "Vitals" ADD COLUMN "lastMenstrualPeriod" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "menstrualCycle" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "mentalStatus" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "mobilityStatus" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "painLevel" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "physicalActivityLevel" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "pregnancyStatus" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "sleepPattern" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "smokingStatus" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "stressLevel" TEXT;
ALTER TABLE "Vitals" ADD COLUMN "waistCircumference" DECIMAL(5,2);

-- 2. Update Invoice-Consultation relationship
-- The relationship changed from Invoice[] to Invoice? (one-to-one instead of one-to-many)
-- This is handled by the unique constraint on consultationId in Invoice table
-- No additional SQL changes needed as the constraint already exists
