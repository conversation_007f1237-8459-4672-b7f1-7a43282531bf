-- Migration to add structured fields for discharge summary to match FHIR bundle specification
-- This migration adds JSON fields to store structured data for discharge summaries
-- Discharge summaries are stored in DocumentReference table

-- Add structured fields to DocumentReference table for discharge summary data
ALTER TABLE "DocumentReference" ADD COLUMN "chiefComplaintsJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "physicalExaminationsJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "allergiesJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "medicalHistoriesJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "familyHistoriesJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "medicationsJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "diagnosticsJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "proceduresJson" TEXT;
ALTER TABLE "DocumentReference" ADD COLUMN "carePlanJson" TEXT;

-- Add comments to explain the JSON structure
COMMENT ON COLUMN "DocumentReference"."chiefComplaintsJson" IS 'JSON array: [{"complaint": "string", "recordedDate": "YYYY-MM-DD", "dateRange": {"from": "YYYY-MM-DD", "to": "YYYY-MM-DD"}}]';
COMMENT ON COLUMN "DocumentReference"."physicalExaminationsJson" IS 'JSON array: [{"observation": "string", "result": "string", "valueQuantity": {"unit": "string", "value": number}}]';
COMMENT ON COLUMN "DocumentReference"."allergiesJson" IS 'JSON array: ["allergy1", "allergy2"]';
COMMENT ON COLUMN "DocumentReference"."medicalHistoriesJson" IS 'JSON array: [{"complaint": "string", "recordedDate": "YYYY-MM-DD", "dateRange": {"from": "YYYY-MM-DD", "to": "YYYY-MM-DD"}}]';
COMMENT ON COLUMN "DocumentReference"."familyHistoriesJson" IS 'JSON array: [{"relationship": "string", "observation": "string"}]';
COMMENT ON COLUMN "DocumentReference"."medicationsJson" IS 'JSON array: [{"medicine": "string", "dosage": "string", "timing": "string", "route": "string", "method": "string", "additionalInstructions": "string", "reason": "string"}]';
COMMENT ON COLUMN "DocumentReference"."diagnosticsJson" IS 'JSON array: [{"serviceName": "string", "serviceCategory": "string", "result": [{"observation": "string", "result": "string", "valueQuantity": {"unit": "string", "value": number}}], "conclusion": "string", "presentedForm": {"contentType": "string", "data": "string"}}]';
COMMENT ON COLUMN "DocumentReference"."proceduresJson" IS 'JSON array: [{"date": "YYYY-MM-DD", "status": "string", "procedureReason": "string", "outcome": "string", "procedureName": "string"}]';
COMMENT ON COLUMN "DocumentReference"."carePlanJson" IS 'JSON object: {"intent": "string", "type": "string", "goal": "string", "description": "string", "notes": "string"}';
