-- Migration to create initial Super Admin user
-- This is a template - replace the values with actual data

-- Step 1: Create the Super Admin user
-- Replace 'your-hashed-password' with actual bcrypt hashed password
-- Replace '<EMAIL>' with actual email
-- Replace 'Super Admin' with actual name

INSERT INTO "User" (
  id, 
  email, 
  password, 
  name, 
  role, 
  "emailVerified", 
  "createdAt", 
  "updatedAt"
) VALUES (
  'super-admin-001',
  '<EMAIL>',
  '$2a$12$your-hashed-password-here', -- Use bcrypt to hash the password
  'Super Admin',
  'superAdmin',
  NOW(),
  NOW(),
  NOW()
) ON CONFLICT (email) DO NOTHING;

-- Step 2: Create organization for Super Admin
INSERT INTO "Organization" (
  id, 
  name, 
  slug, 
  "onboardingCompleted", 
  "createdAt", 
  "updatedAt"
) VALUES (
  'super-admin-org-001',
  'Super Admin Organization',
  'super-admin-org',
  true,
  NOW(),
  NOW()
) ON CONFLICT (slug) DO NOTHING;

-- Step 3: Link user to organization with superAdmin role
INSERT INTO "UserOrganization" (
  id, 
  "userId", 
  "organizationId", 
  roles, 
  "isDefault", 
  "createdAt", 
  "updatedAt"
) VALUES (
  'user-org-super-admin-001',
  'super-admin-001',
  'super-admin-org-001',
  '["superAdmin"]'::jsonb,
  true,
  NOW(),
  NOW()
) ON CONFLICT ("userId", "organizationId") DO NOTHING;
