generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Organization {
  id                     String                  @id @default(cuid())
  name                   String
  logo                   String?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  onboardingCompleted    Boolean                 @default(false)
  slug                   String                  @unique
  status                 String                  @default("active")
  AbhaLinkToken          AbhaLinkToken[]
  abhaProfiles           AbhaProfile[]
  AllergyIntolerance     AllergyIntolerance[]
  appointments           Appointment[]
  branches               Branch[]
  branchAdminInvitations BranchAdminInvitation[]
  careContexts           CareContext[]
  careTypes              CareType[]
  clinicalNotes          ClinicalNote[]
  consents               Consent[]
  consultations          Consultation[]
  departments            Department[]
  DiagnosticReport       DiagnosticReport[]
  doctors                Doctor[]
  invitations            DoctorInvitation[]
  doctorSchedules        DoctorSchedule[]
  DocumentReference      DocumentReference[]
  fhirBundles            FhirBundle[]
  fhirResources          FhirResource[]
  healthRecordFetches    HealthRecordFetch[]
  healthRecordOperations HealthRecordOperation[]
  Immunization           Immunization[]
  invoices               Invoice[]
  labTestRequests        LabTestRequest[]
  linkTokenRequests      LinkTokenRequest[]
  patients               Patient[]
  prescriptions          Prescription[]
  Procedure              Procedure[]
  queueStatuses          QueueStatus[]
  staff                  Staff[]
  staffInvitations       StaffInvitation[]
  userInvitations            UserInvitation[]
  users                      UserOrganization[]
  vitals                     Vitals[]
  HealthInformationRequests  HealthInformationRequest[]

  @@index([status])
}

model Branch {
  id                           String                  @id @default(cuid())
  name                         String
  facilityType                 String                  @default("clinic")
  phone                        String?
  email                        String?
  address                      String?
  city                         String?
  state                        String?
  pincode                      String?
  latitude                     String?
  longitude                    String?
  isHeadOffice                 Boolean                 @default(false)
  organizationId               String
  createdAt                    DateTime                @default(now())
  updatedAt                    DateTime                @updatedAt
  acceptsInsurance             Boolean                 @default(false)
  hasAdminStaff                Boolean                 @default(false)
  hasDoctors                   Boolean                 @default(false)
  hasNurses                    Boolean                 @default(false)
  insuranceProviders           String?
  services                     String?
  specialties                  String?
  teamSize                     Int                     @default(0)
  hipId                        String?
  hipStatus                    String?
  hipRegistrationDate          DateTime?
  hipRegistrationDetails       Json?
  hipBridgeId                  String?
  hipBridgeRegistrationDate    DateTime?
  hipBridgeRegistrationDetails Json?
  abhaLinkTokens               AbhaLinkToken[]
  appointments                 Appointment[]
  organization                 Organization            @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  branchAdminInvitations       BranchAdminInvitation[]
  departments                  BranchDepartment[]
  consultations                Consultation[]
  doctors                      DoctorBranch[]
  doctorSchedules              DoctorSchedule[]
  invoices                     Invoice[]
  labTestRequests              LabTestRequest[]
  linkTokenRequests            LinkTokenRequest[]
  primaryPatients              Patient[]               @relation("PatientPrimaryBranch")
  patientBranches              PatientBranch[]
  staffBranches                StaffBranch[]
  usersWithBranch              User[]                  @relation("UserCurrentBranch")
}

model UserOrganization {
  id             String       @id @default(cuid())
  userId         String
  organizationId String
  roles          Json         @default("[]")
  isDefault      Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
}

model User {
  id                         String                  @id @default(cuid())
  name                       String?
  email                      String?                 @unique
  emailVerified              DateTime?
  password                   String?
  image                      String?
  role                       String                  @default("user")
  currentBranchId            String?
  roles                      Json                    @default("[]")
  accounts                   Account[]
  sentBranchAdminInvitations BranchAdminInvitation[] @relation("BranchAdminInvitationInviter")
  branchAdminInvitations     BranchAdminInvitation[] @relation("BranchAdminInvitationUser")
  doctors                    Doctor?
  sentInvitations            DoctorInvitation[]      @relation("DoctorInvitationInviter")
  invitations                DoctorInvitation[]
  patients                   Patient[]
  sessions                   Session[]
  staff                      Staff?
  currentBranch              Branch?                 @relation("UserCurrentBranch", fields: [currentBranchId], references: [id])
  sentUserInvitations        UserInvitation[]        @relation("UserInvitationInviter")
  userInvitations            UserInvitation[]
  sentStaffInvitations       StaffInvitation[]       @relation("StaffInvitationInviter")
  staffInvitations           StaffInvitation[]       @relation("StaffInvitationUser")
  organizations              UserOrganization[]
}

model Department {
  id             String             @id @default(cuid())
  name           String
  description    String?
  organizationId String
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  departmentCode String
  status         String             @default("active")
  branches       BranchDepartment[]
  careTypes      CareType[]
  organization   Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  doctors        Doctor[]
  staff          Staff[]
}

model BranchDepartment {
  id              String     @id @default(cuid())
  branchId        String
  departmentId    String
  locationDetails String?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt
  departmentHead  String?
  emailAddress    String?
  phoneNumber     String?
  workingHours    String?
  branch          Branch     @relation(fields: [branchId], references: [id], onDelete: Cascade)
  department      Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  @@unique([branchId, departmentId])
}

model CareType {
  id             String       @id @default(cuid())
  name           String
  description    String?
  price          Float
  organizationId String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  departmentId   String
  department     Department   @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  Consent        Consent[]
}

model Doctor {
  id                 String               @id @default(cuid())
  userId             String               @unique
  profileDescription String?
  organizationId     String
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  consultationFee    Float?
  experience         Int?
  qualification      String?
  specialization     String?
  departmentId       String
  status             String               @default("active")
  contactEmail       String?
  contactPhone       String?
  joiningDate        DateTime?
  yearsOfExperience  Int                  @default(0)
  AllergyIntolerance AllergyIntolerance[]
  appointments       Appointment[]
  clinicalNotes      ClinicalNote[]
  consultations      Consultation[]
  DiagnosticReport   DiagnosticReport[]
  department         Department           @relation(fields: [departmentId], references: [id])
  organization       Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user               User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  branches           DoctorBranch[]
  documents          DoctorDocument[]
  schedules          DoctorSchedule[]
  DocumentReference  DocumentReference[]
  Immunization       Immunization[]
  invoices           Invoice[]
  labTestRequests    LabTestRequest[]
  prescriptions      Prescription[]
  Procedure          Procedure[]
  queueStatuses      QueueStatus[]
  vitals             Vitals[]

  @@unique([userId, organizationId])
  @@index([organizationId])
  @@index([userId])
}

model DoctorBranch {
  id        String   @id @default(cuid())
  doctorId  String
  branchId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  branch    Branch   @relation(fields: [branchId], references: [id], onDelete: Cascade)
  doctor    Doctor   @relation(fields: [doctorId], references: [id], onDelete: Cascade)

  @@unique([doctorId, branchId])
}

model DoctorDocument {
  id          String   @id @default(cuid())
  doctorId    String
  name        String
  fileUrl     String
  fileType    String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  doctor      Doctor   @relation(fields: [doctorId], references: [id], onDelete: Cascade)
}

model Staff {
  id             String        @id @default(cuid())
  userId         String?       @unique
  organizationId String
  role           String
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  contactEmail   String?
  contactNumber  String?
  departmentId   String?
  email          String
  joiningDate    DateTime?
  name           String
  status         String        @default("active")
  department     Department?   @relation(fields: [departmentId], references: [id])
  organization   Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?         @relation(fields: [userId], references: [id])
  branches       StaffBranch[]
}

model StaffBranch {
  id        String   @id @default(cuid())
  staffId   String
  branchId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  branch    Branch   @relation(fields: [branchId], references: [id], onDelete: Cascade)
  staff     Staff    @relation(fields: [staffId], references: [id], onDelete: Cascade)

  @@unique([staffId, branchId])
}

model UserInvitation {
  id             String       @id @default(cuid())
  email          String
  organizationId String
  roles          Json         @default("[]")
  branchIds      Json?
  departmentId   String?
  status         String       @default("pending")
  userId         String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  expiresAt      DateTime
  invitedBy      String
  token          String       @unique
  inviter        User         @relation("UserInvitationInviter", fields: [invitedBy], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?        @relation(fields: [userId], references: [id])
}

model StaffInvitation {
  id             String       @id @default(cuid())
  email          String
  token          String       @unique
  status         String       @default("pending")
  organizationId String
  branchIds      Json         @default("[]")
  invitedBy      String
  userId         String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  expiresAt      DateTime
  inviter        User         @relation("StaffInvitationInviter", fields: [invitedBy], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?        @relation("StaffInvitationUser", fields: [userId], references: [id])

  @@index([email])
  @@index([organizationId])
  @@index([status])
}

model DoctorInvitation {
  id             String       @id @default(cuid())
  email          String
  organizationId String
  branchIds      Json?
  departmentId   String?
  status         String       @default("pending")
  userId         String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  expiresAt      DateTime
  invitedBy      String
  token          String       @unique
  inviter        User         @relation("DoctorInvitationInviter", fields: [invitedBy], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?        @relation(fields: [userId], references: [id])
}

model DoctorSchedule {
  id              String             @id @default(cuid())
  doctorId        String
  branchId        String
  organizationId  String
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt
  defaultDuration Int
  description     String?
  name            String
  status          String             @default("active")
  branch          Branch             @relation(fields: [branchId], references: [id], onDelete: Cascade)
  doctor          Doctor             @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization    Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  overrides       ScheduleOverride[]
  slots           ScheduleSlot[]
}

model ScheduleSlot {
  id               String         @id @default(cuid())
  doctorScheduleId String
  dayOfWeek        Int
  startTime        String
  endTime          String
  status           String         @default("active")
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  duration         Int?
  isRecurring      Boolean        @default(true)
  doctorSchedule   DoctorSchedule @relation(fields: [doctorScheduleId], references: [id], onDelete: Cascade)
}

model ScheduleOverride {
  id               String         @id @default(cuid())
  doctorScheduleId String
  date             DateTime
  startTime        String
  endTime          String
  type             String
  reason           String?
  status           String         @default("active")
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  duration         Int?
  doctorSchedule   DoctorSchedule @relation(fields: [doctorScheduleId], references: [id], onDelete: Cascade)
}

model Patient {
  id                       String                  @id @default(cuid())
  userId                   String?
  firstName                String
  lastName                 String
  dateOfBirth              DateTime
  gender                   String
  phone                    String                  // Aadhaar-linked mobile number from ABDM
  communicationMobile      String?                 // User's preferred communication mobile number
  email                    String?
  address                  String?
  city                     String?
  state                    String?
  pincode                  String?
  emergencyContactName     String?
  emergencyContactPhone    String?
  emergencyContactRelation String?
  bloodGroup               String?
  allergies                String?
  currentMedications       String?
  familyMedicalHistory     String?
  organizationId           String
  primaryBranchId          String
  createdAt                DateTime                @default(now())
  updatedAt                DateTime                @updatedAt
  abhaDataSynced           Boolean?                @default(false)
  abhaDataSyncedAt         DateTime?               @db.Timestamp(6)
  alternatePhone           String?
  chronicDiseases          String?
  country                  String                  @default("India")
  district                 String?
  insuranceExpiryDate      DateTime?
  insurancePolicyNumber    String?
  insuranceProvider        String?
  maritalStatus            String?
  middleName               String?
  occupation               String?
  registrationDate         DateTime                @default(now())
  status                   String                  @default("active")
  abhaLinkTokens           AbhaLinkToken[]
  abhaProfile              AbhaProfile?
  AllergyIntolerance       AllergyIntolerance[]
  appointments             Appointment[]
  careContexts             CareContext[]
  clinicalNotes            ClinicalNote[]
  consents                 Consent[]
  consultations            Consultation[]
  DiagnosticReport         DiagnosticReport[]
  DocumentReference        DocumentReference[]
  fhirBundles              FhirBundle[]
  fhirResources            FhirResource[]
  healthRecordFetches      HealthRecordFetch[]
  healthRecordOperations   HealthRecordOperation[]
  Immunization             Immunization[]
  invoices                 Invoice[]
  labTestRequests          LabTestRequest[]
  linkTokenRequests        LinkTokenRequest[]
  organization             Organization            @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  primaryBranch            Branch                  @relation("PatientPrimaryBranch", fields: [primaryBranchId], references: [id])
  user                     User?                   @relation(fields: [userId], references: [id])
  branches                 PatientBranch[]
  documents                PatientDocument[]
  prescriptions            Prescription[]
  Procedure                    Procedure[]
  queueStatuses                QueueStatus[]
  vitals                       Vitals[]
  HealthInformationRequests    HealthInformationRequest[]
}

model PatientDocument {
  id          String   @id @default(cuid())
  patientId   String
  name        String
  fileUrl     String
  fileType    String
  category    String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  patient     Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model PatientBranch {
  id        String   @id @default(cuid())
  patientId String
  branchId  String
  visitDate DateTime @default(now())
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  branch    Branch   @relation(fields: [branchId], references: [id], onDelete: Cascade)
  patient   Patient  @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@unique([patientId, branchId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model BranchAdminInvitation {
  id             String       @id @default(cuid())
  email          String
  branchId       String
  organizationId String
  status         String       @default("pending")
  userId         String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  expiresAt      DateTime
  invitedBy      String
  token          String       @unique
  branch         Branch       @relation(fields: [branchId], references: [id], onDelete: Cascade)
  inviter        User         @relation("BranchAdminInvitationInviter", fields: [invitedBy], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User?        @relation("BranchAdminInvitationUser", fields: [userId], references: [id])
}

model Appointment {
  id                 String        @id @default(cuid())
  patientId          String
  doctorId           String
  branchId           String
  organizationId     String
  startTime          String
  endTime            String
  status             String        @default("scheduled")
  notes              String?
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  type               String        @default("regular")
  appointmentDate    DateTime
  cancellationReason String?
  duration           Int
  branch             Branch        @relation(fields: [branchId], references: [id], onDelete: Cascade)
  doctor             Doctor        @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization       Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient            Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  consultation       Consultation?
  queueStatus        QueueStatus?
}

model QueueStatus {
  id                 String       @id @default(cuid())
  appointmentId      String       @unique
  patientId          String
  doctorId           String
  organizationId     String
  status             String       @default("waiting")
  queueNumber        Int
  estimatedStartTime DateTime?
  actualStartTime    DateTime?
  completionTime     DateTime?
  pauseReason        String?
  notes              String?
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  appointment        Appointment  @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  doctor             Doctor       @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization       Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient            Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Consultation {
  id                 String               @id @default(cuid())
  appointmentId      String?              @unique
  patientId          String
  doctorId           String
  branchId           String
  organizationId     String
  consultationDate   DateTime             @default(now())
  startTime          String?
  endTime            String?
  status             String               @default("in-progress")
  followUpDate       DateTime?
  followUpNotes      String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  AllergyIntolerance AllergyIntolerance[]
  CareContext        CareContext[]
  clinicalNotes      ClinicalNote[]
  appointment        Appointment?         @relation(fields: [appointmentId], references: [id])
  branch             Branch               @relation(fields: [branchId], references: [id], onDelete: Cascade)
  doctor             Doctor               @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization       Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient            Patient              @relation(fields: [patientId], references: [id], onDelete: Cascade)
  DiagnosticReport   DiagnosticReport[]
  DocumentReference  DocumentReference[]
  FhirBundle         FhirBundle[]
  Immunization       Immunization[]
  invoices           Invoice?
  labTestRequests    LabTestRequest[]
  prescriptions      Prescription[]
  Procedure          Procedure[]
  vitals             Vitals[]
}

model Vitals {
  id                     String       @id @default(cuid())
  consultationId         String
  patientId              String
  doctorId               String
  organizationId         String
  bloodPressureSystolic  Int?
  bloodPressureDiastolic Int?
  pulse                  Int?
  temperature            Decimal?     @db.Decimal(5, 2)
  respiratoryRate        Int?
  oxygenSaturation       Int?
  height                 Decimal?     @db.Decimal(5, 2)
  weight                 Decimal?     @db.Decimal(5, 2)
  bmi                    Decimal?     @db.Decimal(5, 2)
  notes                  String?
  recordedAt             DateTime     @default(now())
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  alcoholConsumption     String?
  bodyFatPercentage      Decimal?     @db.Decimal(5, 2)
  breastfeedingStatus    String?
  dietaryHabits          String?
  exerciseDuration       String?
  exerciseFrequency      String?
  exerciseType           String?
  generalAppearance      String?
  hipCircumference       Decimal?     @db.Decimal(5, 2)
  lastMenstrualPeriod    String?
  menstrualCycle         String?
  mentalStatus           String?
  mobilityStatus         String?
  painLevel              String?
  physicalActivityLevel  String?
  pregnancyStatus        String?
  sleepPattern           String?
  smokingStatus          String?
  stressLevel            String?
  waistCircumference     Decimal?     @db.Decimal(5, 2)
  consultation           Consultation @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  doctor                 Doctor       @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization           Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient                Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model ClinicalNote {
  id                      String       @id @default(cuid())
  consultationId          String
  patientId               String
  doctorId                String
  organizationId          String
  content                 String?
  chiefComplaints         String?
  chiefComplaintsDateFrom String?
  chiefComplaintsDateTo   String?
  allergies               String?
  medicalHistory          String?
  medicalHistoryDateFrom  String?
  medicalHistoryDateTo    String?
  investigationAdvice     String?
  investigationStatus     String?
  investigationSpecimen   String?
  procedure               String?
  procedureStatus         String?
  procedureReason         String?
  procedureOutcome        String?
  followUp                String?
  followUpServiceType     String?
  followUpDate            String?
  referralReason          String?
  referralSpecialty       String?
  referralProvider        String?
  snomedTags              Json?
  snomedDiagnoses         String?
  noteType                String       @default("general")
  createdAt               DateTime     @default(now())
  updatedAt               DateTime     @updatedAt
  consultation            Consultation @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  doctor                  Doctor       @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization            Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient                 Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Prescription {
  id               String             @id @default(cuid())
  consultationId   String
  patientId        String
  doctorId         String
  organizationId   String
  prescriptionDate DateTime           @default(now())
  validUntil       DateTime?
  instructions     String?
  status           String             @default("active")
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  consultation     Consultation       @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  doctor           Doctor             @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization     Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient          Patient            @relation(fields: [patientId], references: [id], onDelete: Cascade)
  items            PrescriptionItem[]
}

model PrescriptionItem {
  id             String       @id @default(cuid())
  prescriptionId String
  medicationName String
  dosage         String
  frequency      String
  duration       String
  route          String?
  method         String?
  instructions   String?
  reason         String?
  snomedCode     String?
  rxcui          String?
  rxNormData     String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  prescription   Prescription @relation(fields: [prescriptionId], references: [id], onDelete: Cascade)
}

model DiagnosticReport {
  id               String          @id @default(cuid())
  consultationId   String
  patientId        String
  doctorId         String
  organizationId   String
  reportType       String
  reportDate       DateTime        @default(now())
  status           String          @default("final")
  category         String?
  code             String
  codeDisplay      String
  conclusion       String?
  presentedForm    Json?
  result           Json?
  performer        String?
  specimen         String?
  effectiveDate    DateTime        @default(now())
  issuedDate       DateTime        @default(now())
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  labTestRequestId String?
  consultation     Consultation    @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  doctor           Doctor          @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  labTestRequest   LabTestRequest? @relation(fields: [labTestRequestId], references: [id])
  organization     Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient          Patient         @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([labTestRequestId])
}

model LabTestRequest {
  id                String             @id @default(cuid())
  consultationId    String
  patientId         String
  doctorId          String
  organizationId    String
  branchId          String
  testType          String
  testName          String
  priority          String             @default("routine")
  status            String             @default("pending")
  requestDate       DateTime           @default(now())
  requestedBy       String
  notes             String?
  expectedDate      DateTime?
  completedDate     DateTime?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  diagnosticReports DiagnosticReport[]
  branch            Branch             @relation(fields: [branchId], references: [id], onDelete: Cascade)
  consultation      Consultation       @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  doctor            Doctor             @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization      Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient           Patient            @relation(fields: [patientId], references: [id], onDelete: Cascade)

  @@index([branchId])
  @@index([completedDate])
  @@index([consultationId])
  @@index([doctorId])
  @@index([organizationId])
  @@index([patientId])
  @@index([requestDate])
  @@index([status])
}

model Procedure {
  id             String       @id @default(cuid())
  consultationId String
  patientId      String
  doctorId       String
  organizationId String
  procedureDate  DateTime     @default(now())
  status         String       @default("completed")
  category       String?
  code           String
  codeDisplay    String
  bodySite       String?
  outcome        String?
  complication   String?
  followUp       String?
  notes          String?
  performer      String?
  location       String?
  reasonCode     String?
  reasonDisplay  String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  consultation   Consultation @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  doctor         Doctor       @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient        Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model AllergyIntolerance {
  id                 String        @id @default(cuid())
  patientId          String
  doctorId           String
  organizationId     String
  consultationId     String?
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  asserter           String?
  category           String
  clinicalStatus     String        @default("active")
  code               String
  codeDisplay        String
  criticality        String        @default("low")
  lastOccurrence     DateTime?
  note               String?
  onsetDateTime      DateTime?
  recordedDate       DateTime      @default(now())
  recorder           String?
  type               String        @default("allergy")
  verificationStatus String        @default("confirmed")
  reaction           Json?
  consultation       Consultation? @relation(fields: [consultationId], references: [id])
  doctor             Doctor        @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization       Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient            Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Immunization {
  id                 String        @id @default(cuid())
  patientId          String
  doctorId           String
  organizationId     String
  consultationId     String?
  status             String        @default("completed")
  statusReason       String?
  vaccineCode        String
  vaccineDisplay     String
  occurrenceDateTime DateTime      @default(now())
  recorded           DateTime      @default(now())
  primarySource      Boolean       @default(true)
  reportOrigin       String?
  location           String?
  manufacturer       String?
  lotNumber          String?
  expirationDate     DateTime?
  site               String?
  route              String?
  doseQuantity       String?
  performer          String?
  note               String?
  reasonCode         String?
  reasonDisplay      String?
  isSubpotent        Boolean       @default(false)
  subpotentReason    String?
  programEligibility String?
  fundingSource      String?
  reaction           Json?
  protocolApplied    Json?
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  consultation       Consultation? @relation(fields: [consultationId], references: [id])
  doctor             Doctor        @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization       Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient            Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model DocumentReference {
  id              String        @id @default(cuid())
  patientId       String
  doctorId        String
  organizationId  String
  consultationId  String?
  status          String        @default("current")
  docStatus       String        @default("final")
  type            String
  typeDisplay     String
  category        String
  categoryDisplay String
  subject         String?
  date            DateTime      @default(now())
  author          String?
  authenticator   String?
  custodian       String?
  description     String?
  securityLabel   String?
  content         Json
  context         Json?
  // Structured fields for discharge summary FHIR bundle compliance
  chiefComplaintsJson      String?
  physicalExaminationsJson String?
  allergiesJson           String?
  medicalHistoriesJson    String?
  familyHistoriesJson     String?
  medicationsJson         String?
  diagnosticsJson         String?
  proceduresJson          String?
  carePlanJson            String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  consultation    Consultation? @relation(fields: [consultationId], references: [id])
  doctor          Doctor        @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization    Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient         Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Invoice {
  id             String        @id @default(cuid())
  invoiceNumber  String        @unique
  consultationId String        @unique
  patientId      String
  doctorId       String
  organizationId String
  branchId       String
  invoiceDate    DateTime      @default(now())
  dueDate        DateTime?
  status         String        @default("issued")
  type           String        @default("consultation")
  subtotal       Decimal       @db.Decimal(10, 2)
  taxAmount      Decimal       @default(0) @db.Decimal(10, 2)
  discountAmount Decimal       @default(0) @db.Decimal(10, 2)
  totalAmount    Decimal       @db.Decimal(10, 2)
  currency       String        @default("INR")
  paymentTerms   String?
  paymentStatus  String        @default("pending")
  notes          String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  branch         Branch        @relation(fields: [branchId], references: [id], onDelete: Cascade)
  consultation   Consultation  @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  doctor         Doctor        @relation(fields: [doctorId], references: [id], onDelete: Cascade)
  organization   Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient        Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  items          InvoiceItem[]
}

model InvoiceItem {
  id             String   @id @default(cuid())
  invoiceId      String
  sequence       Int
  serviceCode    String?
  serviceDisplay String
  category       String   @default("service")
  quantity       Decimal  @default(1) @db.Decimal(10, 2)
  unitPrice      Decimal  @db.Decimal(10, 2)
  mrp            Decimal? @db.Decimal(10, 2)
  discountAmount Decimal  @default(0) @db.Decimal(10, 2)
  taxRate        Decimal  @default(0) @db.Decimal(5, 2)
  cgstAmount     Decimal  @default(0) @db.Decimal(10, 2)
  sgstAmount     Decimal  @default(0) @db.Decimal(10, 2)
  igstAmount     Decimal  @default(0) @db.Decimal(10, 2)
  totalAmount    Decimal  @db.Decimal(10, 2)
  notes          String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  invoice        Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
}

model ConsentAuditLog {
  id        String   @id @default(cuid())
  consentId String
  action    String
  actorId   String
  actorRole String
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  consent   Consent  @relation(fields: [consentId], references: [id], onDelete: Cascade)
}

model AbhaProfile {
  id              String       @id @default(cuid())
  abhaNumber      String?
  abhaAddress     String?
  healthIdNumber  String?
  organizationId  String
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  aadhaarNumber   String?
  abhaCardUrl     String?
  abhaStatus      String?
  linkToken       String?
  linkTokenExpiry DateTime?
  patientId       String       @unique
  xToken          String?
  xTokenExpiresAt DateTime?
  kycVerified     Boolean?     @default(false)
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient         Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model AbhaLinkToken {
  id              String       @id @default(cuid())
  patientId       String
  organizationId  String
  branchId        String
  status          String       @default("active")
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  linkToken       String
  linkTokenExpiry DateTime
  hipId           String
  requestId       String?
  branch          Branch       @relation(fields: [branchId], references: [id], onDelete: Cascade)
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient         Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model CareContext {
  id             String               @id @default(cuid())
  patientId      String
  organizationId String
  display        String
  hiTypes        String[]
  additionalInfo Json?
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  status         String               @default("Requested")
  requestId      String?
  consultationId String
  consultation   Consultation         @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  organization   Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient        Patient              @relation(fields: [patientId], references: [id], onDelete: Cascade)
  consentLinks   ConsentCareContext[]

  @@unique([organizationId, consultationId])
}

model ConsentCareContext {
  id            String      @id @default(cuid())
  consentId     String
  careContextId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  careContext   CareContext @relation(fields: [careContextId], references: [id], onDelete: Cascade)
  consent       Consent     @relation(fields: [consentId], references: [id], onDelete: Cascade)

  @@unique([consentId, careContextId])
}

model HealthRecordOperation {
  id                String            @id @default(cuid())
  fetchId           String
  operationType     String
  status            String            @default("pending")
  requestId         String?
  responseData      Json?
  errorData         Json?
  timestamp         DateTime?
  patientId         String?
  recordType        String?
  recordId          String?
  bundleId          String?
  consentId         String?
  transactionId     String?
  errorMessage      String?
  userId            String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime
  organizationId    String
  HealthRecordFetch HealthRecordFetch @relation(fields: [fetchId], references: [id], onDelete: Cascade)
  organization      Organization      @relation(fields: [organizationId], references: [id])
  patient           Patient?          @relation(fields: [patientId], references: [id])
}

model HealthRecordFetch {
  id                    String                  @id @default(cuid())
  patientId             String
  organizationId        String
  consentId             String
  transactionId         String
  status                String                  @default("pending")
  requestTimestamp      DateTime?
  responseTimestamp     DateTime?
  responseData          Json?
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  consent               Consent                 @relation(fields: [consentId], references: [id])
  organization          Organization            @relation(fields: [organizationId], references: [id])
  patient               Patient                 @relation(fields: [patientId], references: [id])
  HealthRecordOperation HealthRecordOperation[]

  @@index([patientId, transactionId])
}

model Consent {
  id                String               @id @default(cuid())
  consentRequestId  String
  consentId         String?              @unique
  patientId         String
  organizationId    String
  status            String               @default("REQUESTED")
  purpose           String
  careContexts      Json?
  hiTypes           String[]
  requestedHiTypes  String[]
  permission        Json
  consentArtifact   String?
  expiryDate        DateTime?
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  revokedAt         DateTime?
  careTypeId        String?
  CareType          CareType?            @relation(fields: [careTypeId], references: [id])
  organization      Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient           Patient              @relation(fields: [patientId], references: [id], onDelete: Cascade)
  auditLogs                  ConsentAuditLog[]
  careContextLinks           ConsentCareContext[]
  HealthRecordFetch          HealthRecordFetch[]
  HealthInformationRequests  HealthInformationRequest[]
}

model ConsentNotify {
  id                   String   @id @default(cuid())
  requestId            String   @unique
  status               String
  consentId            String
  schemaVersion        String
  patientId            String
  careContexts         Json
  purpose              Json
  hipId                String
  consentManagerId     String
  hiTypes              String[]
  permission           Json
  signature            String
  grantAcknowledgement Boolean
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
}

model HiRequest {
  id                  String   @id @default(cuid())
  requestId           String   @unique
  transactionId       String   @unique
  consentId           String
  dateRange           Json
  dataPushUrl         String
  keyMaterial         Json
  acknowledgementSent Boolean  @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

model HealthInformationRequest {
  id              String       @id @default(cuid())
  requestId       String       @unique
  transactionId   String?      @unique
  consentId       String
  patientId       String
  organizationId  String
  status          String       @default("INITIATED") // INITIATED, REQUESTED, COMPLETED, FAILED
  requestPayload  Json?
  callbackPayload Json?
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  // Relations
  consent      Consent      @relation(fields: [consentId], references: [id], onDelete: Cascade)
  patient      Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([consentId])
  @@index([patientId])
  @@index([organizationId])
  @@index([status])
  @@map("health_information_requests")
}

model CareContextNotify {
  id          String   @id @default(cuid())
  abhaAddress String
  status      String
  requestId   String
  response    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model GenerateTokenNotify {
  id        String   @id @default(cuid())
  requestId String   @unique
  error     Json?
  response  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model LinkTokenRequest {
  id             String       @id @default(cuid())
  requestId      String       @unique
  patientId      String
  organizationId String
  branchId       String
  hipId          String
  abhaAddress    String
  status         String       @default("pending")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  branch         Branch       @relation(fields: [branchId], references: [id], onDelete: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient        Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model UILOtpNotify {
  id                 String    @id @default(cuid())
  linkRefNumber      String    @unique
  otp                String
  transactionId      String
  patientId          String
  careContexts       Json
  expiresAt          DateTime
  verified           Boolean   @default(false)
  requestId          String?
  abhaAddress        String?   // Store original ABHA address from discovery request
  smsDeliveryStatus  String?   @default("pending") // Status: pending, sent, failed, delivered
  smsMessageId       String?   // Message ID from Fast2SMS API
  smsDeliveredAt     DateTime? // Timestamp when SMS was sent
  smsError           String?   // Error message if SMS delivery failed
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @default(now()) @updatedAt
}

model FhirBundle {
  id              String         @id @default(cuid())
  bundleType      String
  bundleJson      Json
  consultationId  String?
  patientId       String
  status          String         @default("active")
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  bundleId        String         @unique
  consentId       String?
  transactionId   String?
  packageChecksum String?
  statusDetails   Json?
  organizationId  String
  Consultation    Consultation?  @relation(fields: [consultationId], references: [id], onDelete: Cascade)
  organization    Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient         Patient        @relation(fields: [patientId], references: [id], onDelete: Cascade)
  resources       FhirResource[]
}

model FhirResource {
  id             String       @id @default(cuid())
  resourceType   String
  resourceId     String
  fhirJson       Json
  patientId      String?
  organizationId String
  sourceType     String
  sourceId       String
  bundleId       String
  version        String       @default("4.0.1")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  bundle         FhirBundle   @relation(fields: [bundleId], references: [bundleId], onDelete: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  patient        Patient?     @relation(fields: [patientId], references: [id])

  @@unique([resourceType, resourceId, bundleId])
}

model UILOtp {
  id            String   @id
  linkRefNumber String   @unique
  otp           String
  transactionId String
  patientId     String
  careContexts  Json
  expiresAt     DateTime
  verified      Boolean  @default(false)
  requestId     String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime
}

model MobileOtp {
  id          String   @id @default(cuid())
  mobile      String
  otp         String
  purpose     String
  expiresAt   DateTime
  verified    Boolean  @default(false)
  attempts    Int      @default(0)
  maxAttempts Int      @default(3)
  patientId   String?
  userId      String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([mobile, purpose])
}

model HiuConsentNotify {
  id          String   @id @default(cuid())
  requestId   String   @unique
  webhookType String
  payload     Json
  headers     Json?
  processed   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
