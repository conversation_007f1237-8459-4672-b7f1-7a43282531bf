#!/usr/bin/env node

/**
 * GitHub Issue Creator
 *
 * This script reads a structured PRD from a Markdown file and creates GitHub issues
 * based on the feature stories.
 *
 * Usage: npm run create:issues [milestone-name]
 */

import fs from 'fs';
import path from 'path';
import { Octokit } from '@octokit/rest';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// GitHub configuration
const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
const GITHUB_OWNER = process.env.GITHUB_OWNER;
const GITHUB_REPO = process.env.GITHUB_REPO;

// Check if GitHub configuration is available
if (!GITHUB_TOKEN || !GITHUB_OWNER || !GITHUB_REPO) {
  console.error("Error: GitHub configuration is missing in .env file");
  console.error("Required variables: GITHUB_TOKEN, GITHUB_OWNER, GITHUB_REPO");
  process.exit(1);
}

// Initialize Octokit
const octokit = new Octokit({
  auth: GITHUB_TOKEN,
});

// Path to the PRD Markdown file
const PRD_FILE_PATH = path.join(process.cwd(), "prd", "phase1_features.md");

// Parse command line arguments
const args = process.argv.slice(2).filter((arg) => !arg.startsWith("--"));
const milestoneName = args.length > 0 ? args[0] : null;

// Check for dry run flag
const isDryRun = process.argv.includes("--dry-run");

// Function to parse the Markdown file and extract feature stories
function parseMarkdownFeatures(markdownContent) {
  const features = [];

  // Split the content by feature sections (using the horizontal rule as delimiter)
  const featureSections = markdownContent
    .split("---")
    .filter((section) => section.trim());

  // Process each feature section
  for (const section of featureSections) {
    // Skip the header section if it exists
    if (section.trim().startsWith("# 🚀")) continue;

    try {
      // Extract title - handle both formats: "## 🏷️ Title: Feature Name" and "## 🏷️ Feature Name"
      let titleMatch = section.match(/## 🏷️ Title: (.+)/);
      if (!titleMatch) {
        titleMatch = section.match(/## 🏷️ (.+)/);
      }
      const title = titleMatch ? titleMatch[1].trim() : "Untitled Feature";

      // Extract milestone
      const milestoneMatch = section.match(
        /\*\*Milestone\*\*:\s*(.+?)(?:\s*\n)/
      );
      const milestone = milestoneMatch ? milestoneMatch[1].trim() : "";
      console.log(`Extracted milestone: ${milestone} for feature: ${title}`);

      // Extract type
      const typeMatch = section.match(/\*\*Type\*\*:\s*`(.+?)`/);
      const type = typeMatch ? typeMatch[1].trim() : "Feature";
      console.log(`Extracted type: ${type} for feature: ${title}`);

      // Extract labels
      let labels = [];

      // Try to extract all labels
      const fullLabelsText = section.match(/\*\*Labels\*\*:(.+?)\n/);
      if (fullLabelsText) {
        const labelsText = fullLabelsText[1];
        // Extract all text between backticks
        const labelMatches = labelsText.match(/`([^`]+)`/g);
        if (labelMatches) {
          labels = labelMatches.map((label) =>
            label
              .replace(/`/g, "")
              .trim()
              .replace(/\s*<!--.*?-->\s*/g, "")
          );
        }
      }

      console.log(
        `Extracted labels: ${labels.join(", ")} for feature: ${title}`
      );

      // Extract context
      const contextMatch = section.match(/### 🧠 Context\s+> (.+?)\s+###/s);
      const context = contextMatch ? contextMatch[1].trim() : "";

      // Extract task breakdown
      const taskMatch = section.match(
        /### ✅ Task Breakdown\s+([\s\S]+?)\s+###/
      );
      const tasks = taskMatch ? taskMatch[1].trim() : "";

      // Extract test cases
      const testMatch = section.match(/### 📋 Test Cases\s+([\s\S]+?)\s+###/);
      const tests = testMatch ? testMatch[1].trim() : "";

      // Extract acceptance criteria
      const acMatch = section.match(
        /### 🧪 Acceptance Criteria\s+([\s\S]+?)\s+###/
      );
      const acceptanceCriteria = acMatch ? acMatch[1].trim() : "";

      // Extract technical constraints
      const techMatch = section.match(
        /### ⚙️ Technical Constraints \/ Guidelines\s+([\s\S]+?)\s+###/
      );
      const technicalConstraints = techMatch ? techMatch[1].trim() : "";

      // Extract references
      const refMatch = section.match(
        /### 🔗 References \/ Designs \/ Docs\s+([\s\S]+?)\s+###/
      );
      const references = refMatch ? refMatch[1].trim() : "";

      // Extract priority
      const priorityMatch = section.match(
        /### ⏱️ Priority\s+> (.+?)(\s+---|$)/s
      );
      const priority = priorityMatch
        ? priorityMatch[1].trim().replace(/\s*<!--.*?-->\s*/g, "")
        : "Medium";

      // Check if this feature has been created (look for a special marker in the markdown)
      const createdMatch = section.match(/<!-- Created: (true|false) -->/);
      const created = createdMatch ? createdMatch[1] === "true" : false;

      // Generate a unique ID for the feature based on the title
      const id = title.toLowerCase().replace(/[^a-z0-9]+/g, "-");

      // Construct the description
      const description = [
        `## Type\n${type}\n`,
        `## Context\n${context}\n`,
        `## Task Breakdown\n${tasks}\n`,
        `## Test Cases\n${tests}\n`,
        `## Acceptance Criteria\n${acceptanceCriteria}\n`,
        `## Technical Constraints / Guidelines\n${technicalConstraints}\n`,
        references ? `## References / Designs / Docs\n${references}\n` : "",
        // Priority is no longer included in the description
      ].join("\n");

      features.push({
        id,
        title,
        description,
        milestone,
        type,
        labels,
        priority,
        created,
      });
    } catch (error) {
      console.warn(
        `Warning: Could not parse a feature section: ${error.message}`
      );
    }
  }

  return features;
}

/**
 * Main function to create GitHub issues
 */
async function createGitHubIssues() {
  try {
    // Read and parse the Markdown file
    const fileContent = fs.readFileSync(PRD_FILE_PATH, "utf8");
    const features = parseMarkdownFeatures(fileContent);

    // Filter features by milestone name (if provided) and created status
    const featuresToCreate = milestoneName
      ? features.filter(
          (feature) =>
            feature.milestone.includes(milestoneName) && !feature.created
        )
      : features.filter((feature) => !feature.created);

    if (featuresToCreate.length === 0) {
      console.log(
        milestoneName
          ? `No features found for milestone "${milestoneName}" that need to be created.`
          : "No features found that need to be created."
      );
      return;
    }

    // Group features by milestone
    const featuresByMilestone = {};
    for (const feature of featuresToCreate) {
      if (!featuresByMilestone[feature.milestone]) {
        featuresByMilestone[feature.milestone] = [];
      }
      featuresByMilestone[feature.milestone].push(feature);
    }

    // Process each milestone
    for (const [milestoneName, features] of Object.entries(
      featuresByMilestone
    )) {
      // Get GitHub milestones
      const { data: milestones } = await octokit.issues.listMilestones({
        owner: GITHUB_OWNER,
        repo: GITHUB_REPO,
        state: "open",
      });

      // Find the milestone by name
      let milestone = milestones.find((m) => m.title === milestoneName);

      // Create milestone if it doesn't exist
      if (!milestone) {
        console.log(
          `Milestone "${milestoneName}" not found in GitHub. Creating it now...`
        );
        try {
          const { data: newMilestone } = await octokit.issues.createMilestone({
            owner: GITHUB_OWNER,
            repo: GITHUB_REPO,
            title: milestoneName,
            description: `${milestoneName} for the project`,
          });

          console.log(`Created milestone: ${newMilestone.title}`);
          milestone = newMilestone;
        } catch (error) {
          console.error(`Error creating milestone: ${error.message}`);
          if (error.response) {
            console.error("GitHub API response:", error.response.data);
          }
          continue; // Skip this milestone and move to the next one
        }
      }

      console.log(
        `Creating ${features.length} issues for milestone: ${milestone.title}`
      );

      // Create issues for each feature
      for (const feature of features) {
        // Prepare labels (don't add type as a label)
        const labels = [...feature.labels];

        try {
          // Create the issue
          const { data: issue } = await octokit.issues.create({
            owner: GITHUB_OWNER,
            repo: GITHUB_REPO,
            title: feature.title,
            body: feature.description,
            milestone: milestone.number,
            labels: labels,
          });

          console.log(`Created issue #${issue.number}: ${issue.title}`);
          console.log(`Issue URL: ${issue.html_url}`);

          // Mark the feature as created in the Markdown file
          markFeatureAsCreated(feature.id);
        } catch (error) {
          console.error(
            `Error creating issue for "${feature.title}": ${error.message}`
          );
          if (error.response) {
            console.error("GitHub API response:", error.response.data);
          }
        }
      }
    }

    console.log("Finished creating issues");
  } catch (error) {
    console.error("Error creating GitHub issues:", error.message);
    if (error.response) {
      console.error("GitHub API response:", error.response.data);
    }
    process.exit(1);
  }
}

/**
 * Mark a feature as created in the Markdown file
 */
function markFeatureAsCreated(featureId) {
  try {
    let fileContent = fs.readFileSync(PRD_FILE_PATH, "utf8");

    // Split the content by feature sections
    const sections = fileContent.split("---");

    // Find the section with the matching feature ID
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i];
      let titleMatch = section.match(/## 🏷️ Title: (.+)/);
      if (!titleMatch) {
        titleMatch = section.match(/## 🏷️ (.+)/);
      }

      if (titleMatch) {
        const title = titleMatch[1].trim();
        const id = title.toLowerCase().replace(/[^a-z0-9]+/g, "-");

        if (id === featureId) {
          // Check if the created marker already exists
          if (section.includes("<!-- Created:")) {
            // Update the existing marker
            sections[i] = section.replace(
              /<!-- Created: (true|false) -->/,
              "<!-- Created: true -->"
            );
          } else {
            // Add the marker at the end of the section
            sections[i] = section.trim() + "\n\n<!-- Created: true -->\n";
          }
          break;
        }
      }
    }

    // Join the sections back together with proper formatting
    const updatedContent = sections
      .map((section, index) => {
        // Skip the first section (header) or empty sections
        if (index === 0 || !section.trim()) {
          return section;
        }

        // Make sure each section starts with a newline after the separator
        if (!section.startsWith("\n")) {
          return "\n\n" + section.trim();
        }

        return section;
      })
      .join("---");

    // Write the updated content back to the file
    fs.writeFileSync(PRD_FILE_PATH, updatedContent);

    console.log(
      `Marked feature "${featureId}" as created in the Markdown file`
    );
  } catch (error) {
    console.error(`Error marking feature as created: ${error.message}`);
  }
}

/**
 * Dry run function to simulate issue creation without actually creating them
 */
async function dryRunIssues() {
  try {
    // Read and parse the Markdown file
    const fileContent = fs.readFileSync(PRD_FILE_PATH, "utf8");
    const features = parseMarkdownFeatures(fileContent);

    // Filter features by milestone name (if provided) and created status
    const featuresToCreate = milestoneName
      ? features.filter(
          (feature) =>
            feature.milestone.includes(milestoneName) && !feature.created
        )
      : features.filter((feature) => !feature.created);

    if (featuresToCreate.length === 0) {
      console.log(
        milestoneName
          ? `No features found for milestone "${milestoneName}" that need to be created.`
          : "No features found that need to be created."
      );
      return;
    }

    console.log(`DRY RUN: Would create ${featuresToCreate.length} issues:`);

    // Display features that would be created
    for (const feature of featuresToCreate) {
      console.log("\n-----------------------------------");
      console.log(`Issue: ${feature.title}`);
      console.log(`Milestone: ${feature.milestone}`);
      console.log(
        `Description: ${feature.description.substring(0, 100)}${
          feature.description.length > 100 ? "..." : ""
        }`
      );

      // Prepare labels (don't add type as a label)
      const labels = [...feature.labels];

      console.log(`Labels: ${labels.join(", ")}`);
      console.log("-----------------------------------");
    }

    console.log(
      "\nDRY RUN: No changes were made to the Markdown file or GitHub"
    );
  } catch (error) {
    console.error("Error in dry run:", error.message);
    process.exit(1);
  }
}

// Run the appropriate function based on mode
if (isDryRun) {
  console.log("Running in dry-run mode - no issues will be created");
  dryRunIssues();
} else {
  createGitHubIssues();
}
