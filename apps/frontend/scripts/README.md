# Database Scripts

This directory contains SQL scripts for making direct changes to the database without using Prisma migrations.

## add_uil_otp_table.sql

This script adds the UILOtp table to the database for the User Initiated Linking (UIL) flow.

### Usage

You can run this script directly against your PostgreSQL database using the `psql` command-line tool:

```bash
psql -U your_username -d your_database -f scripts/add_uil_otp_table.sql
```

Or you can copy and paste the SQL commands into your PostgreSQL client.

### What it does

1. Checks if the UILOtp table already exists
2. If it doesn't exist, creates the table with the required columns
3. Creates a unique index on the linkRefNumber column

### Safety

This script is designed to be safe to run multiple times. It will only create the table if it doesn't already exist, and it won't affect any existing data in your database.

## After running the script

After adding the table to your database, you should be able to use the User Initiated Linking (UIL) flow without any issues. The Prisma client will automatically recognize the new table as long as it matches the schema definition.
