#!/usr/bin/env node

/**
 * Script to create a Super Admin user
 * Usage: node scripts/create-super-admin.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const readline = require('readline');

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

function questionHidden(query) {
  return new Promise(resolve => {
    process.stdout.write(query);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

async function createSuperAdmin() {
  try {
    console.log('🔐 Super Admin User Creation Script');
    console.log('=====================================\n');

    // Get user input
    const name = await question('Enter Super Admin name: ');
    const email = await question('Enter Super Admin email: ');
    const password = await questionHidden('Enter password: ');
    const confirmPassword = await questionHidden('Confirm password: ');

    if (password !== confirmPassword) {
      console.log('\n❌ Passwords do not match!');
      process.exit(1);
    }

    if (password.length < 6) {
      console.log('\n❌ Password must be at least 6 characters long!');
      process.exit(1);
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      console.log('\n❌ User with this email already exists!');
      process.exit(1);
    }

    // Hash password
    console.log('\n🔄 Creating Super Admin user...');
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user and organization in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          role: 'superAdmin',
          emailVerified: new Date(),
        }
      });

      // Create organization
      const organization = await tx.organization.create({
        data: {
          name: 'Super Admin Organization',
          slug: `super-admin-org-${Date.now()}`,
          onboardingCompleted: true,
          status: 'active',
        }
      });

      // Create user-organization relationship
      const userOrganization = await tx.userOrganization.create({
        data: {
          userId: user.id,
          organizationId: organization.id,
          roles: ['superAdmin'],
          isDefault: true,
        }
      });

      return { user, organization, userOrganization };
    });

    console.log('\n✅ Super Admin user created successfully!');
    console.log('\nDetails:');
    console.log(`👤 Name: ${result.user.name}`);
    console.log(`📧 Email: ${result.user.email}`);
    console.log(`🏢 Organization: ${result.organization.name}`);
    console.log(`🔑 Role: ${result.user.role}`);
    console.log('\n🚀 You can now sign in with these credentials and access the admin portal at /admin');

  } catch (error) {
    console.error('\n❌ Error creating Super Admin user:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    rl.close();
  }
}

// Run the script
createSuperAdmin();
