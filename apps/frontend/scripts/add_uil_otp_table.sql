-- Add UILOtp table to the database
-- Run this script directly against your PostgreSQL database to add the UILOtp table
-- without affecting existing data

-- Check if the table already exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'UILOtpNotify') THEN
        -- Create the UILOtp table
        CREATE TABLE "UILOtpNotify" (
            "id" TEXT NOT NULL,
            "linkRefNumber" TEXT NOT NULL,
            "otp" TEXT NOT NULL,
            "transactionId" TEXT NOT NULL,
            "patientId" TEXT NOT NULL,
            "careContexts" JSONB NOT NULL,
            "expiresAt" TIMESTAMP(3) NOT NULL,
            "verified" BOOLEAN NOT NULL DEFAULT false,
            "requestId" TEXT,
            "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updatedAt" TIMESTAMP(3) NOT NULL,

            CONSTRAINT "UILOtpNotify_pkey" PRIMARY KEY ("id")
        );

        -- Create unique index on linkRefNumber
        CREATE UNIQUE INDEX "UILOtp_linkRefNumber_key" ON "UILOtpNotify"("linkRefNumber");
        
        RAISE NOTICE 'UILOtpNotify table created successfully';
    ELSE
        RAISE NOTICE 'UILOtpNotify table already exists';
    END IF;
END
$$;
