#!/usr/bin/env node

/**
 * <PERSON>ript to add status field to Organization table
 * Usage: node scripts/add-organization-status.js
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addOrganizationStatus() {
  try {
    console.log('🔄 Adding status field to Organization table...');

    // Check if status field already exists
    try {
      await prisma.$queryRaw`SELECT status FROM "Organization" LIMIT 1`;
      console.log('✅ Status field already exists in Organization table');
      return;
    } catch (error) {
      // Field doesn't exist, proceed with adding it
      console.log('📝 Status field not found, adding it...');
    }

    // Add status field with default value
    await prisma.$executeRaw`ALTER TABLE "Organization" ADD COLUMN "status" TEXT NOT NULL DEFAULT 'active'`;
    
    // Create index for better query performance
    await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Organization_status_idx" ON "Organization"("status")`;
    
    // Update existing organizations to have active status (just in case)
    const updateResult = await prisma.$executeRaw`UPDATE "Organization" SET "status" = 'active' WHERE "status" IS NULL OR "status" = ''`;
    
    console.log('✅ Successfully added status field to Organization table');
    console.log(`📊 Updated ${updateResult} existing organizations to active status`);
    
    // Verify the change
    const orgCount = await prisma.organization.count();
    const activeOrgCount = await prisma.organization.count({
      where: { status: 'active' }
    });
    
    console.log(`📈 Total organizations: ${orgCount}`);
    console.log(`📈 Active organizations: ${activeOrgCount}`);
    
  } catch (error) {
    console.error('❌ Error adding status field:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addOrganizationStatus();
