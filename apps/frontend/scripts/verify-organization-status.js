#!/usr/bin/env node

/**
 * Script to verify organization status migration
 * Usage: node scripts/verify-organization-status.js
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyOrganizationStatus() {
  try {
    console.log('🔍 Verifying organization status migration...\n');

    // Test 1: Check if status field exists and is accessible
    try {
      const orgWithStatus = await prisma.organization.findFirst({
        select: {
          id: true,
          name: true,
          status: true,
        }
      });
      
      console.log('✅ Status field exists and is accessible');
      if (orgWithStatus) {
        console.log(`   Sample org: ${orgWithStatus.name} - Status: ${orgWithStatus.status}`);
      }
    } catch (error) {
      console.log('❌ Status field not accessible:', error.message);
      return;
    }

    // Test 2: Count organizations by status
    const totalOrgs = await prisma.organization.count();
    const activeOrgs = await prisma.organization.count({
      where: { status: 'active' }
    });
    const inactiveOrgs = await prisma.organization.count({
      where: { status: 'inactive' }
    });

    console.log('\n📊 Organization Status Summary:');
    console.log(`   Total Organizations: ${totalOrgs}`);
    console.log(`   Active Organizations: ${activeOrgs}`);
    console.log(`   Inactive Organizations: ${inactiveOrgs}`);

    // Test 3: Try to update a status (and revert it)
    if (totalOrgs > 0) {
      const testOrg = await prisma.organization.findFirst();
      if (testOrg) {
        console.log('\n🧪 Testing status update functionality...');
        
        const originalStatus = testOrg.status;
        const newStatus = originalStatus === 'active' ? 'inactive' : 'active';
        
        // Update status
        await prisma.organization.update({
          where: { id: testOrg.id },
          data: { status: newStatus }
        });
        console.log(`   ✅ Updated ${testOrg.name} from ${originalStatus} to ${newStatus}`);
        
        // Revert status
        await prisma.organization.update({
          where: { id: testOrg.id },
          data: { status: originalStatus }
        });
        console.log(`   ✅ Reverted ${testOrg.name} back to ${originalStatus}`);
      }
    }

    console.log('\n🎉 Organization status migration verification completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Restart your development server: npm run dev');
    console.log('   2. Go to /admin/organizations');
    console.log('   3. Try toggling organization status');

  } catch (error) {
    console.error('\n❌ Error during verification:', error);
    
    if (error.message.includes('Unknown field')) {
      console.log('\n💡 Suggestion: The status field might not be added to the database yet.');
      console.log('   Run the migration first:');
      console.log('   psql your_database -f apps/frontend/prisma/migrations/20250616000001_add_organization_status/migration.sql');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
verifyOrganizationStatus();
