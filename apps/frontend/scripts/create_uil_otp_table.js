/**
 * <PERSON><PERSON><PERSON> to create the UILOtp table in the database
 * Run with: node scripts/create_uil_otp_table.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createUILOtpTable() {
  try {
    // Check if the table already exists
    try {
      await prisma.$queryRaw`SELECT 1 FROM "UILOtp" LIMIT 1`;
      console.log('UILOtp table already exists');
      return;
    } catch (error) {
      // Table doesn't exist, continue with creation
      console.log('UILOtp table does not exist, creating...');
    }

    // Create the UILOtp table
    await prisma.$executeRaw`
      CREATE TABLE "UILOtp" (
        "id" TEXT NOT NULL,
        "linkRefNumber" TEXT NOT NULL,
        "otp" TEXT NOT NULL,
        "transactionId" TEXT NOT NULL,
        "patientId" TEXT NOT NULL,
        "careContexts" JSONB NOT NULL,
        "expiresAt" TIMESTAMP(3) NOT NULL,
        "verified" BOOLEAN NOT NULL DEFAULT false,
        "requestId" TEXT,
        "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP(3) NOT NULL,
        CONSTRAINT "UILOtp_pkey" PRIMARY KEY ("id")
      );
    `;

    // Create unique index on linkRefNumber
    await prisma.$executeRaw`
      CREATE UNIQUE INDEX "UILOtp_linkRefNumber_key" ON "UILOtp"("linkRefNumber");
    `;

    console.log('UILOtp table created successfully');
  } catch (error) {
    console.error('Error creating UILOtp table:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createUILOtpTable();
