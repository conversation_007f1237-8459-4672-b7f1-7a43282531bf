const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Get the file path from command line arguments
const filePath = process.argv[2];

if (!filePath) {
  console.error('Please provide a file path');
  process.exit(1);
}

// Resolve the absolute path
const absolutePath = path.resolve(process.cwd(), filePath);

// Check if the file exists
if (!fs.existsSync(absolutePath)) {
  console.error(`File not found: ${absolutePath}`);
  process.exit(1);
}

// Determine the platform-specific command to open the file
let command;
const platform = os.platform();

if (platform === 'darwin') {
  // macOS
  command = `open "${absolutePath}"`;
} else if (platform === 'win32') {
  // Windows
  command = `start "" "${absolutePath}"`;
} else {
  // Linux and others
  command = `xdg-open "${absolutePath}"`;
}

// Execute the command
console.log(`Opening ${filePath}...`);
exec(command, (error) => {
  if (error) {
    console.error(`Error opening file: ${error.message}`);
    process.exit(1);
  }
});
