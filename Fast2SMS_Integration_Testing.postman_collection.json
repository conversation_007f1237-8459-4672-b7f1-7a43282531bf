{"info": {"_postman_id": "fast2sms-integration-test-collection", "name": "Fast2SMS Integration Testing", "description": "Comprehensive testing collection for Fast2SMS integration in Arancare webapp including UIL flow, OTP delivery, and SMS services", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Fast2SMS Service Tests", "item": [{"name": "Check Fast2SMS Configuration & Balance", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/sms/fast2sms/test", "host": ["{{BASE_URL}}"], "path": ["api", "sms", "fast2sms", "test"]}, "description": "Check Fast2SMS service configuration and wallet balance"}, "response": []}, {"name": "Test Fast2SMS OTP Sending", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{TEST_PHONE_NUMBER}}\",\n    \"otp\": \"{{$randomInt}}\",\n    \"testType\": \"otp\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/sms/fast2sms/test", "host": ["{{BASE_URL}}"], "path": ["api", "sms", "fast2sms", "test"]}, "description": "Test sending OTP via Fast2SMS OTP route"}, "response": []}, {"name": "Test Fast2SMS Custom Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{TEST_PHONE_NUMBER}}\",\n    \"message\": \"This is a test message from Arancare webapp Fast2SMS integration.\",\n    \"testType\": \"custom\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/sms/fast2sms/test", "host": ["{{BASE_URL}}"], "path": ["api", "sms", "fast2sms", "test"]}, "description": "Test sending custom message via Fast2SMS Quick SMS route"}, "response": []}], "description": "Direct Fast2SMS service testing endpoints"}, {"name": "User Initiated Linking (UIL) Flow", "item": [{"name": "1. Simulate Discovery Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "REQUEST-ID", "value": "{{$guid}}"}, {"key": "TIMESTAMP", "value": "{{$isoTimestamp}}"}, {"key": "X-CM-ID", "value": "{{X_CM_ID}}"}], "body": {"mode": "raw", "raw": "{\n    \"requestId\": \"{{$guid}}\",\n    \"timestamp\": \"{{$isoTimestamp}}\",\n    \"query\": {\n        \"id\": \"{{TEST_ABHA_ADDRESS}}\",\n        \"purpose\": \"LINK\",\n        \"requester\": {\n            \"type\": \"HIU\",\n            \"id\": \"{{HIU_ID}}\"\n        }\n    },\n    \"transactionId\": \"{{$guid}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/webhook/api/v3/hip/patient/care-context/discover", "host": ["{{BASE_URL}}"], "path": ["api", "webhook", "api", "v3", "hip", "patient", "care-context", "discover"]}, "description": "Simulate ABDM discovery request to start UIL flow"}, "response": []}, {"name": "2. Simulate Link Init Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "REQUEST-ID", "value": "{{$guid}}"}, {"key": "TIMESTAMP", "value": "{{$isoTimestamp}}"}, {"key": "X-CM-ID", "value": "{{X_CM_ID}}"}], "body": {"mode": "raw", "raw": "{\n    \"requestId\": \"{{$guid}}\",\n    \"timestamp\": \"{{$isoTimestamp}}\",\n    \"transactionId\": \"{{TRANSACTION_ID}}\",\n    \"patient\": {\n        \"id\": \"{{TEST_ABHA_ADDRESS}}\",\n        \"verifiers\": [\n            {\n                \"type\": \"MOBILE\",\n                \"value\": \"{{TEST_PHONE_NUMBER}}\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "{{BASE_URL}}/api/webhook/api/v3/hip/link/care-context/init", "host": ["{{BASE_URL}}"], "path": ["api", "webhook", "api", "v3", "hip", "link", "care-context", "init"]}, "description": "Simulate ABDM link init request - this will generate and send OTP via Fast2SMS"}, "response": []}, {"name": "3. Simulate Link Confirm Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "REQUEST-ID", "value": "{{$guid}}"}, {"key": "TIMESTAMP", "value": "{{$isoTimestamp}}"}, {"key": "X-CM-ID", "value": "{{X_CM_ID}}"}], "body": {"mode": "raw", "raw": "{\n    \"requestId\": \"{{$guid}}\",\n    \"timestamp\": \"{{$isoTimestamp}}\",\n    \"transactionId\": \"{{TRANSACTION_ID}}\",\n    \"confirmation\": {\n        \"linkRefNumber\": \"{{LINK_REF_NUMBER}}\",\n        \"token\": \"{{OTP_FROM_SMS}}\"\n    }\n}"}, "url": {"raw": "{{BASE_URL}}/api/webhook/api/v3/hip/link/care-context/confirm", "host": ["{{BASE_URL}}"], "path": ["api", "webhook", "api", "v3", "hip", "link", "care-context", "confirm"]}, "description": "Simulate ABDM link confirm request with OTP received via SMS"}, "response": []}], "description": "Complete User Initiated Linking flow with Fast2SMS OTP delivery"}, {"name": "Patient OTP Management", "item": [{"name": "Get Active UIL OTP for Patient", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/patients/{{TEST_PATIENT_ID}}/uil-otps/active", "host": ["{{BASE_URL}}"], "path": ["api", "patients", "{{TEST_PATIENT_ID}}", "uil-otps", "active"]}, "description": "Get active UIL OTP for a specific patient"}, "response": []}, {"name": "Resend UIL OTP via SMS", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/patients/{{TEST_PATIENT_ID}}/uil-otps/send-sms", "host": ["{{BASE_URL}}"], "path": ["api", "patients", "{{TEST_PATIENT_ID}}", "uil-otps", "send-sms"]}, "description": "Resend active UIL OTP via SMS using Fast2SMS"}, "response": []}, {"name": "Resend UIL OTP via Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/patients/{{TEST_PATIENT_ID}}/uil-otps/send-email", "host": ["{{BASE_URL}}"], "path": ["api", "patients", "{{TEST_PATIENT_ID}}", "uil-otps", "send-email"]}, "description": "Resend active UIL OTP via email"}, "response": []}], "description": "Patient-specific OTP management endpoints"}, {"name": "General OTP Services", "item": [{"name": "Request OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"mobile\": \"{{TEST_PHONE_NUMBER}}\",\n    \"purpose\": \"test-verification\",\n    \"patientId\": \"{{TEST_PATIENT_ID}}\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/otp/request", "host": ["{{BASE_URL}}"], "path": ["api", "otp", "request"]}, "description": "Request OTP for general purposes"}, "response": []}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"mobile\": \"{{TEST_PHONE_NUMBER}}\",\n    \"otp\": \"{{OTP_RECEIVED}}\",\n    \"purpose\": \"test-verification\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/otp/verify", "host": ["{{BASE_URL}}"], "path": ["api", "otp", "verify"]}, "description": "Verify OTP"}, "response": []}], "description": "General OTP service endpoints"}, {"name": "Error Scenarios & Edge Cases", "item": [{"name": "Test Invalid Phone Number", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"invalid-phone\",\n    \"otp\": \"123456\",\n    \"testType\": \"otp\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/sms/fast2sms/test", "host": ["{{BASE_URL}}"], "path": ["api", "sms", "fast2sms", "test"]}, "description": "Test error handling for invalid phone number format"}, "response": []}, {"name": "Test Missing OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"{{TEST_PHONE_NUMBER}}\",\n    \"testType\": \"otp\"\n}"}, "url": {"raw": "{{BASE_URL}}/api/sms/fast2sms/test", "host": ["{{BASE_URL}}"], "path": ["api", "sms", "fast2sms", "test"]}, "description": "Test error handling when OTP is missing"}, "response": []}, {"name": "Test Patient Without Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/patients/{{PATIENT_WITHOUT_PHONE_ID}}/uil-otps/send-sms", "host": ["{{BASE_URL}}"], "path": ["api", "patients", "{{PATIENT_WITHOUT_PHONE_ID}}", "uil-otps", "send-sms"]}, "description": "Test error handling for patient without phone number"}, "response": []}, {"name": "Test Non-existent Patient", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{BASE_URL}}/api/patients/non-existent-patient-id/uil-otps/send-sms", "host": ["{{BASE_URL}}"], "path": ["api", "patients", "non-existent-patient-id", "uil-otps", "send-sms"]}, "description": "Test error handling for non-existent patient"}, "response": []}], "description": "Error scenarios and edge case testing"}], "variable": [{"key": "BASE_URL", "value": "http://localhost:3000", "description": "Base URL for the Arancare webapp"}, {"key": "TEST_PHONE_NUMBER", "value": "**********", "description": "Test phone number for SMS testing (10 digits)"}, {"key": "TEST_ABHA_ADDRESS", "value": "test@abdm", "description": "Test ABHA address for UIL flow"}, {"key": "TEST_PATIENT_ID", "value": "patient-id-here", "description": "Test patient ID for patient-specific operations"}, {"key": "X_CM_ID", "value": "sbx", "description": "ABDM CM ID for sandbox environment"}, {"key": "HIU_ID", "value": "test-hiu-id", "description": "HIU ID for testing"}, {"key": "TRANSACTION_ID", "value": "", "description": "Transaction ID from discovery response"}, {"key": "LINK_REF_NUMBER", "value": "", "description": "Link reference number from init response"}, {"key": "OTP_FROM_SMS", "value": "", "description": "OTP received via SMS"}, {"key": "OTP_RECEIVED", "value": "", "description": "OTP received for verification"}]}