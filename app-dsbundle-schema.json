{"type": "array", "items": {"type": "object", "properties": {"fullUrl": {"type": "string"}, "resource": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "meta": {"type": "object", "properties": {"profile": {"type": "array", "items": {"type": "string", "format": "uri"}}, "versionId": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "required": ["profile"]}, "resourceType": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "type": {"anyOf": [{"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}}, {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}]}, "title": {"type": "string"}, "author": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "status": {"type": "string"}, "section": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "title": {"type": "string"}, "entry": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "text": {"type": "object", "properties": {"div": {"type": "string"}, "status": {"type": "string"}}, "required": ["div", "status"]}}, "required": ["code", "title"]}}, "subject": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "language": {"type": "string"}, "custodian": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "encounter": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "name": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "use": {"type": "string"}, "given": {"type": "array", "items": {"type": "string"}}, "family": {"type": "string"}}, "required": ["text"]}}, {"type": "string"}]}, "active": {"type": "boolean"}, "gender": {"type": "string"}, "address": {"type": "array", "items": {"type": "object", "properties": {"use": {"type": "string"}, "city": {"type": "string"}, "line": {"type": "array", "items": {"type": "string"}}, "type": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}}, "required": ["use", "city", "line", "type", "state", "country", "postalCode"]}}, "telecom": {"type": "array", "items": {"type": "object", "properties": {"use": {"type": "string"}, "value": {"type": "string"}, "system": {"type": "string"}}, "required": ["use", "value", "system"]}}, "birthDate": {"type": "string", "format": "date"}, "qualification": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, "required": ["code"]}}, "text": {"type": "object", "properties": {"div": {"type": "string"}, "status": {"type": "string"}}, "required": ["div", "status"]}, "identifier": {"anyOf": [{"type": "object", "properties": {"value": {"type": "string", "format": "uuid"}, "system": {"type": "string", "format": "uri"}}, "required": ["value", "system"]}, {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "value": {"type": "string"}, "system": {"type": "string", "format": "uri"}}, "required": ["type", "value", "system"]}}, {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "value": {"type": "string"}, "system": {"type": "string", "format": "uri"}}, "required": ["type", "value", "system"]}}, {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "value": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}}, "required": ["type", "value", "system"]}}]}, "class": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}, "period": {"type": "object", "properties": {"end": {"type": "string", "format": "date-time"}, "start": {"type": "string", "format": "date-time"}}, "required": ["end", "start"]}, "participant": {"type": "array", "items": {"type": "object", "properties": {"individual": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "actor": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "status": {"type": "string"}}}}, "serviceProvider": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "code": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "category": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}}, {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}}, {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}}]}, "recorder": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "severity": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "recordedDate": {"type": "string", "format": "date-time"}, "onsetDateTime": {"type": "string", "format": "date-time"}, "clinicalStatus": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "verificationStatus": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "outcome": {"type": "string"}, "performer": {"type": "array", "items": {"type": "object", "properties": {"actor": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "display": {"type": "string"}, "reference": {"type": "string"}}}}, "performedDateTime": {"type": "string", "format": "date-time"}, "intent": {"type": "string"}, "requester": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "authoredOn": {"type": "string", "format": "date-time"}, "dosageInstruction": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "route": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "method": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "timing": {"type": "object", "properties": {"repeat": {"type": "object", "properties": {"period": {"type": "string"}, "frequency": {"type": "string"}, "periodUnit": {"type": "string"}}, "required": ["period", "frequency", "periodUnit"]}}, "required": ["repeat"]}, "asNeededBoolean": {"type": "string"}, "asNeededCodeableConcept": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}}, "required": ["text", "route", "method", "timing", "asNeededBoolean", "asNeededCodeableConcept"]}}, "medicationCodeableConcept": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "valueQuantity": {"type": "object", "properties": {"code": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "string"}, "system": {"type": "string", "format": "uri"}}, "required": ["code", "unit", "value", "system"]}, "effectiveDateTime": {"type": "string", "format": "date-time"}, "issued": {"type": "string", "format": "date-time"}, "result": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "conclusion": {"type": "string"}, "end": {"type": "string", "format": "date-time"}, "start": {"type": "string", "format": "date-time"}, "created": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "content": {"type": "array", "items": {"type": "object", "properties": {"attachment": {"type": "object", "properties": {"data": {"type": "string"}, "title": {"type": "string"}, "creation": {"type": "string", "format": "date-time"}, "contentType": {"type": "string"}}, "required": ["data", "title", "creation", "contentType"]}}, "required": ["attachment"]}}, "context": {"type": "object", "properties": {"encounter": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}}, "required": ["encounter"]}, "docStatus": {"type": "string"}}, "required": ["id", "meta", "resourceType"]}}, "required": ["fullUrl", "resource"]}}