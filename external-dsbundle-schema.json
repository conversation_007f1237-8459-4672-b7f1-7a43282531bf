{"type": "array", "items": {"type": "object", "properties": {"fullUrl": {"type": "string"}, "resource": {"type": "object", "properties": {"id": {"type": "string"}, "resourceType": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "type": {"anyOf": [{"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, {"type": "string"}]}, "title": {"type": "string"}, "author": {"type": "array", "items": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}}, "status": {"type": "string"}, "section": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "entry": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}}, "required": ["reference"]}}}, "required": ["code", "entry"]}}, "subject": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference"]}, "custodian": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "encounter": {"type": "object", "properties": {"reference": {"type": "string"}}, "required": ["reference"]}, "meta": {"type": "object", "properties": {"profile": {"type": "array", "items": {"type": "string", "format": "uri"}}, "versionId": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}}, "required": ["profile"]}, "name": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, {"type": "string"}]}, "gender": {"type": "string"}, "birthDate": {"type": "string", "format": "date"}, "identifier": {"anyOf": [{"type": "object", "properties": {"value": {"type": "string", "format": "uuid"}, "system": {"type": "string", "format": "uri"}}, "required": ["value", "system"]}, {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "value": {"type": "string"}, "system": {"type": "string", "format": "uri"}}, "required": ["type", "value", "system"]}}, {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "value": {"type": "string"}, "system": {"type": "string", "format": "uri"}}, "required": ["type", "value", "system"]}}, {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "value": {"type": "string"}, "system": {"type": "string", "format": "uri"}}, "required": ["type", "value", "system"]}}, {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "value": {"type": "string", "format": "uuid"}, "system": {"type": "string", "format": "uri"}}, "required": ["type", "value", "system"]}}]}, "class": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}, "period": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time"}}, "required": ["start"]}, "code": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "onsetPeriod": {"type": "object", "properties": {"end": {"type": "string", "format": "date"}, "start": {"type": "string", "format": "date"}}, "required": ["end", "start"]}, "recordedDate": {"type": "string", "format": "date"}, "performer": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}, "display": {"type": "string"}}, "required": ["reference"]}}, "valueCodeableConcept": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}, "patient": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "recorder": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "clinicalStatus": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["coding"]}, "condition": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}}, "required": ["code"]}}, "relationship": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "note": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}, "intent": {"type": "string"}, "activity": {"type": "array", "items": {"type": "object", "properties": {"detail": {"type": "object", "properties": {"description": {"type": "string"}}, "required": ["description"]}}, "required": ["detail"]}}, "category": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}, "text": {"type": "string"}}, "required": ["coding"]}}, "description": {"type": "string"}, "requester": {"type": "object", "properties": {"display": {"type": "string"}, "reference": {"type": "string"}}, "required": ["display", "reference"]}, "authoredOn": {"type": "string", "format": "date-time"}, "dosageInstruction": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "route": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "method": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "format": "date"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "timing": {"type": "object", "properties": {"repeat": {"type": "object", "properties": {"period": {"type": "integer"}, "frequency": {"type": "integer"}, "periodUnit": {"type": "string"}}, "required": ["period", "frequency", "periodUnit"]}}, "required": ["repeat"]}, "additionalInstruction": {"type": "array", "items": {"type": "object", "properties": {"coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}}, "required": ["code", "system"]}}}, "required": ["coding"]}}}, "required": ["text", "route", "method", "timing", "additionalInstruction"]}}, "medicationCodeableConcept": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "issued": {"type": "string", "format": "date-time"}, "result": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}}, "required": ["reference"]}}, "conclusion": {"type": "string"}, "presentedForm": {"type": "array", "items": {"type": "object", "properties": {"data": {"type": "string"}, "contentType": {"type": "string"}}, "required": ["data", "contentType"]}}, "conclusionCode": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}}, "resultsInterpreter": {"type": "array", "items": {"type": "object", "properties": {"reference": {"type": "string"}}, "required": ["reference"]}}, "outcome": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}, "reasonCode": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "coding": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "system": {"type": "string", "format": "uri"}, "display": {"type": "string"}}, "required": ["code", "system", "display"]}}}, "required": ["text", "coding"]}}, "performedDateTime": {"type": "string", "format": "date"}, "content": {"type": "array", "items": {"type": "object", "properties": {"attachment": {"type": "object", "properties": {"data": {"type": "string"}, "title": {"type": "string"}, "creation": {"type": "string", "format": "date-time"}, "contentType": {"type": "string"}}, "required": ["data", "title", "creation", "contentType"]}}, "required": ["attachment"]}}, "docStatus": {"type": "string"}}, "required": ["id", "resourceType"]}}, "required": ["fullUrl", "resource"]}}