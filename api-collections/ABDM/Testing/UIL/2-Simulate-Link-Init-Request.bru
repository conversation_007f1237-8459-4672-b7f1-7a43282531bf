meta {
  name: 2-Simulate-Link-Init-Request
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/api/webhook/api/v3/hip/link/care-context/init
  body: json
  auth: none
}

headers {
  REQUEST-ID: bce5a953-c6f8-4457-9104-3579372c5e91
  TIMESTAMP: {{$isoTimestamp}}
  X-CM-ID: {{cmId}}
  X-HIP-ID: {{hipId}}
}

body:json {
  {"transactionId":"c5d74bc8-6127-4bec-897f-6f41a31b82ea","abhaAddress":"kumarashutosh1508@sbx","patient":[{"referenceNumber":"cmacajllm000euy890aq8vnu4","careContexts":[{"referenceNumber":"cmacal207000quy89hydsn599"},{"referenceNumber":"cmacjexlv000794e1yhwfpl4o"},{"referenceNumber":"cmadqyuwz000k3q7p5jqrj4xn"},{"referenceNumber":"cmadwzffm000er1sawwbdvliw"},{"referenceNumber":"cmadx5s9t000tr1sak3x4qcme"}],"hiType":"Prescription","count":5}]}
}

vars:pre-request {
  requestId: "{{$guid}}"
}

docs {
  # Simulate Link Init Request
  
  This request simulates the link init request from ABDM to your system.
  
  ## Usage
  
  1. Run this request after the discovery request
  2. Use the same transaction ID from the discovery request
  
  ## Expected Response
  
  ```json
  {
    "status": "Processing"
  }
  ```
  
  ## Notes
  
  - This request initiates the OTP generation
  - Your system should generate an OTP and store it in the database
  - Your system should then send an on-init response to ABDM
  - Check the logs to see the OTP generated (since we're not actually sending SMS)
  - You'll need this OTP for the next step
}
