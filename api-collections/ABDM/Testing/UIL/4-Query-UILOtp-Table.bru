meta {
  name: 4-Query-UILOtp-Table
  type: http
  seq: 4
}

get {
  url: {{baseUrl}}/api/debug/uil-otp?linkRefNumber={{linkRefNumber}}
  auth: none
}

headers {
  Content-Type: application/json
}

vars:pre-request {
  linkRefNumber: "enter-link-ref-number-from-logs"
}

docs {
  # Query UILOtp Table
  
  This request queries the UILOtp table to check the status of the OTP.
  
  ## Usage
  
  1. Run this request after the link confirm request
  2. Update the `linkRefNumber` variable with the link reference number from the logs
  
  ## Expected Response
  
  ```json
  {
    "id": "...",
    "linkRefNumber": "...",
    "otp": "...",
    "transactionId": "...",
    "patientId": "...",
    "careContexts": [...],
    "expiresAt": "...",
    "verified": true,
    "requestId": "...",
    "createdAt": "...",
    "updatedAt": "..."
  }
  ```
  
  ## Notes
  
  - This request is for debugging purposes only
  - It allows you to check if the OTP was properly stored and verified
  - The `verified` field should be `true` after a successful link confirm request
  
  ## Implementation
  
  You'll need to create a debug API route to expose this information:
  
  ```typescript
  // apps/frontend/src/app/api/debug/uil-otp/route.ts
  import { NextRequest, NextResponse } from "next/server";
  import { db } from "@/lib/db";
  
  export async function GET(req: NextRequest) {
    // Only allow in development
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json({ error: "Not available in production" }, { status: 403 });
    }
    
    const linkRefNumber = req.nextUrl.searchParams.get("linkRefNumber");
    
    if (!linkRefNumber) {
      return NextResponse.json({ error: "linkRefNumber is required" }, { status: 400 });
    }
    
    const otpData = await db.uILOtp.findUnique({
      where: { linkRefNumber },
    });
    
    if (!otpData) {
      return NextResponse.json({ error: "OTP not found" }, { status: 404 });
    }
    
    return NextResponse.json(otpData);
  }
  ```
}
