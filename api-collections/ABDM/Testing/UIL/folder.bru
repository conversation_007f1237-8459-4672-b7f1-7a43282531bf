meta {
  name: User Initiated Linking Testing
}

auth {
  mode: none
}

docs {
  # User Initiated Linking (UIL) Testing
  
  This collection contains requests for testing the User Initiated Linking (UIL) flow.
  
  ## Flow
  
  1. **Simulate Discovery Request**: Simulates the discovery request from ABDM
  2. **Simulate Link Init Request**: Simulates the link init request from ABDM
  3. **Simulate Link Confirm Request**: Simulates the link confirm request from ABDM
  
  ## Testing Steps
  
  1. First, update the environment variables in `environment.bru`
  2. Run the "Simulate Discovery Request" to start the flow
  3. Check the logs to see the OTP generated (since we're not actually sending SMS)
  4. Use that OTP in the "Simulate Link Confirm Request"
  
  ## Expected Results
  
  - All requests should return a 202 Accepted status code
  - The logs should show the OTP generated
  - The UILOtp table should have entries for the flow
  - The care contexts should be linked to the ABHA address
}
