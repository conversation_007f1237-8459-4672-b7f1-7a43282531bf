vars {
  baseUrl: "http://localhost:3000"
  abhaAddress: "johndoe@sbx"
  patientId: "clqwerty123"
  transactionId: "{{$guid}}"
  requestId: "{{$guid}}"
  hipId: "your-hip-id"
  cmId: "sbx"
}

docs {
  # User Initiated Linking (UIL) Testing Environment
  
  This environment file contains variables used for testing the User Initiated Linking (UIL) flow.
  
  ## Variables
  
  - `baseUrl`: The base URL of your local server (default: http://localhost:3000)
  - `abhaAddress`: The ABHA address of the patient
  - `patientId`: The patient ID in your system
  - `transactionId`: A unique transaction ID (auto-generated)
  - `requestId`: A unique request ID (auto-generated)
  - `hipId`: Your facility's HIP ID
  - `cmId`: Consent Manager ID (default: sbx)
  
  ## Usage
  
  These variables can be referenced in the API requests using the {{variableName}} syntax.
  For example, to use the baseUrl variable, you would write {{baseUrl}} in your request URL.
  
  ## Setup
  
  Before testing, make sure to:
  
  1. Update the `abhaAddress` with a valid ABHA address from your database
  2. Update the `patientId` with a valid patient ID from your database
  3. Update the `hipId` with your facility's HIP ID
}
