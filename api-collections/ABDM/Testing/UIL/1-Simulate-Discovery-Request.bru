meta {
  name: 1-Simulate-Discovery-Request
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/api/v3/hip/patient/care-context/discover
  body: json
  auth: none
}

headers {
  REQUEST-ID: {{requestId}}
  TIMESTAMP: {{$isoTimestamp}}
  X-CM-ID: {{cmId}}
  X-HIP-ID: {{hipId}}
}

body:json {
  {
    "transactionId": "{{transactionId}}",
    "patient": {
      "id": "{{abhaAddress}}",
      "name": "<PERSON> Do<PERSON>",
      "gender": "M",
      "yearOfBirth": 1990,
      "verifiedIdentifiers": [
        {
          "type": "MOBILE",
          "value": "+************"
        }
      ],
      "unverifiedIdentifiers": []
    }
  }
}

vars:pre-request {
  transactionId: "{{$guid}}"
}

docs {
  # Simulate Discovery Request
  
  This request simulates the discovery request from ABDM to your system.
  
  ## Usage
  
  1. Make sure you have a patient with the ABHA address specified in the environment variables
  2. Run this request to start the UIL flow
  
  ## Expected Response
  
  ```json
  {
    "status": "Processing"
  }
  ```
  
  ## Notes
  
  - This request initiates the UIL flow
  - Your system should find the patient with the specified ABHA address
  - Your system should then send an on-discover response to ABDM
  - Check the logs to see the details of the flow
}
