meta {
  name: 3-Simulate-Link-Confirm-Request
  type: http
  seq: 3
}

post {
  url: {{baseUrl}}/api/v3/hip/link/care-context/confirm
  body: json
  auth: none
}

headers {
  REQUEST-ID: {{requestId}}
  TIMESTAMP: {{$isoTimestamp}}
  X-CM-ID: {{cmId}}
  X-HIP-ID: {{hipId}}
}

body:json {
  {
    "confirmation": {
      "linkRefNumber": "{{linkRefNumber}}",
      "token": "{{otp}}"
    }
  }
}

vars:pre-request {
  requestId: "{{$guid}}"
  linkRefNumber: "enter-link-ref-number-from-logs"
  otp: "enter-otp-from-logs"
}

docs {
  # Simulate Link Confirm Request
  
  This request simulates the link confirm request from ABDM to your system.
  
  ## Usage
  
  1. Run this request after the link init request
  2. Update the `linkRefNumber` variable with the link reference number from the logs
  3. Update the `otp` variable with the OTP from the logs
  
  ## Expected Response
  
  ```json
  {
    "status": "Processing"
  }
  ```
  
  ## Notes
  
  - This request validates the OTP
  - Your system should validate the OTP and mark it as verified
  - Your system should then send an on-confirm response to ABDM
  - Check the logs to see the details of the flow
  - After this step, the care contexts should be linked to the ABHA address
}
