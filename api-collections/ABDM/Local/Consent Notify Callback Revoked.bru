meta {
  name: Consent Notify Callback Revoked (Local)
  type: http
  seq: 7
}

post {
  url: http://localhost:3000/api/webhook/api/v3/hiu/consent/request/notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: {{$guid}}
  x-hiu-id: IN3310001477
}

body:json {
  {
    "notification": {
      "consentRequestId": "{{consentRequestId}}",
      "status": "REVOKED",
      "reason": "Patient revoked the consent"
    }
  }
}

vars:pre-request {
  consentRequestId: "mock-consent-req-1718450000000"
}

docs {
  # Consent Notify Callback Revoked (Local Testing)
  
  This request is for local testing of the ABDM consent notify callback webhook with a REVOKED status. It simulates the notification that would be received from ABDM when a consent is revoked by the patient.
  
  ## Setup
  
  Before running this test:
  
  1. Make sure your local server is running on port 3000
  2. Create a test consent record in your database with the specified consentRequestId
  3. Update the `consentRequestId` variable with a valid ID from your database
  
  ## Request
  
  - **Method**: POST
  - **URL**: `http://localhost:3000/api/webhook/api/v3/hiu/consent/request/notify`
  - **Headers**:
    - `Content-Type`: application/json
    - `request-id`: A unique UUID for the request
    - `x-hiu-id`: The HIU ID (IN3310001477)
  
  ## Response
  
  The response should be a 202 Accepted status with a success message:
  
  ```json
  {
    "status": "Accepted",
    "message": "Consent notify callback processed successfully"
  }
  ```
  
  ## Expected Behavior
  
  When this request is processed:
  
  1. The consent status should be updated to "REVOKED" in the database
  2. An audit log entry should be created with the revocation reason
  3. The system should not attempt to create a ConsentNotify record since the consent was revoked
  4. An acknowledgment should still be sent to ABDM
}
