meta {
  name: Consent Notify Callback (Local)
  type: http
  seq: 4
}

post {
  url: http://localhost:3005/api/webhook/api/v3/hiu/consent/request/notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: {{$guid}}
  x-hiu-id: IN3310001861
}

body:json {
  {
    "notification": {
      "consentRequestId": "59d453b5-3f05-4123-bcbb-cd8635bad17e",
      "status": "GRANTED",
      "consentArtefacts": [
        {
          "id": "0090bd02-fb09-484f-bc71-ed2920c40ef7"
        },
        {
          "id": "1f97b4c6-68ce-4cb3-ba4d-0d01a71fad5c"
        },
        {
          "id": "6301e259-03b7-4fbe-b515-1a6bf7c88a1a"
        }
      ]
    }
  }
}

vars:pre-request {
  consentRequestId: "mock-consent-req-1718450000000",
  consentArtefactId: "consent-artefact-1718450000000"
}

docs {
  # Consent Notify Callback (Local Testing)
  
  This request is for local testing of the ABDM consent notify callback webhook. It simulates the notification that would be received from ABDM when a consent is GRANTED, DENIED, or REVOKED.
  
  ## Setup
  
  Before running this test:
  
  1. Make sure your local server is running on port 3000
  2. Create a test consent record in your database with the specified consentRequestId
  3. Update the `consentRequestId` variable with a valid ID from your database
  4. Update the `consentArtefactId` variable with a test artefact ID
  
  ## Request
  
  - **Method**: POST
  - **URL**: `http://localhost:3000/api/webhook/api/v3/hiu/consent/request/notify`
  - **Headers**:
    - `Content-Type`: application/json
    - `request-id`: A unique UUID for the request
    - `x-hiu-id`: The HIU ID (IN3310001477)
  
  ## Response
  
  The response should be a 202 Accepted status with a success message:
  
  ```json
  {
    "status": "Accepted",
    "message": "Consent notify callback processed successfully"
  }
  ```
  
  ## Different Status Scenarios
  
  ### GRANTED (Default)
  
  ```json
  {
    "notification": {
      "consentRequestId": "CONSENT_REQUEST_ID",
      "status": "GRANTED",
      "consentArtefacts": [
        {
          "id": "CONSENT_ARTEFACT_ID"
        }
      ]
    }
  }
  ```
  
  ### DENIED
  
  ```json
  {
    "notification": {
      "consentRequestId": "CONSENT_REQUEST_ID",
      "status": "DENIED",
      "reason": "Patient denied the consent request"
    }
  }
  ```
  
  ### REVOKED
  
  ```json
  {
    "notification": {
      "consentRequestId": "CONSENT_REQUEST_ID",
      "status": "REVOKED",
      "reason": "Patient revoked the consent"
    }
  }
  ```
}
