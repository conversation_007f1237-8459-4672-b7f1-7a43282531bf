# Local Testing for ABDM Webhooks

This directory contains Bruno API collections for local testing of ABDM webhook endpoints. These collections simulate the callbacks that would be received from ABDM in a production environment.

## Available Tests

### Consent Webhooks

1. **Consent Init Callback (Local)** - Simulates the callback received after a consent request is initiated

   - Endpoint: `/api/webhook/api/v3/hiu/consent/request/on-init`
   - File: `Consent Init Callback.bru`

2. **Consent Init Callback Error (Local)** - Simulates an error scenario for the consent init callback

   - Endpoint: `/api/webhook/api/v3/hiu/consent/request/on-init`
   - File: `Consent Init Callback Error.bru`

3. **Consent Notify Callback (Local)** - Simulates the notification when a consent is GRANTED

   - Endpoint: `/api/webhook/api/v3/hiu/consent/request/notify`
   - File: `Consent Notify Callback.bru`

4. **Consent Notify Callback Denied (Local)** - Simulates the notification when a consent is DENIED

   - Endpoint: `/api/webhook/api/v3/hiu/consent/request/notify`
   - File: `Consent Notify Callback Denied.bru`

5. **Consent Notify Callback Revoked (Local)** - Simulates the notification when a consent is REVOKED
   - Endpoint: `/api/webhook/api/v3/hiu/consent/request/notify`
   - File: `Consent Notify Callback Revoked.bru`

### Other Webhooks

6. **ConsentNotify** - Simulates the consent notification webhook (older version)

   - Endpoint: `/api/abdm/webhook/v0.5/consent-notify`
   - File: `ConsentNotify.bru`

7. **HIRequest** - Simulates the health information request webhook
   - Endpoint: `/api/abdm/webhook/v0.5/health-information/request`
   - File: `HIRequest.bru`

## Setup for Testing

Before running these tests:

1. Make sure your local server is running on port 3000
2. Create test records in your database with the specified IDs
3. Update the variables in each test with valid IDs from your database

## Variables

Each test file contains variables that you need to update before running the test:

- `consentRequestId` - The ID of the consent request in your database
- `consentArtefactId` - A test artefact ID for GRANTED consent notifications

## Expected Responses

All webhook endpoints should return a success response with an appropriate status code (usually 202 Accepted). The response should include a status and message indicating that the webhook was processed successfully.

## Verifying Results

After running a test, you can verify the results by:

1. Checking the database to see if the records were updated correctly
2. Looking at the server logs for any errors or information messages
3. Checking the audit logs in the `ConsentAuditLog` table
