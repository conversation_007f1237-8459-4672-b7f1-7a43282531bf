meta {
  name: Consent Init Callback (Local)
  type: http
  seq: 3
}

post {
  url: http://localhost:3005/api/webhook/api/v3/hiu/consent/request/on-init
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: {{$guid}}
  x-hiu-id: IN3310001861
}

body:json {
  {
    "consentRequest": {
      "id": "59d453b5-3f05-4123-bcbb-cd8635bad17e"
    },
    "error": null,
    "response": {
      "requestId": "581a5618-d8b3-4ffa-a3d4-02731851a185"
    }
  }
}

vars:pre-request {
  consentRequestId: "mock-consent-req-1718450000000"
}

docs {
  # Consent Init Callback (Local Testing)
  
  This request is for local testing of the ABDM consent init callback webhook. It simulates the callback that would be received from ABDM after a consent request is initiated.
  
  ## Setup
  
  Before running this test:
  
  1. Make sure your local server is running on port 3000
  2. Create a test consent record in your database with the specified consentRequestId
  3. Update the `consentRequestId` variable with a valid ID from your database
  
  ## Request
  
  - **Method**: POST
  - **URL**: `http://localhost:3000/api/webhook/api/v3/hiu/consent/request/on-init`
  - **Headers**:
    - `Content-Type`: application/json
    - `request-id`: A unique UUID for the request
    - `x-hiu-id`: The HIU ID (IN3310001477)
  
  ## Response
  
  The response should be a 202 Accepted status with a success message:
  
  ```json
  {
    "status": "Accepted",
    "message": "Consent init callback processed successfully"
  }
  ```
  
  ## Error Scenario
  
  To test error handling, you can use the following payload:
  
  ```json
  {
    "consentRequest": {
      "id": "CONSENT_REQUEST_ID"
    },
    "response": {
      "requestId": "REQUEST_ID"
    },
    "error": {
      "code": "INVALID_REQUEST",
      "message": "Invalid consent request"
    }
  }
  ```
}
