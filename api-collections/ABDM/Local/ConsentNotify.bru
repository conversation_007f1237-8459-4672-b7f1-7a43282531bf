meta {
  name: ConsentNotify
  type: http
  seq: 1
}

post {
  url: http://localhost:3005/api/abdm/webhook/v0.5/consent-notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "id": "cmacjey9w0001onkerb0kzi24",
    "requestId": "58b5e8af-32b5-459b-911a-701e3060d934",
    "status": "GRANTED",
    "consentId": "9317f65a-3af0-4216-af6a-eaadb7ebce45",
    "schemaVersion": "v3",
    "patientId": "kumarashutosh1508@sbx",
    "careContexts": [
      {
        "patientReference": "cmacajllm000euy890aq8vnu4",
        "careContextReference": "cmacj9th8000594e1q5qmqo5o"
      }
    ],
    "purpose": {
      "code": "PATRQT",
      "text": "Self Requested",
      "refUri": "www.abdm.gov.in"
    },
    "hipId": "IN2910001861",
    "consentManagerId": "sbx",
    "hiTypes": [
      "Prescription",
      "DiagnosticReport",
      "OPConsultation",
      "DischargeSummary",
      "ImmunizationRecord",
      "HealthDocumentRecord",
      "WellnessRecord",
      "Invoice"
    ],
    "permission": {
      "dateRange": {
        "to": "2025-05-06T13:20:37.356Z",
        "from": "1925-05-06T13:20:37.356Z"
      },
      "frequency": {
        "unit": "DAY",
        "value": 0,
        "repeats": 2
      },
      "accessMode": "VIEW",
      "dataEraseAt": "2125-05-06T13:20:37.356Z"
    },
    "signature": "Ps8P17OOe+0VuZ89gN04xjENrs11ygYtSJUKRcdJhwJ13l2SEEK5rFZTr3uRxCeKZj01QWA1iOonKKaRf+fmUxDnhrd2h1C3BLkC4nUu2qm7dnPkXvMAKw237lGEegmZETFbzTBPdJrFW8OfSb6u6Oina5lnZjg/1IdsuvF1wboVKETn1mhKj7K1h3cuRKueLlmvRI8+b301jcR2SodKb0Zvkv642vAAB9rlBa9fdjrra0cB8HImSUP0pz6a6K4MB7CuCkBqLvC/1WdnMScHlhn9/IaABEAn9DitNFBy33PDf1jGJc9w8szwV9/bkShtTJGJKRJrhIPbAPE3KY7grg==",
    "grantAcknowledgement": true,
    "createdAt": "2025-05-06T13:20:37.893Z",
    "updatedAt": "2025-05-06T13:20:38.233Z",
    "organizationId": "cm9jbtj4600001008sjpwobrt"
  }
}

docs {
  # Consent Notify API
  
  This API simulates the ABDM Consent Notify webhook for local testing.
  
  ## Request
  
  The request body contains a consent notification with the following key fields:
  - `id`: Unique identifier for the notification
  - `requestId`: Request identifier
  - `status`: Consent status (GRANTED, DENIED, REVOKED)
  - `consentId`: Unique identifier for the consent
  - `patientId`: ABHA address of the patient
  - `careContexts`: Array of care contexts included in the consent
  - `hiTypes`: Types of health information included in the consent
  - `permission`: Details about access permissions
  
  ## Response
  
  The API should create a record in the consentNotify table and return a success response.
}
