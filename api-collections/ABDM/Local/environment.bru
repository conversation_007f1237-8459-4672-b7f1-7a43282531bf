vars {
  baseUrl: http://localhost:3000
  consentId: 9317f65a-3af0-4216-af6a-eaadb7ebce45
  patientId: kuma<PERSON><PERSON>sh1508@sbx
  patientReference: cmacajllm000euy890aq8vnu4
  careContextReference: cmacj9th8000594e1q5qmqo5o
  hipId: IN2910001861
}

docs {
  # Local Testing Environment
  
  This environment file contains variables used for local testing of ABDM webhook handlers.
  
  ## Variables
  
  - `baseUrl`: The base URL of your local server (default: http://localhost:3000)
  - `consentId`: A sample consent ID used in both ConsentNotify and HIRequest
  - `patientId`: The ABHA address of the patient
  - `patientReference`: The patient reference ID in your system
  - `careContextReference`: The care context reference ID in your system
  - `hipId`: Your facility's HIP ID
  
  ## Usage
  
  These variables can be referenced in the API requests using the {{variableName}} syntax.
  For example, to use the baseUrl variable, you would write {{baseUrl}} in your request URL.
}
