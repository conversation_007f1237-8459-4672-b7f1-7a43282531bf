meta {
  name: HIRequest
  type: http
  seq: 2
}

post {
  url: http://localhost:3005//api/abdm/webhook/v0.5/health-information/request
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "id": "cmacjez730002onke039797be",
    "requestId": "40546749-e55e-4ac0-9c71-384986ef0bbb",
    "transactionId": "82eaab79-d383-4d52-8c42-75ede814cf53",
    "consentId": "9317f65a-3af0-4216-af6a-eaadb7ebce45",
    "dateRange": {
      "to": "2025-05-06T13:20:37.356Z",
      "from": "1925-05-06T13:20:37.356Z"
    },
    "dataPushUrl": "https://abhasbx.abdm.gov.in/abha/api/v3/patient-hiu/app/v0.5/health-information/transfer",
    "keyMaterial": {
      "curve": "curve25519",
      "nonce": "m6BwOmQJqkz55XMjV40L1Dxfeo9rfQmd/CZ3I61ct8k=",
      "cryptoAlg": "ECDH",
      "dhPublicKey": {
        "expiry": "2025-05-16T13:20:37.734Z",
        "keyValue": "BC+SuiaGm+5ZfCkmRFlerOn387VSp7fdPOpo6HzzpN7PJteUiKtuZIm+GedIiYYN38JAyCXqXlej0JqyDg+8iW0=",
        "parameters": "Ephemeral public key"
      }
    },
    "acknowledgementSent": true,
    "createdAt": "2025-05-06T13:20:39.087Z",
    "updatedAt": "2025-05-06T13:20:39.208Z"
  }
}

docs {
  # Health Information Request API
  
  This API simulates the ABDM Health Information Request webhook for local testing.
  
  ## Request
  
  The request body contains a health information request with the following key fields:
  - `id`: Unique identifier for the request
  - `requestId`: Request identifier
  - `transactionId`: Transaction identifier
  - `consentId`: Consent identifier (should match a consent in the consentNotify table)
  - `dateRange`: Date range for the requested health information
  - `dataPushUrl`: URL to push the health information to
  - `keyMaterial`: Cryptographic key material for encrypting the health information
  
  ## Response
  
  The API should create a record in the hiRequest table and return a success response.
}
