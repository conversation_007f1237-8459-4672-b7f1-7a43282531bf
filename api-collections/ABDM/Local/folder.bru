meta {
  name: Local Testing
}

auth {
  mode: none
}

docs {
  # Local Testing APIs

  This folder contains API requests for local testing of ABDM webhook handlers.

  These APIs simulate the ABDM webhooks and can be used to test the application's handling of consent notifications and health information requests without needing to set up the actual ABDM integration.

  ## Available APIs

  1. **ConsentNotify** - Simulates the consent notification webhook
     - Endpoint: `/api/abdm/webhook/v0.5/consent-notify`
     - Creates a record in the ConsentNotify table

  2. **HIRequest** - Simulates the health information request webhook
     - Endpoint: `/api/abdm/webhook/v0.5/health-information/request`
     - Creates a record in the HiRequest table

  ## Usage

  1. Make sure your local server is running on port 3000
  2. Send the requests to test the webhook handlers
  3. Verify that the appropriate records are created in the database

  ## Response Format

  Both endpoints will return a response in the same format as the actual ABDM webhooks:

  - For ConsentNotify:
    ```json
    {
      "acknowledgement": {
        "status": "SUCCESS"
      },
      "response": {
        "requestId": "the-request-id-from-payload"
      }
    }
    ```

  - For HIRequest:
    ```json
    {
      "requestId": "the-request-id-from-payload",
      "timestamp": "2025-05-06T13:20:39.208Z",
      "acknowledgement": {
        "status": "OK"
      },
      "resp": {
        "requestId": "the-request-id-from-payload"
      }
    }
    ```
}
