meta {
  name: Consent Init Callback Error (Local)
  type: http
  seq: 5
}

post {
  url: http://localhost:3000/api/webhook/api/v3/hiu/consent/request/on-init
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: {{$guid}}
  x-hiu-id: IN3310001477
}

body:json {
  {
    "consentRequest": {
      "id": "{{consentRequestId}}"
    },
    "response": {
      "requestId": "{{$guid}}"
    },
    "error": {
      "code": "INVALID_REQUEST",
      "message": "Invalid consent request"
    }
  }
}

vars:pre-request {
  consentRequestId: "mock-consent-req-1718450000000"
}

docs {
  # Consent Init Callback Error (Local Testing)
  
  This request is for local testing of the ABDM consent init callback webhook with an error scenario. It simulates the callback that would be received from ABDM when there's an error with the consent request.
  
  ## Setup
  
  Before running this test:
  
  1. Make sure your local server is running on port 3000
  2. Create a test consent record in your database with the specified consentRequestId
  3. Update the `consentRequestId` variable with a valid ID from your database
  
  ## Request
  
  - **Method**: POST
  - **URL**: `http://localhost:3000/api/webhook/api/v3/hiu/consent/request/on-init`
  - **Headers**:
    - `Content-Type`: application/json
    - `request-id`: A unique UUID for the request
    - `x-hiu-id`: The HIU ID (IN3310001477)
  
  ## Response
  
  The response should be a 202 Accepted status with a success message:
  
  ```json
  {
    "status": "Accepted",
    "message": "Consent init callback processed successfully"
  }
  ```
  
  ## Expected Behavior
  
  When this request is processed:
  
  1. The consent status should be updated to "ERROR" in the database
  2. An audit log entry should be created with the error details
  3. The error should be logged in the server logs
}
