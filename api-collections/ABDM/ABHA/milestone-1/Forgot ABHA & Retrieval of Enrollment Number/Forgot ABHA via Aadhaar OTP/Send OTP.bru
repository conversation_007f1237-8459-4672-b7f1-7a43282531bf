meta {
  name: Send OTP
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/request/otp
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "aadhaar-verify"
      ],
      "loginHint": "aadhaar",
      "loginId": "{{encrypted Aadhaar}}",
      "otpSystem": "aadhaar"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  test("Status code is 200", function () {
      expect(res.getStatus()).to.equal(200);
  });
  console.log(verifyResponse);
  if(verifyResponse!=null && verifyResponse.txnId!=null){
  bru.setGlobalEnvVar("txnId", verifyResponse.txnId);
  }
}
