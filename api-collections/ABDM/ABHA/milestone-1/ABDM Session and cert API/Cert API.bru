meta {
  name: Cert API
  type: http
  seq: 2
}

get {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/public/certificate
  body: none
  auth: none
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.asMk9N7MLO0lGIU8xXz3FKz_rS9sXiDa4ZjD0mEBMjmTH9cFv5GpD3zg1PEoEYqfFRKJpxCnXTKW0tkm3ClPKq8lDAN164px3N4TzgacnEzeBFWusWVgU4TIwQVNEjrx9cdohWADM2NSYTwFlmcsVhd2HNz0LnnEre-Hi1UrSmhTChkMkHJJXVLj1HgXL0CIC5bJ2Wkp2dK2sEjKJPkGsZmLM2yq7jM3F8Qq6wF8QiA9XbMQoWLEkxSGb5KZRVbaDQuKIs9Yb2UvUJ5enRaAURqcpVQnZtv57L9vNNASJqsyLPnFdnjPjBw_fTOuL9vouWJrtJ6mZA3poRvtXQFOsg
}

docs {
  Generated from cURL: curl --location 'https://abhasbx.abdm.gov.in/abha/api/v3/profile/public/certificate' \
  --header 'REQUEST-ID: 037b7896-05ee-4215-bd32-de6a80ab640f' \
  --header 'TIMESTAMP: 2024-11-20T06:55:56.832Z' \
  --header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJBbFJiNVdDbThUbTlFSl9JZk85ejA2ajlvQ3Y1MXBLS0ZrbkdiX1RCdkswIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EmKx0vjoQU6EDfWEwPLO1l8movpqdrI3PUGe8U8OmKinmgsbtQW2kv-vZKts1ZeNno1KMDamteVDZclqkvg9pYzwnh5ZEoCrhmjbzY_cVze-nhuLIXpdPJitLujl4mooIJYYEELu9mUHwWnSdWSDwcbC0mdYB06P_lDGLitxj6vhCS36hQBP3ajUoTOV7049hf9QOeuOlC82X_lP2LHHubTlyHrnb_Q4HkEDlrIwvpo6BhMNvt34hOogF_7xia_zAlODlemZD6mA6eKnLjJ56LghkM-28wxkbABwVS9FHaPKZiJsr0q_yw3p_cO9dmCshQyhcSCmruCPGIdK_NJF0A'
   
}
