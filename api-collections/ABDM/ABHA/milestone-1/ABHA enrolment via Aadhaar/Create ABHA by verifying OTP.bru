meta {
  name: Create ABHA by verifying OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/enrol/byAadhaar
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$randomUUID}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "authData": {
          "authMethods": [
              "otp"
          ],
          "otp": {
             
              "txnId": "{{txnId}}",
              "otpValue": "{{encrypted otp}}",
              "mobile": "{{mobile number for ABHA communication}}"
          }
      },
      "consent": {
          "code": "abha-enrollment",
          "version": "1.4"
      }
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  if(verifyResponse!=null && verifyResponse.token!=null)
  bru.setVar("jwtToken", verifyResponse.token);
  if(verifyResponse!=null && verifyResponse.refreshToken!=null)
  bru.setVar("R-jwtToken", verifyResponse.refreshToken);
}
