meta {
  name: Send OTP
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/request/otp
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "txnId": "",
      "scope": [
          "abha-enrol"
      ],
      "loginHint": "aadhaar",
      "loginId": "{{encrypted aadhaar}}",
      "otpSystem": "aadhaar"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  console.log(verifyResponse);
  if(verifyResponse!=null && verifyResponse.txnId!=null)
  {
  bru.setGlobalEnvVar("txnId", verifyResponse.txnId);
  }
  
}
