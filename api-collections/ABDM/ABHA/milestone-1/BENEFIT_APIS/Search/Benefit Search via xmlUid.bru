meta {
  name: Benefit Search via xmlUid
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/benefit/search
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
  BENEFIT_NAME: {{Benefit Name}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:json {
  {
      "scope": [
          "search"
      ],
      "loginHint": "xmlUid",
      "loginId": "{{encrypted xmlUid}}"
  }
}
