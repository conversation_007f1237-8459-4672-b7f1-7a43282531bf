meta {
  name: Create ABHA by verifying OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/enrol/byAadhaar
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
  Benefit-Name: {{Benefit Name}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:json {
  {
      "authData": {
          "authMethods": [
              "otp"
          ],
          "otp": {
              "timeStamp": "{{$timestamp}}",
              "txnId": "{{txnId}}",
              "otpValue": "{{encrypted otp}}",
              "mobile": "{{mobile number}}"
          }
      },
      "consent": {
          "code": "abha-enrollment",
          "version": "1.4"
      }
  }
}
