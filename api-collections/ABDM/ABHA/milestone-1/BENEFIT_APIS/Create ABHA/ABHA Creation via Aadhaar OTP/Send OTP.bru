meta {
  name: Send OTP
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/request/otp
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:json {
  {
      "txnId": "",
      "scope": [
          "abha-enrol"
      ],
      "loginHint": "aadhaar",
      "loginId": "{{encrypted aadhaar number}}",
      "otpSystem": "aadhaar"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  test("Status code is 200", function () {
      expect(res.getStatus()).to.equal(200);
  });
  console.log(verifyResponse);
  if(verifyResponse!=null && verifyResponse.txnId!=null){
  bru.setGlobalEnvVar("txnId", verifyResponse.txnId);
  }
}
