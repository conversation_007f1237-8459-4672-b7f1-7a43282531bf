meta {
  name: DemoAuth API
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/enrol/byAadhaar
  body: text
  auth: bearer
}

headers {
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$randomUUID}}
  Benefit-Name: {{Benefit Name}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:text {
  {
      "authData": {
          "authMethods": [
              "demo_auth"
          ],
          "demo_auth": {
              "aadhaarNumber": "{{encrypted aadhaar number}}",
              "districtCode": "{{District code}}",
              "stateCode": "{{State code}}",
              "dateOfBirth": "{{DOB}}",
              "gender": "{{Gender}}",
              "name": "{{Full name}}",
              "mobile": "{{Mobile number}}",
              "profilePhoto": "{{Base64 Plain String}}",
              "address": "New Delhi",
              "pincode" : "110092"
  
          }
      },
      "consent": {
          "code": "abha-enrollment",
          "version": "1.4"
      }
  }
}
