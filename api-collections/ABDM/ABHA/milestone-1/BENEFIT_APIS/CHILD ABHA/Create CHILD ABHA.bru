meta {
  name: Create CHILD ABHA
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/enrol/byAadhaar
  body: text
  auth: bearer
}

headers {
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$guid}}
  Benefit-Name: {{Benefit Name}}
  X-token: Bearer {{X-token}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:text {
  {
      "authData": {
          "authMethods": [
              "child"
          ],
          "child": {
              "dayOfBirth": "{{day Of Birth}}",
              "monthOfBirth": "{{month Of Birth}}",
              "yearOfBirth": "{{year Of Birth}}",
              "gender": "{{Gender}}",
              "password": "",
              "name": "{{Name}}",
             "profilePhoto": "",
              "parentConsent":"true"
          }
      },
      "consent": {
          "code": "abha-enrollment",
          "version": "1.4"
      }
  }
}
