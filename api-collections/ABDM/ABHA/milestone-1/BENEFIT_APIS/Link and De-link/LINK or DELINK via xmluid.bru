meta {
  name: LINK or DELINK via xmluid
  type: http
  seq: 3
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/benefit/linkAndDelink
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
  BENEFIT_NAME: {{Benefit Name}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:json {
  {
      "scope": [
          "de-link"
      ],
      "loginHint": "xmlUid",
      "loginId": "{{encrypted xmlUid}}"
  }
}
