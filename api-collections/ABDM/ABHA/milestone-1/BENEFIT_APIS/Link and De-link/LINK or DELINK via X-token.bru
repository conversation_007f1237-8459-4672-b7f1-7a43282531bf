meta {
  name: LINK or DELINK via X-token
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/benefit/linkAndDelink
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
  BENEFIT_NAME: {{Benefit Name}}
  X-token: Bearer {{X-token}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:json {
  {
      "scope": [
          "link"
      ]
  }
}
