meta {
  name: LINK or DELINK via ABHA number
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/benefit/linkAndDelink
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
  BENEFIT_NAME: {{Benefit Name}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:json {
  {
      "scope": [
          "link"
      ],
      "loginHint": "abha-number",
      "loginId": "{{encrypted abha-number}}"
  }
}
