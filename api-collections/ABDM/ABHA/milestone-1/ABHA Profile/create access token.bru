meta {
  name: create access token
  type: http
  seq: 1
}

post {
  url: https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions
  body: json
  auth: none
}

body:json {
  {
      "clientId": "{{clientId}}",
      "clientSecret": "{{clientSecret}}",
      "grantType": "client_credentials"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  if(verifyResponse!=null && verifyResponse.accessToken!=null)
  bru.setGlobalEnvVar("accesstoken", verifyResponse.accessToken);
}
