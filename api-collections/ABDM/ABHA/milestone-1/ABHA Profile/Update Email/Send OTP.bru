meta {
  name: Send OTP
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/account/request/otp
  body: json
  auth: bearer
}

headers {
  X-token: Bearer {{jwtToken}}
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-profile",
          "email-verify"
      ],
      "loginHint": "email",
      "loginId": "{{encrypted email}}",
      "otpSystem": "abdm"
  }
}

tests {
  var jsonData = res.getBody();
  bru.setEnvVar("txnId", jsonData.txnId);
}
