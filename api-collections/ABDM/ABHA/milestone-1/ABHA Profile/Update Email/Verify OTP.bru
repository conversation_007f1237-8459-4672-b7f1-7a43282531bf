meta {
  name: Verify OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/account/verify
  body: json
  auth: bearer
}

headers {
  X-token: Bearer {{jwtToken}}
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-profile",
          "email-verify"
      ],
      "authData": {
          "authMethods": [
              "otp"
          ],
          "otp": {
              "txnId": "{{txnId}}",
              "otpValue": "{{encrypted otp}}"
          }
      }
  }
}
