meta {
  name: PASSWORD_UPDATE-OLD PASSWORD
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/account/verify
  body: json
  auth: bearer
}

headers {
  X-token: Bearer {{jwtToken}}
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$guid}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-profile",
          "change-password"
      ],
      "authData": {
          "authMethods": [
              "password"
          ],
          "password": {
              "newPassword": "{{encrypted new password}}",
              "confirmPassword": "{{encrypted confirm password}}",
              "oldPassword": "{{encrypted old password}}"
          }
      }
  }
}
