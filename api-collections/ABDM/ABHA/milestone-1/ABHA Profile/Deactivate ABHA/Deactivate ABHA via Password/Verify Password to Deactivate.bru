meta {
  name: Verify Password to Deactivate
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/account/verify
  body: json
  auth: bearer
}

headers {
  X-token: Bearer {{jwtToken}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-profile",
          "de-activate"
      ],
      "authData": {
          "authMethods": [
              "password"
          ],
          "password": {
              "password": "{{encrypted password}}"
          }
      },
      "reasons": [
          "aaaaa",
          "bbbb"
      ]
  }
}
