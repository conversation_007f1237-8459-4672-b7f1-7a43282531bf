meta {
  name: Verify Password to Delete
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/account/verify
  body: json
  auth: bearer
}

headers {
  X-token: Bearer {{jwtToken}}
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$guid}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-profile",
          "delete"
      ],
      "authData": {
          "authMethods": [
              "password"
          ],
          "password": {
              "password": "{{encrypted password}}"
          }
      },
      "reasons": [
          "aaaaa",
          "bbbb"
      ]
  }
}
