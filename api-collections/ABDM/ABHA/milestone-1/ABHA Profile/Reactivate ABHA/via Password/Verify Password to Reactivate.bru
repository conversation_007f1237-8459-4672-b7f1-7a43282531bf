meta {
  name: Verify Password to Reactivate
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/verify
  body: json
  auth: bearer
}

headers {
  REQUEST_ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accesstoken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "password-verify",
          "re-activate"
      ],
      "authData": {
          "authMethods": [
              "password"
          ],
          "password": {
              "ABHANumber": "{{abha-number}}",
              "password": "{{encrypted password}}"
          }
      }
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  bru.setVar("jwtToken", verifyResponse.token);
}
