meta {
  name: Registration of bridge services (HIP/HIU)
  type: http
  seq: 3
}

post {
  url: https://facilitysbx.abdm.gov.in/v1/bridges/MutipleHRPAddUpdateServices
  body: json
  auth: bearer
}

headers {
  accept: application/json
  Content-Type: application/json
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
    "facilityId": "IN0710******",
    "facilityName": "{{facility name}}",
    "HRP": [
      {
        "bridgeId": "{{client-id}}",
        "hipName": "{{hip name}}",
        "type": "HIP",
        "active": true
      }
    ]
  }
}

docs {
  Generated from cURL: curl --location 'https://facility.abdm.gov.in/v1/bridges/MutipleHRPAddUpdateServices' \
  
  --header 'accept: application/json' \
  
  --header 'Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI0dUx2Wmp2T3V2c1E1Ry1sVkdydzVQYThWVG04V2dKV3YwZVJXM1pYVmpnIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aIzMeu30AJOsvnVju9bjNFmWuetQzRPgpNB4_c7O5HBQ088_q4dXIncKjcB2m9ywJMHZS0V97n1wXs06r7kfpvr28E_Z-D3-cawlcKmIMPo9FndPt4Z3ZURdhtTHXKJlpd6RUk2VTla1cNvf7lC-1-pyPEl8kkfzorwK274g9NlgPIRyq8HFHZkvaglA7UBiX694NDyKm_kDv_LDN2UaT3OSvhYileUaAXd00iNtq7I7Xp3aIxNWznbvLswDybrHdXHAp78xf7B7jksUnPUPxzCACMJnvbt_tgrgKUtZtopx3I6RkFOnhPC_ahiw0BgMr8R5fpNfW88wLoSt8AulWQ' \
  
  --header 'Content-Type: application/json' \
  
  --header 'Cookie: JSESSIONID=C313889D45450E126B42A48C13146049.node1; JSESSIONID=A3D66210CFC437CB18C8A8B31A33C3BA.node1; TS01ddee46=01445fed0479602d8ece0ed901ee3cbe9aa1aa9bede50645897445764a41a8a7fe6d0435838c523646277488c7ced488ee55f0709f60dcd5f322147bbd9438fa294070c328' \
  
  --data '{
  
      "facilityId": "IN0110005827",
  
      "facilityName": "TEST HIP",
  
      "HRP": [
  
          {
  
              "bridgeId": "TEST_PROD",
  
              "hipName": "TEST HIP",
  
              "type": "HIP",
  
              "active": true
  
          },
  
                  {
  
              "bridgeId": "TEST_PROD",
  
              "hipName": "TEST HIP",
  
              "type": "HIU",
  
              "active": true
  
          }
  
      ]
  
  }'
}
