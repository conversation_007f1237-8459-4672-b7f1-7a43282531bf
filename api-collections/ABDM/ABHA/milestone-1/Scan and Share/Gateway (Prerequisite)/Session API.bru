meta {
  name: Session API
  type: http
  seq: 1
}

post {
  url: https://dev.abdm.gov.in/api/hiecm/gateway/v3/sessions
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  X-CM-ID: sbx
}

body:json {
  {
      "clientId": "<<Enter Client Id>>",
      "clientSecret": "<<Enter Client Secret>>",
      "grantType": "client_credentials"
  }
}

tests {
  var jsonData = res.getBody();
  bru.setEnvVar("accessToken", jsonData.accessToken);
}
