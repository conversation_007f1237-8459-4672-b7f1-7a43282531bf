meta {
  name: 01 profile-share (Callback to HIP)
  type: http
  seq: 1
}

post {
  url: {{callback_url}}/api/hiecm/patient-share/v3/share
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  X-CM-ID: {{cm-id}}
  X-HIU-ID: {{hiu-id}}
  X-AUTH-TOKEN: {{auth-token}}
}

auth:bearer {
  token: {{BEARER_AUTH}}
}

body:json {
  {
      "intent": "PROFILE_SHARE",
      "metaData": {
          "hipId": "{{hip-id}}",
          "context": "1",
          "hprId": "<EMAIL>",
          "latitude": "-38.670",
          "longitude": "58.498"
      },
      "profile": {
          "patient": {
              "abhaNumber": {{abha-number}},
              "abhaAddress": "{{abha-address}}",
              "name": "{{name}}",
              "gender": "M",
              "dayOfBirth": "1*",
              "monthOfBirth": "0*",
              "yearOfBirth": "19**",
              "address": {
                  "line": "{{Address}}",
                  "district": null,
                  "state": null,
                  "pinCode": null
              },
              "phoneNumber": "{{mobile}}"
          }
      }
  }
}
