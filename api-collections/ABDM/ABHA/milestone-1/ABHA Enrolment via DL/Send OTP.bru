meta {
  name: Send OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/enrollment/request/otp
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-enrol",
          "mobile-verify",
          "dl-flow"
          
      ],
      "loginHint":"mobile",
      "loginId": "{{encrypted mobile number}}",
      "otpSystem": "abdm"
  }
}

tests {
  var jsonData = res.getBody();
  bru.setEnvVar("txnId", jsonData.txnId);
}
