meta {
  name: Session API
  type: http
  seq: 1
}

post {
  url: https://dev.abdm.gov.in/gateway/v0.5/sessions
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{SK_AUTH}}
}

body:json {
  {
      "clientId": "{{clientId}}",
      "clientSecret": "{{clientSecret}}",
      "grantType": "client_credentials"
  }
}

tests {
  var jsonData = res.getBody();
  bru.setEnvVar("accessToken", jsonData.accessToken);
}
