meta {
  name: 1.3 Search
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/account/abha/search
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  BENEFIT_NAME: healthid api
}

auth:bearer {
  token: {{token}}
}

body:json {
  {
      "scope": ["search-abha"],
      "mobile": "{{rsaMobileEncryptionOutput}}"
  }
  
}

tests {
  var jsonData = JSON.parse(res.getBody()?.toString());
  bru.setVar("searchTxnId",jsonData[0].txnId);
}
