meta {
  name: 1.5 Request OTP
  type: http
  seq: 2
}

post {
  url: {{sbx}}/abha/api/v3/profile/login/request/otp
  body: json
  auth: bearer
}

headers {
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$guid}}
  Content-Type: application/json
  BENEFIT_NAME: healthid
  ~Authorization: Bearer {{accesstoken}}
}

auth:bearer {
  token: {{token}}
}

body:json {
  {
      "scope": ["abha-login","search-abha","aadhaar-bio-verify"], //aadhaar-face-verify,adhaar-iris-verify
      "loginHint": "index",
      "loginId": "{{rsaIndexEncryptionOutput}}",
      "otpSystem": "aadhaar",
      "txnId":"{{searchTxnId}}"
  }
}

tests {
  var jsonData = JSON.parse(res.getBody()?.toString());
  bru.setVar("otpTxnId",jsonData.txnId);
  var template = `
  <style type="text/css">
      .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}
      .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}
      .tftable tr {background-color:#ffffff;}
      .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}
      .tftable tr:hover {background-color:#e0ffff;}
  </style>
  
  <table class="tftable" border="1">
      <tr>
          <th>Transaction ID</th>
          <th>Message</th>
      </tr>
      <tr>
          <td>{{response.txnId}}</td>
          <td>{{response.message}}</td>
      </tr>
  </table>
  `;
  
  function constructVisualizerPayload() {
      return {response: res.getBody()}
  }
  
  // pm.visualizer.set(template, constructVisualizerPayload());
}
