meta {
  name: 1.7 Verify OTP
  type: http
  seq: 3
}

post {
  url: {{BaseURI}}/api/abha/v3/profile/login/verify
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  Content-Type: application/json
}

auth:bearer {
  token: {{token}}
}

body:json {
  {
      // For Fingerprint Authentication
      "scope": ["abha-login","aadhaar-bio-verify"], 
      "authData": {"authMethods": ["bio"], 
          "bio": {
              "txnId": "{{otpTxnId}}",
              "fingerPrintAuthPid": "{{fingerPrintAuthPid}}"
          }
      }
  }
  
  // {
  //     // For Face Authentication
  //     "scope": ["abha-login","aadhaar-face-verify"], 
  //     "authData": {"authMethods": ["face"], 
  //         "face": {
  //             "txnId": "{{otpTxnId}}",
  //             "faceAuthPid": "{{faceAuthPid}}"
  //         }
  //     }
  // }
  
  
  //{
  //     // For Iris Authentication
  //     "scope": ["abha-login","aadhaar-iris-verify"], 
  //     "authData": {"authMethods": ["iris"], 
  //         "iris": {
  //             "txnId": "{{otpTxnId}}",
  //             "irisAuthPid": "{{irisAuthPid}}"
  //         }
  //     }
  // }
}

tests {
  var jsonData = JSON.parse(res.getBody()?.toString());
  bru.setVar("userTxnId",jsonData.txnId);
  bru.setVar("X-Token",jsonData.token);
}
