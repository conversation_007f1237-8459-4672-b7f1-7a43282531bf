meta {
  name: Verify OTP
  type: http
  seq: 3
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/verify
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  Content-Type: application/json
}

auth:bearer {
  token: {{token}}
}

body:json {
  // Verification of OTP via Mobile 
  
  {
      "scope": ["abha-login","mobile-verify"],
      "authData": {"authMethods": ["otp"],
          "otp": {
              "txnId": "{{otpTxnId}}",
              "otpValue": "{{rsaOtpEncryptionOutput}}"
          }
      }
  }
  
  // Verification of OTP via Aadhaar
  
  // {
  //     "scope": ["abha-login","aadhaar-verify"],
  //     "authData": {"authMethods": ["otp"],
  //         "otp": {
  //             "txnId": "{{otpTxnId}}",
  //             "otpValue": "{{rsaOtpEncryptionOutput}}"
  //         }
  //     }
  // }
  
  
  // Verification via Biometric - Fingerprint 
  
  // {
  //   "scope": [
  //     "abha-login",
  //     "aadhaar-bio-verify"
  //   ],
  //   "authData": {
  //     "authMethods": [
  //       "bio"
  //     ],
  //     "bio": {
  //       "txnId": "{{txnId}}",
  //       "fingerPrintAuthPid": "{{PID}}"
  //     }
  //   }
  // }
  
  
  // Verification via Biometric - Face
  
  // {
  //   "scope": [
  //     "abha-login",
  //     "aadhaar-face-verify"
  //   ],
  //   "authData": {
  //     "authMethods": [
  //       "face"
  //     ],
  //     "face": {
  //       "txnId": "{{txnId}}",
  //       "faceAuthPid": "{{PID}}"
  //     }
  //   }
  // }
  
  // Verification via Biometric - IRIS
  
  // {
  //   "scope": [
  //     "abha-login",
  //     "aadhaar-iris-verify"
  //   ],
  //   "authData": {
  //     "authMethods": [
  //       "iris"
  //     ],
  //     "iris": {
  //       "txnId": "{{txnId}}",
  //       "irisAuthPid": "{{PID}}"
  //     }
  //   }
  // }
}

tests {
  var jsonData = JSON.parse(res.getBody()?.toString());
  bru.setVar("userTxnId",jsonData.txnId);
  bru.setVar("X-Token",jsonData.token);
}
