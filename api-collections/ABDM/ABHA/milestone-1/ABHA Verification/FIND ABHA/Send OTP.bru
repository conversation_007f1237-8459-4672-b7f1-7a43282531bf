meta {
  name: Send OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/request/otp
  body: json
  auth: bearer
}

headers {
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$guid}}
  Content-Type: application/json
  ~Authorization: Bearer {{accesstoken}}
}

auth:bearer {
  token: {{token}}
}

body:json {
  // Find ABHA via Mobile Number - Send OTP
  
  {
      "scope": ["abha-login","search-abha","mobile-verify"], 
      "loginHint": "index",
      "loginId": "{{rsaIndexEncryptionOutput}}",
      "otpSystem": "abdm",
      "txnId":"{{searchTxnId}}"
  }
  
  // Find ABHA via Aadhaar Linked Mobile Number - Send OTP
  
  // {
  //   "scope": [
  //     "abha-login",
  //     "search-abha",
  //     "aadhaar-verify"
  //   ],
  //   "loginHint": "index",
  //   "loginId": "{{rsaIndexEncryptionOutput}}",
  //   "otpSystem": "aadhaar",
  //   "txnId": "{{searchTxnId}}"
  // }
  
  
  // Find ABHA via Biometric (Fingerprint) - Send Fingerprint Authentication Request 
  
  // {
  //   "scope": [
  //     "abha-login",
  //     "search-abha",
  //     "aadhaar-bio-verify"
  //   ],
  //   "loginHint": "index",
  //   "loginId": "{{rsaIndexEncryptionOutput}}",
  //   "otpSystem": "aadhaar",
  //   "txnId": "{{searchTxnId}}"
  // }
  
  // Find ABHA via Biometric ( Face ) - Send Face Authentication Request 
  
  // {
  //   "scope": [
  //     "abha-login",
  //     "search-abha",
  //     "aadhaar-face-verify"
  //   ],
  //   "loginHint": "index",
  //   "loginId": "{{rsaIndexEncryptionOutput}}",
  //   "otpSystem": "aadhaar",
  //   "txnId": "{{searchTxnId}}"
  // }
  
  // Find ABHA via Biometric (IRIS) - Send IRIS Authentication Request 
  
  // {
  //   "scope": [
  //     "abha-login",
  //     "search-abha",
  //     "aadhaar-iris-verify"
  //   ],
  //   "loginHint": "index",
  //   "loginId": "{{rsaIndexEncryptionOutput}}",
  //   "otpSystem": "aadhaar",
  //   "txnId": "{{searchTxnId}}"
  // }
  
}

tests {
  var jsonData = JSON.parse(res.getBody()?.toString());
  bru.setVar("otpTxnId",jsonData.txnId);
}
