meta {
  name: Verify OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/verify
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "mobile-verify"
      ],
      "authData": {
          "authMethods": [
              "otp"
          ],
          "otp": {
              "txnId": "{{txnId}}",
              "otpValue": "{{encrypted OTP}}"
          }
      }
  }
}

tests {
  var jsonData = res.getBody();
  bru.setEnvVar("jwtToken", jsonData.token);
}
