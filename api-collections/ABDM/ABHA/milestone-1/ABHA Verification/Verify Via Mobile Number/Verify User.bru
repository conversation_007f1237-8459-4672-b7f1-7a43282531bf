meta {
  name: Verify User
  type: http
  seq: 3
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/verify/user
  body: json
  auth: bearer
}

headers {
  T-token: Bearer {{jwtToken}}
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "ABHANumber":"{{abha-number}}",
      "txnId":"{{txnId}}"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  bru.setVar("jwtToken", verifyResponse.token);
  
  var verifyResponse1 = JSON.parse(responseBody);
  bru.setVar("R-jwtToken", verifyResponse1.refreshToken);
  
  
}
