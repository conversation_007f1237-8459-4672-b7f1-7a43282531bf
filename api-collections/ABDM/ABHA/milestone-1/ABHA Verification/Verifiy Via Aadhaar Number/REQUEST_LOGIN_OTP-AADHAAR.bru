meta {
  name: REQUEST_LOGIN_OTP-AADHAAR
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/request/otp
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{gatewayToken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "aadhaar-verify"
      ],
      "loginHint": "aadhaar",
      "loginId": "{{AadhaarencryptedOutput}}",
      "otpSystem": "aadhaar"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  
  if(verifyResponse!=null && verifyResponse.txnId!=null)
      bru.setVar("txnId", verifyResponse.txnId);
  
}
