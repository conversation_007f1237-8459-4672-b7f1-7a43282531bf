meta {
  name: VERIFY-AADHAAR
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/verify
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{gatewayToken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "aadhaar-verify"
      ],
      "authData": {
          "authMethods": [
              "otp"
          ],
          "otp": {
              "txnId": "{{txnId}}",
              "otpValue": "{{OTP_encryption}}"
          }
      }
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  
  if(verifyResponse!=null && verifyResponse.token!=null)
      bru.setVar("jwtToken", verifyResponse.token);
  
  if(verifyResponse!=null && verifyResponse.refreshToken!=null)
      bru.setVar("R-jwtToken", verifyResponse.refreshToken);
}
