meta {
  name: Request otp
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/login/request/otp
  body: json
  auth: none
}

headers {
  REQUEST-ID: e5a91069-bcbb-4c6c-a8a0-69bdee2d4fb5
  TIMESTAMP: 2024-10-01T11:32:03.899Z
  Content-Type: application/json
}

body:json {
  {
      "scope": [
          "abha-login",
          "aadhaar-iris-verify"
      ],
      "loginHint": "abha-number",
      "loginId": "{{encrypted abha-number}}",
      "otpSystem": "aadhaar"
  }
}

docs {
  Generated from cURL: curl --location 'http://abha2dev.abdm.gov.internal/api/v3/profile/login/request/otp' \
  --header 'REQUEST-ID: e5a91069-bcbb-4c6c-a8a0-69bdee2d4fb5' \
  --header 'TIMESTAMP: 2024-10-01T11:32:03.899Z' \
  --header 'Content-Type: application/json' \
  --data '{
      "scope": [
          "abha-login",
          "aadhaar-iris-verify"
      ],
      "loginHint": "abha-number",
      "loginId": "g3pv3TNvyYPrVO8l6DuYHAOccUA9NsW+tcdAVrK4QxkJerdVfANwYo1Q8Um5W6MVBKBQ3Uz6u17ucm7mVegmo695VvA2H5P9E3qRoZtPidDU6oe3MKUpzQH91/d+zFLvZvrGExihWzf36HlpBsgyjwDUQtP/EQl2hFeWf8BCQsVGTgJdxfGhF+Be2cqxP7R1+DDGR0r9Je2ITwodWuvSe6P7P/VIKu6085DMpzoLdWZhR3nuDvpVSfxjnu0OzxJUzJotNj83RCgW9lOZ51vPYUiB8olin0Ro4x3YZfUWaDulsUER4ln2H+sHySBr+G0JF5daWKJ3FmsIAncVHmyvihSnRYovgTFvFTzdL7eSm10MY/4IZ5Ca83kFEkHKVpDDRDeHk0bIpFjDPgXTyzWDsV3CxaFkknDG20teJMJaqxx1UNpVuYq3XJ5LVBfnr1T/oOSh+zqKQ8/FOxUK5NbhH8Q/jLhNJUthFpMiAwXeMkGx/QfCE1Oe9V8zFxC0jaBON5OIy/AQ0ij9a6/o/iRHBtAJ+OMYtHSHEw/BC1P6qL4MnhzMzRtLWB6s8gY8WsHf7E1ORBTD1+eiaf+vzmZvoC0hiWMkpAea27qt5Y0K8xntF3h3uRfAlFO/HbpYPAoGP7Cfhwwq8eW7qwrXv8pqx7tzvwVkhilRC5zLYKyixDs=",
      "otpSystem": "aadhaar"
  }'
   
}
