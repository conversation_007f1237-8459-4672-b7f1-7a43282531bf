meta {
  name: REQUEST_LOGIN_FingerPrint-AADHAAR
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/request/otp
  body: json
  auth: none
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
  ~Authorization: Bearer {{gatewayToken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "aadhaar-bio-verify"
      ],
      "loginHint": "abha-number",
      "loginId": "{{encrypted abha-number}}",
      "otpSystem": "aadhaar"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  
  if(verifyResponse!=null && verifyResponse.txnId!=null)
      bru.setVar("txnId", verifyResponse.txnId);
  
}
