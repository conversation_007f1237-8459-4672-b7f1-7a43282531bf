meta {
  name: REQUEST_LOGIN_FACE-AADHAAR
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/request/otp
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$randomUUID}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{AccessToken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "aadhaar-face-verify"
      ],
      "loginHint": "abha-number",
      "loginId": "{{encrypted abha-number}}",
      "otpSystem": "aadhaar"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  
  if(verifyResponse!=null && verifyResponse.txnId!=null)
      bru.setVar("txnId", verifyResponse.txnId);
  
}
