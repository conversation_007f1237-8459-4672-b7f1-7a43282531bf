meta {
  name: Verify Password
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/verify
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "password-verify"
      ],
      "authData": {
          "authMethods": [
              "password"
          ],
          "password": {
              "ABHANumber": "{{abha-number}}",
              "password": "{{encrypted password}}"
          }
      }
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  bru.setVar("jwtToken", verifyResponse.token);
}
