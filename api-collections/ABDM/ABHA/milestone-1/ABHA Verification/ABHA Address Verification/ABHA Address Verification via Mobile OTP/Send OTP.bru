meta {
  name: Send OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/phr/web/login/abha/request/otp
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{sessionToken}}
}

body:json {
  {
      "scope": [
          "abha-address-login",
          "mobile-verify"
      ],
      "loginHint": "abha-address",
      "loginId": "VTwOF8Fz8KKr0/EsUIgVoF9bSyI2INUer7a3nqEMqWFSimSK67oJ6jRZxzo4bR5fbqLUyHAphK9/seSkOWUPj7f2yij2fmkOJX3PjMb8dooMfvPN4pBuA627fs8IVaNB1u8fthvjBmdItWocdi2ULXCf7MMBzQzC0FDCO2gk8XF9tohvk1q944svJe/qe4O6tLS494e5Jgm+u+DJ1BN2hhownZbAavLX8gmNR3AcENH1/hvLR6iomM8dDHa8MtwHMvLiHBWGDCW7pfL9xKANpDMaRcXG/IU4BkUEstOGCHNaMj974XVOhsZOfnVMdwMVvCIJgk7WKsAoJDzqNq4L1rQfVyKc8sQBULKQcTuPKCjqUGUzOSHzVAaAeFj4PcOa3Nn1AvjNXAjlMqP36iHarJRywccWVNy50p8YSBDhq7iwvZjPJQua7d9eSM+26TxBnz0rdmEh2mpEKxKUi5RryJFZDovqrZn6lJXg+KUzxyrfhtWD6VGiC9vC/jCZFPbZi83eoBQH71RCO+iVbfj8jgWRPfJjCxkZAar1N2KdaidlUdEaEG6e7DPIWCGGrucYpTcvTbL12seqm1WswLoplZJm1UBytvCdIk50Ft6MMBOJdY+8kyIF9xZ6b4tudey/UtOFvpiL08y0a+7N5bjHgkHJK3QJ08YXiFwjbH8OmXQ=",
      "otpSystem": "abdm"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  if(verifyResponse!=null && verifyResponse.txnId!=null){
  bru.setGlobalEnvVar("transactionId", verifyResponse.txnId);
  }
  
}
