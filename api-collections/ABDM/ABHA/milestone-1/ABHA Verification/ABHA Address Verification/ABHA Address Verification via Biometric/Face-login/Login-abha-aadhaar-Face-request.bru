meta {
  name: Login-abha-aadhaar-Face-request
  type: http
  seq: 1
}

post {
  url: {{web_url}}login/abha/request/otp
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  Content-Type: application/json
}

auth:bearer {
  token: {{SK_AUTH}}
}

body:json {
  {
      "scope": [
          "abha-login",
          "aadhaar-face-verify"
      ],
      "loginHint": "abha-address",
      "loginId": "{{encryptedData}}",
      "otpSystem": "aadhaar"
  }
}

tests {
  var jsonData = res.getBody();
  bru.setEnvVar("transactionId", jsonData.txnId);
}

docs {
  Generated from cURL: curl --location '
  http://localhost:8003/api/v3/abhaAddress/login/abha/request/otp'
  \
  --header 'REQUEST-ID: 3104e26a-f1b2-4923-bb38-9102a415962b' \
  --header 'TIMESTAMP: 2024-11-08T07:49:32.071Z' \
  --header 'Content-Type: application/json' \
  --data '{
      "scope": [
          "abha-login",
          "aadhaar-face-verify"
      ],
      "loginHint": "abha-address",
      "loginId": "T3KxS9F+OOmEfnBhYcO116CUEVbA7qgGAf9H10CYqf5i69iy5wndJqagmi7f1nXQVqcdydzXXGDkuUVIRs3AkXr61UAIgkALd4uz2qHgbn1pNIyxdDpKdv5rqlzPGweHsdafS/XA2YCorI+FVTNNd0ZwaWq8NOvszBIr0O0fiVTpxf4FGf/YVgwNroGUusYzsmMyOYvPZ3DrTWrP4uq3uUosMjo2mEEbApwTnVEpGMio/lX49k4RpbcKtTbELVaHrJfJIMuJT7Gdveim9tQIvNZ4+d5LxVL61v/UZj9E+Oy4WTSsG+Jm/1v5KRQpRsSpUobCs4GZ5eodsKqbD6ti8hvERuEb7lcEjzFW8dLn3SOKdcy4iOcKh2d9w7q6mL98hnDy/GszgeeRQQvJ0BCxD+9l7GV/oo8zQ02UBEIpBozGYfTteV/+3ZLKKak7SxlwCD2xJC0SynhQF0yRqRiFLCrs/yBJ4OSy2dnIusMYLC1SjFbnui5OqQUjaiZP+7/IaSl81ADghI7dUkgYCuYmC+IhF75HOHlK9NogVpwu5gKz3rFpGfd1dEkd0ROTXrvtYGlPT/B157LgVOG1ngvz79JQzu6Wz6r9Bv2M3gNPHqofURLWtXYMlgjTPgD+qMuZ4AhcigA0J1HyUrcbJZw+eOVsDirfIGURen+7SbgOXOg=",
      "otpSystem": "aadhaar"
  }'
}
