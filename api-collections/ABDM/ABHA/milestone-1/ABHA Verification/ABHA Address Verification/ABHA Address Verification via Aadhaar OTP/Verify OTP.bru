meta {
  name: Verify OTP
  type: http
  seq: 3
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/phr/web/login/abha/verify
  body: json
  auth: none
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

body:json {
  {
      "scope": [
          "abha-address-login",
          "aadhaar-verify"
      ],
      "authData": {
          "authMethods": [
              "otp"
          ],
          "otp": {
              "txnId": "41e59beb-6ee7-421e-a844-3652b2482038",
              "otpValue": "SOs5Mh5NLkPIBlqqbR1QngR3MlJ7C4iY2r+fAIPKac2N4D+2QsH0yWJqYvpSI0wZZxAXzkd5joE/QtniQc537YG3kr0e0QGWRUWronHIxxtV27dORTL/o7gmTmm3br8QcXtb0rzYuMpR45KNHwhaljqztVDKmJ3jMh7vrLQZJViKeCPfFX9zzTrajkknAs5KrlKjBDLnxWZx7BgXDz1EMAVyHPxhCvMR9xSQkBBXPSAP9mpizOc6ySevqC350Kqiy0iz4hmFC6pn8Ix7vKCJazyIfNWhQ6fFJMO2jRcywSpRZvG8zBybmfm311HxPrFpkqafXnm1vFy+rzX5yb94xwTsTEusKp737nJNScWQyFGBq40o2BAFaI+sJ26HZYk9K2i/L8JRQ8VS4vFBmPe5+as60uBEar3nGadwpIEr1Zdw8If7BCQNtt+2Sm5OUi82nFbpeoj5SW4F6+pxIaXWheeqkZw/mSg5hWrcLrPozEiKxUmY30nomEA/mOYN9Pk8ejPWXPs6lw1q6rAK5jpqnRBAvdivIcRTQdBSDLMfl3PtMYuKfDC7K8EXyX6vNO+YAOgQdZ/ogeZT3H3RaEb9CVbrs9S9cUAxGtU1nPWqB8yHCUqUSKEzMrcx+20d/4WMNeXuzhqju6TPT+1IdKHiYxMaCkV+bzzDoIj8WMvsFt8="
          }
      }
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  if(verifyResponse!=null && verifyResponse.token!=null)
  bru.setVar("T-token", verifyResponse.token);
  if(verifyResponse!=null && verifyResponse.refreshToken!=null)
  bru.setVar("R-jwtToken", verifyResponse.refreshToken);
}
