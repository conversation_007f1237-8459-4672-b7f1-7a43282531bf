meta {
  name: Send OTP
  type: http
  seq: 2
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/phr/web/login/abha/request/otp
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

auth:bearer {
  token: {{sessionToken}}
}

body:json {
  {
      "scope": [
          "abha-address-login",
          "aadhaar-verify"
      ],
      "loginHint": "abha-address",
      "loginId": "JBEt4oO6oqdpTbDeDmikVTk2FFLNIFsJu7CpYXBEdy8cm9IkHaIMFUKJC5C7utPpA8LAiQ3AQKmoL2u7eJFrYyJCiN98oaUcUwbYOgjY7tBFWD04ZxHNOK+WNvzI4PoSmuWTfc/0XagtruzEkkOguq8K+eWM4xq1Y81e9qSP1VjyirIxb0kD8tlNM5t4sQIsDU1egLEvfUohAPLGK6UDS0lBQl0gVZUiu+MMmVkgqXzRZiaKzt3oQJrpF8uoHCjFgiVT+4M6T0BoOlzwYP2agymT4DnVkMpXJ8KYpThYdA6BPiG4IP45L/Dk1BVDBjXPFZ7rtcEW5iYSGhal1iVQCa4zVAXRwX0ZFeZnaJRYIn0jx2J6yZoimbyORvdh1GY+HIbENXHYHv0PNmZjA+HF0VUEY8MEh5sRkDMf2oPZtGLUhLlrrYXgZleWjKerMmMU91UbcJCdxFFdraE4ihCIK5R02NUHF+qeYiTMzspA9myX7J3BsvfPHAEiBRebI+F3BueKYJTLjLB4TNo8y+vs0aQ/7VzJiB8Wzr9qSR1H5vd6zjX0KFW/5R/YbSO+4USsfjrTf8ZGo04Iq5nkLihjOwYhMifOzwwrslBHW27Ttg3nkB3aWXpMi2myjSJbt2EFSBj4vVuybPqcowo0jXW8IAM/rJMfQaERvctyFNftNpc=",
      "otpSystem": "aadhaar"
  }
}

tests {
  var verifyResponse = JSON.parse(responseBody);
  if(verifyResponse!=null && verifyResponse.txnId!=null){
  bru.setGlobalEnvVar("transactionId", verifyResponse.txnId);
  }
  
}
