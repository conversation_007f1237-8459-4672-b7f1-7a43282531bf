meta {
  name: Send OTP
  type: http
  seq: 1
}

post {
  url: https://abhasbx.abdm.gov.in/abha/api/v3/profile/login/request/otp
  body: json
  auth: bearer
}

headers {
  TIMESTAMP: {{$isoTimestamp}}
  REQUEST-ID: {{$guid}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "scope": [
            "abha-login",
          "mobile-verify"
      ],
      "loginHint": "abha-number",
      "loginId": "{{encrypted abha-number}}",
      "otpSystem": "abdm"
  }
}

tests {
  var jsonData = res.getBody();
  bru.setEnvVar("txnId", jsonData.txnId);
}
