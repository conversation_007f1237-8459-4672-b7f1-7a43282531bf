meta {
  name: Find Bridge Service By Service ID
  type: http
  seq: 4
}

get {
  url: https://dev.abdm.gov.in/api/hiecm/gateway/v3/bridge-service/serviceId/{{service ID}}
  body: none
  auth: bearer
}

params:query {
  ~stateCode: 10
  ~districtCode: 20
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  X-CM-ID: {{X-CM-ID}}
}

auth:bearer {
  token: {{accessToken}}
}

script:pre-request {
  const datetime = require('moment');
  bru.setGlobalEnvVar("current_timestamp",datetime().format("YYYY-MM-DD HH:mm:ss.SSS"));
}
