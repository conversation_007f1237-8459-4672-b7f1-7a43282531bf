meta {
  name: Update Bridge URL
  type: http
  seq: 2
}

patch {
  url: https://dev.abdm.gov.in/api/hiecm/gateway/v3/bridge/url
  body: json
  auth: bearer
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  X-CM-ID: {{X-CM-ID}}
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "url": "{{URL to be updated}}"
  }
}

script:pre-request {
  const datetime = require('moment');
  bru.setGlobalEnvVar("current_timestamp",datetime().format("YYYY-MM-DD HH:mm:ss.SSS"));
}
