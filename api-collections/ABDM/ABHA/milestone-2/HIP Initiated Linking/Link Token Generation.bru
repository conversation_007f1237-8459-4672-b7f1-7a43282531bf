meta {
  name: Link Token Generation
  type: http
  seq: 1
}

post {
  url: https://dev.abdm.gov.in/api/hiecm/v3/token/generate-token
  body: json
  auth: none
}

headers {
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
  X-HIP-ID: {{X-HIP-ID}}
  X-CM-ID: {{X-CM-ID}}
}

body:json {
  {
      "abhaNumber": {{ABHA Number}},
      "abhaAddress": "{{ABHA Address}}",
      "name": "{{name}}",
      "gender": "{{gender}}",
      "yearOfBirth": {{year}}
  }
}

tests {
  test("Positive Scenarios : Generate Link Token", function () {
  });
  test("Status code is 202", function () {
      expect(res.getStatus()).to.equal(202);
  });
  test("Body matches string : " + (res.getBody()?.toString()), function () {
      expect(res.getBody()?.toString()).to.equals("");
  });
  
  
}
