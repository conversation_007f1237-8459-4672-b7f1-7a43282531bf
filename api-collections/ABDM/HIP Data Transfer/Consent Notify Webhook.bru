meta {
  name: Consent Notify Webhook
  type: http
  seq: 1
}

post {
  url: {{qa}}/api/webhook/api/v3/consent/request/hip/notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: 80bff574-f0c0-485e-867e-e50b05bfa7e9
}

body:json {
  {"notification":{"status":"GRANTED","consentId":"4115ed83-4a26-4299-b6f5-389fc0654399","consentDetail":{"schemaVersion":"v3","consentId":"4115ed83-4a26-4299-b6f5-389fc0654399","createdAt":"2025-05-23T13:08:44.559Z","patient":{"id":"kumar_kumar.151515@sbx"},"careContexts":[{"patientReference":"cmac213t70002z7djo92is6nb","careContextReference":"cmac5hayv0005j3j1pwhvp4oi"},{"patientReference":"cmac213t70002z7djo92is6nb","careContextReference":"cmacafz0k0009uy894xb097q1"},{"patientReference":"cmaux333l001ymykjiv9mnd5s","careContextReference":"cmauxdb6r000cyhizu2rxt6ia"},{"patientReference":"cmaux333l001ymykjiv9mnd5s","careContextReference":"cmaw2utqh000a27fkc5uzfiyn"}],"purpose":{"text":"Care Management","code":"CAREMGT","refUri":"www.abhasbx.gov.in"},"hip":{"id":"IN2910001861","name":"ashutosh test","type":"HIP"},"consentManager":{"id":"sbx"},"hiTypes":["OPConsultation","WellnessRecord","Prescription"],"permission":{"accessMode":"VIEW","dateRange":{"from":"2025-04-23T13:08:32.362Z","to":"2025-05-23T13:08:32.362Z"},"dataEraseAt":"2026-05-30T00:00:00.000Z","frequency":{"unit":"HOUR","value":0,"repeats":0}}},"signature":"O8TKZuUTNjICXYIKiZ+SR9AijoBwyMF5+A8IkR5TiiySTqr0Lj9UTURmoOIGV8eI/SSBwJhZ4n0hnB9lpNfo8uW8hG6+h6C/28GVP+aS3XFu5313RhT6tPgPj8Ly7R5bwE0d7X6eBqLSBM+pd7mjwOElHTIiHmgXoltJCs7kbloTl0dRUOZ/msqPFvrqC/SLGaqDpGBPd3Y/i4+4WLOMOXdrJ/1hrsOdpNA0Oyu3VmyZIfDur+HzqafgxUn/ozPOA/03l1K5fj17XgT5HKdoQDcnPDsABcmzRE8xL5YIomqwToi/Z4YiD+WF0GIrrSU2kc1apqsmj7NzutlSG3eyVw==","grantAcknowledgement":false}}
}

docs {
  # Consent Notify Webhook (Full)
  
  This request simulates the ABDM consent notification webhook with the full structure of careContexts. It sends a notification to your application when a consent status changes.
  
  ## Request
  
  - **Method**: POST
  - **URL**: `/api/webhook/api/v3/consent/request/hip/notify`
  - **Headers**:
    - `Content-Type`: application/json
    - `REQUEST-ID`: A unique UUID for the request
  
  ## Response
  
  The response should include an acknowledgment of the notification:
  
  ```json
  {
    "acknowledgement": {
      "status": "SUCCESS"
    },
    "response": {
      "requestId": "the-request-id-from-headers"
    }
  }
  ```
  
  ## Testing
  
  1. Send this request to test the consent notification webhook
  2. Verify that a new ConsentNotify record is created in the database
  3. Check the notification using the GET /api/abdm/consent/notifications endpoint
}
