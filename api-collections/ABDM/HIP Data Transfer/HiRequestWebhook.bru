meta {
  name: Health Information Request Webhook
  type: http
  seq: 2
}

post {
  url: {{qa}}/api/webhook/api/v3/hip/health-information/request
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: fb3ac427-66ae-4c8a-b970-655e1b76d890
}

body:json {
  {
    "transactionId": "c1e9a87f-0b8e-4493-8369-cc54ecb812e5",
    "hiRequest": {
      "consent": {
        "id": "01c29290-c40a-4322-958b-6845f00f82b3"
      },
      "dateRange": {
        "from": "2025-04-22T05:04:35.995Z",
        "to": "2025-05-22T05:04:35.996Z"
      },
      "dataPushUrl": "https://webhook.site/257748ee-34e7-4658-ac6c-02fd35a38754",
      "keyMaterial": {
        "cryptoAlg": "ECDH",
        "curve": "curve25519",
        "dhPublicKey": {
          "expiry": "2025-05-22T07:32:08.311Z",
          "parameters": "Curve25519/32byte random key",
          "keyValue": "MIIBMTCB6gYHKoZIzj0CATCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCANCAAQoOh1XHSyimDjvrTAND8upriNwdBGcGJOBCxLpVRym3hDSGgq+mmnswv77VD8cv4p9TCOGalfuvUCZFeuMdWpe"
        },
        "nonce": "GBPpzWJjbRyzZk+TETkjh2HZF0mDyTdUS1Uk8vO7O5g="
      }
    }
  }
}

docs {
  # Health Information Request Webhook
  
  This request simulates the ABDM health information request webhook. It sends a request to your application when a HIU requests health information based on a consent.
  
  ## Request
  
  - **Method**: POST
  - **URL**: `/api/webhook/api/v3/hip/health-information/request`
  - **Headers**:
    - `Content-Type`: application/json
    - `REQUEST-ID`: A unique UUID for the request
  
  ## Response
  
  The response should include an acknowledgment of the request:
  
  ```json
  {
    "requestId": "the-request-id-from-headers",
    "timestamp": "2023-04-29T02:31:00.599Z",
    "acknowledgement": {
      "status": "OK"
    },
    "resp": {
      "requestId": "the-request-id-from-headers"
    }
  }
  ```
  
  ## Testing
  
  1. Send this request to test the health information request webhook
  2. Verify that a new HiRequest record is created in the database
  3. Check the request using the GET /api/abdm/health-information/requests endpoint
}
