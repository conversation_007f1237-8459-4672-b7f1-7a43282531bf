meta {
  name: View Consent Artifacts
  type: http
  seq: 7
}

get {
  url: {{baseUrl}}/api/abdm/consent/fetch-artifacts?consentId={{consentId}}
  body: none
  auth: none
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  consentId: "REPLACE_WITH_VALID_CONSENT_ID"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  // Verify the response structure
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.success) {
      console.log("✅ Consent artifact data retrieved successfully");
      
      const data = responseBody.data;
      console.log(`📋 Consent ID: ${data.consentId}`);
      console.log(`🆔 ABDM Consent ID: ${data.abdmConsentId || 'Not set'}`);
      console.log(`📊 Status: ${data.status}`);
      console.log(`📅 Last Updated: ${data.lastUpdated}`);
      console.log(`📦 Has Artifact Data: ${data.hasArtifactData}`);
      
      if (data.artifactData) {
        const artifacts = data.artifactData.artifacts || [];
        const detailedInfo = data.artifactData.detailedInfo || [];
        console.log(`🔢 Total Artifacts: ${artifacts.length}`);
        console.log(`✅ Detailed Info Available: ${detailedInfo.length}`);
        console.log(`📅 Last Fetched: ${data.artifactData.fetchedAt || 'Never'}`);
        
        if (data.artifactData.errors && data.artifactData.errors.length > 0) {
          console.log(`❌ Errors: ${data.artifactData.errors.length}`);
        }
      }
    } else {
      console.log("❌ Unexpected response structure:", responseBody);
    }
  } else {
    console.log("❌ Request failed with status:", res.getStatus());
  }
}

docs {
  # View Consent Artifacts

  This endpoint retrieves the current consent artifact data for a given consent without making new API calls to ABDM.

  ## Purpose

  This is a read-only endpoint that shows:
  - Current consent artifact IDs stored in the database
  - Detailed consent information that has been fetched from ABDM
  - Fetch timestamps and error information
  - Overall status of the consent artifact data

  ## Request Structure

  - **consentId**: The internal consent ID (passed as query parameter)

  ## Expected Response

  ```json
  {
    "success": true,
    "data": {
      "consentId": "internal-consent-id",
      "abdmConsentId": "abdm-consent-artifact-id",
      "status": "GRANTED",
      "lastUpdated": "2024-01-01T12:00:00.000Z",
      "hasArtifactData": true,
      "artifactData": {
        "artifacts": [
          { "id": "artifact-1" },
          { "id": "artifact-2" }
        ],
        "detailedInfo": [
          {
            "artifactId": "artifact-1",
            "consentDetail": { /* Full ABDM consent details */ },
            "fetchedAt": "2024-01-01T12:00:00.000Z"
          }
        ],
        "fetchedAt": "2024-01-01T12:00:00.000Z",
        "errors": [
          {
            "artifactId": "artifact-2",
            "error": "API timeout"
          }
        ]
      }
    }
  }
  ```

  ## Use Cases

  1. **Data Verification**: Check if consent artifacts have been fetched
  2. **Debugging**: View error information for failed fetches
  3. **Status Monitoring**: Monitor the completeness of consent data
  4. **Before Fetch**: Check current state before triggering new fetches

  ## Response Fields

  - **consentId**: Internal database consent ID
  - **abdmConsentId**: ABDM consent artifact ID (if available)
  - **status**: Current consent status (GRANTED, DENIED, etc.)
  - **lastUpdated**: When the consent record was last modified
  - **hasArtifactData**: Boolean indicating if artifact data exists
  - **artifactData**: Complete artifact information including:
    - `artifacts`: Array of consent artifact IDs
    - `detailedInfo`: Array of fetched detailed consent information
    - `fetchedAt`: Timestamp of last fetch operation
    - `errors`: Array of errors encountered during fetching

  ## Testing Steps

  1. **Update Variables**: Replace `{{consentId}}` with a valid internal consent ID
  2. **Execute Request**: Send the GET request
  3. **Review Data**: Check the artifact data structure and completeness
  4. **Identify Issues**: Look for missing detailed info or errors

  ## Troubleshooting

  - **404 Consent Not Found**: Verify the consent ID exists
  - **hasArtifactData: false**: Consent may not have been granted or processed
  - **Empty detailedInfo**: Artifacts may not have been fetched yet
  - **Errors Array**: Shows specific issues with individual artifact fetches

  ## Related Endpoints

  - `POST /api/abdm/consent/fetch-artifacts`: Trigger fetching of artifact details
  - `GET /api/abdm/consent/[id]`: Get general consent information
}
