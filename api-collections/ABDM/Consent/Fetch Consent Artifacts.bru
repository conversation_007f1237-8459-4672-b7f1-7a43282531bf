meta {
  name: Fetch Consent Artifacts
  type: http
  seq: 6
}

post {
  url: {{baseUrl}}/api/abdm/consent/fetch-artifacts
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "consentId": "{{consentId}}"
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  consentId: "REPLACE_WITH_VALID_CONSENT_ID"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  // Verify the response structure
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.success) {
      console.log("✅ Consent artifacts fetched successfully");
      console.log(`📊 Results: ${responseBody.data.successfulFetches}/${responseBody.data.totalArtifacts} artifacts fetched`);
      
      if (responseBody.data.errors > 0) {
        console.log(`⚠️ ${responseBody.data.errors} errors occurred during fetching`);
      }
    } else {
      console.log("❌ Unexpected response structure:", responseBody);
    }
  } else {
    console.log("❌ Request failed with status:", res.getStatus());
  }
}

docs {
  # Fetch Consent Artifacts

  This endpoint fetches detailed consent information for all consent artifacts associated with a consent record by making multiple calls to the ABDM `/hiecm/consent/v3/fetch` API.

  ## Purpose

  When a consent is granted, ABDM provides consent artifact IDs but not the detailed consent information. This endpoint:
  1. Retrieves all consent artifact IDs for a given consent
  2. Makes individual API calls to ABDM's `/hiecm/consent/v3/fetch` for each artifact ID
  3. Stores the detailed consent information in the database
  4. Returns a summary of the fetching process

  ## Request Structure

  - **consentId**: The internal consent ID (not the ABDM consent artifact ID)

  ## Process Flow

  1. **Find Consent**: Looks up the consent record in the database
  2. **Parse Artifacts**: Extracts consent artifact IDs from stored data
  3. **Fetch Details**: Makes individual ABDM API calls for each artifact
  4. **Store Results**: Updates the consent record with detailed information
  5. **Return Summary**: Provides success/error counts and details

  ## Expected Response

  ```json
  {
    "success": true,
    "message": "Fetched detailed consent information for 2 artifacts",
    "data": {
      "consentId": "consent-internal-id",
      "totalArtifacts": 2,
      "successfulFetches": 2,
      "errors": 0,
      "artifactDetails": [
        {
          "artifactId": "abdm-artifact-id-1",
          "consentDetail": { /* ABDM consent details */ },
          "fetchedAt": "2024-01-01T12:00:00.000Z"
        },
        {
          "artifactId": "abdm-artifact-id-2", 
          "consentDetail": { /* ABDM consent details */ },
          "fetchedAt": "2024-01-01T12:00:01.000Z"
        }
      ]
    }
  }
  ```

  ## Error Handling

  - **Individual Artifact Errors**: If some artifacts fail to fetch, the process continues with others
  - **Partial Success**: Returns success with error details for failed artifacts
  - **Complete Failure**: Returns error if no artifacts can be fetched

  ## Usage Scenarios

  1. **Manual Trigger**: Call this endpoint to fetch artifact details for existing consents
  2. **Retry Failed Fetches**: Re-run for consents where some artifacts failed to fetch initially
  3. **Data Verification**: Verify that all consent artifacts have detailed information

  ## Prerequisites

  - Valid consent ID that exists in the database
  - Consent must have consent artifacts (typically from a GRANTED consent)
  - ABDM API access and authentication must be configured

  ## Testing Steps

  1. **Update Variables**: Replace `{{consentId}}` with a valid internal consent ID
  2. **Execute Request**: Send the POST request
  3. **Check Results**: Verify the success/error counts in the response
  4. **Database Verification**: Check that the consent record's `consentArtifact` field is updated

  ## Related Endpoints

  - `GET /api/abdm/consent/fetch-artifacts?consentId=xxx`: View current artifact data
  - `POST /api/webhook/api/v3/hiu/consent/request/notify`: Auto-triggers this process on consent grant

  ## Troubleshooting

  - **404 Consent Not Found**: Verify the consent ID exists in your database
  - **No Artifacts Found**: Consent may not have been granted or artifacts not stored
  - **ABDM API Errors**: Check ABDM authentication and network connectivity
  - **Partial Failures**: Some artifacts may have invalid IDs or be expired
}
