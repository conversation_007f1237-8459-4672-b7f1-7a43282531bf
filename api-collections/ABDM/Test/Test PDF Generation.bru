meta {
  name: Test PDF Generation
  type: http
  seq: 1
}

get {
  url: {{localUrl}}/api/test-pdf-generation?consultationId={{consultationId}}&bundleType={{bundleType}}
  body: none
  auth: none
}

params:query {
  consultationId: your-consultation-id-here
  bundleType: ImmunizationRecord
}

vars:pre-request {
  localUrl: "http://localhost:3005"
  consultationId: "your-consultation-id-here"
  bundleType: "ImmunizationRecord"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.success) {
      console.log("✅ PDF Generation Test Results:");
      console.log(`   Consultation ID: ${responseBody.consultationId}`);
      console.log(`   Bundle Type: ${responseBody.bundleType}`);
      console.log(`   PDF Generated: ${responseBody.pdfGenerated}`);
      console.log(`   Is Placeholder: ${responseBody.isPlaceholder}`);
      console.log(`   PDF Size: ${responseBody.pdfSize} characters`);
      console.log(`   Message: ${responseBody.message}`);
      
      if (responseBody.isPlaceholder) {
        console.log("❌ PDF generation is still using placeholder!");
        console.log("   This means the PDF generation is failing.");
      } else {
        console.log("🎉 PDF generation is working correctly!");
      }
    }
  } else {
    console.log("❌ Test failed:", res.getBody());
  }
}

docs {
  # Test PDF Generation

  This endpoint tests the PDF generation functionality for diagnostic reports and immunization records to debug why the bundle generation is producing "PDF Generation Failed" placeholders.

  ## Usage

  1. **Set Consultation ID**: Replace `your-consultation-id-here` with an actual consultation ID from your database
  2. **Set Bundle Type**: Choose either:
     - `ImmunizationRecord` - for immunization PDF testing
     - `DiagnosticReportRecord` - for diagnostic report PDF testing
  3. **Run the test** to see if PDF generation is working

  ## Expected Results

  ### ✅ Success Response:
  ```json
  {
    "success": true,
    "consultationId": "consultation-id",
    "bundleType": "ImmunizationRecord",
    "pdfGenerated": true,
    "isPlaceholder": false,
    "pdfSize": 12345,
    "pdfPreview": "JVBERi0xLjQKMSAwIG9iago...",
    "message": "✅ PDF generated successfully"
  }
  ```

  ### ❌ Placeholder Response:
  ```json
  {
    "success": true,
    "consultationId": "consultation-id", 
    "bundleType": "ImmunizationRecord",
    "pdfGenerated": true,
    "isPlaceholder": true,
    "pdfSize": 408,
    "pdfPreview": "JVBERi0xLjQKMSAwIG9iago...",
    "message": "❌ PDF generation failed - using placeholder"
  }
  ```

  ## Troubleshooting

  If `isPlaceholder: true`, check:

  1. **Consultation exists**: Make sure the consultation ID is valid
  2. **Data availability**: Check if the consultation has the required data (immunizations/diagnostic reports)
  3. **PDF template issues**: Check if the PDF templates are working correctly
  4. **Database relations**: Verify Prisma relations are correct

  ## Next Steps

  After identifying the issue:
  1. Fix the root cause in the PDF generation logic
  2. Test again until `isPlaceholder: false`
  3. Verify that bundle generation now includes real PDFs instead of placeholders
}
