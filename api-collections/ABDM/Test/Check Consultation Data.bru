meta {
  name: Check Consultation Data
  type: http
  seq: 2
}

post {
  url: {{localUrl}}/api/test-pdf-generation
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "consultationId": "{{consultationId}}",
    "bundleType": "{{bundleType}}"
  }
}

vars:pre-request {
  localUrl: "http://localhost:3005"
  consultationId: "your-consultation-id-here"
  bundleType: "ImmunizationRecord"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.success) {
      console.log("✅ Consultation Data Check Results:");
      console.log(`   Consultation ID: ${responseBody.consultationId}`);
      console.log(`   Bundle Type: ${responseBody.bundleType}`);
      console.log(`   Patient: ${responseBody.patientName}`);
      console.log(`   Doctor: ${responseBody.doctorName}`);
      console.log(`   Organization: ${responseBody.organizationName}`);
      console.log(`   Date: ${responseBody.consultationDate}`);
      
      console.log("📊 Available Data:");
      const data = responseBody.dataAvailable;
      console.log(`   Vitals: ${data.vitals}`);
      console.log(`   Prescriptions: ${data.prescriptions}`);
      console.log(`   Clinical Notes: ${data.clinicalNotes}`);
      console.log(`   Diagnostic Reports: ${data.diagnosticReports}`);
      console.log(`   Immunizations: ${data.immunizations}`);
      
      // Check if there's enough data for PDF generation
      if (responseBody.bundleType === "ImmunizationRecord" && data.immunizations === 0) {
        console.log("⚠️ No immunization records found - PDF will be empty/placeholder");
      } else if (responseBody.bundleType === "DiagnosticReportRecord" && data.diagnosticReports === 0) {
        console.log("⚠️ No diagnostic reports found - PDF will use default data");
      } else {
        console.log("✅ Sufficient data available for PDF generation");
      }
    }
  } else if (res.getStatus() === 404) {
    console.log("❌ Consultation not found - check the consultation ID");
  } else {
    console.log("❌ Data check failed:", res.getBody());
  }
}

docs {
  # Check Consultation Data

  This endpoint checks if a consultation has the necessary data for PDF generation.

  ## Usage

  1. **Set Consultation ID**: Replace `your-consultation-id-here` with an actual consultation ID
  2. **Set Bundle Type**: Choose the bundle type you want to test
  3. **Run the check** to see what data is available

  ## Response

  ```json
  {
    "success": true,
    "consultationId": "consultation-id",
    "bundleType": "ImmunizationRecord",
    "consultationExists": true,
    "patientName": "John Doe",
    "doctorName": "Dr. Smith",
    "organizationName": "Healthcare Clinic",
    "consultationDate": "2025-01-25T10:00:00.000Z",
    "dataAvailable": {
      "vitals": 1,
      "prescriptions": 2,
      "clinicalNotes": 1,
      "diagnosticReports": 0,
      "immunizations": 3
    }
  }
  ```

  ## Data Requirements

  - **ImmunizationRecord**: Needs immunization records for meaningful PDF
  - **DiagnosticReportRecord**: Needs diagnostic reports for meaningful PDF
  - **Basic consultation data**: Always needed (patient, doctor, organization)

  ## Troubleshooting

  - **404 Error**: Consultation ID doesn't exist
  - **Zero counts**: No data available for that type
  - **Missing names**: Check database relations and includes
}
