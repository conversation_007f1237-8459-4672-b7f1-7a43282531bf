# ABDM Webhook Testing Collection

This Bruno collection is designed to help test the ABDM webhook implementations in your application.

## Webhooks Included

1. **Link Token Webhook**: For testing link token generation and storage
2. **Health Information Notification Webhook**: For testing health information notifications
3. **Patient Share Webhook**: For testing patient profile sharing functionality

## Link Token Webhook

The Link Token webhook is used by ABDM to send the link token to your application after a link token generation request. This collection includes requests to:

1. **Generate Link Token**: Initiate the link token generation process
2. **Simulate Link Token Webhook**: Simulate ABDM sending the link token to your webhook
3. **Get Patient ABHA Profile**: Verify that the link token was properly stored in the database

## How to Use This Collection

### Prerequisites

1. Make sure your application is running locally on port 3001 (or update the `baseUrl` in the environment.bru file)
2. Have a valid patient ID and ABHA address from your database

### Testing the Link Token Webhook Flow

1. Update the `environment.bru` file with your patient ID and ABHA address
2. Call the "Generate Link Token" request to initiate the link token generation process
3. Call the "Simulate Link Token Webhook" request to simulate ABDM sending the link token to your webhook
4. Call the "Get Patient ABHA Profile" request to verify that the link token was properly stored

### Expected Results

After completing the testing flow, you should see:

1. The link token generation request returns a response indicating that the process is asynchronous
2. The webhook simulation returns a success response
3. The patient ABHA profile now includes a link token

## Troubleshooting

If you encounter issues during testing:

1. Check the server logs for error messages
2. Verify that the patient ID and ABHA address are valid
3. Make sure your application is properly configured to handle webhook requests
4. Verify that the webhook endpoint is accessible from the Bruno client

## Health Information Webhooks

ABDM uses two main webhooks for health information exchange:

1. **Health Information Request Webhook**: Used by ABDM to request health information based on a consent
2. **Health Information Notification Webhook**: Used by ABDM to notify your application when health information is available after a fetch request

This collection includes requests to simulate both webhooks:

1. **Simulate Health Information Request**: Simulate ABDM sending a health information request to your webhook
2. **Simulate Health Information Notification**: Simulate ABDM sending the health information notification to your webhook

### Testing the Health Information Request Webhook Flow

1. First, get a valid consent ID from your database
2. Update the `environment.bru` file with the consent ID
3. Call the "Simulate Health Information Request" request to simulate ABDM sending a request
4. Verify that a new health record fetch entry has been created in your database

### Testing the Health Information Notification Webhook Flow

1. First, initiate a health record fetch request in your application
2. Update the `environment.bru` file with the transaction ID from the fetch request
3. Call the "Simulate Health Information Notification" request to simulate ABDM sending the notification
4. Verify that the health record fetch status has been updated in your application

### Health Information Expected Results

After completing the testing flows, you should see:

1. For the Request webhook: A new health record fetch entry created in the database and a notification sent to ABDM
2. For the Notification webhook: The health record fetch status updated to "RECEIVED" in the database
3. Audit log entries created for both operations
4. Both webhooks returning success responses with the correct acknowledgement formats

## Patient Share Webhook

The Patient Share webhook is used by ABDM to send patient profile data to your application when a patient shares their profile with your HIP (Health Information Provider). This collection includes requests to:

1. **Simulate Patient Share Webhook**: Simulate ABDM sending a patient profile to your webhook

### Testing the Patient Share Webhook Flow

1. Make sure you have a valid HIP ID configured in your application
2. Update the patient details in the Simulate Patient Share Webhook request
3. Call the "Simulate Patient Share Webhook" request to simulate ABDM sending a patient profile
4. Verify that a patient record was created or updated in your database

### Patient Share Expected Results

After completing the testing flow, you should see:

1. A success response from your webhook with the acknowledgment payload
2. A new patient record in your database (if the patient didn't exist)
3. Updated ABHA profile information (if the patient already existed)
4. A link between the patient and the branch associated with the HIP ID

## Notes

- In a production environment, ABDM would call your webhooks automatically
- This testing approach allows you to verify your implementation without waiting for ABDM
- The link token is required for care context linking, so it's important to verify that it's properly stored
- Health information notifications are essential for completing the health record fetch flow
- Patient profile sharing streamlines the patient registration process
