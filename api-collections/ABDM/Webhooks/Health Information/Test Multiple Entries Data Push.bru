meta {
  name: Test Multiple Entries Data Push
  type: http
  seq: 5
}

post {
  url: {{baseUrl}}/api/webhook/api/v3/hiu/data/push
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
}

body:json {
  {
    "transactionId": "{{transactionId}}",
    "pageNumber": 1,
    "pageCount": 1,
    "entries": [
      {
        "content": "***********************************************************************************************************************************************************",
        "media": "application/fhir+json",
        "checksum": "d2d2d2d2d2d2d2d2d2d2d2d2d2d2d2d2",
        "careContextReference": "care-context-1"
      },
      {
        "content": "********************************************************************************************************************************************************************",
        "media": "application/fhir+json", 
        "checksum": "a1a1a1a1a1a1a1a1a1a1a1a1a1a1a1a1",
        "careContextReference": "care-context-2"
      },
      {
        "content": "**************************************************************************************************************************************************************************",
        "media": "application/fhir+json",
        "checksum": "b2b2b2b2b2b2b2b2b2b2b2b2b2b2b2b2",
        "careContextReference": "care-context-3"
      }
    ]
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  transactionId: "REPLACE_WITH_VALID_TRANSACTION_ID"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  // Verify the response structure
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.status === "success") {
      console.log("✅ Multiple entries data push processed successfully");
    } else {
      console.log("❌ Unexpected response structure:", responseBody);
    }
  } else {
    console.log("❌ Request failed with status:", res.getStatus());
  }
}

docs {
  # Test Multiple Entries Data Push

  This request tests the enhanced data push webhook that can handle multiple encrypted entries in a single request.

  ## Purpose

  This simulates ABDM sending multiple encrypted FHIR bundles in a single data push webhook call, which is the new functionality added to support multiple care contexts or multiple health records in one transaction.

  ## Request Structure

  - **transactionId**: Must match a valid transaction ID from a previous health record fetch request
  - **pageNumber**: Current page number (for pagination support)
  - **pageCount**: Total number of pages
  - **entries**: Array of encrypted entries, each containing:
    - `content`: Base64 encoded encrypted FHIR bundle
    - `media`: Content type (typically "application/fhir+json")
    - `checksum`: MD5 checksum of the encrypted content
    - `careContextReference`: Reference to the care context this entry belongs to

  ## Testing Steps

  1. **Prerequisites**: 
     - Ensure you have a valid transaction ID from a health record fetch request
     - The transaction ID should be associated with a consent that has been granted
     - The HealthRecordFetch record should exist in the database

  2. **Update Variables**:
     - Replace `{{transactionId}}` with a valid transaction ID from your database
     - Optionally update the `baseUrl` if testing against a different environment

  3. **Execute Request**:
     - Send the request to trigger the multiple entries processing
     - The webhook will attempt to decrypt each entry individually
     - Each successfully decrypted entry will be stored as a separate FHIR bundle

  ## Expected Behavior

  The webhook should:
  1. Find the corresponding HealthRecordFetch record using the transaction ID
  2. Process each entry in the entries array individually
  3. Decrypt each encrypted content using the stored key material
  4. Detect the bundle type for each decrypted FHIR bundle
  5. Store each bundle as a separate record in the FhirBundle table
  6. Update the HealthRecordFetch status based on processing results:
     - "RECEIVED" if all entries processed successfully
     - "PARTIAL" if some entries failed
     - "FAILED" if all entries failed
  7. Log comprehensive information about the processing results

  ## Expected Response

  ```json
  {
    "status": "success",
    "message": "Health information data received and processed successfully",
    "details": {
      "transactionId": "your-transaction-id",
      "entriesProcessed": 3,
      "successfulDecryptions": 3,
      "failedDecryptions": 0,
      "finalStatus": "RECEIVED"
    }
  }
  ```

  ## Troubleshooting

  - **404 Error**: Transaction ID not found - verify the transaction ID exists in HealthRecordFetch or HiRequest tables
  - **Decryption Failures**: Check that the key material is properly stored and environment variables are set
  - **Partial Success**: Some entries may fail due to encryption issues while others succeed
  - **Database Errors**: Ensure all required database tables and relationships are properly set up

  ## Backward Compatibility

  This endpoint maintains full backward compatibility with single-entry payloads. If only one entry is provided, it will process exactly as before.
}
