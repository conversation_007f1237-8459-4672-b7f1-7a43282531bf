meta {
  name: Test Webhook Flow
  type: http
  seq: 3
}

script:pre-request {
  // This script demonstrates the full flow of testing the webhook
  // It's not an actual HTTP request, but a guide for manual testing

  /*
  Testing the Health Information Webhook Flows:

  A. Testing the Health Information Request Webhook:

  1. First, get a valid consent ID from your database
     - This should be a consent that has been granted
     - You can find this in the Consent table

  2. Update the environment.bru file with the consent ID
     - Set the consentId variable to the consent ID from step 1

  3. Call the "Simulate Health Information Request" request
     - This simulates ABDM sending a health information request to your webhook
     - The webhook will process the request and send a notification to ABDM

  4. Verify the health record fetch status in your application
     - Check that a new record has been created in the HealthRecordFetch table
     - Check that an audit log entry has been created

  B. Testing the Health Information Notification Webhook:

  1. First, initiate a health record fetch request in your application
     - Go to a patient's health records page
     - Click on "Fetch Health Records" for a consent that has been granted
     - Note the transaction ID from the response or from the database

  2. Update the environment.bru file with the transaction ID
     - Set the transactionId variable to the transaction ID from step 1

  3. Call the "Simulate Health Information Notification" request
     - This simulates ABDM sending the health information notification to your webhook
     - The webhook will update the health record fetch status in the database

  4. Verify the health record fetch status in your application
     - Check that the status has been updated to "RECEIVED"
     - Check that an audit log entry has been created
  */
}

docs {
  # Test Health Information Webhook Flows

  This is a guide for testing the complete health information webhook flows.

  ## A. Testing the Health Information Request Webhook

  ### Testing Steps

  1. First, get a valid consent ID from your database
     - This should be a consent that has been granted
     - You can find this in the Consent table

  2. Update the environment.bru file with the consent ID
     - Set the consentId variable to the consent ID from step 1

  3. Call the "Simulate Health Information Request" request
     - This simulates ABDM sending a health information request to your webhook
     - The webhook will process the request and send a notification to ABDM

  4. Verify the health record fetch status in your application
     - Check that a new record has been created in the HealthRecordFetch table
     - Check that an audit log entry has been created

  ### Expected Results

  After completing the testing flow, you should see:

  1. A new health record fetch entry created in the database
  2. An audit log entry created for the request
  3. The webhook returning a success response with the correct acknowledgement format
  4. A notification sent to ABDM (visible in the logs)

  ## B. Testing the Health Information Notification Webhook

  ### Testing Steps

  1. First, initiate a health record fetch request in your application
     - Go to a patient's health records page
     - Click on "Fetch Health Records" for a consent that has been granted
     - Note the transaction ID from the response or from the database

  2. Update the environment.bru file with the transaction ID
     - Set the transactionId variable to the transaction ID from step 1

  3. Call the "Simulate Health Information Notification" request
     - This simulates ABDM sending the health information notification to your webhook
     - The webhook will update the health record fetch status in the database

  4. Verify the health record fetch status in your application
     - Check that the status has been updated to "RECEIVED"
     - Check that an audit log entry has been created

  ### Expected Results

  After completing the testing flow, you should see:

  1. The health record fetch status updated to "RECEIVED" in the database
  2. An audit log entry created for the notification
  3. The webhook returning a success response with the correct acknowledgement format
}
