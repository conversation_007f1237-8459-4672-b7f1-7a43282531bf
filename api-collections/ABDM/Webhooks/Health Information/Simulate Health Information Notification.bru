meta {
  name: Simulate Health Information Notification
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/api/webhook/api/hiecm/data-flow/v3/health-information/notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: {{$guid}}
}

body:json {
  {
    "notification": {
      "status": "GRANTED",
      "consentId": "be26832f-b4c3-4c4b-8412-3616fe777bc6",
      "consentDetail": {
        "schemaVersion": "v3",
        "consentId": "be26832f-b4c3-4c4b-8412-3616fe777bc6",
        "createdAt": "2025-04-21T12:23:54.829Z",
        "patient": {
          "id": "91721026835034@sbx"
        },
        "careContexts": [
          {
            "patientReference": "cm9o4g2ty000jhb9wnbcpphri",
            "careContextReference": "cm9qwovik0001fixctmz98ax3"
          }
        ],
        "purpose": {
          "text": "Self Requested",
          "code": "PATRQT",
          "refUri": "www.abdm.gov.in"
        },
        "hip": {
          "id": "IN3310001477"
        },
        "consentManager": {
          "id": "sbx"
        },
        "hiTypes": [
          "Prescription",
          "DiagnosticReport",
          "OPConsultation",
          "DischargeSummary",
          "ImmunizationRecord",
          "HealthDocumentRecord",
          "WellnessRecord",
          "Invoice"
        ],
        "permission": {
          "accessMode": "VIEW",
          "dateRange": {
            "from": "1925-04-21T12:23:54.396Z",
            "to": "2025-04-21T12:23:54.396Z"
          },
          "dataEraseAt": "2125-04-21T12:23:54.396Z",
          "frequency": {
            "unit": "DAY",
            "value": 0,
            "repeats": 2
          }
        }
      },
      "signature": "uwy2vaRHDmyb3pHInX7cN6GwADJ7HTYzSx3Th2PytuDQLGFtd/INcb/mKFLqNgtzCKOrdtGSlcbPCIjJCE2hlAYoy/5Dij5Gd9N9G+HE+m1r4uobefEJujARAtVzj0W7w9kpK3TVgnYVDrNSgXniq/oxZuYmXTdd2zQavDKyKuDBVVNF8KOo7tW+lmDmhHRo0fOU2J6d5TQ30KWnBAlhJhvNc7X4zeYcWH46CTw0zEr/nbRgxiCs/cpWqYmm79keBt1JEy1gtchTE1PQTG4vDPTXCuBs91/4L7icvO1gkjSyz8TBEeng4cJVaYJNJRL/3M9KaMsWcrorC5ezS0vCaA==",
      "grantAcknowledgement": false
    }
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  transactionId: "REPLACE_WITH_VALID_TRANSACTION_ID"
}

docs {
  # Health Information Notification Webhook Simulator
  
  This request simulates the ABDM webhook callback for health information notifications.
  
  ## Usage
  
  1. Replace `{{transactionId}}` with a valid transaction ID from a health record fetch request
  2. The `requestId` is automatically generated as a UUID
  
  ## Expected Response
  
  ```json
  {
    "acknowledgement": {
      "status": "ok",
      "consentId": "consent-id-value"
    },
    "response": {
      "requestId": "request-id-value"
    }
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM when health information is available after a fetch request
  - The health record fetch status is updated to "RECEIVED" in the database
  - An audit log entry is created for the notification
}
