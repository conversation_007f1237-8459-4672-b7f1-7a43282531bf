meta {
  name: Test Complete M3 Flow
  type: http
  seq: 10
}

post {
  url: {{localUrl}}/api/webhook/api/v3/hiu/data/push
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

body:json {
  {
    "transactionId": "59f04acd-0058-40e2-bc4b-a178f3158a1f",
    "entries": [
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-1",
        "media": "application/fhir+json",
        "careContextReference": "wellnessrecord-4a00d504-bc4d-4c85-968d-1d7e64b260be"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-2", 
        "media": "application/fhir+json",
        "careContextReference": "immunizationrecord-a42235f9-0395-463b-a477-490609ebf6f3"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-3",
        "media": "application/fhir+json", 
        "careContextReference": "opconsultation-8f87919a-838d-4fb7-924d-************"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-4",
        "media": "application/fhir+json",
        "careContextReference": "diagnosticreport-19fd9e42-2c6f-48e7-a241-d66da256cc64"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-5",
        "media": "application/fhir+json",
        "careContextReference": "healthrecord-b5f9e904-027d-40b4-904e-cc8614ea7912"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-6",
        "media": "application/fhir+json",
        "careContextReference": "immunizationrecord-5e95103c-40a4-47f3-98b0-828bb7a19e71"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-7",
        "media": "application/fhir+json",
        "careContextReference": "prescription-167d2a9f-4fa9-4fb0-8625-0b042bd0b888"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-8",
        "media": "application/fhir+json",
        "careContextReference": "wellnessrecord-829e732e-20bd-45a9-ab12-bb3b54481e8a"
      },
      {
        "content": "eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkEyNTZHQ00ifQ.encrypted-content-9",
        "media": "application/fhir+json",
        "careContextReference": "prescription-609cb33f-88fc-4b54-88d1-abdf847abc21"
      }
    ]
  }
}

vars:pre-request {
  localUrl: "http://localhost:3000"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  // Verify the response structure
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.success) {
      console.log("✅ Data push processed successfully");
      
      if (responseBody.data && responseBody.data.processingResults) {
        const results = responseBody.data.processingResults;
        console.log(`📊 Processing Results:`);
        console.log(`   Total Entries: ${results.totalEntries}`);
        console.log(`   Processed: ${results.processedEntries}`);
        console.log(`   Successful: ${results.successfulDecryptions}`);
        console.log(`   Failed: ${results.failedDecryptions}`);
        console.log(`   Final Status: ${results.finalStatus}`);
        
        if (results.successfulDecryptions > 0) {
          console.log("🎉 Some entries were successfully processed!");
        }
        
        if (results.failedDecryptions > 0) {
          console.log(`⚠️ ${results.failedDecryptions} entries failed processing`);
        }
      }
    } else {
      console.log("❌ Unexpected response structure:", responseBody);
    }
  } else if (res.getStatus() === 404) {
    const responseBody = res.getBody();
    console.log("❌ Transaction ID not found in mapping tables");
    
    if (responseBody.availableInHealthInformationRequest) {
      console.log("Available in HealthInformationRequest:", responseBody.availableInHealthInformationRequest);
    }
    if (responseBody.availableInHealthRecordFetch) {
      console.log("Available in HealthRecordFetch:", responseBody.availableInHealthRecordFetch);
    }
    if (responseBody.availableInHiRequest) {
      console.log("Available in HiRequest:", responseBody.availableInHiRequest);
    }
  } else {
    console.log("❌ Request failed with status:", res.getStatus());
  }
}

docs {
  # Test Complete M3 Flow

  This endpoint tests the complete M3 flow with the new mapping system for handling multiple encrypted entries from external HIPs.

  ## Flow Overview

  ### Step 1: Health Record Fetch (Manual)
  1. User clicks "Pull Health Records" button
  2. System creates `HealthInformationRequest` with `requestId` → `consentId` mapping
  3. Makes API call to ABDM with `REQUEST-ID` header

  ### Step 2: On-Request Callback (Automatic)
  1. ABDM calls `/api/webhook/api/v3/hiu/health-information/on-request`
  2. Payload contains `response.requestId` and `hiRequest.transactionId`
  3. System updates `HealthInformationRequest` with `transactionId`

  ### Step 3: Data Push (This Test)
  1. External HIP sends encrypted data to `/api/webhook/api/v3/hiu/data/push`
  2. Payload contains only `transactionId` and `entries` array
  3. System uses mapping chain: `transactionId` → `HealthInformationRequest` → `consentId`
  4. Each entry is decrypted and stored as separate `FhirBundle` with correct `consentId`

  ## Test Payload Structure

  ```json
  {
    "transactionId": "59f04acd-0058-40e2-bc4b-a178f3158a1f",
    "entries": [
      {
        "content": "encrypted-fhir-data",
        "media": "application/fhir+json", 
        "careContextReference": "context-reference"
      }
      // ... more entries
    ]
  }
  ```

  ## Expected Processing

  ### For Each Entry:
  1. **Validation**: Check required fields (content, media, careContextReference)
  2. **Decryption**: Decrypt the content using stored key material
  3. **Parsing**: Parse decrypted data as FHIR Bundle
  4. **Type Detection**: Determine bundle type (DiagnosticReport, Prescription, etc.)
  5. **Storage**: Store as `FhirBundle` with correct `consentId`

  ## Success Indicators

  ### ✅ Successful Response:
  ```json
  {
    "success": true,
    "data": {
      "processingResults": {
        "totalEntries": 9,
        "processedEntries": 9,
        "successfulDecryptions": 9,
        "failedDecryptions": 0,
        "finalStatus": "RECEIVED"
      }
    }
  }
  ```

  ### ❌ Mapping Not Found:
  ```json
  {
    "error": "Health record fetch not found",
    "availableInHealthInformationRequest": ["txn-1", "txn-2"],
    "availableInHealthRecordFetch": ["txn-3", "txn-4"],
    "availableInHiRequest": ["txn-5", "txn-6"]
  }
  ```

  ## Prerequisites

  Before running this test:

  1. **Run Migration**: Apply the database migration to create `HealthInformationRequest` table
  2. **Create Mapping**: Either:
     - Manually insert a record in `HealthInformationRequest` with `transactionId: "59f04acd-0058-40e2-bc4b-a178f3158a1f"`
     - Or run the complete flow starting with health record fetch

  ## Troubleshooting

  ### Transaction ID Not Found
  - Check if the `HealthInformationRequest` record exists with the correct `transactionId`
  - Verify the mapping chain: `requestId` → `transactionId` → `consentId`

  ### Decryption Failures
  - Ensure key material is available in the `HealthRecordFetch` record
  - Check that the encrypted content format is correct

  ### Bundle Storage Issues
  - Verify consent exists and is valid
  - Check organization and patient associations

  ## Database Verification

  After successful processing, check:

  ```sql
  -- Check the mapping
  SELECT * FROM health_information_requests 
  WHERE transactionId = '59f04acd-0058-40e2-bc4b-a178f3158a1f';

  -- Check created bundles
  SELECT id, bundleType, consentId, careContextReference 
  FROM fhir_bundles 
  WHERE transactionId = '59f04acd-0058-40e2-bc4b-a178f3158a1f';
  ```

  ## UI Verification

  1. Navigate to the patient's consent details page
  2. Check that all bundles appear in the correct consent tab
  3. Verify no cross-contamination between different consents
}
