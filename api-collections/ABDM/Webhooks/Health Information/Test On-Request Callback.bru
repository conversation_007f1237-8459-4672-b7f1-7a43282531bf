meta {
  name: Test On-Request Callback
  type: http
  seq: 4
}

post {
  url: {{localUrl}}/api/webhook/api/v3/hiu/health-information/on-request
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: 47942819-fe48-44bf-8564-568ff66937a3
}

body:json {
  {"hiRequest":{"transactionId":"d6bc22bf-55a2-446f-9d3d-6c460b76e865","sessionStatus":"REQUESTED"},"error":null,"response":{"requestId":"077fd015-5670-42bc-8c13-aa2b4cf86982"}}
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  transactionId: "REPLACE_WITH_VALID_TRANSACTION_ID"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  // Verify the response structure
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.status === "success") {
      console.log("✅ On-request callback processed successfully");
      
      // Store the transaction ID for use in subsequent requests
      bru.setVar("processedTransactionId", "{{transactionId}}");
    } else {
      console.log("❌ Unexpected response structure:", responseBody);
    }
  } else {
    console.log("❌ Request failed with status:", res.getStatus());
  }
}

docs {
  # Test On-Request Callback
  
  This request tests the enhanced health information on-request callback webhook that properly maps transaction IDs to consents and handles the new callback structure.
  
  ## Purpose
  
  This simulates the callback that ABDM sends after receiving a health information request. The callback confirms that ABDM has received the request and provides the session status.
  
  ## New Callback Structure
  
  The updated callback now includes:
  - `hiRequest.transactionId`: The transaction ID for tracking
  - `hiRequest.sessionStatus`: Status of the session (typically "REQUESTED")
  - `error`: Any error information (null if successful)
  - `response.requestId`: The request ID for this callback
  
  ## Request Flow
  
  This callback is typically received after:
  1. Healthcare provider clicks "Pull Health Records" button
  2. Application sends health information request to ABDM
  3. ABDM acknowledges the request with this callback
  4. Later, ABDM sends the actual encrypted data via the data push webhook
  
  ## Testing Steps
  
  1. **Prerequisites**:
     - Ensure you have a valid transaction ID from a health record fetch request
     - The transaction ID should be associated with a consent
     - Optionally, have a corresponding HiRequest record in the database
  
  2. **Update Variables**:
     - Replace `{{transactionId}}` with a valid transaction ID
     - Update `baseUrl` if testing against a different environment
  
  3. **Execute Request**:
     - Send the request to trigger the callback processing
     - The webhook will attempt to find the corresponding HealthRecordFetch record
     - If not found, it will look for a HiRequest record and create a HealthRecordFetch
  
  ## Expected Behavior
  
  The webhook should:
  1. Extract the transaction ID from the payload
  2. Look for an existing HealthRecordFetch record with that transaction ID
  3. If found, update it with the callback information and set status to "PROCESSING"
  4. If not found, look for a HiRequest record with that transaction ID
  5. If HiRequest found, create a new HealthRecordFetch record from the HiRequest data
  6. Log comprehensive information about the patient and organization
  7. Return a success response
  
  ## Expected Response
  
  ```json
  {
    "status": "success",
    "message": "Health information on-request callback processed successfully",
    "details": {
      "transactionId": "your-transaction-id",
      "sessionStatus": "REQUESTED",
      "action": "HEALTH_RECORD_FETCH_UPDATED" // or "HEALTH_RECORD_FETCH_CREATED_FROM_HI_REQUEST"
    }
  }
  ```
  
  ## Database Changes
  
  After successful processing, you should see:
  1. A HealthRecordFetch record with status "PROCESSING"
  2. The responseData field populated with callback information
  3. Proper linking to the associated consent and patient records
  
  ## Enhanced Logging
  
  The webhook now logs:
  - Organization details (ID and name)
  - Patient details (ID, name, ABHA number)
  - HI types when pulling health records
  - Reception and processing stages
  - Transaction ID mapping results
  
  ## Troubleshooting
  
  - **400 Error**: Missing transaction ID in payload
  - **No Records Found**: Neither HealthRecordFetch nor HiRequest found for transaction ID
  - **Database Errors**: Issues with consent or patient record relationships
  - **Logging Issues**: Check that ABDM logger is properly configured
  
  ## Integration with Data Push
  
  This callback prepares the system for receiving the actual encrypted health data. The transaction ID from this callback will be used to match the subsequent data push webhook that contains the encrypted FHIR bundles.
}
