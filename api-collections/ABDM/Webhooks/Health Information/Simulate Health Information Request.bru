meta {
  name: Simulate Health Information Request
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/api/webhook/api/v3/hip/health-information/request
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: {{$guid}}
}

body:json {
  {
    "transactionId": "{{$guid}}",
    "hiRequest": {
      "consent": {
        "id": "{{consentId}}"
      },
      "dateRange": {
        "from": "1925-04-21T12:51:51.959Z",
        "to": "2025-04-21T12:51:51.959Z"
      },
      "dataPushUrl": "https://abhasbx.abdm.gov.in/abha/api/v3/patient-hiu/app/v0.5/health-information/transfer",
      "keyMaterial": {
        "cryptoAlg": "ECDH",
        "curve": "curve25519",
        "dhPublicKey": {
          "expiry": "2025-05-01T12:51:52.382Z",
          "parameters": "Ephemeral public key",
          "keyValue": "BCdNmBEeJ6/ONwqsjkksFSrg7sWBviUXM4Vt0eqjzJF1I+UKMa9qHo/WouHfTp8K00XsT9fJb5kcQbWAqNDxYkA="
        },
        "nonce": "O1hbl88La/Xnn9u6BIjH5ttUAKY86EovNfJvkBNb2jw="
      }
    }
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  consentId: "REPLACE_WITH_VALID_CONSENT_ID"
}

docs {
  # Health Information Request Webhook Simulator
  
  This request simulates the ABDM webhook callback for health information requests.
  
  ## Usage
  
  1. Replace `{{consentId}}` with a valid consent ID from your database
  2. The `transactionId` is automatically generated as a UUID
  
  ## Expected Response
  
  ```json
  {
    "requestId": "request-id-value",
    "timestamp": "2023-04-21T12:51:51.959Z",
    "acknowledgement": {
      "status": "OK"
    },
    "resp": {
      "requestId": "request-id-value"
    }
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM when a HIU requests health information based on a consent
  - The system will process the request asynchronously and send a notification to ABDM when done
}
