vars {
  baseUrl: "http://localhost:3000",
  hipId: "IN3310001477",
  abhaNumber: "91-1320-5817-0516",
  abhaAddress: "yokeshk199819@sbx",
  patientName: "<PERSON> <PERSON> Yokesh",
  gender: "M",
  dayOfBirth: "19",
  monthOfBirth: "4",
  yearOfBirth: "1998",
  addressLine: "NO 21 BAJANAI KOIL STREET, PUNGAM BEDU, PONNERI TALUK MINJUR POST, Minjur, Ponneri, Tiruvallur, Tamil Nadu",
  district: "THIRUVALLUR",
  state: "TAMIL NADU",
  pincode: "601203",
  phoneNumber: "**********"
}

docs {
  # Patient Share Webhook Environment
  
  This environment file contains variables used for testing the Patient Share webhook.
  
  ## Variables
  
  - `baseUrl`: The base URL of your application (default: http://localhost:3000)
  - `hipId`: Your facility's HIP ID
  - `abhaNumber`: A valid ABHA number
  - `abhaAddress`: A valid ABHA address
  - `patientName`: The patient's full name
  - `gender`: The patient's gender (M, F, O)
  - `dayOfBirth`: The patient's day of birth
  - `monthOfBirth`: The patient's month of birth
  - `yearOfBirth`: The patient's year of birth
  - `addressLine`: The patient's address line
  - `district`: The patient's district
  - `state`: The patient's state
  - `pincode`: The patient's pincode
  - `phoneNumber`: The patient's phone number
  
  ## Usage
  
  Update these variables with valid values before running the webhook tests.
  
  ## Notes
  
  - The HIP ID should match a facility ID in your database
  - The ABHA number and address should be valid ABHA identifiers
  - The patient details should be realistic
}
