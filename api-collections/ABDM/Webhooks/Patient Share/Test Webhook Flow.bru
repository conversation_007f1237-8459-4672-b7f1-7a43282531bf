meta {
  name: Test Webhook Flow
  type: http
  seq: 2
}

script:pre-request {
  // This script demonstrates the full flow of testing the webhook
  // It's not an actual HTTP request, but a guide for manual testing
  
  /*
  Testing the Patient Share Webhook Flow:
  
  1. First, make sure you have a valid HIP ID configured in your application
     - This should be associated with a branch in your database
     - Update the hipId variable in the Simulate Patient Share Webhook request
  
  2. Update the patient details in the Simulate Patient Share Webhook request
     - Use valid ABHA number and ABHA address
     - Provide realistic patient information
  
  3. Call the "Simulate Patient Share Webhook" request
     - This simulates ABDM sending a patient profile to your webhook
     - The webhook will process the data and create/update patient records
  
  4. Check your database to verify that:
     - A patient record was created (if new) or updated (if existing)
     - The patient's ABHA profile was properly stored
     - The patient is linked to the branch associated with the HIP ID
  */
}

docs {
  # Test Patient Share Webhook Flow
  
  This is a guide for testing the complete patient share webhook flow.
  
  ## Testing Steps
  
  1. First, make sure you have a valid HIP ID configured in your application
     - This should be associated with a branch in your database
     - Update the hipId variable in the Simulate Patient Share Webhook request
  
  2. Update the patient details in the Simulate Patient Share Webhook request
     - Use valid ABHA number and ABHA address
     - Provide realistic patient information
  
  3. Call the "Simulate Patient Share Webhook" request
     - This simulates ABDM sending a patient profile to your webhook
     - The webhook will process the data and create/update patient records
  
  4. Check your database to verify that:
     - A patient record was created (if new) or updated (if existing)
     - The patient's ABHA profile was properly stored
     - The patient is linked to the branch associated with the HIP ID
  
  ## Expected Results
  
  After completing the testing flow, you should see:
  
  1. A success response from your webhook with the acknowledgment payload
  2. A new patient record in your database (if the patient didn't exist)
  3. Updated ABHA profile information (if the patient already existed)
  4. A link between the patient and the branch associated with the HIP ID
  
  ## Notes
  
  - The patient share webhook is an important part of the ABDM integration
  - It allows patients to easily share their profile with your facility
  - This can streamline the patient registration process
  - The webhook handles both new patient creation and existing patient updates
}
