# ABDM Patient Share Webhook

This collection is designed to test the ABDM patient profile sharing webhook implementation.

## Overview

The Patient Share webhook is used by ABDM to send patient profile data to your application when a patient shares their profile with your HIP (Health Information Provider). This typically happens when a patient scans a QR code at your facility or selects your facility in the ABHA app.

## Webhook Flow

1. <PERSON><PERSON> initiates profile sharing from ABHA app or by scanning QR code
2. AB<PERSON> sends patient profile data to your webhook endpoint
3. Your application processes the data and creates/updates patient records
4. Your application sends an acknowledgment back to ABDM

## Testing with this Collection

### Prerequisites

1. Make sure your application is running locally on port 3000 (or update the `baseUrl` in the pre-request variables)
2. Have a valid HIP ID configured in your application (this should be associated with a branch)

### Testing Steps

1. Update the variables in the pre-request section of the "Simulate Patient Share Webhook" request:

   - `hipId`: Your facility's HIP ID
   - `abhaNumber`: A valid ABHA number
   - `abhaAddress`: A valid ABHA address
   - Other patient details as needed

2. Send the "Simulate Patient Share Webhook" request

   - This simulates ABDM sending a patient profile to your webhook

3. Check your application logs to verify that:
   - The webhook was received
   - A patient record was created or updated
   - An acknowledgment was sent back to ABDM

### Expected Results

After sending the webhook request, you should see:

1. A success response from your webhook with the acknowledgment payload
2. A new patient record in your database (if the patient didn't exist)
3. Updated ABHA profile information (if the patient already existed)
4. A link between the patient and the branch associated with the HIP ID

## Troubleshooting

If you encounter issues during testing:

1. Check the server logs for error messages
2. Verify that the HIP ID in the request matches a facility ID in your database
3. Make sure your application is properly configured to handle webhook requests
4. Verify that the webhook endpoint is accessible from the Bruno client

## ABDM Documentation Reference

For more information on the ABDM patient profile sharing API, refer to the official ABDM documentation:

- [ABDM Sandbox Documentation](https://sandbox.abdm.gov.in/docs/patient-profile-sharing)
- [ABDM API Specification](https://sandbox.abdm.gov.in/swagger/ndhm-hip.yaml)
