meta {
  name: Simulate Patient Share Webhook
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/api/webhook/api/v3/hip/patient/share
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

body:json {
  {
    "intent": "PROFILE_SHARE",
    "metaData": {
      "hipId": "{{hipId}}",
      "context": "1",
      "hprId": "<EMAIL>",
      "latitude": "12.9156002",
      "longitude": "77.6345798"
    },
    "profile": {
      "patient": {
        "abhaNumber": "{{abhaNumber}}",
        "abhaAddress": "{{abhaAddress}}",
        "name": "{{patientName}}",
        "gender": "{{gender}}",
        "dayOfBirth": "{{dayOfBirth}}",
        "monthOfBirth": "{{monthOfBirth}}",
        "yearOfBirth": "{{yearOfBirth}}",
        "address": {
          "line": "{{addressLine}}",
          "district": "{{district}}",
          "state": "{{state}}",
          "pincode": "{{pincode}}"
        },
        "phoneNumber": "{{phoneNumber}}"
      }
    }
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  hipId: "{{hipId}}",
  abhaNumber: "{{abhaNumber}}",
  abhaAddress: "{{abhaAddress}}",
  patientName: "{{patientName}}",
  gender: "{{gender}}",
  dayOfBirth: "{{dayOfBirth}}",
  monthOfBirth: "{{monthOfBirth}}",
  yearOfBirth: "{{yearOfBirth}}",
  addressLine: "{{addressLine}}",
  district: "{{district}}",
  state: "{{state}}",
  pincode: "{{pincode}}",
  phoneNumber: "{{phoneNumber}}"
}

docs {
  # Patient Share Webhook Simulator

  This request simulates the ABDM webhook callback for patient profile sharing.

  ## Usage

  1. Update the variables in the pre-request section with valid patient data
  2. The `REQUEST-ID` is automatically generated as a UUID

  ## Expected Response

  ```json
  {
    "acknowledgement": {
      "status": "SUCCESS",
      "abhaAddress": "yokeshk199819@sbx",
      "profile": {
        "context": "1",
        "tokenNumber": "15",
        "expiry": "1800"
      }
    },
    "response": {
      "requestId": "request-id-value"
    }
  }
  ```

  ## Notes

  - This webhook is called by ABDM when a patient shares their profile with your HIP
  - The system will create a new patient record if the patient doesn't exist
  - If the patient already exists, their ABHA profile will be updated
  - The patient will be linked to the branch associated with the HIP ID
}
