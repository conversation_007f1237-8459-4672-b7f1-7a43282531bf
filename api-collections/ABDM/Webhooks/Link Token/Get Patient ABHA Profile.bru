meta {
  name: Get Patient ABHA Profile
  type: http
  seq: 2
}

get {
  url: {{baseUrl}}/api/patients/{{patientId}}/abha-profile
  body: none
  auth: none
}

headers {
  Content-Type: application/json
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Get Patient ABHA Profile
  
  This request retrieves the ABHA profile for a patient, including the link token.
  
  ## Usage
  
  1. Replace `{{patientId}}` with a valid patient ID from your database
  
  ## Expected Response
  
  ```json
  {
    "abhaProfile": {
      "id": "...",
      "abhaNumber": "...",
      "abhaAddress": "...",
      "healthIdNumber": "...",
      "abhaStatus": "...",
      "abhaCardUrl": "...",
      "linkToken": "...",
      "linkTokenExpiry": "..."
    }
  }
  ```
  
  ## Notes
  
  - Use this request to verify that the link token was properly stored in the database
  - This can be used after simulating the webhook to check if the token was saved
}
