meta {
  name: Simulate Link Token Webhook copy copy
  type: http
  seq: 6
}

post {
  url: http://localhost:3005/api/webhook/api/v3/hip/health-information/request
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: 688734a2-6aac-4c5f-8e05-ea4edfc7c6a8
}

body:json {
  {
    "transactionId": "c1e9a87f-0b8e-4493-8369-cc54ecb812e5",
    "hiRequest": {
      "consent": {
        "id": "01c29290-c40a-4322-958b-6845f00f82b3"
      },
      "dateRange": {
        "from": "2025-04-22T05:04:35.995Z",
        "to": "2025-05-22T05:04:35.996Z"
      },
      "dataPushUrl": "https://webhook.site/257748ee-34e7-4658-ac6c-02fd35a38754",
      "keyMaterial": {
        "cryptoAlg": "ECDH",
        "curve": "curve25519",
        "dhPublicKey": {
          "expiry": "2025-05-22T07:32:08.311Z",
          "parameters": "Curve25519/32byte random key",
          "keyValue": "MIIBMTCB6gYHKoZIzj0CATCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCANCAAQoOh1XHSyimDjvrTAND8upriNwdBGcGJOBCxLpVRym3hDSGgq+mmnswv77VD8cv4p9TCOGalfuvUCZFeuMdWpe"
        },
        "nonce": "GBPpzWJjbRyzZk+TETkjh2HZF0mDyTdUS1Uk8vO7O5g="
      }
    }
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Link Token Webhook Simulator
  
  This request simulates the ABDM webhook callback for link token generation.
  
  ## Usage
  
  1. Replace `{{abhaAddress}}` with a valid ABHA address from your database
  2. The `linkToken` is automatically generated as a UUID
  3. The `requestId` is also automatically generated
  
  ## Expected Response
  
  ```json
  {
    "status": "success",
    "message": "Link token received and stored successfully"
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM after a link token generation request
  - The link token is stored in the database and associated with the patient
  - The link token is used for care context linking
}
