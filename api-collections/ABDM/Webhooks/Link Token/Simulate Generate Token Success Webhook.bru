meta {
  name: Simulate Generate Token Success Webhook
  type: http
  seq: 3
}

post {
  url: http://localhost:3000/api/webhook/api/v3/hip/token/on-generate-token
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
}

body:json {
  "abhaAddress": "kumarashutosh1508@sbx",
  "linkToken": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "response": {
    "requestId": "{{$guid}}"
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Generate Token Success Webhook Simulator
  
  This request simulates the ABDM webhook callback for successful token generation.
  
  ## Usage
  
  1. The `requestId` is automatically generated as a UUID
  
  ## Expected Response
  
  ```json
  {
    "status": "success",
    "message": "Link token received and stored successfully"
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM when token generation succeeds
  - The token information is stored in the database in both AbhaLinkToken and GenerateTokenNotify models
  - This allows tracking of token generation history
}
