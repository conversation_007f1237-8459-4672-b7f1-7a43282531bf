meta {
  name: Test Webhook Flow
  type: http
  seq: 4
}

script:pre-request {
  // This script demonstrates the full flow of testing the webhook
  // It's not an actual HTTP request, but a guide for manual testing
  
  /*
  Testing the Link Token Webhook Flow:
  
  1. First, get a valid patient ID and ABHA address from your database
     - Update the environment.bru file with these values
  
  2. Call the "Generate Link Token" request
     - This will initiate the link token generation process
     - Note the requestId in the response
  
  3. Call the "Simulate Link Token Webhook" request
     - This simulates ABDM sending the link token to your webhook
     - The webhook will store the token in the database
  
  4. Call the "Get Patient ABHA Profile" request
     - This verifies that the link token was properly stored
     - Check that the linkToken field is populated
  
  5. Now you can proceed with care context linking
     - The link token is required for care context linking
  */
}

docs {
  # Test Webhook Flow
  
  This is a guide for testing the complete link token webhook flow.
  
  ## Testing Steps
  
  1. First, get a valid patient ID and ABHA address from your database
     - Update the environment.bru file with these values
  
  2. Call the "Generate Link Token" request
     - This will initiate the link token generation process
     - Note the requestId in the response
  
  3. Call the "Simulate Link Token Webhook" request
     - This simulates AB<PERSON> sending the link token to your webhook
     - The webhook will store the token in the database
  
  4. Call the "Get Patient ABHA Profile" request
     - This verifies that the link token was properly stored
     - Check that the linkToken field is populated
  
  5. Now you can proceed with care context linking
     - The link token is required for care context linking
  
  ## Notes
  
  - This flow simulates the asynchronous nature of the ABDM API
  - In a real scenario, ABDM would call your webhook automatically
  - This testing approach allows you to verify your implementation without waiting for ABDM
}
