meta {
  name: Generate Link Token
  type: http
  seq: 3
}

post {
  url: {{baseUrl}}/api/patients/{{patientId}}/care-contexts/link-token
  body: none
  auth: none
}

headers {
  Content-Type: application/json
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Generate Link Token
  
  This request initiates the link token generation process.
  
  ## Usage
  
  1. Replace `{{patientId}}` with a valid patient ID from your database
  
  ## Expected Response
  
  ```json
  {
    "message": "Link token generation initiated. Token will be received via webhook.",
    "token": null,
    "requestId": "...",
    "async": true
  }
  ```
  
  ## Notes
  
  - This request initiates the asynchronous link token generation process
  - The actual token will be sent to the webhook endpoint
  - After making this request, you can simulate the webhook callback to test the full flow
}
