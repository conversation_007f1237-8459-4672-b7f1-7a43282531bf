meta {
  name: Simulate Link Token Webhook
  type: http
  seq: 1
}

post {
  url: http://localhost:3005/api/webhook/api/v3/hip/token/on-generate-token
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {"abhaAddress":"kumar_kumar.151515@sbx","linkToken":"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","response":{"requestId":"9015aa85-87e9-4166-b5d2-144fd7b90934"}}
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Link Token Webhook Simulator
  
  This request simulates the ABDM webhook callback for link token generation.
  
  ## Usage
  
  1. Replace `{{abhaAddress}}` with a valid ABHA address from your database
  2. The `linkToken` is automatically generated as a UUID
  3. The `requestId` is also automatically generated
  
  ## Expected Response
  
  ```json
  {
    "status": "success",
    "message": "Link token received and stored successfully"
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM after a link token generation request
  - The link token is stored in the database and associated with the patient
  - The link token is used for care context linking
}
