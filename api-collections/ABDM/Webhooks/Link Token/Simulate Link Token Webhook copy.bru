meta {
  name: Simulate Link Token Webhook copy
  type: http
  seq: 5
}

post {
  url: http://localhost:3005/api/webhook/api/v3/consent/request/hip/notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: 0c1fa152-1411-4b61-b0d6-a2f2b8563a50
}

body:json {
  {"notification":{"status":"GRANTED","consentId":"c3687e2f-6829-480e-b89d-e539d9123354","consentDetail":{"schemaVersion":"v3","consentId":"c3687e2f-6829-480e-b89d-e539d9123354","createdAt":"2025-04-28T18:10:40.990Z","patient":{"id":"yokeshk199819@sbx"},"careContexts":[{"patientReference":"cma14spw5000b107zd2f0wrur","careContextReference":"cma1e8zkg0001rpmkdu8trmc3"}],"purpose":{"text":"Self Requested","code":"PATRQT","refUri":"www.abdm.gov.in"},"hip":{"id":"IN3310001477"},"consentManager":{"id":"sbx"},"hiTypes":["Prescription","DiagnosticReport","OPConsultation","DischargeSummary","ImmunizationRecord","HealthDocumentRecord","WellnessRecord","Invoice"],"permission":{"accessMode":"VIEW","dateRange":{"from":"1925-04-28T18:10:40.787Z","to":"2025-04-28T18:10:40.787Z"},"dataEraseAt":"2125-04-28T18:10:40.787Z","frequency":{"unit":"DAY","value":0,"repeats":2}}},"signature":"tYoQmuRJXeiwdPX0+yfKX24nyacGQMxk3YqkpgruyRrd1whDhZH20ilK4izyPSnC1TsQXSwGONrnw2hLD/wK/YZa37s87SERgOU41SKK8EIF82u9bqlYok9N94Zi2AeNvHZDlg/Br4scB98Objk16it9G7rO/ACeBeRCxrN+JxwuWa/r2bZmlGDr8rEl7dJJlRBIToCj+mICra7G0R/CVysvYYk126c1R91KI3+6+IdKzsHGnh5/luNAN4zMS1thBJ2yhTVczFS+5t+nQpKH4VKj/f9LmjfEUS/IJHhPITsJ/IM8+SKw0igkjrSxPCz2sR8lIVbViw/hMAujIOIW/w==","grantAcknowledgement":false}}
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Link Token Webhook Simulator
  
  This request simulates the ABDM webhook callback for link token generation.
  
  ## Usage
  
  1. Replace `{{abhaAddress}}` with a valid ABHA address from your database
  2. The `linkToken` is automatically generated as a UUID
  3. The `requestId` is also automatically generated
  
  ## Expected Response
  
  ```json
  {
    "status": "success",
    "message": "Link token received and stored successfully"
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM after a link token generation request
  - The link token is stored in the database and associated with the patient
  - The link token is used for care context linking
}
