meta {
  name: Simulate Generate Token Error Webhook
  type: http
  seq: 2
}

post {
  url: http://localhost:3000/api/webhook/api/v3/hip/token/generate-token-notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
}

body:json {
  "error": {
    "code": "ABDM-1207: ",
    "message": "The information you provided does not match the details on record with <PERSON><PERSON><PERSON><PERSON>. Please verify and provide accurate information."
  },
  "response": {
    "requestId": "{{$guid}}"
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Generate Token Error Webhook Simulator
  
  This request simulates the ABDM webhook callback for generate token errors.
  
  ## Usage
  
  1. The `requestId` is automatically generated as a UUID
  
  ## Expected Response
  
  ```json
  {
    "acknowledgement": {
      "status": "SUCCESS"
    },
    "response": {
      "requestId": "request-id-value"
    }
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM when token generation fails
  - The error information is stored in the database in the GenerateTokenNotify model
  - This allows tracking and debugging of token generation issues
}
