meta {
  name: Simulate Care Context Link Webhook
  type: http
  seq: 1
}

post {
  url: {{baseUrl}}/api/webhook/api/v3/link/on_carecontext
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$timestamp}}
}

body:json {
  {
    "abhaAddress": "kumarashutosh1508@sbx",
    "status": "Successfully Linked care context",
    "response": {
      "requestId": "94ebd91b-8e84-46a7-bb62-c72076583b8f"
    }
  }
}

vars:pre-request {
  baseUrl: "http://localhost:3001"
}

docs {
  # Care Context Link Webhook Simulator
  
  This request simulates the ABDM webhook callback for care context linking.
  
  ## Usage
  
  1. Replace `abhaAddress` with a valid ABHA address from your database
  2. The `requestId` should match the requestId used when linking the care context
  
  ## Expected Response
  
  ```json
  {
    "abhaAddress": "kumarashutosh1508@sbx",
    "status": "Successfully Linked care context",
    "response": {
      "requestId": "94ebd91b-8e84-46a7-bb62-c72076583b8f"
    }
  }
  ```
  
  ## Notes
  
  - This webhook is called by ABDM after a care context linking request
  - The care context status is updated to "Linked" in the database
  - The webhook payload is stored in the additionalInfo field of the CareContext model
}
