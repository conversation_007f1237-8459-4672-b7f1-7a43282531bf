meta {
  name: Consent Init Callback
  type: http
  seq: 1
}

post {
  url: {{localUrl}}/api/webhook/api/v3/hiu/consent/request/on-init
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: e5e947be-d4ec-4151-83d0-2b1d97400a6f
}

body:json {
  {"consentRequest":{"id":"c10fcd71-e501-43ab-911e-815bfad4df3f"},"error":null,"response":{"requestId":"7e1f606d-e657-4f40-b2b4-46dcd0e696cc"}}
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  consentRequestId: "REPLACE_WITH_VALID_CONSENT_REQUEST_ID",
  hiuId: "IN3310001477"
}

docs {
  # Consent Init Callback
  
  This request simulates the ABDM consent init callback webhook. It sends an acknowledgment to your application after a consent request is initiated.
  
  ## Request
  
  - **Method**: POST
  - **URL**: `/api/webhook/api/v3/hiu/consent/request/on-init`
  - **Headers**:
    - `Content-Type`: application/json
    - `request-id`: A unique UUID for the request
    - `x-hiu-id`: The HIU ID
  
  ## Response
  
  The response should be a 202 Accepted status with a success message:
  
  ```json
  {
    "status": "Accepted",
    "message": "Consent init callback processed successfully"
  }
  ```
  
  ## Error Scenario
  
  To test error handling, you can use the following payload:
  
  ```json
  {
    "consentRequest": {
      "id": "CONSENT_REQUEST_ID"
    },
    "response": {
      "requestId": "REQUEST_ID"
    },
    "error": {
      "code": "INVALID_REQUEST",
      "message": "Invalid consent request"
    }
  }
  ```
}
