meta {
  name: Consent Notify Callback
  type: http
  seq: 2
}

post {
  url: {{localUrl}}/api/webhook/api/v3/hiu/consent/request/notify
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  request-id: 94dd42f0-2452-429f-8edd-cc535bd719e8
}

body:json {
  {"notification":{"consentRequestId":"c10fcd71-e501-43ab-911e-815bfad4df3f","status":"GRANTED","consentArtefacts":[{"id":"4115ed83-4a26-4299-b6f5-389fc0654399"}]}}
}

vars:pre-request {
  baseUrl: "http://localhost:3000",
  consentRequestId: "REPLACE_WITH_VALID_CONSENT_REQUEST_ID",
  consentArtefactId: "REPLACE_WITH_VALID_CONSENT_ARTEFACT_ID",
  hiuId: "IN3310001477"
}

docs {
  # Consent Notify Callback
  
  This request simulates the ABDM consent notify callback webhook. It sends a notification to your application when a consent is GRANTED, DENIED, or REVOKED.
  
  ## Request
  
  - **Method**: POST
  - **URL**: `/api/webhook/api/v3/hiu/consent/request/notify`
  - **Headers**:
    - `Content-Type`: application/json
    - `request-id`: A unique UUID for the request
    - `x-hiu-id`: The HIU ID
  
  ## Response
  
  The response should be a 202 Accepted status with a success message:
  
  ```json
  {
    "status": "Accepted",
    "message": "Consent notify callback processed successfully"
  }
  ```
  
  ## Different Status Scenarios
  
  ### GRANTED
  
  ```json
  {
    "notification": {
      "consentRequestId": "CONSENT_REQUEST_ID",
      "status": "GRANTED",
      "consentArtefacts": [
        {
          "id": "CONSENT_ARTEFACT_ID"
        }
      ]
    }
  }
  ```
  
  ### DENIED
  
  ```json
  {
    "notification": {
      "consentRequestId": "CONSENT_REQUEST_ID",
      "status": "DENIED",
      "reason": "Patient denied the consent request"
    }
  }
  ```
  
  ### REVOKED
  
  ```json
  {
    "notification": {
      "consentRequestId": "CONSENT_REQUEST_ID",
      "status": "REVOKED",
      "reason": "Patient revoked the consent"
    }
  }
  ```
}
