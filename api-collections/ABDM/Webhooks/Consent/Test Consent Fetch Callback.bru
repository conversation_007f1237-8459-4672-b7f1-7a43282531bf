meta {
  name: Test Consent Fetch Callback
  type: http
  seq: 8
}

post {
  url: {{localUrl}}/api/webhook/api/v3/hiu/consent/on-fetch
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  REQUEST-ID: {{$guid}}
  TIMESTAMP: {{$isoTimestamp}}
}

body:json {
  {
    "consent": {
      "status": "GRANTED",
      "consentDetail": {
        "consentId": "15538d65-0663-4c2d-bb71-ad70eef3b1aa",
        "hip": {
          "id": "IN2710002376",
          "name": "Manorama Test Facility",
          "type": "HIP"
        },
        "hiu": {
          "id": "IN2910001918"
        },
        "hiTypes": [
          "DiagnosticReport",
          "HealthDocumentRecord",
          "OPConsultation",
          "WellnessRecord",
          "ImmunizationRecord",
          "Prescription",
          "DischargeSummary"
        ],
        "patient": {
          "id": "varun2001@sbx"
        },
        "purpose": {
          "text": "Care Management",
          "code": "CAREMGT",
          "refUri": "www.abhasbx.gov.in"
        },
        "createdAt": "2025-07-25T11:35:24.525Z",
        "requester": {
          "name": "Bose",
          "identifier": {
            "value": "MCI-10001",
            "type": "REGNO",
            "system": "https://www.mciindia.org"
          }
        },
        "permission": {
          "accessMode": "VIEW",
          "dateRange": {
            "from": "2025-06-25T11:35:15.394Z",
            "to": "2025-07-25T11:35:15.394Z"
          },
          "dataEraseAt": "2026-07-25T11:35:15.394Z",
          "frequency": {
            "unit": "HOUR",
            "value": 0,
            "repeats": 0
          }
        },
        "lastUpdated": "2025-07-25T11:35:24.556Z",
        "careContexts": [
          {
            "patientReference": "601831",
            "careContextReference": "KBHOP250030802"
          }
        ],
        "schemaVersion": "v3",
        "consentManager": {
          "id": "sbx"
        }
      },
      "signature": "GZxeTYXgPzgiHgEImdMPyjNsjnifqmCoA2Ta4J3fRvZYmvrWOfMQfJHrjaKpmGa6ZeSaDq4FS0M5fRBFOs25Pfxd2M+rQKVQ5ZcdeLCPF+Iyz6INltIBBfrgIQaNnijC83LoysgMeH0mIX3qbvUtLuKhSnOK860fVVtm5Te2uwA2Hb2XYsBCsm3QX8b/MlQ200fxqf6+5Djenr9G+Z5q+aRN98r3YhIGF/QGM1boG7ZnP8T818xSZ47Dq4mrAVUWf+FjmBk3RmT/yp/sGt5+X2jwAyj2dU2zpLi0VYd83yWofux5gaOFSVJN5YTieNubC/WpIOp+5rN6xYxmg33HyA=="
    },
    "error": null,
    "response": {
      "requestId": "39d2efe9-9e9d-427e-a4bc-d20883367a3e"
    },
    "resp": null
  }
}

vars:pre-request {
  localUrl: "http://localhost:3000"
}

script:post-response {
  // Log the response for debugging
  console.log("Response Status:", res.getStatus());
  console.log("Response Body:", res.getBody());
  
  // Verify the response structure
  if (res.getStatus() === 200) {
    const responseBody = res.getBody();
    if (responseBody && responseBody.success) {
      console.log("✅ Consent fetch callback processed successfully");
      
      if (responseBody.data) {
        const data = responseBody.data;
        console.log(`📋 Consent Details:`);
        console.log(`   Internal ID: ${data.consentId}`);
        console.log(`   ABDM Consent ID: ${data.abdmConsentId}`);
        console.log(`   Status: ${data.status}`);
        console.log(`   Updated At: ${data.updatedAt}`);
      }
    } else {
      console.log("❌ Unexpected response structure:", responseBody);
    }
  } else if (res.getStatus() === 404) {
    const responseBody = res.getBody();
    console.log("❌ Consent not found");
    console.log(`   ABDM Consent ID: ${responseBody.abdmConsentId}`);
    console.log("   Make sure a consent record exists with this ABDM consent ID");
  } else if (res.getStatus() === 400) {
    const responseBody = res.getBody();
    console.log("❌ Bad request:", responseBody.error);
  } else {
    console.log("❌ Request failed with status:", res.getStatus());
  }
}

docs {
  # Test Consent Fetch Callback

  This endpoint tests the ABDM consent fetch callback webhook that receives detailed consent information after a consent fetch request.

  ## Purpose

  When the system calls the ABDM consent fetch API (`/hiecm/consent/v3/fetch`), ABDM responds with detailed consent information via this callback webhook. The webhook updates the local consent record with:

  - Detailed consent information
  - Digital signature
  - Care contexts
  - HI types
  - Permission details
  - Expiry information

  ## Flow Overview

  1. **Consent Fetch Request**: System calls ABDM `/hiecm/consent/v3/fetch` API
  2. **ABDM Processing**: ABDM processes the request and prepares detailed consent info
  3. **Callback Webhook**: ABDM calls this webhook with detailed consent information
  4. **Database Update**: System updates local consent record with detailed info

  ## Payload Structure

  The callback payload contains:

  ### `consent.status`
  - Current status of the consent (GRANTED, DENIED, EXPIRED, etc.)

  ### `consent.consentDetail`
  - **consentId**: ABDM consent artifact ID
  - **hip**: Health Information Provider details
  - **hiu**: Health Information User details  
  - **hiTypes**: Array of health information types
  - **patient**: Patient ABHA details
  - **purpose**: Purpose of consent (Care Management, etc.)
  - **requester**: Doctor/requester information
  - **permission**: Access permissions and date ranges
  - **careContexts**: Care context references
  - **createdAt/lastUpdated**: Timestamps

  ### `consent.signature`
  - Digital signature for consent verification

  ### `response.requestId`
  - Request ID for tracking and correlation

  ## Database Updates

  The webhook updates the consent record with:

  ```sql
  UPDATE Consent SET
    status = 'GRANTED',
    consentId = '15538d65-0663-4c2d-bb71-ad70eef3b1aa',
    consentArtifact = '{"consentDetail": {...}, "signature": "...", "fetchedAt": "..."}',
    careContexts = '[{"patientReference": "601831", "careContextReference": "KBHOP250030802"}]',
    hiTypes = '["DiagnosticReport", "Prescription", ...]',
    permission = '{"accessMode": "VIEW", "dateRange": {...}}',
    expiryDate = '2026-07-25T11:35:15.394Z',
    updatedAt = NOW()
  WHERE consentId = '15538d65-0663-4c2d-bb71-ad70eef3b1aa'
     OR consentRequestId = '15538d65-0663-4c2d-bb71-ad70eef3b1aa';
  ```

  ## Success Response

  ```json
  {
    "success": true,
    "message": "Consent fetch callback processed successfully",
    "data": {
      "consentId": "internal-consent-id",
      "abdmConsentId": "15538d65-0663-4c2d-bb71-ad70eef3b1aa",
      "status": "GRANTED",
      "updatedAt": "2025-07-25T12:00:00.000Z"
    }
  }
  ```

  ## Error Responses

  ### Consent Not Found (404)
  ```json
  {
    "error": "Consent not found",
    "abdmConsentId": "15538d65-0663-4c2d-bb71-ad70eef3b1aa"
  }
  ```

  ### Missing Consent Details (400)
  ```json
  {
    "error": "Missing consent details"
  }
  ```

  ### ABDM Error (400)
  ```json
  {
    "error": "Consent fetch error received"
  }
  ```

  ## Testing Steps

  1. **Update Consent ID**: Replace the `consentId` in the payload with an actual consent ID from your database
  2. **Execute Request**: Send the POST request to the webhook
  3. **Check Response**: Verify successful processing
  4. **Database Verification**: Check that the consent record was updated with detailed information

  ## Prerequisites

  - A consent record must exist in the database with the specified `consentId` or `consentRequestId`
  - The consent should be in a state where it can receive detailed information

  ## Database Verification

  After successful processing, verify the updates:

  ```sql
  -- Check the updated consent
  SELECT 
    id, 
    consentId, 
    status, 
    hiTypes, 
    consentArtifact,
    updatedAt
  FROM Consent 
  WHERE consentId = '15538d65-0663-4c2d-bb71-ad70eef3b1aa';

  -- Check audit log
  SELECT * FROM ConsentAuditLog 
  WHERE action = 'CONSENT_FETCH_CALLBACK' 
  ORDER BY createdAt DESC 
  LIMIT 5;
  ```

  ## Integration with Consent Fetch

  This webhook is automatically called by ABDM after:
  1. Manual consent artifact fetching via `/api/abdm/consent/fetch-artifacts`
  2. Automatic consent artifact fetching during consent grant processing
  3. Direct ABDM API calls to `/hiecm/consent/v3/fetch`

  ## Troubleshooting

  - **404 Error**: Ensure consent exists with matching `consentId` or `consentRequestId`
  - **400 Error**: Check payload structure and required fields
  - **500 Error**: Check server logs for database or processing errors
}
