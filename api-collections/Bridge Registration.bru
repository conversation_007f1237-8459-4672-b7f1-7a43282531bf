meta {
  name: Bridge Registration
  type: http
  seq: 2
}

post {
  url: https://facilitysbx.abdm.gov.in/v1/bridges/MutipleHRPAddUpdateServices
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "facilityId": "IN3310001477",
    "facilityName": "Yokesh Clinic Test",
    "HRP": [
      {
        "bridgeId": "SBXID_009205",
        "hipName": "Yokesh Clinic Test",
        "type": "HIP",
        "active": true
      }
    ]
  }
}

docs {
  # Bridge Registration API
  
  This API is used to register a bridge service (HIP/HIU) with ABDM.
  
  ## Request Details
  
  - **URL**: https://facilitysbx.abdm.gov.in/v1/bridges/MutipleHRPAddUpdateServices
  - **Method**: POST
  - **Content-Type**: application/json
  
  ## Request Body
  
  The request body contains information about the facility and the bridge service:
  
  - `facilityId`: The ABDM Facility ID
  - `facilityName`: The name of the facility
  - `HRP`: An array of bridge services to register
    - `bridgeId`: The Bridge ID
    - `hipName`: The name of the HIP
    - `type`: The type of bridge service (HIP or HIU)
    - `active`: Whether the bridge service is active
  
  ## Response
  
  The API returns a 200 OK status code on successful registration.
}
