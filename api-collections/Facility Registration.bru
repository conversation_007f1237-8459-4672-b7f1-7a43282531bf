meta {
  name: Facility Registration
  type: http
  seq: 1
}

post {
  url: https://apihspsbx.abdm.gov.in/v4/hfr/facility/master
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "infoDtlsRequestDTO": {
        "facName": "Demo Clinic 2",
        "country": "India",
        "state": "33",
        "district": "589",
        "pincode": "601203",
        "geolocation": "13.29402796564807,80.24876214788236",
        "subDistrict": "5698",
        "vilCityTown": "629010",
        "address1": "no 21  street",
        "address2": "",
        "landlineNo": "",
        "mobileNo": "8098444187",
        "facEmail": "",
        "facWebsite": "",
        "facOwnership": "P",
        "facOwnerGovt": "",
        "facOwnerPrivate": "",
        "ownerSubType": "",
        "facCentral": "",
        "privateProfitType": "",
        "privateNotProfitType": "",
        "mobileNoVerified": "Yes",
        "facEmailVerified": "No",
        "systemOfMedicine": "H",
        "typeOfService": "OPD",
        "facilityType": 63,
        "facilitySubtype": 28,
        "facOperStatus": "F",
        "bookAppUrl": "",
        "ifAnyOther": "",
        "crtUsr": "71-0310-0551-5027",
        "emrSystem": "N",
        "emrSoftware": "",
        "entityType": "",
        "healthId": "71-0310-0551-5027",
        "facRegion": "R"
    },
    "attachmentsDtlsRequestDTO": [],
    "medicalInfraRequestDTO": [],
    "operationRequestDTO": [],
    "optionalDetailRequestDTO": [],
    "fieldQueryRequestDTO": [
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "facImage1Review",
            "queryComment": "facImage1Review",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "otherQueryReview",
            "queryComment": "otherQueryReview",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "facEmailReview",
            "queryComment": "facEmailReview",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "countryReview",
            "queryComment": "countryReview",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "facilitySubtypeReview",
            "queryComment": "facilitySubtypeReview",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "addProofReview",
            "queryComment": "addProofReview",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "facImage1Review",
            "queryComment": "facImage1Review",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "mobileNoReview",
            "queryComment": "mobileNoReview",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "addProof3Review",
            "queryComment": "addProof3Review",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        },
        {
            "id": 226,
            "hprProfileId": 2525,
            "fieldName": "subDistrictReview",
            "queryComment": "subDistrictReview",
            "queryBy": 177,
            "queryStatus": "0",
            "createdAt": "2022-04-08T07:15:36.000+00:00",
            "updatedAt": null,
            "fieldLabel": "Registered With Council",
            "queryType": 2,
            "sectionName": null
        }
    ],
    "tfacilityLinkedUseRequestDTO": [],
    "equipmentDtlsRequestDTO": []
  }
}

docs {
  # Facility Registration API
  
  This API is used to register a healthcare facility with ABDM as a Health Information Provider (HIP).
  
  ## Request Details
  
  - **URL**: https://apihspsbx.abdm.gov.in/v4/hfr/facility/master
  - **Method**: POST
  - **Content-Type**: application/json
  
  ## Request Body
  
  The request body contains detailed information about the healthcare facility:
  
  - `infoDtlsRequestDTO`: Basic facility information (name, address, contact details, etc.)
  - `attachmentsDtlsRequestDTO`: Any attachments related to the facility
  - `medicalInfraRequestDTO`: Medical infrastructure details
  - `operationRequestDTO`: Operational details
  - `optionalDetailRequestDTO`: Optional details
  - `fieldQueryRequestDTO`: Field queries for verification
  - `tfacilityLinkedUseRequestDTO`: Linked users
  - `equipmentDtlsRequestDTO`: Equipment details
  
  ## Response
  
  The API returns a 200 OK status code on successful registration.
}
