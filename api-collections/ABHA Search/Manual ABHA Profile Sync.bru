meta {
  name: Manual ABHA Profile Sync
  type: http
  seq: 4
}

post {
  url: {{baseUrl}}/api/patients/{{patientId}}/abha-profile-patient-sync
  body: none
  auth: none
}

headers {
  Content-Type: application/json
}

docs {
  title: "Manual ABHA Profile Sync"
  description: "This endpoint allows you to manually trigger the ABHA profile synchronization for a patient. While this synchronization happens automatically after ABHA verification or login, this endpoint can be used to manually refresh the patient data from ABHA if needed."
  
  section: "Request" {
    description: "This is a POST request with no body required. The patient ID is specified in the URL path."
  }
  
  section: "Response" {
    description: "Returns the updated patient information with ABHA details."
    
    example: {
      "message": "Patient data synchronized with ABHA profile successfully",
      "patient": {
        "id": "clq1234abcd",
        "firstName": "John",
        "lastName": "Doe",
        "abhaNumber": "12-3456-7890-1234",
        "abhaAddress": "johndoe@abdm",
        "syncedAt": "2023-12-25T10:30:45.123Z"
      }
    }
  }
  
  section: "Prerequisites" {
    description: "Before using this endpoint, ensure that:
1. The patient has a valid ABHA profile in the system
2. The X-Token stored in the database is not expired
3. You have the necessary permissions to access patient data"
  }
  
  section: "Use Cases" {
    description: "This manual sync can be useful in scenarios such as:
1. When patient information in ABHA has been updated and you want to refresh it in your system
2. When the automatic sync failed for some reason
3. For testing or troubleshooting ABHA integration"
  }
}

vars:pre-request {
  // You can set variables here that will be available during the request
}

vars:post-response {
  // You can parse the response here and set variables for use in other requests
}
