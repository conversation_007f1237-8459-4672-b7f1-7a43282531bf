meta {
  name: Search ABHA by Mobile
  type: http
  seq: 1
}

post {
  url: http://localhost:3000/api/abdm/abha-search/mobile
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "mobile": "**********"
  }
}

docs {
  # Search ABHA by Mobile API
  
  This API allows searching for ABHA (Ayushman Bharat Health Account) details using a mobile number.
  
  ## Request Details
  
  - **URL**: /api/abdm/abha-search/mobile
  - **Method**: POST
  - **Content-Type**: application/json
  
  ## Request Body
  
  The request body contains the mobile number to search with:
  
  - `mobile`: The mobile number to search (required)
  
  ## Response
  
  The API returns the search results from ABDM, which typically includes:
  
  - `txnId`: Transaction ID for the search
  - Other ABHA details if found
  
  ## Error Responses
  
  - 400 Bad Request: If the mobile number is missing or invalid
  - 401 Unauthorized: If the user is not authenticated
  - 500 Internal Server Error: For server-side errors
  
  ## ABDM API Reference
  
  This API integrates with the ABDM API endpoint:
  - V3 URL: {{base_url}}/v3/profile/account/abha/search
  - V3 Request: POST
}
