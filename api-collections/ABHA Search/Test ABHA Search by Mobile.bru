meta {
  name: Test ABHA Search by Mobile
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/api/abdm/abha-search/mobile
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "mobile": "**********"
  }
}

docs {
  # Test ABHA Search by Mobile API
  
  This API allows testing the search for ABHA (Ayushman Bharat Health Account) details using a mobile number.
  
  ## Request Details
  
  - **URL**: /api/abdm/abha-search/mobile
  - **Method**: POST
  - **Content-Type**: application/json
  
  ## Request Body
  
  The request body contains the mobile number to search with:
  
  - `mobile`: The mobile number to search (required)
  
  ## Response
  
  The API returns the search results from ABDM, which typically includes:
  
  - `txnId`: Transaction ID for the search
  - Other ABHA details if found
  
  ## Testing Notes
  
  - Use this API to test the ABHA search functionality
  - Update the mobile number to test different scenarios
  - This API is used by the patient registration form to pre-fill patient details
}
