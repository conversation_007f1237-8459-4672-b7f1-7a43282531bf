---
name: "Bug Report"
about: "Report something that's broken or not working as expected"
title: "[Bug] <concise-summary>"
labels: [bug]
assignees: []
---

### 🐞 Bug Summary

> What’s broken? Include a short but clear description of the issue.

---

### 📍 Steps to Reproduce

> Be precise. Help the agent (or dev) consistently reproduce the bug.

1. Go to `/dashboard`
2. Click on the "Filter" button
3. Select a filter option
4. Observe: UI doesn't update

---

### 📷 Screenshots / Logs (optional)

> Add screenshot, console error, or stack trace if helpful

---

### 🧪 Expected vs. Actual

**Expected:**

> Results should update when filters are applied.

**Actual:**

> The UI stays static; nothing changes.

---

### 🧬 Environment

> Fill in all that apply

- OS: Mac / Windows / Linux
- Browser: Chrome / Safari / Firefox
- Environment: Local / Staging / Prod
- Version: `v1.3.0`

---

### 📋 Test Cases

> What should be tested once the bug is fixed?

- ✅ Applying a filter updates the list in real time
- ✅ No console errors on interaction
- ✅ Works across all major browsers

---

### 📂 Suspected Files / Modules

> If you know where the bug might live, list it here.

- `components/UserDashboard.tsx`
- `pages/api/users.ts`

---

### ⚙️ Notes / Constraints

> Known edge cases, related modules, tech limitations.

---

### 🔗 Related Issues or References

> Link to other issues, PRs, or error reports
