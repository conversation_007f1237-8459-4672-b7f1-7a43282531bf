{"info": {"_postman_id": "42d82a1b-d679-4079-9aaf-62b08d91e31e", "name": "FAST2SMS API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "28905351", "_collection_link": "https://www.postman.com/fast2sms/fast2sms/collection/vqpz1ay/fast2sms-api?action=share&source=collection_link&creator=28905351"}, "item": [{"name": "DLT SMS API", "item": [{"name": "GET Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://www.fast2sms.com/dev/bulkV2?authorization={{authorization}}&route=dlt&sender_id=DLT_SENDER_ID&message=YOUR_MESSAGE_ID&variables_values=ABC1|DEF2|GHI3|&numbers=8888888888&schedule_time=01-01-2025-10-30&flash=0", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"], "query": [{"key": "authorization", "value": "{{authorization}}"}, {"key": "route", "value": "dlt"}, {"key": "sender_id", "value": "DLT_SENDER_ID"}, {"key": "message", "value": "YOUR_MESSAGE_ID"}, {"key": "variables_values", "value": "ABC1|DEF2|GHI3|"}, {"key": "numbers", "value": "8888888888"}, {"key": "schedule_time", "value": "01-01-2025-10-30"}, {"key": "flash", "value": "0"}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use query parameters to help specify the resource you want data on (e.g., in this request, we have `id=1`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "POST Method (single)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"route\": \"dlt\",\n    \"sender_id\": \"DLT_SENDER_ID\",\n    \"message\": \"YOUR_MESSAGE_ID\",\n    \"variables_values\": \"ABC1|DEF2|GHI3|\",\n    \"numbers\": \"9999999999,8888888888,7777777777\",\n    \"flash\": 0,\n    \"schedule_time\": \"01-01-2025-10-30\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/bulkV2", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}, {"name": "POST Method (multiple)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"route\": \"dlt\",\n    \"requests\": [\n        {\n            \"sender_id\": \"DLT_SENDER_ID\",\n            \"message\": \"YOUR_MESSAGE_ID\",\n            \"variables_values\": \"ABC1|DEF2|GHI3|\",\n            \"numbers\": \"8888888888\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/custom", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "custom"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}]}, {"name": "DLT SMS (Manual) API", "item": [{"name": "GET Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://www.fast2sms.com/dev/bulkV2?authorization={{authorization}}&route=dlt_manual&sender_id=DLT_APPROVED_SENDER_ID&entity_id=DLT_ENTITY_ID&template_id=DLT_CONTENT_TEMPLATE_ID&message=DLT_APPROVED_MESSAGE&numbers=9999999999&schedule_time=01-01-2025-10-30&flash=0", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"], "query": [{"key": "authorization", "value": "{{authorization}}"}, {"key": "route", "value": "dlt_manual"}, {"key": "sender_id", "value": "DLT_APPROVED_SENDER_ID"}, {"key": "entity_id", "value": "DLT_ENTITY_ID"}, {"key": "template_id", "value": "DLT_CONTENT_TEMPLATE_ID"}, {"key": "message", "value": "DLT_APPROVED_MESSAGE"}, {"key": "numbers", "value": "9999999999"}, {"key": "schedule_time", "value": "01-01-2025-10-30"}, {"key": "flash", "value": "0"}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use query parameters to help specify the resource you want data on (e.g., in this request, we have `id=1`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "POST Method (single)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"route\": \"dlt_manual\",\n    \"sender_id\": \"DLT_APPROVED_SENDER_ID\",\n    \"template_id\": \"DLT_CONTENT_TEMPLATE_ID\",\n    \"entity_id\": \"DLT_ENTITY_ID\",\n    \"message\": \"DLT_APPROVED_MESSAGE\",\n    \"numbers\": \"9999999999,8888888888,7777777777\",\n    \"flash\": 0,\n    \"schedule_time\": \"01-01-2025-10-30\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/bulkV2", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}, {"name": "POST Method (multiple)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"route\": \"dlt_manual\",\n    \"requests\": [\n        {\n            \"sender_id\": \"DLT_APPROVED_SENDER_ID\",\n            \"template_id\": \"DLT_CONTENT_TEMPLATE_ID\",\n            \"entity_id\": \"DLT_ENTITY_ID\",\n            \"message\": \"DLT_APPROVED_MESSAGE\",\n            \"numbers\": \"9999999999\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/custom", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "custom"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}]}, {"name": "Quick SMS API", "item": [{"name": "GET Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://www.fast2sms.com/dev/bulkV2?authorization={{authorization}}&route=q&message=This is test message&numbers=8888888888&schedule_time=01-01-2025-10-30&flash=0", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"], "query": [{"key": "authorization", "value": "{{authorization}}"}, {"key": "route", "value": "q"}, {"key": "message", "value": "This is test message"}, {"key": "numbers", "value": "8888888888"}, {"key": "schedule_time", "value": "01-01-2025-10-30"}, {"key": "flash", "value": "0"}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use query parameters to help specify the resource you want data on (e.g., in this request, we have `id=1`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "POST Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"route\": \"q\",\n    \"message\": \"This is test message\",\n    \"numbers\": \"9999999999,8888888888,7777777777\",\n    \"flash\": 0,\n    \"schedule_time\": \"01-01-2025-10-30\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/bulkV2", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}]}, {"name": "OTP SMS API", "item": [{"name": "GET Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://www.fast2sms.com/dev/bulkV2?authorization={{authorization}}&route=otp&variables_values=5678&numbers=8888888888&flash=0", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"], "query": [{"key": "authorization", "value": "{{authorization}}"}, {"key": "route", "value": "otp"}, {"key": "variables_values", "value": "5678"}, {"key": "numbers", "value": "8888888888"}, {"key": "flash", "value": "0"}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use query parameters to help specify the resource you want data on (e.g., in this request, we have `id=1`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "POST Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"route\": \"otp\",\n    \"variables_values\": \"5432\",\n    \"numbers\": \"9999999999,8888888888,7777777777\",\n    \"flash\": 0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/bulkV2", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "bulkV2"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}]}, {"name": "Wallet Balance API", "item": [{"name": "GET Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://www.fast2sms.com/dev/wallet?authorization={{authorization}}", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "wallet"], "query": [{"key": "authorization", "value": "{{authorization}}"}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use query parameters to help specify the resource you want data on (e.g., in this request, we have `id=1`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "POST Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/wallet", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "wallet"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}]}, {"name": "DLT Manager", "item": [{"name": "List Sender", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://www.fast2sms.com/dev/dlt_manager?authorization={{authorization}}&type=template", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "dlt_manager"], "query": [{"key": "authorization", "value": "{{authorization}}"}, {"key": "type", "value": "template"}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use query parameters to help specify the resource you want data on (e.g., in this request, we have `id=1`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "List Sender", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/dlt_manager", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "dlt_manager"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}, {"name": "List Template", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://www.fast2sms.com/dev/dlt_manager?authorization={{authorization}}&type=template", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "dlt_manager"], "query": [{"key": "authorization", "value": "{{authorization}}"}, {"key": "type", "value": "template"}]}, "description": "This is a GET request and it is used to \"get\" data from an endpoint. There is no request body for a GET request, but you can use query parameters to help specify the resource you want data on (e.g., in this request, we have `id=1`).\n\nA successful GET response will have a `200 OK` status, and should include some kind of response body - for example, HTML web content or JSON data."}, "response": []}, {"name": "List Template", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authorization}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"type\" : \"template\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://www.fast2sms.com/dev/dlt_manager", "protocol": "https", "host": ["www", "fast2sms", "com"], "path": ["dev", "dlt_manager"]}, "description": "This is a POST request, submitting data to an API via the request body. This request submits JSON data, and the data is reflected in the response.\n\nA successful POST request typically returns a `200 OK` or `201 Created` response code."}, "response": []}]}], "auth": {"type": "inherit"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "authorization", "value": "YOUR_API_KEY", "type": "string"}]}